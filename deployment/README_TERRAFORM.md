# Wecandoo - Deployment

This folder should contain all files related to the deployment: CI, CD, ECS, K8S, ...

## Terraform

[Terraform](https://www.terraform.io/) must be used in order to update ANY part of our cloud infrastructure.

**Requirement: the AWS CLI command must be already configured:**

```bash
aws sts get-caller-identity
# Check if the "Account" value is ************
```

**You must be full admin on the AWS account**

### Configuration on your computer

- Install & configure AWS CLI
- You must have the `AdministratorAccess` policy (e.g.: by being member of the `admin-wecandoo` group)
- Install Terraform
- Test the config: `make terraform-check` (you should not see anything if everything is okay)

### Update everything from a new computer

```bash
make terraform-init
make terraform-download-secrets
```

```bash
make terraform-apply
make terraform-datadog-apply
make terraform-bastion-apply
make terraform-dev-services-apply
```

### Run a new stack

> See [the Terraform workspaces documentation](https://www.terraform.io/language/state/workspaces) in order to understand how to deploy a new independent env.

> Caution: this command will upload your `.env` file by default, as it should be run from a pipeline, after all external services `install.sh` scripts
> See deployment/aws/testing_stack_workspace/variables.tf in order to know all allowed variables

```bash
TF_WORKSPACE="my-branch-name" make terraform-testing-apply
# or:
TF_WORKSPACE="my-branch-name" terraform -chdir=deployment/aws/testing_stack_workspace apply
```

## Prod Bastion

**This instance should be the only one with a SSH server exposed in the `prod` VPC**

### SSH tunnel for MySQL

A dedicated `wecandoo` user has been created on the bastion server. You can use it in order to create an SSH tunnel to the hosted MySQL & Redis services:

- Your SSH public key must be added to `deployment/aws/bastion/authorized_keys`, and the `terraform -chdir=deployment/aws/bastion apply` must be run.
- `ssh <EMAIL>`

## Prod services

### SSH tunnel for MySQL

A dedicated `wecandoo` user has been created on the dev_services server. You can use it in order to create a SSH tunnel to the hosted MySQL & Redis services:

- Your SSH public key must be added to `deployment/aws/dev_services/authorized_keys`, and the `terraform -chdir=deployment/aws/dev_services apply` must be run.
- `ssh <EMAIL>`

### Get the services' generated passwords

```bash
terraform -chdir=deployment/aws/dev_services output -raw dev_services_mysql_root_password
```

### SSH connect

You can connect by running:

```bash
make terraform-download-secrets
ssh -i deployment/aws/.terraform/secrets/dev_services.pem <EMAIL>
```

#### Get the server Public IP

If you don't already know it, have a look to [the AWS console](https://eu-west-3.console.aws.amazon.com/ec2/home?region=eu-west-3),
or run `terraform -chdir=deployment/aws output -raw dev_services_public_ip`

#### SSH key

If you want to connect to the server, you must have you ssh key installed on it, or you must download the main private key:

**Option 1: ask somebody to add you public key to the server**

```bash
cat ~/.ssh/id_rsa.pub
```

**Option 2: get the main SSH private key**

```bash
make terraform-download-secrets
ssh -i deployment/aws/.terraform/secrets/dev_services.pem ec2-user@<SERVER_PUBLIC_IP>
```


# Initial configuration (should not be used until we want to change our AWS account)

##  1. Create the backend resources

> This step must only be done ONCE, in order to create the remote state storage resources.

- comment out the `terraform.backend.s3` part from the `state.tf` file
- run `make terraform-init`
- run `make terraform-apply`
- uncomment the backend part
- run another `make terraform-init` in order to migrate the state from you local drive to s3 (you must answer **yes** when asked)

## 2. Configure everything

**OVH DNS**

- Go to `OVH` manager, and open the `wecandoo.com`'s `DNS zone` page.
- Go to [Route 53](https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones#),
  open the new `aws.wecandoo.com` zone, and copy the `NS` configuration
- Set all AWS's `NS` records in the OVH's `DNS zones` page

**Gitlab registry credentials**

- Go to the [Gitlab's repository settings page](https://gitlab.com/wecandoo/wecandoo/-/settings/repository)
  and create a new `deploy token` with `read_registry` scope.
- Go to [AWS Secrets Manager](https://eu-west-3.console.aws.amazon.com/secretsmanager/listsecrets?region=eu-west-3)
- Update `ecs/testing/gitlab_registry_credentials` and set the good `username` and `password` values.
- Update `ecs/prod/gitlab_registry_credentials` and set the good `username` and `password` values.

**Preprod & prod secrets**

- Go to [AWS Secrets Manager](https://eu-west-3.console.aws.amazon.com/secretsmanager/listsecrets?region=eu-west-3)
- Update `ecs/preprod-env-secret` and set the good values.
- Update `ecs/prod-env-secret` and set the good values.

**Datadog API keys**

- See bellow, the `Datadog` part or this file
- Go to [AWS Secrets Manager](https://eu-west-3.console.aws.amazon.com/secretsmanager/listsecrets?region=eu-west-3)
- Update the required secrets:
  - `datadog_api_key`: you can generate a key in [https://app.datadoghq.eu/organization-settings/api-keys](https://app.datadoghq.eu/organization-settings/api-keys)
  - `datadog_external_id`: you can get it in [https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual](https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual)

## 3. Create the dev-services docker containers

```bash
make terraform-download-secrets
make terraform-dev-services-apply
```

If any secret has changed, you must update [the CI/CD variables](https://gitlab.com/wecandoo/wecandoo/-/settings/ci_cd).

# DataDog

## Set up

*See [https://docs.datadoghq.com/integrations/amazon_web_services/](https://docs.datadoghq.com/integrations/amazon_web_services/) (we follow the method: `Manual > Role delegation`)*

- Go to [https://app.datadoghq.eu/organization-settings/api-keys](https://app.datadoghq.eu/organization-settings/api-keys)
- Generate a new `AWS terraform` API key, and copy its `key` value
- Set the `datadog_api_key` value in [AWS Secrets Manager](https://eu-west-3.console.aws.amazon.com/secretsmanager/listsecrets?region=eu-west-3)

- Go to [https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual](https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual)
- Copy the `AWS External ID`
- Set the `datadog_external_id` value in [AWS Secrets Manager](https://eu-west-3.console.aws.amazon.com/secretsmanager/listsecrets?region=eu-west-3)

- Run `terraform -chdir=deployment/aws/datadog apply`

- In [https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual](https://app.datadoghq.eu/integrations/amazon-web-services/add?method=manual)
  - check the `I confirm that the Datadog IAM Role has been added to the AWS Account` box, and set the following values and save the page
  - `Account ID` => the AWS account ID you can find in locals.tf
  - `AWS Role Name` => `DatadogAWSIntegrationRole`
- After the AWS integration has been added to your terraform account, go to its `Metric Collection` page, then disable all, and enable the wanted ones:
  - Application ELB
  - CloudFront
  - EC2
  - ECS
  - ElastiCache
  - Lambda
  - RDS
  - S3

## Logs format

- Go to [https://app.datadoghq.eu/logs/pipelines](https://app.datadoghq.eu/logs/pipelines)
- Click on `Preprocessing for JSON logs > Edit`
- Put `dd.service` at the first place of `Service attributes`
- Add `level_name` before `level` in `Status attributes`
- you can also add some pipelines & processors in order to remap logs to the right services. You can also add some parser to nginx logs (GeoIP, User-Agent, ...)

# How to

## Release a Terraform lock

```bash
terraform -chdir=deployment/aws force-unlock "<LOCK_UUID>"
TF_WORKSPACE=main terraform -chdir=deployment/aws/testing_stack_workspace force-unlock "<LOCK_UUID>"
TF_WORKSPACE=preprod terraform -chdir=deployment/aws/prod_stack_workspace force-unlock "<LOCK_UUID>"
```

## Enable Datadog MySQL monitoring

```yaml
# deployment/aws/bastion/mysql.yaml

# Full config file: docker run --rm datadog/agent cat /etc/datadog-agent/conf.d/mysql.d/conf.yaml.example
init_config:

instances:
  - host: ${host_writer}
    username: datadog
    password: ${datadog_user_password}
    port: 3306
    options:
      extra_performance_metrics: true
      extra_status_metrics: true
      extra_innodb_metrics: true
  - host: ${host_reader}
    username: datadog
    password: ${datadog_user_password}
    port: 3306
    options:
      replication: true
      extra_performance_metrics: true
      disable_innodb_metrics: true
  - host: ${host_reader_heavy}
    username: datadog
    password: ${datadog_user_password}
    port: 3306
    options:
      replication: true
      extra_performance_metrics: true
      disable_innodb_metrics: true
```

```terraform
# deployment/aws/bastion/main.tf

data "aws_secretsmanager_secret" "datadog_api_key" {
  name = local.datadog_api_key_secret_name
}

data "aws_secretsmanager_secret_version" "datadog_api_key" {
  secret_id = data.aws_secretsmanager_secret.datadog_api_key.id
}

data "aws_db_instance" "aurora_prod_writer" {
  db_instance_identifier = "wecandoo-aurora-prod-writer"
}

data "aws_db_instance" "aurora_prod_reader" {
  db_instance_identifier = "wecandoo-aurora-prod-reader"
}

data "aws_db_instance" "aurora_prod_reader_heavy" {
  db_instance_identifier = "wecandoo-aurora-prod-reader-heavy"
}

data "aws_secretsmanager_secret" "datadog_mysql_user_password" {
  name = local.datadog_mysql_user_password_secret_name
}

data "aws_secretsmanager_secret_version" "datadog_mysql_user_password" {
  secret_id = data.aws_secretsmanager_secret.datadog_mysql_user_password.id
}

resource "docker_image" "datadog_agent" {
  name = "public.ecr.aws/datadog/agent:7"
}
resource "docker_container" "datadog_agent" {
  name         = "datadog_agent"
  image        = docker_image.datadog_agent.image_id
  restart      = "always"
  network_mode = "internal"

  env = [
    "DD_API_KEY=${data.aws_secretsmanager_secret_version.datadog_api_key.secret_string}",
    "DD_SITE=datadoghq.eu",
    "DD_HOSTNAME=bastion",
    "DD_LOG_LEVEL=info",
  ]

  upload {
    file       = "/etc/datadog-agent/conf.d/mysql.d/conf.yaml"
    executable = false
    content = templatefile("./mysql.yaml", {
      datadog_user_password = data.aws_secretsmanager_secret_version.datadog_mysql_user_password.secret_string
      host_writer           = data.aws_db_instance.aurora_prod_writer.address
      host_reader           = data.aws_db_instance.aurora_prod_reader.address
      host_reader_heavy     = data.aws_db_instance.aurora_prod_reader_heavy.address
    })
  }

  # ...
}
```

```mysql
# deployment/aws/files/prod_rds_init.sql

# ...

CREATE USER IF NOT EXISTS datadog@'%' IDENTIFIED BY '${datadog_user_password}';
GRANT REPLICATION CLIENT ON *.* TO datadog@'%' WITH MAX_USER_CONNECTIONS 5;
GRANT PROCESS ON *.* TO datadog@'%';
GRANT SELECT ON performance_schema.* TO datadog@'%';

CREATE SCHEMA IF NOT EXISTS datadog;
GRANT EXECUTE ON datadog.* to datadog@'%';
GRANT CREATE TEMPORARY TABLES ON datadog.* TO datadog@'%';

FLUSH PRIVILEGES;

DROP PROCEDURE IF EXISTS datadog.explain_statement;
DELIMITER $$
CREATE PROCEDURE datadog.explain_statement(IN query TEXT)
  SQL SECURITY DEFINER
BEGIN
  SET @explain := CONCAT('EXPLAIN FORMAT=json ', query);
  PREPARE stmt FROM @explain;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
END $$
DELIMITER ;

DROP PROCEDURE IF EXISTS datadog.enable_events_statements_consumers;
DELIMITER $$
CREATE PROCEDURE datadog.enable_events_statements_consumers()
  SQL SECURITY DEFINER
BEGIN
  UPDATE performance_schema.setup_consumers SET enabled='YES' WHERE name LIKE 'events_statements_%';
  UPDATE performance_schema.setup_consumers SET enabled='YES' WHERE name = 'events_waits_current';
END $$
DELIMITER ;
GRANT EXECUTE ON PROCEDURE datadog.enable_events_statements_consumers TO datadog@'%';

DROP PROCEDURE IF EXISTS prod.explain_statement;
DELIMITER $$
CREATE PROCEDURE prod.explain_statement(IN query TEXT)
  SQL SECURITY DEFINER
BEGIN
  SET @explain := CONCAT('EXPLAIN FORMAT=json ', query);
  PREPARE stmt FROM @explain;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
END $$
DELIMITER ;
GRANT EXECUTE ON PROCEDURE prod.explain_statement TO datadog@'%';

DROP PROCEDURE IF EXISTS preprod.explain_statement;
DELIMITER $$
CREATE PROCEDURE preprod.explain_statement(IN query TEXT)
  SQL SECURITY DEFINER
BEGIN
  SET @explain := CONCAT('EXPLAIN FORMAT=json ', query);
  PREPARE stmt FROM @explain;
  EXECUTE stmt;
  DEALLOCATE PREPARE stmt;
END $$
DELIMITER ;
GRANT EXECUTE ON PROCEDURE preprod.explain_statement TO datadog@'%';

## Down script:
# DROP USER IF EXISTS datadog;
# DROP SCHEMA IF EXISTS datadog;
# DROP PROCEDURE IF EXISTS prod.explain_statement;
# DROP PROCEDURE IF EXISTS preprod.explain_statement;
```

```terraform
# deployment/aws/rds.tf

resource "random_password" "mysql_datadog_user_password" {
  length  = 16
  special = false
}

output "mysql_datadog_user_password" {
  value     = random_password.mysql_datadog_user_password.result
  sensitive = true
}

resource "aws_secretsmanager_secret" "mysql_datadog_user_password" {
  name = "mysql_datadog_user_password"
}

resource "aws_secretsmanager_secret_version" "mysql_datadog_user_password_initial" {
  secret_id     = aws_secretsmanager_secret.mysql_datadog_user_password.id
  secret_string = random_password.mysql_datadog_user_password.result
}


## Datadog probe requirements. See https://docs.datadoghq.com/database_monitoring/setup_mysql/aurora/?tab=mysql57
resource "aws_db_parameter_group" "prod" {
  name   = "aurora-mysql-cluster-prod"
  family = "aurora-mysql8.0"

  # ...

  parameter {
    name         = "performance_schema"
    value        = "1"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance_schema_consumer_events_statements_current"
    value        = "1"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance-schema-consumer-events-waits-current"
    value        = "ON"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance_schema_consumer_events_statements_history"
    value        = "1"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance_schema_consumer_events_statements_history_long"
    value        = "1"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance_schema_max_digest_length"
    value        = "4096"
    apply_method = "pending-reboot"
  }
  parameter {
    name         = "performance_schema_max_sql_text_length"
    value        = "4096"
    apply_method = "pending-reboot"
  }
}

resource "ssh_resource" "prod_aurora_init" {
  # ...
  file {
    content = templatefile("./files/prod_rds_init.sql", {
      # ...
      datadog_user_password       = random_password.mysql_datadog_user_password.result
    })
  }
}
```

## Deploy a Lambda from a docker image

```terraform
provider "docker" {
  host = "unix:///var/run/docker.sock"

  registry_auth {
    address  = data.aws_ecr_authorization_token.default.proxy_endpoint
    username = data.aws_ecr_authorization_token.default.user_name
    password = data.aws_ecr_authorization_token.default.password
  }
}

resource "aws_ecr_repository" "image_resizer_lambda" {
  name                 = "infra/image-resizer-lambda"
  force_delete         = true
  image_tag_mutability = "IMMUTABLE"
}

data "aws_ecr_authorization_token" "default" {
}

resource "docker_registry_image" "image_resizer_lambda" {
  name = "${aws_ecr_repository.image_resizer_lambda.repository_url}:${local.image_resizer_version}"

  build {
    context    = local.image_resizer_path
    dockerfile = "cloudfront-handler.Dockerfile"
  }
}

resource "time_sleep" "image_resizer_lambda_sleep_after_creation" {
  create_duration = "30s"

  depends_on = [docker_registry_image.image_resizer_lambda]
  triggers   = {
    image_digest = docker_registry_image.image_resizer_lambda.sha256_digest
  }
}

resource "aws_lambda_function" "image_resizer" {
  function_name = "image_resizer"
  package_type  = "Image"
  role          = aws_iam_role.image_resizer.arn
  image_uri     = "${aws_ecr_repository.image_resizer_lambda.repository_url}:${local.image_resizer_version}"
  memory_size   = 1024
  publish       = true

  depends_on = [docker_registry_image.image_resizer_lambda, time_sleep.image_resizer_lambda_sleep_after_creation]

  environment {
    variables = {
      AWS_PUBLIC_IMAGES_BUCKET = aws_s3_bucket.img_public.bucket
      AWS_THUMBNAIL_BUCKET     = aws_s3_bucket.img_public.bucket
    }
  }
}

resource "aws_lambda_function_url" "image_resizer" {
  function_name      = aws_lambda_function.image_resizer.function_name
  authorization_type = "NONE"
}
```
