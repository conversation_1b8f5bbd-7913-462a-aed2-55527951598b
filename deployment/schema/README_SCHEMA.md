# Infra schema

This directory contains all [draw.io / diagrams.net](https://app.diagrams.net/?splash=0&libs=aws4) infrastructure schema.

You can export them to PNG files by running:
```bash
deployment/schema/export-drawio-schema.sh
#or: make export-infra-schema
```

## How to store uncompressed schema for a beautiful git diff

- Edit the schema XML file and use [the converter](https://jgraph.github.io/drawio-tools/tools/convert.html) on each `<diagram>` content
- Add `compressed="false"` on the `<mxfile>` tag
- Save, then edit the file
