#!/usr/bin/env bash
set -e

IMAGE_PATH=$1
if [ -z "${IMAGE_PATH}" ]; then
  echo "You must specify an image cache" && exit 1
fi

# Remove the first /
IMAGE_PATH=$(echo "${IMAGE_PATH}" | sed 's/^\///')

echo "> Getting the new file metadata..."
aws s3api head-object --bucket wecandoo-img-public --key "${IMAGE_PATH}" || (echo "Unable to find the $IMAGE_PATH image, aborting." && exit 1)

echo "> Searching all thumbnails related to this file..."

FILES_TO_DELETE=$(aws s3 rm s3://wecandoo-img-public/thumbnail --recursive --exclude "*" --include "*/${IMAGE_PATH}" --dryrun)

if [ -n "${FILES_TO_DELETE}" ]; then
  echo "> List of files to delete:"
  echo "${FILES_TO_DELETE}"
  read -p ">> Continue? You must answer no if you see anything weird in the previous list of files. All thumbnails are going to be re-created when somebody will try to access them. (y/N) " -n 1 -r
  if ! [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborting." && exit 2
  fi

  echo "";
  echo "> Deleting...";
  aws s3 rm s3://wecandoo-img-public/thumbnail --recursive --exclude "*" --include "*/${IMAGE_PATH}"
else
  echo ">> No thumbnails to delete"
fi

echo "> Creating an invalidation request for CloudFront..."
INVALIDATION_ID=$(aws cloudfront create-invalidation --distribution-id E1L3N60UUPF3YT --paths "/${IMAGE_PATH}" "/${IMAGE_PATH}*" "/thumbnail/*/${IMAGE_PATH}" --query 'Invalidation.Id'  --output text)
echo "Invalidation ID: $INVALIDATION_ID"

echo "> Waiting for the invalidation to be completed..."
aws cloudfront wait invalidation-completed --distribution-id E1L3N60UUPF3YT --id "$INVALIDATION_ID"

echo "> All thumbnails and CloudFront cache have been flushed, you must now run CTRL+F5 in your browser in order to get the new image."
