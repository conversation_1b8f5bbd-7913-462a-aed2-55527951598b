resource "aws_route53_zone" "aws_com" {
  name = "aws.wecandoo.com"

  lifecycle {
    prevent_destroy = true
  }

  tags = local.common_tags
}

# CDN

resource "aws_route53_record" "cloudfront_public_images" {
  zone_id = aws_route53_zone.aws_com.id
  name    = local.images_cdn_domain
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.public_images.domain_name
    zone_id                = aws_cloudfront_distribution.public_images.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_route53_record" "cloudfront_assets_files" {
  zone_id = aws_route53_zone.aws_com.id
  name    = local.assets_cdn_domain
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.assets_files.domain_name
    zone_id                = aws_cloudfront_distribution.assets_files.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_acm_certificate" "cloudfront_public_images" {
  provider    = aws.us-east-1
  domain_name = local.images_cdn_domain
  subject_alternative_names = [
    local.assets_cdn_domain,
  ]

  validation_method = "DNS"
  tags              = local.common_prod_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "cloudfront_public_images_certvalidation" {
  for_each = {
    for dvo in aws_acm_certificate.cloudfront_public_images.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.aws_com.zone_id
}

resource "aws_acm_certificate_validation" "cloudfront_public_images" {
  provider                = aws.us-east-1
  certificate_arn         = aws_acm_certificate.cloudfront_public_images.arn
  validation_record_fqdns = [for record in aws_route53_record.cloudfront_public_images_certvalidation : record.fqdn]
}

# Preprod

resource "aws_route53_record" "preprod_cloudfront_public_images" {
  zone_id = aws_route53_zone.aws_com.id
  name    = local.preprod_images_cdn_domain
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.preprod_public_images.domain_name
    zone_id                = aws_cloudfront_distribution.preprod_public_images.hosted_zone_id
    evaluate_target_health = false
  }
}

resource "aws_acm_certificate" "preprod_cloudfront_public_images" {
  provider    = aws.us-east-1
  domain_name = local.preprod_images_cdn_domain
  # subject_alternative_names = [
  #   local.preprod_assets_cdn_domain,
  # ]

  validation_method = "DNS"
  tags              = local.common_preprod_tags

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "preprod_cloudfront_public_images_certvalidation" {
  for_each = {
    for dvo in aws_acm_certificate.preprod_cloudfront_public_images.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.aws_com.zone_id
}

resource "aws_acm_certificate_validation" "preprod_cloudfront_public_images" {
  provider                = aws.us-east-1
  certificate_arn         = aws_acm_certificate.preprod_cloudfront_public_images.arn
  validation_record_fqdns = [for record in aws_route53_record.preprod_cloudfront_public_images_certvalidation : record.fqdn]
}

# dev services

resource "aws_route53_record" "dev_services_public" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "*.dev.aws.wecandoo.com"
  type    = "A"
  ttl     = 60
  records = [aws_eip.dev_services_eip.public_ip]
}

resource "aws_route53_record" "dev_services_private" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "*.dev.internal.aws.wecandoo.com"
  type    = "A"
  ttl     = 60
  records = [aws_eip.dev_services_eip.private_ip]
}

# Bastion

resource "aws_route53_record" "bastion" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "bastion.prod.aws.wecandoo.com"
  type    = "A"
  ttl     = 60
  records = [aws_eip.bastion_eip.public_ip]
}

resource "aws_route53_record" "bastion_wildcard" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "*.bastion.prod.aws.wecandoo.com"
  type    = "A"
  ttl     = 60
  records = [aws_eip.bastion_eip.public_ip]
}

# Testing stacks

resource "aws_route53_record" "testing_wildcard" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "*.testing.aws.wecandoo.com"
  type    = "CNAME"
  ttl     = 60
  records = [aws_lb.testing.dns_name]
}

resource "aws_acm_certificate" "testing_aws" {
  domain_name       = "*.testing.aws.wecandoo.com"
  validation_method = "DNS"
  tags              = local.common_testing_tags
}

resource "aws_route53_record" "testing_aws_certvalidation" {
  for_each = {
    for dvo in aws_acm_certificate.testing_aws.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = aws_route53_zone.aws_com.zone_id
}

resource "aws_acm_certificate_validation" "testing_aws" {
  certificate_arn         = aws_acm_certificate.testing_aws.arn
  validation_record_fqdns = [for record in aws_route53_record.testing_aws_certvalidation : record.fqdn]
}

resource "aws_route53_record" "mysql_aurora_prod_writer_private" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "mysql.prod.internal.aws.wecandoo.com"
  type    = "CNAME"
  ttl     = 5
  records = [aws_rds_cluster.aurora_prod_mysql_8_serverless.endpoint]
}

resource "aws_route53_record" "mysql_aurora_prod_reader_heavy_private" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "mysql-reader-heavy.prod.internal.aws.wecandoo.com"
  type    = "CNAME"
  ttl     = 60
  records = [aws_rds_cluster_endpoint.aurora_prod_mysql_8_heavy.endpoint]
}

resource "aws_route53_record" "mysql_aurora_preprod_private" {
  zone_id = aws_route53_zone.aws_com.id
  name    = "mysql.preprod.internal.aws.wecandoo.com"
  type    = "CNAME"
  ttl     = 60
  records = [aws_rds_cluster.aurora_preprod.endpoint]
}
