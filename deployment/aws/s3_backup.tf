#################
# BACKUP BUCKET #
#################

resource "aws_kms_key" "backup" {
  description             = "This key is used to encrypt objects in backup bucket"
  deletion_window_in_days = 10
}

resource "aws_kms_alias" "backup" {
  name          = "alias/backup"
  target_key_id = aws_kms_key.backup.key_id
}

resource "aws_s3_bucket" "backup" {
  bucket = "wecandoo-backup"

  tags = local.common_prod_tags
}

resource "aws_s3_bucket_server_side_encryption_configuration" "backup" {
  bucket = aws_s3_bucket.backup.bucket

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.backup.arn
      sse_algorithm     = "aws:kms"
    }
  }
}

resource "aws_s3_bucket_acl" "backup" {
  bucket = aws_s3_bucket.backup.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "backup" {
  bucket = aws_s3_bucket.backup.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "backup" {
  bucket = aws_s3_bucket.backup.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "backup" {

  depends_on = [aws_s3_bucket_versioning.backup]

  bucket = aws_s3_bucket.backup.bucket

  rule {
    id     = "archival"
    status = "Enabled"

    filter {
      prefix = "daily/"
    }

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 90
      storage_class = "DEEP_ARCHIVE"
    }

    expiration {
      days = 120
    }
  }
}

resource "aws_s3_object" "backup_daily_folder" {
  bucket = aws_s3_bucket.backup.id
  key    = "daily/"
}

resource "aws_s3_object" "backup_one_shot_folder" {
  bucket = aws_s3_bucket.backup.id
  key    = "one_shot/"
}

resource "aws_s3_object" "backup_dev_folder" {
  bucket = aws_s3_bucket.backup.id
  key    = "dev/"
}

resource "aws_iam_policy" "s3_backup_write" {
  name        = "s3-backup-write"
  description = "Allows the user to send files to the backup bucket"
  tags        = local.common_prod_tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # s3
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket",
        ]
        Resource = [
          aws_s3_bucket.backup.arn
        ],
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
        ]
        Resource = [
          "${aws_s3_bucket.backup.arn}/*",
        ]
      },
      # kms
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
        ]
        Resource = [
          aws_kms_key.backup.arn,
        ]
      },
    ]
  })
}

resource "aws_iam_policy" "s3_backup_dev" {
  name        = "s3-backup-dev"
  description = "Allows the user to read & write files to the backup bucket's dev folder"
  tags        = local.common_dev_services_tags

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # s3
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket",
        ]
        Resource = [
          aws_s3_bucket.backup.arn
        ],
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:Get*",
          "s3:List*",
          "s3:Describe*",
        ]
        Resource = [
          "${aws_s3_bucket.backup.arn}/dev/*",
        ]
      },
      # kms
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
        ]
        Resource = [
          aws_kms_key.backup.arn,
        ]
      },
    ]
  })
}
