locals {
  server_public_ip     = file("../.terraform/secrets/dev_services_public_ip")
  ssh_private_key_path = "../.terraform/secrets/dev_services.pem"
  # This local is only here in oder to let the "file" function fail if the file does not exist
  ssh_private_key = file(local.ssh_private_key_path)
  ssh_user        = "ec2-user"
}

locals {
  aws_region                  = "eu-west-3"
  datadog_api_key_secret_name = "datadog_api_key"
}

terraform {
  required_providers {
    docker = {
      source  = "kreuzwerker/docker"
      version = ">= 2.21.0"
    }
    bcrypt = {
      source  = "viktorradnai/bcrypt"
      version = ">= 0.1.2"
    }
    ssh = {
      source  = "loafoe/ssh"
      version = ">= 2.2.1"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  backend "s3" {
    bucket         = "wecandoo-terraform-state"
    key            = "state/dev-services.tfstate"
    region         = "eu-west-3"
    encrypt        = "true"
    dynamodb_table = "terraform-state"
  }
}

provider "docker" {
  host     = "ssh://${local.ssh_user}@${local.server_public_ip}"
  ssh_opts = ["-i", local.ssh_private_key_path, "-o", "StrictHostKeyChecking=no", "-o", "UserKnownHostsFile=/dev/null"]
}

provider "bcrypt" {
}

provider "ssh" {

}

provider "aws" {
  region = local.aws_region
}

# SSH Access

resource "ssh_resource" "authorized_keys" {
  when = "create"

  host        = local.server_public_ip
  user        = local.ssh_user
  private_key = local.ssh_private_key

  file {
    content     = file("./authorized_keys")
    destination = "/home/<USER>/wecandoo_authorized_keys"
  }

  commands = [
    "echo \"\" >> wecandoo_authorized_keys",
    "cat .ssh/authorized_keys >> wecandoo_authorized_keys",
    "sudo mv wecandoo_authorized_keys /home/<USER>/.ssh/authorized_keys",
    "sudo chmod 600 /home/<USER>/.ssh/authorized_keys && sudo chown -R wecandoo:wecandoo /home/<USER>/.ssh"
  ]

  timeout = "60s"
}

# backup

resource "ssh_resource" "backup_script" {
  when = "create"

  host        = local.server_public_ip
  user        = local.ssh_user
  private_key = local.ssh_private_key

  file {
    content     = file("./files/import-anonymized-prod-backup.sh")
    destination = "/home/<USER>/import-anonymized-prod-backup.sh"
    permissions = "755"
  }

  file {
    content     = file("./files/prepare-next-testing-import.sh")
    destination = "/home/<USER>/prepare-next-testing-import.sh"
    permissions = "755"
  }

  file {
    content     = file("./files/rename-mysql-database.sh")
    destination = "/home/<USER>/rename-mysql-database.sh"
    permissions = "755"
  }

  commands = [
    "sudo mv /home/<USER>/*.sh /usr/local/bin/",
    "echo \"0 6 * * mon-fri /usr/local/bin/prepare-next-testing-import.sh >> /tmp/prepare-next-testing-import-cron.log\" | crontab -",
  ]

  timeout = "60s"
}

# Secrets

resource "random_password" "traefik_password" {
  length  = 32
  special = false
}

output "traefik_password" {
  value     = random_password.traefik_password.result
  sensitive = true
}

resource "bcrypt_hash" "traefik_password" {
  cleartext = random_password.traefik_password.result
}

resource "random_password" "mailer_password" {
  length  = 32
  special = false
}

output "mailer_password" {
  value     = random_password.mailer_password.result
  sensitive = true
}

resource "bcrypt_hash" "mailer_password" {
  cleartext = random_password.mailer_password.result
}

resource "random_password" "mysql_root_password" {
  length  = 16
  special = false
}

output "mysql_root_password" {
  value     = random_password.mysql_root_password.result
  sensitive = true
}

resource "random_password" "minio_password" {
  length  = 16
  special = false
}

output "minio_password" {
  value     = random_password.minio_password.result
  sensitive = true
}

resource "random_password" "redis_password" {
  length  = 16
  special = false
}

output "redis_password" {
  value     = random_password.redis_password.result
  sensitive = true
}

resource "random_password" "rediscommander_password" {
  length  = 16
  special = false
}

output "rediscommander_password" {
  value     = random_password.rediscommander_password.result
  sensitive = true
}

# Network

resource "docker_network" "internal" {
  name = "internal"
}

# Containers

resource "docker_image" "traefik" {
  name = "traefik:2.8"
}

resource "docker_container" "traefik" {
  name         = "traefik"
  image        = docker_image.traefik.image_id
  restart      = "always"
  network_mode = "internal"

  volumes {
    container_path = "/var/run/docker.sock"
    host_path      = "/var/run/docker.sock"
    read_only      = false
  }

  volumes {
    container_path = "/etc/traefik/acme.json"
    host_path      = "/home/<USER>/data/traefik-config/acme.json"
    read_only      = false
  }

  ports {
    internal = 80
    external = 80
  }

  ports {
    internal = 443
    external = 443
  }

  command = [
    # DEBUG, INFO, ERROR
    "--log.level=INFO",

    # curl <IP_OR_DOMAIN>/ping
    "--ping.entryPoint=web",

    "--entrypoints.web.address=:80",
    "--entrypoints.websecure.address=:443",

    "--providers.docker=true",

    "--api=true",
    "--api.dashboard=true",

    # LetsEncrypt Staging Server - uncomment when testing
    # "--certificatesResolvers.letsencrypt.acme.caServer=https://acme-staging-v02.api.letsencrypt.org/directory",

    "--certificatesresolvers.le.acme.tlschallenge=true",
    "--certificatesresolvers.le.acme.email=<EMAIL>",
    "--certificatesresolvers.le.acme.storage=/etc/traefik/acme.json",
  ]


  # Traefik dashboard
  labels {
    label = "traefik.http.routers.traefik.rule"
    value = "Host(`traefik.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.services.traefik.loadbalancer.server.port"
    value = 8080
  }
  labels {
    label = "traefik.http.routers.traefik.service"
    value = "api@internal"
  }
  labels {
    label = "traefik.http.routers.traefik.entrypoints"
    value = "websecure"
  }
  labels {
    label = "traefik.http.routers.traefik.tls.certresolver"
    value = "le"
  }
  labels {
    label = "traefik.http.middlewares.auth.basicauth.users"
    value = "wecandoo:${bcrypt_hash.traefik_password.id}"
  }
  labels {
    label = "traefik.http.routers.traefik.middlewares"
    value = "auth"
  }

  # Redirect HTTP to HTTPS
  labels {
    label = "traefik.http.routers.http-catchall.rule"
    value = "hostregexp(`{host:.*\\.dev\\.aws\\.wecandoo\\.com}`)"
  }
  labels {
    label = "traefik.http.routers.http-catchall.entrypoints"
    value = "web"
  }
  labels {
    label = "traefik.http.routers.http-catchall.middlewares"
    value = "redirect-to-https"
  }
  labels {
    label = "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme"
    value = "https"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "mysql" {
  name = "mysql:8.0.32"
}

resource "docker_container" "mysql" {
  name         = "mysql"
  image        = docker_image.mysql.image_id
  restart      = "always"
  network_mode = "internal"

  command = ["mysqld", "--character-set-server=utf8mb4", "--collation-server=utf8mb4_unicode_ci", "--skip-log-bin"]

  # Use "0" in order to only use the first core, and "0-1" in order to allow using the two first cores
  cpu_set     = "0-3" # old : "0-1" # Caution: one core is not really enough in order to quickly import a DB. Caution-bis: a not-optimized script can kill our server.
  cpu_shares  = 3072  # old: 1024
  memory      = 14336 # old value : 2048
  memory_swap = 14336 # old value : 2048

  ports {
    internal = 3306
    external = 3306
  }

  volumes {
    container_path = "/var/lib/mysql"
    host_path      = "/home/<USER>/data/mysql-data"
    read_only      = false
  }

  env = [
    "MYSQL_ROOT_PASSWORD=${random_password.mysql_root_password.result}",
    "MYSQL_DATABASE=wecandoo", # This DB won't be used for now
    "MYSQL_USER=wecandootesting",
    "MYSQL_PASSWORD=${random_password.mysql_root_password.result}",
  ]

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "phpmyadmin" {
  name = "phpmyadmin/phpmyadmin"
}
resource "docker_container" "phpmyadmin" {
  name         = "phpmyadmin"
  image        = docker_image.phpmyadmin.image_id
  restart      = "always"
  network_mode = "internal"

  env = [
    "PMA_HOST=mysql",
  ]

  labels {
    label = "traefik.http.services.phpmyadmin.loadbalancer.server.port"
    value = 80
  }
  labels {
    label = "traefik.http.routers.phpmyadmin.rule"
    value = "Host(`phpmyadmin.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.phpmyadmin.tls.certresolver"
    value = "le"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "redis" {
  name = "bitnami/redis"
}
resource "docker_container" "redis" {
  name         = "redis"
  image        = docker_image.redis.image_id
  restart      = "always"
  network_mode = "internal"

  # max: 2Go memory, and 0.5 vCPU core
  memory      = 2048
  memory_swap = 2048
  cpu_set     = 1
  cpu_shares  = 512

  ports {
    internal = 6379
    external = 6379
  }

  volumes {
    container_path = "/bitnami/redis/data"
    host_path      = "/home/<USER>/data/redis-data"
    read_only      = false
  }

  upload {
    file    = "/opt/bitnami/redis/mounted-etc/overrides.conf"
    content = file("files/redis.overrides.conf")
  }

  env = [
    "REDIS_PASSWORD=${random_password.redis_password.result}",
  ]

  lifecycle {
    ignore_changes = [ulimit, ports]
  }
}

locals {
  rediscommander_hosts = [for i in range(0, 63) : "local:redis:6379:${i}:${random_password.redis_password.result}"]
}

resource "docker_image" "rediscommander" {
  name = "rediscommander/redis-commander"
}
resource "docker_container" "rediscommander" {
  name         = "rediscommander"
  image        = docker_image.rediscommander.image_id
  restart      = "always"
  network_mode = "internal"

  env = [
    "REDIS_HOSTS=${join(",", local.rediscommander_hosts)}",
    "HTTP_USER=wecandoo",
    "HTTP_PASSWORD=${random_password.rediscommander_password.result}",
  ]

  labels {
    label = "traefik.http.services.rediscommander.loadbalancer.server.port"
    value = 8081
  }
  labels {
    label = "traefik.http.routers.rediscommander.rule"
    value = "Host(`rediscommander.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.rediscommander.tls.certresolver"
    value = "le"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "mailpit" {
  name = "axllent/mailpit:v1.21.4"
}
resource "docker_container" "mailer" {
  name         = "mailer"
  image        = docker_image.mailpit.image_id
  restart      = "always"
  network_mode = "internal"

  cpu_set     = 0
  cpu_shares  = 256
  memory      = 512
  memory_swap = 512

  env = [
    "MP_MAX_MESSAGES=80000", # default: 500
    "MP_MAX_AGE=4d",         # auto delete after 4 days
  ]

  ports {
    internal = 1025
    external = 1025
  }

  labels {
    label = "traefik.http.services.mailer.loadbalancer.server.port"
    value = 8025
  }
  labels {
    label = "traefik.http.routers.mailer.rule"
    value = "Host(`mailer.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.mailer.tls.certresolver"
    value = "le"
  }
  labels {
    label = "traefik.http.middlewares.mailer_auth.basicauth.users"
    value = "wecandoo:${bcrypt_hash.mailer_password.id}"
  }
  labels {
    label = "traefik.http.routers.mailer.middlewares"
    value = "mailer_auth"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "minio" {
  name = "minio/minio"
}
resource "docker_container" "minio" {
  name         = "minio"
  image        = docker_image.minio.image_id
  restart      = "always"
  network_mode = "internal"

  command     = ["server", "/data", "--console-address", ":9001"]
  working_dir = "/data"

  memory      = 512
  memory_swap = 512
  cpu_set     = 1
  cpu_shares  = 256

  volumes {
    container_path = "/data"
    host_path      = "/home/<USER>/data/s3-data"
    read_only      = false
  }

  volumes {
    container_path = "/root/.minio"
    host_path      = "/home/<USER>/data/s3-config"
    read_only      = false
  }

  env = [
    "MINIO_ROOT_USER=awsaccesskey",
    "MINIO_ROOT_PASSWORD=${random_password.minio_password.result}",
    "MINIO_BROWSER_REDIRECT_URL=https://minio.dev.aws.wecandoo.com",
  ]

  labels {
    label = "traefik.http.services.s3.loadbalancer.server.port"
    value = 9000
  }

  labels {
    label = "traefik.http.services.minio.loadbalancer.server.port"
    value = 9001
  }

  # S3 external, HTTPS
  labels {
    label = "traefik.http.routers.s3external.rule"
    value = "Host(`s3.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.s3external.service"
    value = "s3"
  }
  labels {
    label = "traefik.http.routers.s3external.tls.certresolver"
    value = "le"
  }
  # S3 internal, HTTP
  labels {
    label = "traefik.http.routers.s3internal.rule"
    value = "Host(`s3.dev.internal.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.s3internal.service"
    value = "s3"
  }
  # Minio GUI HTTPS
  labels {
    label = "traefik.http.routers.minio.rule"
    value = "Host(`minio.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.minio.tls.certresolver"
    value = "le"
  }
  labels {
    label = "traefik.http.routers.minio.service"
    value = "minio"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

resource "docker_image" "image_resizer_proxy" {
  name = "image-resizer-proxy"

  build {
    path       = "../../image-resizer"
    dockerfile = "cdn-proxy.Dockerfile"
  }

  triggers = {
    dir_sha1 = sha1(join("", concat([for f in fileset("", "../../image-resizer/src/**") : filesha1(f)], [for f in fileset("", "../../image-resizer/{package.json,package-lock.json,cdn-proxy.Dockerfile}") : filesha1(f)])))
  }
}

resource "docker_container" "image_resizer_proxy" {
  name         = "image_resizer_proxy"
  image        = docker_image.image_resizer_proxy.image_id
  restart      = "always"
  network_mode = "internal"

  env = [
    "AWS_ACCESS_KEY_ID=awsaccesskey",
    "AWS_SECRET_ACCESS_KEY=${random_password.minio_password.result}",
    "AWS_S3_ENDPOINT=http://minio:9000",
    "AWS_REGION=eu-west-3",
    "AWS_PUBLIC_IMAGES_BUCKET=wecandoo-img-public",
    "AWS_THUMBNAIL_BUCKET=wecandoo-img-public",
    "AWS_THUMBNAIL_FOLDER=thumbnail",
    "PRODUCTION_CDN_URL=https://cdn.aws.wecandoo.com",
  ]

  cpu_set     = 0
  cpu_shares  = 256
  memory      = 256
  memory_swap = 256

  labels {
    label = "traefik.http.services.cdn.loadbalancer.server.port"
    value = 3000
  }

  labels {
    label = "traefik.http.routers.cdn.rule"
    value = "Host(`cdn.dev.aws.wecandoo.com`)"
  }
  labels {
    label = "traefik.http.routers.cdn.tls.certresolver"
    value = "le"
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}

data "aws_secretsmanager_secret" "datadog_api_key" {
  name = local.datadog_api_key_secret_name
}

data "aws_secretsmanager_secret_version" "datadog_api_key" {
  secret_id = data.aws_secretsmanager_secret.datadog_api_key.id
}

resource "docker_image" "datadog_agent" {
  name = "public.ecr.aws/datadog/agent:7"
}
## Connect the tools' agent: ssh <EMAIL> docker exec datadog_agent agent status
resource "docker_container" "datadog_agent" {
  name         = "datadog_agent"
  image        = docker_image.datadog_agent.image_id
  restart      = "always"
  network_mode = "internal"

  env = [
    "DD_API_KEY=${data.aws_secretsmanager_secret_version.datadog_api_key.secret_string}",
    "DD_SITE=datadoghq.eu",
    "DD_HOSTNAME=dev-services",
    "DD_LOG_LEVEL=error",
    "DD_APM_ENABLED=true",
    "DD_APM_NON_LOCAL_TRAFFIC=true",
    "DD_APM_IGNORE_RESOURCES=\"Barryvdh\\\\Debugbar\"",
  ]

  cpu_set     = 0
  cpu_shares  = 256
  memory      = 256
  memory_swap = 256

  ports {
    internal = 8126
    external = 8126
  }

  volumes {
    host_path      = "/var/run/docker.sock"
    container_path = "/var/run/docker.sock"
    read_only      = true
  }

  volumes {
    host_path      = "/proc/"
    container_path = "/host/proc/"
    read_only      = true
  }

  volumes {
    host_path      = "/sys/fs/cgroup/"
    container_path = "/host/sys/fs/cgroup"
    read_only      = true
  }

  lifecycle {
    ignore_changes = [ulimit]
  }
}
