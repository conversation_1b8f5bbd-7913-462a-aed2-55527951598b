resource "aws_security_group" "prod_alb" {
  name        = "prod-alb"
  description = "Allow all HTTP traffic"
  vpc_id      = aws_vpc.prod.id

  ingress {
    description      = "HTTP"
    from_port        = 80
    to_port          = 80
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "HTTPS"
    from_port        = 443
    to_port          = 443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = local.common_prod_tags
}

resource "aws_lb" "prod" {
  name               = "prod"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.prod_alb.id]
  subnets            = aws_subnet.prod_public.*.id

  enable_deletion_protection = true

  access_logs {
    bucket  = aws_s3_bucket.logs.bucket
    enabled = true
  }

  tags = local.common_prod_tags
}

resource "aws_alb_listener" "prod_web_http" {
  load_balancer_arn = aws_lb.prod.id
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = 443
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  tags = local.common_prod_tags
}

resource "aws_alb_listener" "prod_https" {
  load_balancer_arn = aws_lb.prod.id
  port              = 443
  protocol          = "HTTPS"

  ssl_policy      = "ELBSecurityPolicy-2016-08"
  certificate_arn = aws_acm_certificate.com.arn

  default_action {
    type = "fixed-response"
    fixed_response {
      status_code  = "404"
      content_type = "text/plain"
      message_body = "This stack has not been found"
    }
  }

  tags = local.common_prod_tags
}

resource "aws_lb_listener_certificate" "prod_com" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.com.arn
}

resource "aws_lb_listener_certificate" "prod_fr" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.fr.arn
}

resource "aws_lb_listener_certificate" "prod_be" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.be.arn
}

resource "aws_lb_listener_certificate" "prod_nl" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.nl.arn
}

resource "aws_lb_listener_certificate" "prod_gb" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.gb.arn
}

resource "aws_lb_listener_certificate" "wildcard_preprod" {
  listener_arn    = aws_alb_listener.prod_https.arn
  certificate_arn = aws_acm_certificate.wildcard_preprod.arn
}

resource "aws_alb_listener_rule" "prod_web" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 10 # Should be the first rule

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.prod_web.arn
  }

  condition {
    host_header {
      # allow all apex domains
      # TODO: remove ea-app.wecandoo.* when all artisan is redirected
      # TODO: remove wecanadmin.wecandoo.fr when all artisan is redirected
      values = ["wecandoo.*", "wecanadmin.wecandoo.fr", "ea-app.wecandoo.*", "booking.wecandoo.*"]
    }
  }
}

resource "aws_alb_listener_rule" "prod_web_www_com" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 20

  action {
    type = "redirect"

    redirect {
      host        = "wecandoo.com"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["www.wecandoo.com"]
    }
  }
}

resource "aws_alb_listener_rule" "prod_web_www_fr" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 21

  action {
    type = "redirect"

    redirect {
      host        = "wecandoo.fr"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["www.wecandoo.fr"]
    }
  }
}

resource "aws_alb_listener_rule" "prod_web_www_be" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 22

  action {
    type = "redirect"

    redirect {
      host        = "wecandoo.be"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["www.wecandoo.be"]
    }
  }
}

resource "aws_alb_listener_rule" "prod_web_www_nl" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 23

  action {
    type = "redirect"

    redirect {
      host        = "wecandoo.nl"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["www.wecandoo.nl"]
    }
  }
}

resource "aws_alb_listener_rule" "prod_web_www_gb" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 24

  action {
    type = "redirect"

    redirect {
      host        = "wecandoo.uk"
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["www.wecandoo.uk"]
    }
  }
}

resource "aws_alb_listener_rule" "preprod_web" {
  listener_arn = aws_alb_listener.prod_https.arn
  priority     = 40

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.preprod_web.arn
  }

  condition {
    host_header {
      values = ["preprod.wecandoo.*", "booking.preprod.wecandoo.*"]
    }
  }
}
