###############
# LOGS BUCKET #
###############

data "aws_elb_service_account" "main" {}

resource "aws_s3_bucket" "logs" {
  bucket = "wecandoo-logs"

  tags = local.common_prod_tags
}

resource "aws_s3_bucket_acl" "logs" {
  bucket = aws_s3_bucket.logs.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "logs" {
  bucket = aws_s3_bucket.logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

}

resource "aws_s3_bucket_policy" "logs_s3" {
  bucket = aws_s3_bucket.logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.logs.arn}/*"
        Principal = {
          AWS : data.aws_elb_service_account.main.arn
        }
      },
      {
        Effect   = "Allow"
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.logs.arn}/*"
        Principal = {
          Service : "cloudtrail.amazonaws.com"
        }
      },
      {
        Effect   = "Allow"
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.logs.arn
        Principal = {
          Service : "cloudtrail.amazonaws.com"
        }
      }
    ]
  })
}
