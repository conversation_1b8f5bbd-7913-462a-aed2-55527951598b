#!/usr/bin/env bash

SCRIPT_DIR=$(dirname $0)

${SCRIPT_DIR}/install-deps.sh

SERVICES=(
  "stripe"
  "algolia"
)

if [ ! -f /tmp/terraform_testing_stacks ] && [ -n "$TF_COMMAND" ]; then
  echo -e "\n> terraform_testing_stacks file not found, but terraform command found. Getting running stacks."
  ${TF_COMMAND} workspace list | grep -vw '\* default' | sed 's/^\s*\(.*\)/\1/' | tee /tmp/terraform_testing_stacks
fi

for i in "${SERVICES[@]}"; do
  if [ -f "${SCRIPT_DIR}/${i}/clean.sh" ] && [ -x "${SCRIPT_DIR}/${i}/clean.sh" ]; then
    echo "=========="
    echo ">> Running ${i}"
    ${SCRIPT_DIR}/${i}/clean.sh
  else
    echo ">> file clean.sh cannot be executed for service [$i]";
  fi
done
