#!/usr/bin/env bash
set -e

STACK_NAME=$1

if [ -z "${STACK_NAME}" ]; then
  echo "Unable to find the STACK_NAME variable"
  exit 1
fi

if [ -z "${DEV_SERVICES_REDIS_PASSWORD}" ]; then
  echo "Unable to find the DEV_SERVICES_REDIS_PASSWORD variable"
  exit 1
fi

REDIS_CMD="redis-cli -h redis.dev.aws.wecandoo.com -a ${DEV_SERVICES_REDIS_PASSWORD} -n 0 --raw"

echo ">> Listing all referenced stacks:"
echo "keys stack-db:*" | $REDIS_CMD
echo "keys stack-db-cache:*" | $REDIS_CMD

stack_db=$(echo "GET stack-db:${STACK_NAME}" | $REDIS_CMD)

if [ -z "${stack_db}" ]; then
  echo "> DB default not found, skipping"
else
  echo ">> DB default found: ${stack_db}, deleting it..."
  echo "DEL stack-db:${STACK_NAME}" | $REDIS_CMD
  redis-cli -h redis.dev.aws.wecandoo.com -a "${DEV_SERVICES_REDIS_PASSWORD}" -n "${stack_db}" --raw FLUSHDB
fi

stack_db_cache=$(echo "GET stack-db-cache:${STACK_NAME}" | $REDIS_CMD)

if [ -z "${stack_db_cache}" ]; then
  echo "> DB cache not found, skipping"
else
  echo "<< DB cache found: ${stack_db_cache}, deleting it..."
  echo "DEL stack-db-cache:${STACK_NAME}" | $REDIS_CMD
  redis-cli -h redis.dev.aws.wecandoo.com -a "${DEV_SERVICES_REDIS_PASSWORD}" -n "${stack_db_cache}" --raw FLUSHDB
fi
