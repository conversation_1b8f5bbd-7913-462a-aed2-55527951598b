#!/usr/bin/env php
<?php

require_once(__DIR__ . '/_dd_metrics.php');

if (! isset($_SERVER['argv'][1]) || !file_exists($metricsFile = $_SERVER['argv'][1])) {
    echo 'Usage: '.$_SERVER['argv'][0].' metrics.txt';
    exit(1);
}

$analysisDate = DateTimeImmutable::createFromFormat('U', filemtime($metricsFile) ?: time());
$metricsLines = array_filter(explode("\n", file_get_contents($metricsFile)));
$metrics = DdMetric::fromOpenMetricsList($metricsLines);

sendOpenMetricsToDatadog($metrics, $analysisDate);
