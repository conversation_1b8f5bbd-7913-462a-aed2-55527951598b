#!/usr/bin/env bash

set -e

readonly RESET='\033[0;0m' # Reset color and text style

readonly RED='\033[0;31m' # Color red
readonly GREEN='\033[0;32m' # Color green
readonly BLUE='\033[0;34m' # Color blue

readonly CHANNEL='C064H2ZA54M'

readonly SOFTWARE_NAME="${GREEN}Alert DATA team if a new migration is committed on main.${RESET}"

echo -e "${SOFTWARE_NAME}"
echo -e ""

[ -z "${SLACK_BOT_TOKEN}" ] && echo -e "${RED}The SLACK_BOT_TOKEN var is unset.${RESET}" && exit 1
[ -z "${CI_PROJECT_DIR}" ] && echo -e "${RED}The CI_PROJECT_DIR var is unset.${RESET}" && exit 1

cd ${CI_PROJECT_DIR}
PATH_TO_CHECK="database/migrations"

LAST_CHECKED_COMMIT="$1"
CURRENT_COMMIT=$(git rev-parse --short HEAD)

if [ -z "${LAST_CHECKED_COMMIT}" ]; then
  echo -e "> No last checked commit found. We need to fetch from previous tag"

  echo -e "> Fetching tags"
  # We must fetch everything, as gitlab one fetches the last 20 changes by default. See GIT_DEPTH
  git fetch --depth=9999 -va origin "${CI_COMMIT_REF_NAME:=main}" || true
  git fetch --depth=9999 --tags -va origin "${CI_COMMIT_REF_NAME:=main}" || true

  echo -e "> git describe --tags"
  git describe --tags --debug

  # Get the current commit's first tag if it exist
  current_tag_or_commit=$(git tag --points-at "${CURRENT_COMMIT}" | head -n 1)
  if [ -z "${current_tag_or_commit}" ]; then
    echo -e ">> The current commit is not a tag, getting the last one"
    # There is no tag on the current commit, getting the changes since the last tag
    current_tag_or_commit=${CURRENT_COMMIT}
    LAST_CHECKED_COMMIT=$(git describe --tags --abbrev=0)
  else
    echo -e ">> The current commit is a tag, getting the previous one"
    # There is a tag on the current commit, getting the changes since the second last tag
    LAST_CHECKED_COMMIT=$(git describe --tags --abbrev=0 "${current_tag_or_commit}"^)
  fi
fi

echo -e ">> Current commit: ${CURRENT_COMMIT}"
echo -e ">> Last checked commit on main: ${LAST_CHECKED_COMMIT}"

# Retrieve the list of new files between the branch previous commit and the current commit.
# Files must be committed when this script is executed.
# ("awk -F ' ' '{print $6}'" allow to get the 6th column of the previous command.
diff_files=$(git diff --diff-filter=A "${LAST_CHECKED_COMMIT}" HEAD --raw -- $PATH_TO_CHECK | awk -F ' ' '{print $6}')

echo -e ""
echo -e "${BLUE}Save current commit: ${CURRENT_COMMIT}${RESET}"

ARTIFACTS_DIR="${CI_PROJECT_DIR}/.cache/migrations-data"
mkdir -p "${ARTIFACTS_DIR}"
COMMIT_ID_FILE="${ARTIFACTS_DIR}/commit_id.txt"
echo "${CURRENT_COMMIT}" > "${COMMIT_ID_FILE}"

# If there are no files found, we can return a success.
if [ -z "$diff_files" ]; then
  echo -e "${GREEN} 👏 No new migrations found.${RESET}"
  exit 0
fi

echo -e "New migrations files found:"
migrations_files="" # List of all migrations files
for file in $diff_files; do
  file=$(echo "${file}" | sed "s|^${PATH_TO_CHECK}/||")
  echo -e " ${RED}✘${RESET} $file"

  migrations_files="${migrations_files}- <https://gitlab.com/wecandoo/wecandoo/-/blob/main/${PATH_TO_CHECK}/${file}|${file}>\n"
done

echo -e ""
echo -e "${BLUE}Send Slack notification${RESET}"

curl \
  -H "Content-type: application/json; charset=utf-8" \
  -H "Authorization: Bearer ${SLACK_BOT_TOKEN}" \
  -X POST https://slack.com/api/chat.postMessage \
  --data-binary @- << EOF
{
  "channel": "${CHANNEL}",
  "blocks": [
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": ":robot_face: Database changes identified:\n${migrations_files}"
      }
    }
  ]
}
EOF
