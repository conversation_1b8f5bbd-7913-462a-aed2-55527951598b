#!/usr/bin/env sh

export ECS_CLUSTER=${1:-$ECS_CLUSTER}
export ECS_SERVICE=${2:-$ECS_SERVICE}
IGNORE_INACTIVE_TASK_DEFINITIONS=$3

[ -z "$ECS_CLUSTER" ] && echo "The ECS_CLUSTER variable must be defined" && exit 1;
[ -z "$ECS_SERVICE" ] && echo "The ECS_SERVICE variable must be defined" && exit 1;
[ -z "$DEPLOY_DATE" ] && echo "The DEPLOY_DATE variable must be defined" && exit 1;

MAX_ATTEMPTS=${MAX_ATTEMPTS:-10}

echo "> DEPLOY_DATE to find: ${DEPLOY_DATE}"

for i in $(seq ${MAX_ATTEMPTS}); do
  sleep 5

  echo -e "\n>> Looking for tasks: cluster=$ECS_CLUSTER service-name=$ECS_SERVICE"
  service_tasks_arns=$(aws ecs list-tasks --cluster "$ECS_CLUSTER" --service-name "$ECS_SERVICE" --query 'taskArns[]' --output text)
  echo "${service_tasks_arns}"

  echo -e "\n>> Looking for tasks status"
  tasks_status=$(aws ecs describe-tasks --tasks $(echo $service_tasks_arns) --cluster "$ECS_CLUSTER" --query 'tasks[].[taskArn,lastStatus]' --output text)
  echo "${tasks_status}"

  if [ -n "$(echo "${tasks_status}" | grep -v RUNNING)" ]; then
    echo "> Some tasks are not RUNNING"
    continue
  fi

  echo -e "\n>> Looking for tasks definitions"
  tasks_definition_arn=$(aws ecs describe-tasks --tasks $(echo $service_tasks_arns) --cluster "$ECS_CLUSTER" --query 'tasks[].[taskDefinitionArn]' --output text | sort | uniq)
  echo "${tasks_definition_arn}"

  for task_definition in $tasks_definition_arn; do

    if [ "$IGNORE_INACTIVE_TASK_DEFINITIONS" = "true" ]; then
      echo -e "\n>> Checking $task_definition status"
      task_definition_status=$(aws ecs describe-task-definition --task-definition "$task_definition" --query 'taskDefinition.status' --output text)
      echo "> Found status=$task_definition_status"
      if [ "$task_definition_status" = "INACTIVE" ]; then
        echo "> The task definition is marked as inactive, we can presume the related worker container has already received a QUIT signal asking for stopping all new processes"
        continue
      fi
    fi

    echo -e "\n>> Checking $task_definition DEPLOY_DATE value"
    container_deploy_date=$(aws ecs describe-task-definition --task-definition "$task_definition" --query 'taskDefinition.containerDefinitions[0].environment[?name==`DEPLOY_DATE`].value' --output text)
    echo "> Found container DEPLOY_DATE=$container_deploy_date"

    if [ "$container_deploy_date" != "$DEPLOY_DATE" ]; then
      echo "> The DEPLOY_DATE is not good, retrying..."
      continue 2
    fi
  done

  echo -e "\n>>> The good DEPLOY_DATE has been found in all running tasks !"
  exit 0
done

echo -e "\n>>> Unable to validate the service status"
exit 1
