#!/usr/bin/env bash

set -e

SCRIPT_DIR=$(dirname $(realpath $0))
source "$SCRIPT_DIR/linear-common.sh"

# Squad Artisan
SQUAD_ARTISAN_FROM="165bf4f9-2b9c-42c6-a30d-374b02793be4"
SQUAD_ARTISAN_TO="3b6743bf-6152-469c-853b-fe8578d1c6cd"
# Squad Customer
SQUAD_CUSTOMER_FROM="3f69c67d-3c5a-4a92-88b9-b1f99f858aae"
SQUAD_CUSTOMER_TO="03cfba26-239d-417d-a4ba-c2c8eb109328"
# Tech
TECH_FROM="b06b7164-d052-4609-91fe-44095b625883"
TECH_TO="c2986e35-b653-4778-99eb-493ff6cedfb4"
# Wecandoo
WECANDOO_FROM="6d987fc7-0096-49e9-a562-bffc7ca9089d"
WECANDOO_TO="8f97b7e0-5251-42d8-85aa-c35702102e3d"

STATES=$(cat <<EOF
{
  "states": [
    {"from": "$SQUAD_ARTISAN_FROM", "to": "$SQUAD_ARTISAN_TO"},
    {"from": "$SQUAD_CUSTOMER_FROM", "to": "$SQUAD_CUSTOMER_TO"},
    {"from": "$TECH_FROM", "to": "$TECH_TO"},
    {"from": "$WECANDOO_FROM", "to": "$WECANDOO_TO"}
  ]
}
EOF
)

ISSUES_QUERY_TEMPLATE='query Issues($stateId: ID!) { issues(filter: { state: { id: { eq: $stateId } } }) { nodes { id number } } }'

ISSUE_UPDATE_MUTATION_TEMPLATE='mutation IssueUpdate($id: String!, $stateId: String!) { issueUpdate( input: { stateId: $stateId } id: $id ) { success } }'

# Process each state transition by looping through the list
echo "$STATES" | jq -c '.states[]' | while read -r state; do

    FROM_STATE=$(echo "$state" | jq -r '.from')
    TO_STATE=$(echo "$state" | jq -r '.to')

    # Fetch issues for the current state
    ISSUES_LIST=$(run_graphql_query "$ISSUES_QUERY_TEMPLATE" "{\"stateId\":\"$FROM_STATE\"}")
    ISSUE_IDS=$(echo "$ISSUES_LIST" | jq -r '.data.issues.nodes[].id')

    FAILED=false
    # Update each issue to the new state
    for ISSUE_ID in $ISSUE_IDS; do
        echo "Updating issue $ISSUE_ID from $FROM_STATE to state $TO_STATE"
        UPDATE_RESPONSE=$(run_graphql_query "$ISSUE_UPDATE_MUTATION_TEMPLATE" "{\"id\":\"$ISSUE_ID\", \"stateId\":\"$TO_STATE\"}")
        SUCCESS=$(echo "$UPDATE_RESPONSE" | jq -r '.data.issueUpdate.success')

        if [ "$SUCCESS" != "true" ]; then
          FAILED=true
        fi
    done

    if [ "$FAILED" = true ]; then
      echo "Some issues failed to be updated"
      exit 2
    fi

done
