#!/usr/bin/env php
<?php

// docker run --rm -ti -v `pwd`:/src -w /src alpine
//
// export CI_API_TOKEN=<YOUR_TOKEN with api scope from https://gitlab.com/-/profile/personal_access_tokens>
// export CI_API_V4_URL=https://gitlab.com/api/v4
// export **********************
//
// deployment/pipelines/gitlab/install-clean-registry-deps
// deployment/pipelines/gitlab/clean-registry-tag feature-foo-bar

require_once(__DIR__ . '/_clean_registry.php');

$tag = $_SERVER['argv'][1] ?? null;

if (empty($tag)) {
    throw new RuntimeException('Invalid tag provided');
}

removeTags($tag);
