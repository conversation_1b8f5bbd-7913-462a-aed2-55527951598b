parameters:
	ignoreErrors:
		-
			message: "#^Instanceof between App\\\\Models\\\\Commande and App\\\\Models\\\\Commande will always evaluate to true\\.$#"
			count: 1
			path: app/Console/Commands/BatchCheckOrderConsistency.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:withTrashed\\(\\)\\.$#"
			count: 4
			path: app/Console/Commands/OneTime/CartDeleteEmptyCommand.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$atelier_id\\.$#"
			count: 1
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$atelier_permalien\\.$#"
			count: 1
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$creation_id\\.$#"
			count: 5
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$creation_identifier\\.$#"
			count: 1
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$image_filename\\.$#"
			count: 1
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$image_id\\.$#"
			count: 3
			path: app/Console/Commands/Technique/ExportAllWorkshopsImages.php

		-
			message: """
				#^Call to deprecated method forceRootUrl\\(\\) of class Illuminate\\\\Routing\\\\UrlGenerator\\:
				Use useOrigin$#
			"""
			count: 1
			path: app/Console/Commands/Technique/SitemapGenerateCommand.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\LazyCollection\\<int,object\\>\\:\\:map\\(\\) expects callable\\(object, int\\)\\: App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Jobs\\\\GenerateCommissionInvoice, Closure\\(stdClass\\)\\: App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Jobs\\\\GenerateCommissionInvoice given\\.$#"
			count: 1
			path: app/Domain/Accounting/CommissionInvoices/Commands/GenerateCommissionInvoices.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\LazyCollection\\<int,object\\>\\:\\:map\\(\\) expects callable\\(object, int\\)\\: App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Jobs\\\\NotifyArtisanOfCommissionInvoice, Closure\\(stdClass\\)\\: App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Jobs\\\\NotifyArtisanOfCommissionInvoice given\\.$#"
			count: 1
			path: app/Domain/Accounting/CommissionInvoices/Commands/NotifyArtisansOfCommissionInvoices.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoiceWorkshopBooking\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/CommissionInvoices/Models/CommissionInvoice.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoice\\:\\:workshopBookings\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoiceWorkshopBooking\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoiceWorkshopBooking, App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoiceWorkshop, \\$this\\(App\\\\Domain\\\\Accounting\\\\CommissionInvoices\\\\Models\\\\CommissionInvoice\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/CommissionInvoices/Models/CommissionInvoice.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Exports\\\\ExportsD\\\\DFrPartnerGiftCardsExport\\:\\:getCancelledGiftCards\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int\\|string, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<int, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Exports/ExportsD/DFrPartnerGiftCardsExport.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Exports\\\\ExportsD\\\\DFrPartnerGiftCardsExport\\:\\:getCreatedGiftCards\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int\\|string, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<int, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Exports/ExportsD/DFrPartnerGiftCardsExport.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Exports\\\\ExportsD\\\\DFrPartnerGiftCardsExport\\:\\:getNotSelledAndExpiredGiftCards\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int\\|string, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<int, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Exports/ExportsD/DFrPartnerGiftCardsExport.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Exports\\\\ExportsD\\\\DFrPartnerGiftCardsExport\\:\\:getSelledAndExpiredGiftCards\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int\\|string, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<int, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Exports/ExportsD/DFrPartnerGiftCardsExport.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Exports\\\\ExportsD\\\\DFrPartnerGiftCardsExport\\:\\:getSoldGiftCards\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int\\|string, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<int, array\\{Partenaire\\: mixed, Mois\\: string, Création\\: float, Vendus\\: float, Annulation\\: float, Péremption codes vendus\\: float, Péremption codes non vendus\\: float, Devise\\: string, \\.\\.\\.\\}\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Exports/ExportsD/DFrPartnerGiftCardsExport.php

		-
			message: "#^Only numeric types are allowed in /, float\\|int\\|string\\|null given on the left side\\.$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Commands/ArtisanPaymentsChecks.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Models/Invoice.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Models/Invoice.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\:\\:discountLines\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount, App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLine, \\$this\\(App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Models/Invoice.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\:\\:paymentMethods\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod, App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLine, \\$this\\(App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Models/Invoice.php

		-
			message: "#^Parameter \\#1 \\$items of method Illuminate\\\\Support\\\\Collection\\<int,array\\<string, App\\\\Domain\\\\Accounting\\\\Invoices\\\\Enums\\\\InvoiceProductType\\|bool\\|int\\|string\\|null\\>\\>\\:\\:merge\\(\\) expects Illuminate\\\\Contracts\\\\Support\\\\Arrayable\\<int, array\\{type\\: App\\\\Domain\\\\Accounting\\\\Invoices\\\\Enums\\\\InvoiceProductType, code\\: string\\|null, name\\: string, total_price\\: string, unit_price\\: string, quantity\\: int, artisan\\: string\\|null, client\\: string\\|null, \\.\\.\\.\\}\\>\\|iterable\\<int, array\\{type\\: App\\\\Domain\\\\Accounting\\\\Invoices\\\\Enums\\\\InvoiceProductType, code\\: string\\|null, name\\: string, total_price\\: string, unit_price\\: string, quantity\\: int, artisan\\: string\\|null, client\\: string\\|null, \\.\\.\\.\\}\\>, Illuminate\\\\Support\\\\Collection\\<int, array\\{type\\: App\\\\Domain\\\\Accounting\\\\Orders\\\\Enums\\\\ProductType\\|null, code\\: null, booking_date\\: null, name\\: string\\|null, total_price\\: string, unit_price\\: string, quantity\\: mixed\\}\\> given\\.$#"
			count: 1
			path: app/Domain/Accounting/Invoices/Transformers/InvoiceTransformer.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\:\\:invoiceDiscounts\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLineDiscount, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\:\\:invoicePaymentMethods\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\:\\:refundPaymentMethods\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderDiscount.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderItemGiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemGiftCard\\:\\:orderItem\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemGiftCard\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderItemGiftCard.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderItemTicket.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemTicket\\:\\:orderItem\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemTicket\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderItemTicket.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderPayment.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderPayment.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderPayment\\:\\:invoicePaymentMethods\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\InvoiceLinePaymentMethod, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderPayment\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderPayment.php

		-
			message: "#^Method App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderPayment\\:\\:refundPaymentMethods\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefundPaymentMethod, \\$this\\(App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderPayment\\)\\>\\.$#"
			count: 1
			path: app/Domain/Accounting/Orders/Models/OrderPayment.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$date has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$effect has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$mood has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$origin has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$type has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$what has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Property App\\\\Domain\\\\Booking\\\\Entities\\\\MoodAction\\:\\:\\$who has no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/MoodAction.php

		-
			message: "#^Casting to array something that's already array\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:__construct\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:addAction\\(\\) has parameter \\$status with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:getMoodPresenter\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:setEffect\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:setEffect\\(\\) has parameter \\$action with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Entities\\\\ReservationMood\\:\\:setEffect\\(\\) has parameter \\$status with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Entities/ReservationMood.php

		-
			message: "#^Only booleans are allowed in &&, int given on the right side\\.$#"
			count: 1
			path: app/Domain/Booking/Models/BookingParticipant.php

		-
			message: "#^Only booleans are allowed in &&, string given on the right side\\.$#"
			count: 1
			path: app/Domain/Booking/Models/BookingParticipant.php

		-
			message: "#^Unable to resolve the template type TRelatedModel in call to method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<TRelatedModel of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:whereHas\\(\\)$#"
			count: 1
			path: app/Domain/Booking/Repositories/BookingRepository.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:findByStartAndWorkshop\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsByWorkshopId\\(\\) has parameter \\$limit with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsByWorkshopId\\(\\) has parameter \\$perPage with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsByWorkshopId\\(\\) return type with generic class Illuminate\\\\Pagination\\\\LengthAwarePaginator does not specify its types\\: TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsByWorkshopId\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsForWorkshopsBetweenDates\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsForWorkshopsBetweenDatesBuilder\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsForWorkshopsBuilder\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getEventsFromDateForWorkshopsBuilder\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:getForWorkshopInPeriod\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCapacityForWorkshop\\(\\) has parameter \\$capacity with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCapacityForWorkshop\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCutAndCapacityForWorkshop\\(\\) has parameter \\$capacity with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCutAndCapacityForWorkshop\\(\\) has parameter \\$cut with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCutAndCapacityForWorkshop\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCutForWorkshop\\(\\) has parameter \\$cut with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:massUpdateCutForWorkshop\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Repositories\\\\EventRepository\\:\\:processSearchableChunk\\(\\) has parameter \\$items with generic class Illuminate\\\\Support\\\\Collection but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, int\\|null given\\.$#"
			count: 3
			path: app/Domain/Booking/Repositories/EventRepository.php

		-
			message: "#^Cannot access property \\$artisan on App\\\\Models\\\\Evenement\\|null\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Cannot access property \\$atelier on App\\\\Models\\\\Evenement\\|null\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Cannot call method notify\\(\\) on App\\\\Models\\\\Artisan\\|null\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Cannot call method sendEmailNotification\\(\\) on App\\\\Models\\\\Artisan\\|null\\.$#"
			count: 2
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Cannot call method toImmutable\\(\\) on Carbon\\\\Carbon\\|null\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:canStatusBeChangedGuard\\(\\) has parameter \\$newStatus with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:moveBooking\\(\\) has parameter \\$newStatus with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:transformInGiftCard\\(\\) has parameter \\$causer with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:updateStatus\\(\\) has parameter \\$bookingId with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:updateStatus\\(\\) has parameter \\$change with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\BookingService\\:\\:updateStatus\\(\\) has parameter \\$newStatus with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Only booleans are allowed in an if condition, bool\\|null given\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Parameter \\#1 \\$cart of method App\\\\Domain\\\\Accounting\\\\Orders\\\\OrderService\\:\\:createPaidOrderFromCart\\(\\) expects App\\\\Models\\\\Panier, App\\\\Models\\\\Panier\\|null given\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Parameter \\$email of class App\\\\Domain\\\\Gift\\\\GiftDeliveryEmail constructor expects string, string\\|null given\\.$#"
			count: 1
			path: app/Domain/Booking/Services/BookingService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\EventService\\:\\:updateCapacity\\(\\) has parameter \\$capacity with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/EventService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\EventService\\:\\:updateCapacity\\(\\) has parameter \\$type with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/EventService.php

		-
			message: "#^Method App\\\\Domain\\\\Booking\\\\Services\\\\EventService\\:\\:updateCapacity\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Booking/Services/EventService.php

		-
			message: "#^Parameter \\#1 \\$old of method App\\\\Domain\\\\Box\\\\BoxSync\\\\BoxSyncAbstract\\:\\:changesCount\\(\\) expects Illuminate\\\\Support\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>, Illuminate\\\\Support\\\\Collection\\<int, stdClass\\> given\\.$#"
			count: 1
			path: app/Domain/Box/BoxSync/BoxSyncAbstract.php

		-
			message: "#^Parameter \\#2 \\$new of method App\\\\Domain\\\\Box\\\\BoxSync\\\\BoxSyncAbstract\\:\\:changesCount\\(\\) expects Illuminate\\\\Support\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>, Illuminate\\\\Support\\\\Collection\\<int, stdClass\\> given\\.$#"
			count: 1
			path: app/Domain/Box/BoxSync/BoxSyncAbstract.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Content\\\\Models\\\\SinglePage\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Abstracts/CategorizationModel.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Abstracts\\\\CategorizationModel\\:\\:singlePage\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Content\\\\Models\\\\SinglePage\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Content\\\\Models\\\\SinglePage, \\$this\\(App\\\\Domain\\\\Content\\\\Abstracts\\\\CategorizationModel\\)\\>\\.$#"
			count: 1
			path: app/Domain/Content/Abstracts/CategorizationModel.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\Artisan\\|App\\\\Models\\\\Atelier\\:\\:\\$available_languages\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\Artisan\\|App\\\\Models\\\\Atelier\\:\\:\\$lieu\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\Artisan\\|App\\\\Models\\\\Atelier\\:\\:mainLocation\\(\\)\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Cannot access property \\$id on App\\\\Domain\\\\Content\\\\Models\\\\ContentMedia\\|null\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Cannot access property \\$ville on App\\\\Models\\\\Lieu\\|null\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findArtisanPage\\(\\) has parameter \\$artisanId with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findArtisanPage\\(\\) should return App\\\\Domain\\\\Content\\\\Models\\\\ContentPage but returns App\\\\Domain\\\\Content\\\\Models\\\\ContentPage\\|null\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopByArtisanUuid\\(\\) has parameter \\$artisanUuid with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopByArtisanUuid\\(\\) has parameter \\$workshopUuid with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopById\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopById\\(\\) should return App\\\\Models\\\\Atelier but returns App\\\\Models\\\\Atelier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopPage\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\ContentService\\:\\:findWorkshopPage\\(\\) should return App\\\\Domain\\\\Content\\\\Models\\\\ContentPage but returns App\\\\Domain\\\\Content\\\\Models\\\\ContentPage\\|null\\.$#"
			count: 1
			path: app/Domain/Content/ContentService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Contracts\\\\ContentInterface\\:\\:getAccountable\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Contracts/ContentInterface.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Contracts\\\\ContentInterface\\:\\:getContentNaming\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Contracts/ContentInterface.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Contracts\\\\ContentInterface\\:\\:getPageUrl\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Contracts/ContentInterface.php

		-
			message: "#^Call to an undefined method Yajra\\\\DataTables\\\\DataTableAbstract\\|Yajra\\\\DataTables\\\\DataTables\\:\\:query\\(\\)\\.$#"
			count: 1
			path: app/Domain/Content/DataTables/ContentMediaDataTable.php

		-
			message: "#^Property App\\\\Domain\\\\Content\\\\Exceptions\\\\Page\\\\UnknownPageTypeException\\:\\:\\$unknownType has no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Exceptions/Page/UnknownPageTypeException.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\ContentMedia\\:\\:contentPage\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/ContentMedia.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\ContentMedia\\:\\:onboarding\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/ContentMedia.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Domain\\\\Content\\\\Models\\\\ContentMedia\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Domain/Content/Models/ContentMedia.php

		-
			message: "#^PHPDoc type string of property App\\\\Domain\\\\Content\\\\Models\\\\ContentMedia\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Domain/Content/Models/ContentMedia.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\ContentPage\\:\\:contentMedia\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Models/ContentPage.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Domain\\\\Content\\\\Models\\\\ContentPage\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Domain/Content/Models/ContentPage.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\Creation\\:\\:workshops\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/Creation.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\SinglePage\\:\\:getUrl\\(\\) has parameter \\$subject with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Models/SinglePage.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\SinglePage\\:\\:sections\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/SinglePage.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\SinglePage\\:\\:subject\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/SinglePage.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Models\\\\Technique\\:\\:workshops\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Content/Models/Technique.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Notifications\\\\AdditionalInformationRequired\\:\\:__construct\\(\\) has parameter \\$accountable with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Notifications/AdditionalInformationRequired.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Notifications\\\\AdditionalInformationRequired\\:\\:__construct\\(\\) has parameter \\$page with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Notifications/AdditionalInformationRequired.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Notifications\\\\AdditionalInformationRequired\\:\\:__construct\\(\\) has parameter \\$pageLink with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Notifications/AdditionalInformationRequired.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$currency\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$total\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:findFromSponsorCode\\(\\) should return object\\{id\\: int, prenom\\: string, nom\\: string, nom_alternatif\\: string, active\\: int\\}\\|null but returns object\\|null\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:getArtisansAsFacets\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:getArtisansBuilderForFuturEvents\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:getArtisansWithFuturEventsBuilder\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:getArtisansWithoutFuturEventsBuilder\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\ArtisanRepository\\:\\:getNotSynchronizedArtisans\\(\\) return type with generic class Illuminate\\\\Support\\\\LazyCollection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Parameter \\#1 \\$query of static method Illuminate\\\\Database\\\\Connection\\:\\:select\\(\\) expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/ArtisanRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\CreationRepository\\:\\:fetch\\(\\) return type with generic class Illuminate\\\\Pagination\\\\LengthAwarePaginator does not specify its types\\: TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/CreationRepository.php

		-
			message: "#^Only booleans are allowed in an if condition, string\\|null given\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/CreationRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\PageRepository\\:\\:dataTablesQuery\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Content/Repositories/PageRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\SinglePageRepository\\:\\:getSubjectLinkedSinglePages\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/SinglePageRepository.php

		-
			message: "#^Strict comparison using \\!\\=\\= between int and null will always evaluate to true\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/SinglePageRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\VilleRepository\\:\\:fetch\\(\\) return type with generic class Illuminate\\\\Pagination\\\\LengthAwarePaginator does not specify its types\\: TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/VilleRepository.php

		-
			message: "#^Only booleans are allowed in an if condition, string\\|null given\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/VilleRepository.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$artisan_id\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$id\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$nom\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Call to method Illuminate\\\\Database\\\\Query\\\\Builder\\:\\:leftJoin\\(\\) with incorrect case\\: leftjoin$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:findByArtisanUuid\\(\\) has parameter \\$artisanUuid with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:findByArtisanUuid\\(\\) has parameter \\$workshopUuid with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getAllWorkshopsForHtmlSelect\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getNotSynchronizedWorkshops\\(\\) return type with generic class Illuminate\\\\Support\\\\LazyCollection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getSimilarWorkshopsForWorkshop\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getSimilarWorkshopsForWorkshopOnlyGroups\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getSimilarWorkshopsForWorkshopWithoutGroups\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getSimilarWorkshopsQuery\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getWorkshopsForEventCreation\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getWorkshopsForUnavailabilityCreation\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:getWorkshopsRawForScoreUpdate\\(\\) should return Generator\\<int, object\\{workshop_id\\: int, events_count_next_90d\\: int, events_count_post_90d\\: int, available_seats_next_90d\\: int, comments_count_last_12m\\: int, dissatisfied_customer_count_last_2m\\: int, animated_events_count_last_12m\\: int, rank_by_animated_events_count\\: int, has_animated_event_last_3m\\: int, sales_last_90d\\: int, is_gift_removed\\: int, is_overloaded\\: int, days_count_since_online\\: int, is_promoted\\: int, workshop_previous_score\\: float\\|null, active_workshops_count\\: int, last_90d_max_sales\\: float, max_events_animated_count\\: int\\}, mixed, mixed\\> but returns Generator\\<int, stdClass, mixed, mixed\\>\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:isStackable\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:isStackable\\(\\) has parameter \\$workshopId with no type specified\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\Repositories\\\\WorkshopRepository\\:\\:processSearchableChunk\\(\\) has parameter \\$items with generic class Illuminate\\\\Support\\\\Collection but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Return type of call to method Illuminate\\\\Database\\\\Query\\\\Builder\\:\\:chunkMap\\(\\) contains unresolvable type\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopRepository.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Domain/Content/Repositories/WorkshopStatisticsRepository.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Cannot access property \\$data on App\\\\Domain\\\\Content\\\\Models\\\\SinglePageSection\\|null\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Cannot access property \\$order on App\\\\Domain\\\\Content\\\\Models\\\\SinglePageSection\\|null\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Cannot call method fill\\(\\) on App\\\\Domain\\\\Content\\\\Models\\\\SinglePageSection\\|null\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Cannot call method save\\(\\) on App\\\\Domain\\\\Content\\\\Models\\\\SinglePageSection\\|null\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Domain\\\\Content\\\\Enums\\\\SubjectType\\|null given\\.$#"
			count: 1
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Unable to resolve the template type TKey in call to function collect$#"
			count: 3
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Unable to resolve the template type TValue in call to function collect$#"
			count: 3
			path: app/Domain/Content/SinglePageService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\SingleSectionService\\:\\:getCitiesDataFromSection\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/SingleSectionService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\SingleSectionService\\:\\:getCraftsDataFromSection\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/SingleSectionService.php

		-
			message: "#^Method App\\\\Domain\\\\Content\\\\SingleSectionService\\:\\:getCreationsDataFromSection\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Content/SingleSectionService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartMapper\\:\\:mapCartDiscounts\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int, App\\\\Shared\\\\ECommerce\\\\CartDiscount\\> but returns Illuminate\\\\Support\\\\Collection\\<int, App\\\\Shared\\\\ECommerce\\\\CartDiscount\\|null\\>\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartMapper.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\Collection\\<int,App\\\\Shared\\\\ECommerce\\\\CartDiscount\\|null\\>\\:\\:filter\\(\\) expects \\(callable\\(App\\\\Shared\\\\ECommerce\\\\CartDiscount\\|null, int\\)\\: bool\\)\\|null, Closure\\(App\\\\Shared\\\\ECommerce\\\\CartDiscount\\)\\: bool given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartMapper.php

		-
			message: "#^Access to an undefined property App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:\\$evenement\\.$#"
			count: 6
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:\\$nom\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Call to an undefined method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:getDescription\\(\\)\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Call to an undefined method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:getProductUrl\\(\\)\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:getUrl\\(\\)\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Cannot access property \\$nom on App\\\\Models\\\\Artisan\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Cannot access property \\$nom on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Cannot access property \\$permalien on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Cannot access property \\$prenom on App\\\\Models\\\\Artisan\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Offset 'ticket' does not exist on array\\{ticket\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>, amount\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>\\}\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Unable to resolve the template type TKey in call to function collect$#"
			count: 2
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Unable to resolve the template type TValue in call to function collect$#"
			count: 2
			path: app/Domain/ECommerce/CartPresenter.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:cleanCart\\(\\) has parameter \\$trashIt with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:getCart\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:getCartOrFail\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:getWorkshopForCartItem\\(\\) has parameter \\$field with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:initUserSession\\(\\) has parameter \\$userId with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:simulateItem\\(\\) should return App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem but returns App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:updateCartById\\(\\) has parameter \\$cartId with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Offset 'ticket' does not exist on array\\{ticket\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>, amount\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>\\}\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Domain\\\\ECommerce\\\\Repositories\\\\CartItemRepository\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Parameter \\#1 \\$giftCard of static method App\\\\Domain\\\\ECommerce\\\\DiscountImpacts\\\\GiftCardImpact\\:\\:fromAmountGiftCard\\(\\) expects App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard, App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Parameter \\#1 \\$giftCard of static method App\\\\Domain\\\\ECommerce\\\\DiscountImpacts\\\\GiftCardImpact\\:\\:fromTicketGiftCard\\(\\) expects App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard, App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Parameter \\#2 \\$giftCard of static method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:applyAmountCoupon\\(\\) expects App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard, App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Parameter \\#2 \\$giftCard of static method App\\\\Domain\\\\ECommerce\\\\CartService\\:\\:applyTicketCoupon\\(\\) expects App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard, App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Property App\\\\Models\\\\Panier\\:\\:\\$coupons \\(array\\{ticket\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>, amount\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>\\}\\) does not accept array\\{ticket\\: non\\-empty\\-array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null\\>, amount\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>\\}\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Property App\\\\Models\\\\Panier\\:\\:\\$coupons \\(array\\{ticket\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>, amount\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>\\}\\) does not accept array\\{ticket\\?\\: array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\>, amount\\: non\\-empty\\-array\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|null\\>\\}\\.$#"
			count: 1
			path: app/Domain/ECommerce/CartService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\DiscountInterface\\:\\:descriptionReduction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/DiscountInterface.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\DiscountInterface\\:\\:getDiscountValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/DiscountInterface.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\IProduct\\:\\:commande\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/IProduct.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\IProduct\\:\\:getImageUrl\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/IProduct.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\IProduct\\:\\:getImageUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/IProduct.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Contracts\\\\IProduct\\:\\:produitscomplementaires\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Contracts/IProduct.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 5
			path: app/Domain/ECommerce/DiscountService.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Entities\\\\UserStats\\:\\:updateStat\\(\\) has parameter \\$stat with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Entities/UserStats.php

		-
			message: "#^Variable property access on \\$this\\(App\\\\Domain\\\\ECommerce\\\\Entities\\\\UserStats\\)\\.$#"
			count: 2
			path: app/Domain/ECommerce/Entities/UserStats.php

		-
			message: "#^Call to an undefined method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:getDescription\\(\\)\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Call to method App\\\\Models\\\\Atelier\\:\\:getUrl\\(\\) with incorrect case\\: geturl$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Cannot access property \\$evenement on App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Cannot access property \\$participants on App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Cannot access property \\$sous_titre on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Cannot call method getProductImage\\(\\) on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\:\\:getDescription\\(\\) should return string but returns string\\|null\\.$#"
			count: 2
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\:\\:getImageUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\:\\:relatedDiscount\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\:\\:relatedProduct\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Only booleans are allowed in &&, int given on the right side\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Only booleans are allowed in &&, string given on the right side\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, App\\\\Abstracts\\\\Reduction\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/CartItem.php

		-
			message: "#^Cannot access property \\$price on App\\\\Domain\\\\ECommerce\\\\Models\\\\ProductPrice\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Models/Product.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\ProductService\\:\\:getCacheKeyForPhysicalShipmentPriceForCurrency\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/ECommerce/ProductService.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, int\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/Repositories/CartItemRepository.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Domain\\\\ECommerce\\\\Models\\\\PhysicalCardItem\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/Repositories/CartItemRepository.php

		-
			message: "#^Only booleans are allowed in an if condition, int\\|null given\\.$#"
			count: 1
			path: app/Domain/ECommerce/Repositories/CartItemRepository.php

		-
			message: "#^Variable method call on Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 2
			path: app/Domain/ECommerce/Repositories/CartItemRepository.php

		-
			message: "#^Variable property access on Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 4
			path: app/Domain/ECommerce/Repositories/CartItemRepository.php

		-
			message: "#^Method App\\\\Domain\\\\ECommerce\\\\Repositories\\\\CartRepository\\:\\:getCartFromSession\\(\\) should return App\\\\Models\\\\Panier\\|null but returns App\\\\Models\\\\Panier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Panier\\>\\|null\\.$#"
			count: 1
			path: app/Domain/ECommerce/Repositories/CartRepository.php

		-
			message: "#^Variable method call on Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 2
			path: app/Domain/ECommerce/Repositories/CartRepository.php

		-
			message: "#^Variable property access on Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 4
			path: app/Domain/ECommerce/Repositories/CartRepository.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo\\<Illuminate\\\\Database\\\\Eloquent\\\\Model,App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\>\\:\\:associate\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model\\|null, App\\\\Domain\\\\Gift\\\\Models\\\\Giftable given\\.$#"
			count: 1
			path: app/Domain/Gift/GiftCreator.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftDeliveryEmail\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/Gift.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\:\\:deliveryEmail\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftDeliveryEmail\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\GiftDeliveryEmail, \\$this\\(App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\)\\>\\.$#"
			count: 1
			path: app/Domain/Gift/Models/Gift.php

		-
			message: "#^Cannot access property \\$format on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Cannot access property \\$nom on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Cannot access property \\$permalien on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Cannot call method getUnitPriceIncludeVat\\(\\) on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:associatedWorkshop\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:buyer\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOneThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:cartItem\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:commande\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:expirationChildren\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:getImageUrl\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:getImageUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:getLastAttemptQuery\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:getNewCoupon\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:getOldCoupon\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:gift\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift, \\$this\\(App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\)\\>\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:initialWorkshop\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:invoices\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:isFromTransformation\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:orderItemGiftCard\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:orderItemRefundMethod\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:ordersWhereHasBeenApplied\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:produitscomplementaires\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:receivedBy\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeActive\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeActive\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeAssociated\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeAssociated\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeValuable\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:scopeValuable\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\:\\:toApiResourceArray\\(\\) should return array\\<string, mixed\\> but returns array\\|null\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Parameter \\#2 \\$replace of function __ expects array\\<string, bool\\|float\\|int\\|string\\>, array\\<string, float\\|int\\|null\\> given\\.$#"
			count: 1
			path: app/Domain/Gift/Models/GiftCard.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Models\\\\GiftContact\\:\\:related\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/GiftContact.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Gift/Models/Giftable.php

		-
			message: "#^Method App\\\\Domain\\\\Gift\\\\Notifications\\\\GiftCardsLotExportAvailable\\:\\:__construct\\(\\) has parameter \\$link with no type specified\\.$#"
			count: 1
			path: app/Domain/Gift/Notifications/GiftCardsLotExportAvailable.php

		-
			message: "#^Method App\\\\Domain\\\\Location\\\\Repositories\\\\LocationRepository\\:\\:allWithArtisans\\(\\) should return Illuminate\\\\Support\\\\Collection\\<int, object\\{id\\: int, artisan_id\\: int, is_main\\: int, full_name\\: string\\}\\> but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), stdClass\\>\\.$#"
			count: 1
			path: app/Domain/Location/Repositories/LocationRepository.php

		-
			message: "#^Call to an undefined method Yajra\\\\DataTables\\\\DataTableAbstract\\|Yajra\\\\DataTables\\\\DataTables\\:\\:of\\(\\)\\.$#"
			count: 1
			path: app/Domain/Onboarding/DataTables/OnboardingDataTable.php

		-
			message: "#^Method App\\\\Domain\\\\Onboarding\\\\Models\\\\Onboarding\\:\\:contentPages\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Domain/Onboarding/Models/Onboarding.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Domain\\\\Onboarding\\\\Models\\\\Onboarding\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Domain/Onboarding/Models/Onboarding.php

		-
			message: "#^Cannot access property \\$id on App\\\\Domain\\\\Onboarding\\\\Models\\\\Onboarding\\|null\\.$#"
			count: 1
			path: app/Domain/Onboarding/Services/OnboardingService.php

		-
			message: "#^Only booleans are allowed in &&, App\\\\Domain\\\\Content\\\\Models\\\\ContentPage\\|null given on the right side\\.$#"
			count: 1
			path: app/Domain/Onboarding/Services/OnboardingService.php

		-
			message: "#^Method App\\\\Domain\\\\User\\\\Repositories\\\\UserRepository\\:\\:getNonSynchronizedUsers\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Domain/User/Repositories/UserRepository.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\<App\\\\Models\\\\Reservation\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\: TRelatedModel, TDeclaringModel, TResult$#"
			count: 1
			path: app/Exports/ParticipantsExport.php

		-
			message: "#^Method App\\\\Exports\\\\ParticipantsExport\\:\\:query\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Models\\\\Reservation\\>\\|Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\<App\\\\Models\\\\Reservation\\>\\|Illuminate\\\\Database\\\\Query\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<App\\\\Models\\\\Reservation, App\\\\Models\\\\Evenement\\>\\.$#"
			count: 1
			path: app/Exports/ParticipantsExport.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\>\\:\\:\\$code_reduction\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Cannot access property \\$ville_nom on App\\\\Models\\\\Ville\\|string\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Cannot call method format\\(\\) on Carbon\\\\Carbon\\|false\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFieldList\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFieldList\\(\\) has parameter \\$fieldList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFieldList\\(\\) has parameter \\$index with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFieldList\\(\\) has parameter \\$needle with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFormValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFormValues\\(\\) has parameter \\$fieldList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFormValues\\(\\) has parameter \\$index with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addIndexToFormValues\\(\\) has parameter \\$needle with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addValueToFieldAttribute\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addValueToFieldAttribute\\(\\) has parameter \\$fieldList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:addValueToFieldAttribute\\(\\) has parameter \\$fieldsToReplace with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:cleanFormValues\\(\\) has parameter \\$model with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:cleanFormValues\\(\\) has parameter \\$values with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:createCustomIdField\\(\\) has parameter \\$attribute with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:createCustomIdField\\(\\) has parameter \\$model with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:createCustomIdField\\(\\) has parameter \\$modelClass with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:createCustomIdField\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:createFormValuesFromSchema\\(\\) has parameter \\$schema with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getAttributeFromModel\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getAttributeFromModel\\(\\) has parameter \\$fields with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getAttributeFromModel\\(\\) has parameter \\$model with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getConfiguration\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getCustomFormValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getCustomFormValues\\(\\) has parameter \\$attribute with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getCustomFormValues\\(\\) has parameter \\$model with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getFormValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getFormValues\\(\\) has parameter \\$fieldsList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getFormValues\\(\\) has parameter \\$models with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getModel\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getModel\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getSchema\\(\\) has parameter \\$fieldList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getValues\\(\\) has parameter \\$field with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getValuesFromModels\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getValuesFromModels\\(\\) has parameter \\$fieldList with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:getValuesFromModels\\(\\) has parameter \\$models with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:updateCustomFormValues\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:updateCustomFormValues\\(\\) has parameter \\$key with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:updateCustomFormValues\\(\\) has parameter \\$model with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:updateCustomFormValues\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Method App\\\\Helpers\\\\FormHelper\\:\\:updateFormValues\\(\\) has parameter \\$formValues with no type specified\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\>\\|null given\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null given\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Only booleans are allowed in an if condition, array\\|null given\\.$#"
			count: 1
			path: app/Helpers/FormHelper.php

		-
			message: "#^Variable property access on mixed\\.$#"
			count: 3
			path: app/Helpers/FormHelper.php

		-
			message: "#^Comparison operation \"\\=\\=\" between \\(array\\|float\\|int\\) and 0 results in an error\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminAtelierController.php

		-
			message: "#^Argument of an invalid type array\\|string supplied for foreach, only iterables are supported\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminCollectionController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$accountable\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$adminFirstname\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$adminLastname\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$artisanActive\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$city\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$mediaNotes\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$mediaSource\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$mediaStatus\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$onboardedAltName\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$onboardedFirstname\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$onboardedLastname\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$pageCreatedAt\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$pageNotes\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$pageStatus\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$pageType\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$pageTypeId\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$workshopActive\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$workshopVisible\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Parameter \\#1 \\$result of function getPageDecorator expects stdClass, object given\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/AdminContentManagementController.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<App\\\\Passport\\\\Models\\\\Client,App\\\\Models\\\\Entreprise\\>\\:\\:associate\\(\\) expects App\\\\Passport\\\\Models\\\\Client\\|int\\|string\\|null, Laravel\\\\Passport\\\\Client given\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/Companies/AdminCompanyPartnerApiKeyController.php

		-
			message: "#^Return type of call to method Illuminate\\\\Support\\\\Collection\\<\\*NEVER\\*,\\*NEVER\\*\\>\\:\\:map\\(\\) contains unresolvable type\\.$#"
			count: 1
			path: app/Http/Controllers/Admin/PrivateBooking/AdminPrivateBookingShowController.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\Panier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Panier\\>\\:\\:getDetailedDiscount\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/CodeReductionController.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 1
			path: app/Http/Controllers/Front/CodeReductionController.php

		-
			message: "#^Cannot access property \\$permalien on App\\\\Models\\\\Atelier\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/Front/CodeReductionController.php

		-
			message: "#^Cannot call method isoFormat\\(\\) on Carbon\\\\Carbon\\|string\\.$#"
			count: 3
			path: app/Http/Controllers/Front/CodeReductionController.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Domain\\\\Box\\\\Models\\\\PhysicalBox\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/Front/CodeReductionController.php

		-
			message: "#^Cannot access property \\$availableSeats on \\(int\\|string\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/ReservationController.php

		-
			message: "#^Cannot access property \\$cut on \\(int\\|string\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/ReservationController.php

		-
			message: "#^Cannot access property \\$nb_places_disponibles on \\(int\\|string\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/ReservationController.php

		-
			message: "#^Cannot access property \\$participants_count on \\(int\\|string\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/ReservationController.php

		-
			message: "#^Cannot access property \\$start on \\(int\\|string\\)\\.$#"
			count: 1
			path: app/Http/Controllers/Front/ReservationController.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Crm\\\\Hubspot\\\\Jobs\\\\CreateTaskForStockTransfer\\:\\:dispatch\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: app/Infrastructure/Crm/Hubspot/Jobs/CreateTaskForStockTransfer.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Crm\\\\Hubspot\\\\Jobs\\\\SynchronizeArtisans\\:\\:dispatch\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: app/Infrastructure/Crm/Hubspot/Jobs/SynchronizeArtisans.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Crm\\\\Hubspot\\\\Jobs\\\\SynchronizeArtisansStatistics\\:\\:dispatch\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: app/Infrastructure/Crm/Hubspot/Jobs/SynchronizeArtisansStatistics.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Crm\\\\Hubspot\\\\Jobs\\\\SynchronizeWorkshops\\:\\:dispatch\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: app/Infrastructure/Crm/Hubspot/Jobs/SynchronizeWorkshops.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Crm\\\\Hubspot\\\\Jobs\\\\SynchronizeWorkshopsStatistics\\:\\:dispatch\\(\\) has parameter \\$args with no type specified\\.$#"
			count: 1
			path: app/Infrastructure/Crm/Hubspot/Jobs/SynchronizeWorkshopsStatistics.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\LazyCollection\\<int,object\\>\\:\\:each\\(\\) expects callable\\(object, int\\)\\: mixed, Closure\\(stdClass\\)\\: void given\\.$#"
			count: 1
			path: app/Infrastructure/Data/BigQuery/Repositories/BigQueryArtisanRepository.php

		-
			message: "#^Parameter \\#1 \\$callback of method Illuminate\\\\Support\\\\LazyCollection\\<int,object\\>\\:\\:each\\(\\) expects callable\\(object, int\\)\\: mixed, Closure\\(stdClass\\)\\: void given\\.$#"
			count: 1
			path: app/Infrastructure/Data/BigQuery/Repositories/BigQueryWorkshopRepository.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\> in PHPDoc tag @param for parameter \\$relation does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\: TRelatedModel, TDeclaringModel, TResult$#"
			count: 1
			path: app/Infrastructure/Search/Listener/SearchableRelationshipHandler.php

		-
			message: "#^PHPDoc tag @var for variable \\$dbModels contains generic class Illuminate\\\\Database\\\\Eloquent\\\\Collection but does not specify its types\\: TKey, TModel$#"
			count: 1
			path: app/Infrastructure/Search/Query/CategorizationService.php

		-
			message: "#^Variable property access on App\\\\Domain\\\\Content\\\\Abstracts\\\\CategorizationModel\\.$#"
			count: 1
			path: app/Infrastructure/Search/Query/CategorizationService.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 1
			path: app/Infrastructure/Search/Query/SearchService.php

		-
			message: "#^Method App\\\\Infrastructure\\\\Search\\\\Query\\\\SearchService\\:\\:buildQueryForField\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Infrastructure/Search/Query/SearchService.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Domain\\\\Content\\\\Abstracts\\\\CategorizationModel\\|null given\\.$#"
			count: 1
			path: app/Infrastructure/Search/Query/SearchService.php

		-
			message: "#^Parameter \\#2 \\$itemsToFiler of method App\\\\Infrastructure\\\\Search\\\\Query\\\\SearchService\\:\\:buildInOperator\\(\\) expects array, array\\|int\\|string given\\.$#"
			count: 1
			path: app/Infrastructure/Search/Query/SearchService.php

		-
			message: "#^Part \\$value \\(array\\|int\\|string\\) of encapsed string cannot be cast to string\\.$#"
			count: 4
			path: app/Infrastructure/Search/Query/SearchService.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Interfaces/Sponsor.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Interfaces/Sponsor.php

		-
			message: "#^Instanceof between App\\\\Livewire\\\\Common\\\\Table\\\\Enum\\\\Filter and App\\\\Livewire\\\\Common\\\\Table\\\\Enum\\\\Filter will always evaluate to true\\.$#"
			count: 1
			path: app/Livewire/Admin/Table/PrivateBookingList.php

		-
			message: "#^Property App\\\\Logger\\\\Processor\\\\DatadogProcessor\\:\\:\\$defaultTags \\(array\\<string, string\\|null\\>\\) does not accept array\\<string, bool\\|string\\|null\\>\\.$#"
			count: 1
			path: app/Logger/Processor/DatadogProcessor.php

		-
			message: "#^Method App\\\\Models\\\\Activity\\:\\:causer\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Activity.php

		-
			message: "#^Method App\\\\Models\\\\Activity\\:\\:impersonator\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Activity.php

		-
			message: "#^Only booleans are allowed in an if condition, mixed given\\.$#"
			count: 2
			path: app/Models/Activity.php

		-
			message: "#^PHPDoc type array\\<string, class\\-string\\> of property App\\\\Models\\\\Activity\\:\\:\\$dispatchesEvents is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$dispatchesEvents\\.$#"
			count: 1
			path: app/Models/Activity.php

		-
			message: "#^Method App\\\\Models\\\\Admin\\:\\:cityResponsible\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Admin.php

		-
			message: "#^Method App\\\\Models\\\\Admin\\:\\:companyResponsible\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Admin.php

		-
			message: "#^Method App\\\\Models\\\\Admin\\:\\:roles\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Admin.php

		-
			message: "#^Method App\\\\Models\\\\Admin\\:\\:teams\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Admin.php

		-
			message: "#^Parameter \\#2 \\$values of method Illuminate\\\\Support\\\\Collection\\<int,App\\\\Models\\\\Team\\>\\:\\:whereIn\\(\\) expects Illuminate\\\\Contracts\\\\Support\\\\Arrayable\\|iterable, array\\|string given\\.$#"
			count: 1
			path: app/Models/Admin.php

		-
			message: "#^Method App\\\\Models\\\\AdminTeamRole\\:\\:getRoleLabel\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/AdminTeamRole.php

		-
			message: "#^Method App\\\\Models\\\\AdminTeamRole\\:\\:role\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/AdminTeamRole.php

		-
			message: "#^Method App\\\\Models\\\\AdminTeamRole\\:\\:team\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/AdminTeamRole.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\AdminTeamRole\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/AdminTeamRole.php

		-
			message: "#^Method App\\\\Models\\\\Adresse\\:\\:toApiResourceArray\\(\\) should return array\\<string, mixed\\> but returns array\\|null\\.$#"
			count: 1
			path: app/Models/Adresse.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Adresse\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Adresse.php

		-
			message: "#^Call to function localizedRouteName\\(\\) with incorrect case\\: LocalizedRouteName$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Call to method App\\\\Models\\\\Artisan\\:\\:codeparrainageinvitation\\(\\) with incorrect case\\: CodeParrainageInvitation$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Cannot access property \\$id on App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Cannot call method each\\(\\) on Illuminate\\\\Support\\\\Collection\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Cannot call method increaseAmount\\(\\) on App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Cannot call method increaseUses\\(\\) on App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:client\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:code_reduction_global\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:codeparrainageinvitation\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation, \\$this\\(App\\\\Models\\\\Artisan\\)\\>\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:codeparrainageuser\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageUser, \\$this\\(App\\\\Models\\\\Artisan\\)\\>\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:defaultCurrency\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:entreprise\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:evenements\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getAccountable\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getAllContactsWithBookings\\(\\) has parameter \\$id with no type specified\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getEmailsForNotifications\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getEvenements\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getImages\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getInformationsDestinataire\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getPageUrl\\(\\) has parameter \\$absolute with no type specified\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:getPhoneNumbersForNotifications\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:httpRedirects\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:informations_facturation\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:invoices\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice, \\$this\\(App\\\\Models\\\\Artisan\\)\\>\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:langs\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:locations\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:mainLocation\\(\\) should return App\\\\Models\\\\Lieu but returns App\\\\Models\\\\Lieu\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:onboarding\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:photo_profil\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:propositionsDates\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:rib\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:sendSponsoredConfirmation\\(\\) has parameter \\$sponsor with no type specified\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:toApiResourceArray\\(\\) should return array\\<string, mixed\\> but returns array\\|null\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Method App\\\\Models\\\\Artisan\\:\\:usersartisans\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, string\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Mail\\\\BaseMailable\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Artisan\\|App\\\\Models\\\\User\\|null given\\.$#"
			count: 2
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\InformationFacturation\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\|null given\\.$#"
			count: 4
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\|null given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Only booleans are allowed in an if condition, string\\|null given\\.$#"
			count: 2
			path: app/Models/Artisan.php

		-
			message: "#^PHPDoc type array\\<string, class\\-string\\> of property App\\\\Models\\\\Artisan\\:\\:\\$dispatchesEvents is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$dispatchesEvents\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Parameter \\#1 \\$relations of method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Models\\\\Evenement\\>\\:\\:with\\(\\) expects array\\<array\\|\\(Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Relation\\<\\*, \\*, \\*\\>\\)\\: mixed\\)\\|string\\>\\|string, array\\{atelier\\: Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\)\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo, 0\\: 'atelier\\.artisan', 1\\: 'reservations', 2\\: 'lieu', 3\\: 'privateBookingReque…'\\} given\\.$#"
			count: 1
			path: app/Models/Artisan.php

		-
			message: "#^Call to function localizedRouteName\\(\\) with incorrect case\\: LocalizedRouteName$#"
			count: 3
			path: app/Models/Atelier.php

		-
			message: "#^Cannot access property \\$date_changement on App\\\\Models\\\\ChangementPrix\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Cannot access property \\$nom on App\\\\Models\\\\Atelier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>\\|null\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Cannot access property \\$prenom on App\\\\Models\\\\Admin\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Cannot access property \\$prix_initial on App\\\\Models\\\\ChangementPrix\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Cannot access property \\$ville on App\\\\Models\\\\Lieu\\|null\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Cannot call method ateliers_cumulables\\(\\) on App\\\\Models\\\\Atelier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>\\|null\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Cannot call method getRelationValue\\(\\) on App\\\\Models\\\\Image\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Cannot call method getUrl\\(\\) on App\\\\Models\\\\Image\\|null\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Cannot call method getUrlSource\\(\\) on App\\\\Models\\\\Image\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Casting to string something that's already string\\.$#"
			count: 3
			path: app/Models/Atelier.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklWorkshop\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:activeCreations\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:activeTags\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:activeTechniques\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:addImages\\(\\) has parameter \\$images with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:ajouterAtelierCumulable\\(\\) has parameter \\$atelier_id with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:ateliers_cumulables\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:bookingsWithStandbyStatus\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:boxs\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:cartItem\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:contentPage\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:creations\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:favoritedBy\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:format\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getAccountable\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getBackName\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getBackNameAttribute\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getBookingStatistics\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getContentNaming\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getEvenements\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getEvenementsFuturs\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getEvenementsFutursAvecPlacesDisponibles\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getImageUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getLastEvent\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getNbPlacesReserveesFutur\\(\\) should return float\\|int but returns float\\|int\\|string\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getPageUrl\\(\\) has parameter \\$absolute with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getProductImage\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getProductPagePriceFormat\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getProductThumbnailDescription\\(\\) has parameter \\$comparedWorkshop with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getProductThumbnailDescription\\(\\) has parameter \\$isDifferentWorkshop with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getProductThumbnailDescription\\(\\) has parameter \\$isGift with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getRank\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getSubtitle\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getUrl\\(\\) has parameter \\$boxSlug with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getUrl\\(\\) has parameter \\$isGift with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getXImagesUrl\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getXImagesUrl\\(\\) has parameter \\$count with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:getXImagesUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:httpRedirects\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:images\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:miraklWorkshop\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklWorkshop\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklWorkshop, \\$this\\(App\\\\Models\\\\Atelier\\)\\>\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:orderedImages\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:reservations\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:retirerAtelierCumulable\\(\\) has parameter \\$atelier_id with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeActivated\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeActivated\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeNew\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeNew\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeQueryShouldBeSearchable\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeQueryShouldBeSearchable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeVisible\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeVisible\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeWithFutureEvents\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:scopeWithFutureEvents\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:summarySheets\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:tags\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:techniques\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:unavailabilities\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:vignette\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Method App\\\\Models\\\\Atelier\\:\\:workshopsReplacement\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in &&, Carbon\\\\Carbon\\|null given on the left side\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in &&, bool\\|null given on the right side\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Models\\\\Format\\|null given\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Models\\\\Image\\|null given\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, Carbon\\\\Carbon\\|null given\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string\\|null given\\.$#"
			count: 5
			path: app/Models/Atelier.php

		-
			message: "#^Only booleans are allowed in an if condition, int given\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc tag @var for variable \\$evenements contains generic class Illuminate\\\\Support\\\\Collection but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc tag @var for variable \\$evenementsCumul contains generic class Illuminate\\\\Support\\\\Collection but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc tag @var for variable \\$evenementsLateReg contains generic class Illuminate\\\\Support\\\\Collection but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc type array\\<string, class\\-string\\> of property App\\\\Models\\\\Atelier\\:\\:\\$dispatchesEvents is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$dispatchesEvents\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc type array\\<string, string\\> of property App\\\\Models\\\\Atelier\\:\\:\\$attributes is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$attributes\\.$#"
			count: 1
			path: app/Models/Atelier.php

		-
			message: "#^Parameter \\#1 \\$model of method Spatie\\\\Activitylog\\\\ActivityLogger\\:\\:performedOn\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model, App\\\\Models\\\\Atelier\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Atelier\\>\\|null given\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^Parameter \\#1 \\$query of static method Illuminate\\\\Database\\\\Connection\\:\\:select\\(\\) expects string, float\\|int\\|string given\\.$#"
			count: 2
			path: app/Models/Atelier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\AtelierCumulable\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/AtelierCumulable.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\AtelierImage\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\Pivot\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/AtelierImage.php

		-
			message: "#^Method App\\\\Models\\\\Box\\:\\:ateliers\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Box.php

		-
			message: "#^Method App\\\\Models\\\\Box\\:\\:getProductName\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Box.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Box\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Box.php

		-
			message: "#^Method App\\\\Models\\\\CadeauRecu\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/CadeauRecu.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\CadeauRecu\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/CadeauRecu.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\ChangementPrix\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/ChangementPrix.php

		-
			message: "#^Method App\\\\Models\\\\Client\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Client.php

		-
			message: "#^Method App\\\\Models\\\\Client\\:\\:getIdentity\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Client.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefund\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklOrder\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\ProduitComplementaire\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Method App\\\\Models\\\\Commande\\:\\:invoices\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Invoices\\\\Models\\\\Invoice, \\$this\\(App\\\\Models\\\\Commande\\)\\>\\.$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Method App\\\\Models\\\\Commande\\:\\:miraklOrder\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklOrder\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklOrder, \\$this\\(App\\\\Models\\\\Commande\\)\\>\\.$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Method App\\\\Models\\\\Commande\\:\\:orderItemRefunds\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefund\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemRefund, App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem, \\$this\\(App\\\\Models\\\\Commande\\)\\>\\.$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Method App\\\\Models\\\\Commande\\:\\:produitscomplementaires\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\ProduitComplementaire\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\ProduitComplementaire, \\$this\\(App\\\\Models\\\\Commande\\)\\>\\.$#"
			count: 1
			path: app/Models/Commande.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Client\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Entreprise.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\InformationFacturation\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Entreprise.php

		-
			message: "#^Method App\\\\Models\\\\Entreprise\\:\\:client\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Client\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Client, \\$this\\(App\\\\Models\\\\Entreprise\\)\\>\\.$#"
			count: 1
			path: app/Models/Entreprise.php

		-
			message: "#^Method App\\\\Models\\\\Entreprise\\:\\:informations_facturation\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\InformationFacturation\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\InformationFacturation, \\$this\\(App\\\\Models\\\\Entreprise\\)\\>\\.$#"
			count: 1
			path: app/Models/Entreprise.php

		-
			message: "#^Foreach overwrites \\$event with its value variable\\.$#"
			count: 2
			path: app/Models/Evenement.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklEvent\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:addAvailableForBookingFilterToQuery\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:bookingsFrom\\(\\) has parameter \\$origins with no type specified\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:countConfirmedPlacesByOrigin\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:countInProgressBookingByStatus\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:evenement_recurrent\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:getEndAtEventTimezone\\(\\) should return Carbon\\\\Carbon but returns Carbon\\\\Carbon\\|string\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:getStartAtEventTimezone\\(\\) should return Carbon\\\\Carbon but returns Carbon\\\\Carbon\\|string\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:getValidBookingsNotArtisanQuery\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:getValidBookingsWebsiteOnly\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Collection does not specify its types\\: TKey, TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:miraklEvent\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklEvent\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklEvent, \\$this\\(App\\\\Models\\\\Evenement\\)\\>\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:scopeCollidesWithPeriod\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:scopeCollidesWithPeriod\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:scopePublicScheduled\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:scopePublicScheduled\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\Evenement\\:\\:toApiResourceArray\\(\\) should return array but returns array\\|null\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Only booleans are allowed in &&, int given on the left side\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:addDays\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: app/Models/Evenement.php

		-
			message: "#^Method App\\\\Models\\\\EvenementRecurrent\\:\\:localeStartTime\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Casts\\\\Attribute does not specify its types\\: TGet, TSet$#"
			count: 1
			path: app/Models/EvenementRecurrent.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\EvenementRecurrent\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/EvenementRecurrent.php

		-
			message: "#^Method App\\\\Models\\\\Format\\:\\:convertValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Format.php

		-
			message: "#^Method App\\\\Models\\\\Format\\:\\:convertValue\\(\\) has parameter \\$value with no type specified\\.$#"
			count: 1
			path: app/Models/Format.php

		-
			message: "#^Method App\\\\Models\\\\Format\\:\\:getPlaceSelectorValues\\(\\) should return array\\<string, array\\> but returns array\\<string, array\\<int, array\\<int, \\(array\\|string\\)\\>\\>\\|bool\\|string\\>\\.$#"
			count: 1
			path: app/Models/Format.php

		-
			message: "#^Method App\\\\Models\\\\Format\\:\\:getPlaceSelectorValues\\(\\) should return array\\<string, array\\> but returns array\\<string, array\\<string, array\\<int, string\\>\\>\\|bool\\|null\\>\\.$#"
			count: 1
			path: app/Models/Format.php

		-
			message: "#^Method App\\\\Models\\\\Format\\:\\:priceDecorator\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Format.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Formulaires\\\\AnnulationOuModificationReservation\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Formulaires/AnnulationOuModificationReservation.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\PropositionDate\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Formulaires/ContactAutreDateAtelier.php

		-
			message: "#^Method App\\\\Models\\\\Formulaires\\\\ContactAutreDateAtelier\\:\\:propositionsDates\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\PropositionDate\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Models\\\\PropositionDate, \\$this\\(App\\\\Models\\\\Formulaires\\\\ContactAutreDateAtelier\\)\\>\\.$#"
			count: 1
			path: app/Models/Formulaires/ContactAutreDateAtelier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Formulaires\\\\ContactEntreprise\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Formulaires/ContactEntreprise.php

		-
			message: "#^Method App\\\\Models\\\\InformationFacturation\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/InformationFacturation.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\InformationFacturation\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/InformationFacturation.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\InvitationAmi\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/InvitationAmi.php

		-
			message: "#^Method App\\\\Models\\\\Lang\\:\\:artisans\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Lang.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\LangArtisan\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/LangArtisan.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklLocation\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Lieu.php

		-
			message: "#^Method App\\\\Models\\\\Lieu\\:\\:miraklLocation\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklLocation\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\Marketplace\\\\Models\\\\MiraklLocation, \\$this\\(App\\\\Models\\\\Lieu\\)\\>\\.$#"
			count: 1
			path: app/Models/Lieu.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\PageStatique\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/PageStatique.php

		-
			message: "#^Access to an undefined property App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard\\|App\\\\Models\\\\Atelier\\|App\\\\Models\\\\Reservation\\:\\:\\$evenement\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Cannot call method addHours\\(\\) on Carbon\\\\Carbon\\|null\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:bookings\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:cadeauboxpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:codeparrainageinvitationpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:codeparrainageuserpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:codereductionglobalpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:codereductionuserpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:containGift\\(\\) should return bool but returns bool\\|int\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:descriptionReduction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:getDetailedDiscount\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:getMontant\\(\\) should return float\\|int but returns float\\|int\\|string\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:getMontantReduction\\(\\) should return float\\|int but returns float\\|int\\|string\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:getReduction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:orders\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:scopeAbandoned\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:scopeAbandoned\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:scopeConsideredAbandoned\\(\\) has parameter \\$query with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder but does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:scopeConsideredAbandoned\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Method App\\\\Models\\\\Panier\\:\\:toApiResourceArray\\(\\) should return array\\<string, mixed\\> but returns array\\|null\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in &&, int given on the right side\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in a negated boolean, bool\\|int given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, App\\\\Abstracts\\\\Reduction\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitationPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CadeauBoxPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CodeReductionGlobalPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^PHPDoc type array\\<string, mixed\\> of property App\\\\Models\\\\Panier\\:\\:\\$attributes is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$attributes\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Variable property access on mixed\\.$#"
			count: 1
			path: app/Models/Panier.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\:\\:codeparrainageinvitationpanier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\:\\:orderDiscounts\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount, \\$this\\(App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\)\\>\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\:\\:orders\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\:\\:user\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitationPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitation.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitationPanier\\:\\:descriptionReduction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitationPanier.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitationPanier\\:\\:getDiscountValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitationPanier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitationPanier\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageInvitationPanier.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\:\\:increaseAmount\\(\\) has parameter \\$amount with no type specified\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\:\\:orders\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\:\\:user\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^Property App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\:\\:\\$montant \\(int\\) does not accept float\\|int\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUser.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\:\\:codeparrainageuser\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUserPanier.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\:\\:descriptionReduction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUserPanier.php

		-
			message: "#^Method App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\:\\:getDiscountValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUserPanier.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUserPanier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Parrainage\\\\CodeParrainageUserPanier\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Parrainage/CodeParrainageUserPanier.php

		-
			message: "#^Cannot access property \\$name on App\\\\Domain\\\\ECommerce\\\\Models\\\\Product\\|null\\.$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\ProduitComplementaire\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\ProduitComplementaire\\:\\:getImageUrl\\(\\) has parameter \\$option with no type specified\\.$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\ProduitComplementaire\\:\\:invoiceLines\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\ProduitComplementaire\\:\\:orderItem\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem, \\$this\\(App\\\\Models\\\\ProduitComplementaire\\)\\>\\.$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\ProduitComplementaire\\:\\:produitscomplementaires\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/ProduitComplementaire.php

		-
			message: "#^Method App\\\\Models\\\\PropositionDate\\:\\:atelier\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<App\\\\Models\\\\Atelier, App\\\\Models\\\\PropositionDate\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<App\\\\Models\\\\Atelier, \\$this\\(App\\\\Models\\\\PropositionDate\\)\\>\\.$#"
			count: 1
			path: app/Models/PropositionDate.php

		-
			message: "#^Cannot access property \\$store on App\\\\Domain\\\\Box\\\\Models\\\\PhysicalBox\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Cannot access property \\$tracking_num on App\\\\Domain\\\\Box\\\\Models\\\\PhysicalBox\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Cannot access property \\$tracking_url on App\\\\Domain\\\\Box\\\\Models\\\\PhysicalBox\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:atelierBox\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:dematerializedBox\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:getMaxRemboursement\\(\\) should return float but returns float\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:getMontantPeremption\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:nouveau_boncadeau\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:orderDiscount\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderDiscount, \\$this\\(App\\\\Models\\\\Reductions\\\\CadeauBox\\)\\>\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:physicalBox\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:produitscomplementaires\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:scopeSold\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:scopeSold\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:setAsDispatched\\(\\) has parameter \\$company with no type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:setSold\\(\\) has parameter \\$forced with no type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:setSold\\(\\) has parameter \\$info with no type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Only booleans are allowed in &&, Carbon\\\\Carbon\\|null given on the left side\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CadeauBoxPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CadeauBox\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBox.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CadeauBoxPanier\\:\\:getDetailedDiscount\\(\\) should return App\\\\Abstracts\\\\Reduction but returns App\\\\Models\\\\Reductions\\\\CadeauBox\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBoxPanier.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Reductions\\\\CadeauBoxPanier\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBoxPanier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CadeauBoxPanier\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CadeauBoxPanier.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobal.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\:\\:orderDiscounts\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobal.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CodeReductionGlobalPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobal.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobal.php

		-
			message: "#^Property App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\:\\:\\$valeur \\(int\\) does not accept float\\|int\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobal.php

		-
			message: "#^Cannot access property \\$currency on App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\|null\\.$#"
			count: 2
			path: app/Models/Reductions/CodeReductionGlobalPanier.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CodeReductionGlobalPanier\\:\\:getDetailedDiscount\\(\\) should return App\\\\Abstracts\\\\Reduction but returns App\\\\Models\\\\Reductions\\\\CodeReductionGlobal\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobalPanier.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Reductions\\\\CodeReductionGlobalPanier\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobalPanier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CodeReductionGlobalPanier\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionGlobalPanier.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\|null given\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUser.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CodeReductionUser\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUser.php

		-
			message: "#^Property App\\\\Models\\\\Reductions\\\\CodeReductionUser\\:\\:\\$valeur \\(int\\) does not accept float\\|int\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUser.php

		-
			message: "#^Cannot access property \\$currency on App\\\\Models\\\\Reductions\\\\CodeReductionUser\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUserPanier.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\:\\:getDetailedDiscount\\(\\) should return App\\\\Abstracts\\\\Reduction but returns App\\\\Models\\\\Reductions\\\\CodeReductionUser\\|null\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUserPanier.php

		-
			message: "#^Method App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\:\\:getDiscountValue\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUserPanier.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUserPanier.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\Reductions\\\\CodeReductionUserPanier\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/Reductions/CodeReductionUserPanier.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 3
			path: app/Models/Reservation.php

		-
			message: "#^Class App\\\\Models\\\\Reservation implements generic interface Illuminate\\\\Contracts\\\\Support\\\\Arrayable but does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\PrivateBooking\\\\Models\\\\ProformaInvoice\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:cartItem\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:commissionBookingLines\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:evenementsCumulables\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getDescription\\(\\) should return string but returns string\\|null\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getImageUrl\\(\\) has parameter \\$options with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getOriginInfo\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getParticipantCountry\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getParticipantId\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:getParticipantPhone\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:gift\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Domain\\\\Gift\\\\Models\\\\Gift, \\$this\\(App\\\\Models\\\\Reservation\\)\\>\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:invoices\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:nouveau_boncadeau\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:orderItemTicket\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:produitscomplementaires\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:proformaInvoice\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\PrivateBooking\\\\Models\\\\ProformaInvoice\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Domain\\\\PrivateBooking\\\\Models\\\\ProformaInvoice, \\$this\\(App\\\\Models\\\\Reservation\\)\\>\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:satisfactionSurvey\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeFrom\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeFrom\\(\\) has parameter \\$origins with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeFrom\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeWithStatus\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeWithStatus\\(\\) has parameter \\$query with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:scopeWithStatus\\(\\) has parameter \\$status with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:setCommission\\(\\) has parameter \\$commission with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:toApiResourceArray\\(\\) should return array\\<string, mixed\\> but returns array\\|null\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:updateMoods\\(\\) has parameter \\$email with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Reservation\\:\\:updateMoods\\(\\) has parameter \\$reason with no type specified\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in &&, App\\\\Models\\\\Entreprise\\|null given on the right side\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in &&, App\\\\Models\\\\User\\|null given on the left side\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in &&, int given on the right side\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, App\\\\Abstracts\\\\Reduction\\|null given\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in an if condition, string\\|null given\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in \\|\\|, string\\|null given on the left side\\.$#"
			count: 2
			path: app/Models/Reservation.php

		-
			message: "#^Only booleans are allowed in \\|\\|, string\\|null given on the right side\\.$#"
			count: 2
			path: app/Models/Reservation.php

		-
			message: "#^Only numeric types are allowed in /, float\\|null given on the left side\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Only numeric types are allowed in pre\\-increment, int\\|null given\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^PHPDoc type array\\<string, class\\-string\\> of property App\\\\Models\\\\Reservation\\:\\:\\$dispatchesEvents is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$dispatchesEvents\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\Reservation\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Parameter \\#1 \\$number of static method App\\\\Helpers\\\\CurrencyHelper\\:\\:numberEqualsZero\\(\\) expects float\\|int\\|string, float\\|int\\|null given\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Parameter \\#1 \\$value of static method App\\\\Domain\\\\Booking\\\\Enums\\\\BookingType\\:\\:getLabel\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: app/Models/Reservation.php

		-
			message: "#^Method App\\\\Models\\\\Rib\\:\\:associable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Rib.php

		-
			message: "#^Method App\\\\Models\\\\Rib\\:\\:updateFilenameWithoutSaving\\(\\) has parameter \\$newFileName with no type specified\\.$#"
			count: 1
			path: app/Models/Rib.php

		-
			message: "#^Method App\\\\Models\\\\Role\\:\\:attachedTeams\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Role.php

		-
			message: "#^Method App\\\\Models\\\\Role\\:\\:users\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Role.php

		-
			message: "#^Method App\\\\Models\\\\Tag\\:\\:workshops\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Tag.php

		-
			message: "#^Cannot access property \\$users on App\\\\Models\\\\Team\\|null\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:availableRoles\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:getAdminsListToDisplay\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:getMembersAsArray\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:getMembersAsArray\\(\\) has parameter \\$team with no type specified\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:getMembersAsArray\\(\\) has parameter \\$withRole with no type specified\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:roles\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:users\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\TransactionLePotCommun\\:\\:getTypeTransaction\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/TransactionLePotCommun.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\TransactionLePotCommun\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/TransactionLePotCommun.php

		-
			message: "#^PHPDoc type array\\<string\\> of property App\\\\Models\\\\TransactionLeetchi\\:\\:\\$guarded is not the same as PHPDoc type array\\<string\\>\\|bool of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$guarded\\.$#"
			count: 1
			path: app/Models/TransactionLeetchi.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\TransactionLeetchi\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/TransactionLeetchi.php

		-
			message: "#^Call to method App\\\\Models\\\\User\\:\\:codeparrainageinvitation\\(\\) with incorrect case\\: CodeParrainageInvitation$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Cannot call method increaseAmount\\(\\) on App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\|null\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Cannot call method increaseUses\\(\\) on App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:client\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:codeparrainageinvitation\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation, \\$this\\(App\\\\Models\\\\User\\)\\>\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:codeparrainageuser\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphOne\\<App\\\\Models\\\\Parrainage\\\\CodeParrainageUser, \\$this\\(App\\\\Models\\\\User\\)\\>\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:favorites\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:getDenominationShort\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:getNumberOfParticipations\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:getOrdersCount\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:getOrdersCount\\(\\) has parameter \\$withPayment with no type specified\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:giftCards\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:giftCardsAssociated\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:giftCardsValuable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:panier\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:reservations\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:sendSponsoredConfirmation\\(\\) has parameter \\$sponsor with no type specified\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:stats\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Casts\\\\Attribute does not specify its types\\: TGet, TSet$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:toApiResourceArray\\(\\) should return array but returns array\\|null\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Mail\\\\BaseMailable\\|null given\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Artisan\\|App\\\\Models\\\\User\\|null given\\.$#"
			count: 2
			path: app/Models/User.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageInvitation\\|null given\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Only booleans are allowed in an if condition, App\\\\Models\\\\Parrainage\\\\CodeParrainageUser\\|null given\\.$#"
			count: 4
			path: app/Models/User.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\UserArtisan\\:\\:artisans\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^Method App\\\\Models\\\\UserArtisan\\:\\:getAteliers\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^Method App\\\\Models\\\\UserArtisan\\:\\:getCraftsmenWithoutCompletedProfil\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^Method App\\\\Models\\\\UserArtisan\\:\\:getEvents\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Builder does not specify its types\\: TModel$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^Method App\\\\Models\\\\UserArtisan\\:\\:getFutureEventsWithParticipants\\(\\) return type with generic class Illuminate\\\\Support\\\\Collection does not specify its types\\: TKey, TValue$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^Parameter \\#1 \\$value of method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:limit\\(\\) expects int, int\\|null given\\.$#"
			count: 1
			path: app/Models/UserArtisan.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\UserArtisanArtisan\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/UserArtisanArtisan.php

		-
			message: "#^PHPDoc type string of property App\\\\Models\\\\UserSocial\\:\\:\\$table is not the same as PHPDoc type string\\|null of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$table\\.$#"
			count: 1
			path: app/Models/UserSocial.php

		-
			message: "#^Method App\\\\Models\\\\Ville\\:\\:artisans\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Ville.php

		-
			message: "#^Method App\\\\Models\\\\Ville\\:\\:getActive\\(\\) has no return type specified\\.$#"
			count: 1
			path: app/Models/Ville.php

		-
			message: "#^Method App\\\\Models\\\\Ville\\:\\:responsable\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo does not specify its types\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Ville.php

		-
			message: "#^Method App\\\\Models\\\\Ville\\:\\:workshops\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough does not specify its types\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Ville.php

		-
			message: "#^Doing instanceof PHPStan\\\\Type\\\\IntersectionType is error\\-prone and deprecated\\.$#"
			count: 3
			path: app/PHPStan/Rules/ShouldQueueDependencyRule.php

		-
			message: "#^Doing instanceof PHPStan\\\\Type\\\\ObjectType is error\\-prone and deprecated\\. Use Type\\:\\:isObject\\(\\) or Type\\:\\:getObjectClassNames\\(\\) instead\\.$#"
			count: 4
			path: app/PHPStan/Rules/ShouldQueueDependencyRule.php

		-
			message: "#^Method App\\\\Passport\\\\Models\\\\Client\\:\\:can\\(\\) has parameter \\$requiredAcls with no type specified\\.$#"
			count: 1
			path: app/Passport/Models/Client.php

		-
			message: "#^PHPDoc type array\\<string, string\\> of property App\\\\Passport\\\\Models\\\\Client\\:\\:\\$attributes is not the same as PHPDoc type array of overridden property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$attributes\\.$#"
			count: 1
			path: app/Passport/Models/Client.php

		-
			message: "#^PHPDoc type array\\<string, string\\> of property App\\\\Passport\\\\Models\\\\Client\\:\\:\\$casts is not the same as PHPDoc type array of overridden property Laravel\\\\Passport\\\\Client\\:\\:\\$casts\\.$#"
			count: 1
			path: app/Passport/Models/Client.php

		-
			message: "#^Method App\\\\Payment\\\\Adapters\\\\StripePaymentAdapter\\:\\:getBankTransferInstructionsFromPaymentIntent\\(\\) should return array\\{reference\\: string, instructions_url\\: string, account_holder_name\\: string, bic\\: string\\|null, iban\\: string\\|null, account_number\\: string\\|null, sort_code\\: string\\|null\\} but returns array\\{reference\\: mixed, instructions_url\\: mixed, account_holder_name\\: mixed, bic\\: mixed, iban\\: mixed, account_number\\: mixed, sort_code\\: mixed\\}\\|array\\{reference\\: mixed, instructions_url\\: mixed, account_holder_name\\: mixed, bic\\: mixed, iban\\: mixed, account_number\\: null, sort_code\\: null\\}\\|array\\{reference\\: mixed, instructions_url\\: mixed, account_holder_name\\: mixed, bic\\: null, iban\\: null, account_number\\: mixed, sort_code\\: mixed\\}\\|array\\{reference\\: mixed, instructions_url\\: mixed, account_holder_name\\: null, bic\\: null, iban\\: null, account_number\\: null, sort_code\\: null\\}\\.$#"
			count: 1
			path: app/Payment/Adapters/StripePaymentAdapter.php

		-
			message: """
				#^Class App\\\\Rules\\\\ActivityCheck implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/ActivityCheck.php

		-
			message: "#^Only booleans are allowed in an elseif condition, App\\\\Models\\\\Atelier\\|null given\\.$#"
			count: 1
			path: app/Rules/ActivityCheck.php

		-
			message: "#^Property App\\\\Rules\\\\ActivityCheck\\:\\:\\$missings has no type specified\\.$#"
			count: 1
			path: app/Rules/ActivityCheck.php

		-
			message: "#^Property App\\\\Rules\\\\ActivityCheck\\:\\:\\$unique has no type specified\\.$#"
			count: 1
			path: app/Rules/ActivityCheck.php

		-
			message: """
				#^Class App\\\\Rules\\\\AdressCheck implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/AdressCheck.php

		-
			message: """
				#^Class App\\\\Rules\\\\DateFormatOrCarbon implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/DateFormatOrCarbon.php

		-
			message: "#^Only booleans are allowed in &&, DateTime\\|false given on the left side\\.$#"
			count: 1
			path: app/Rules/DateFormatOrCarbon.php

		-
			message: "#^Parameter \\#2 \\$datetime of static method DateTime\\:\\:createFromFormat\\(\\) expects string, float\\|int\\|string given\\.$#"
			count: 1
			path: app/Rules/DateFormatOrCarbon.php

		-
			message: """
				#^Class App\\\\Rules\\\\DateRangeSize implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\InvokableRule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/DateRangeSize.php

		-
			message: """
				#^Class App\\\\Rules\\\\HasEnoughSlots implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/HasEnoughSlots.php

		-
			message: """
				#^Class App\\\\Rules\\\\Location\\\\LocationInitiation implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/Location/LocationInitiation.php

		-
			message: "#^Only booleans are allowed in a negated boolean, int\\<0, max\\> given\\.$#"
			count: 1
			path: app/Rules/Location/LocationInitiation.php

		-
			message: """
				#^Class App\\\\Rules\\\\MaxMontantRembourse implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/MaxMontantRembourse.php

		-
			message: """
				#^Class App\\\\Rules\\\\NbFilesLimit implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/NbFilesLimit.php

		-
			message: "#^Method App\\\\Rules\\\\NbFilesLimit\\:\\:__construct\\(\\) has parameter \\$nb_files_max with no type specified\\.$#"
			count: 1
			path: app/Rules/NbFilesLimit.php

		-
			message: """
				#^Class App\\\\Rules\\\\Onboarding\\\\ArtisanCustomCodeParrainage implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/Onboarding/ArtisanCustomCodeParrainage.php

		-
			message: "#^Only booleans are allowed in an if condition, int given\\.$#"
			count: 1
			path: app/Rules/Onboarding/ArtisanCustomCodeParrainage.php

		-
			message: "#^Property App\\\\Rules\\\\Onboarding\\\\ArtisanCustomCodeParrainage\\:\\:\\$errors has no type specified\\.$#"
			count: 1
			path: app/Rules/Onboarding/ArtisanCustomCodeParrainage.php

		-
			message: "#^Return type \\(bool\\|Illuminate\\\\Http\\\\JsonResponse\\) of method App\\\\Rules\\\\Onboarding\\\\ArtisanCustomCodeParrainage\\:\\:passes\\(\\) should be covariant with return type \\(bool\\) of method Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:\\:passes\\(\\)$#"
			count: 1
			path: app/Rules/Onboarding/ArtisanCustomCodeParrainage.php

		-
			message: """
				#^Class App\\\\Rules\\\\Onboarding\\\\AtelierOnboardingUpdate implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/Onboarding/AtelierOnboardingUpdate.php

		-
			message: "#^Method App\\\\Rules\\\\Onboarding\\\\AtelierOnboardingUpdate\\:\\:__construct\\(\\) has parameter \\$isValidation with no type specified\\.$#"
			count: 1
			path: app/Rules/Onboarding/AtelierOnboardingUpdate.php

		-
			message: """
				#^Class App\\\\Rules\\\\UniqueMultiple implements deprecated interface Illuminate\\\\Contracts\\\\Validation\\\\Rule\\:
				see ValidationRule$#
			"""
			count: 1
			path: app/Rules/UniqueMultiple.php

		-
			message: "#^Parameter \\#1 \\$string of function mb_trim expects string, bool\\|string given\\.$#"
			count: 9
			path: config/app.php

		-
			message: "#^Parameter \\#1 \\$string of function mb_trim expects string, bool\\|string\\|null given\\.$#"
			count: 1
			path: config/app.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, bool\\|string given\\.$#"
			count: 1
			path: config/app.php

		-
			message: "#^Parameter \\#1 \\$title of static method Illuminate\\\\Support\\\\Str\\:\\:slug\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: config/cache.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, bool\\|string given\\.$#"
			count: 1
			path: config/cors.php

		-
			message: "#^Parameter \\#1 \\$title of static method Illuminate\\\\Support\\\\Str\\:\\:slug\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: config/database.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, string\\|true given\\.$#"
			count: 1
			path: config/database.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, bool\\|string given\\.$#"
			count: 1
			path: config/filesystems.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, bool\\|string given\\.$#"
			count: 1
			path: config/partners-api.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, bool\\|string given\\.$#"
			count: 2
			path: config/passport.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, bool\\|string given\\.$#"
			count: 1
			path: config/services.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\User\\>\\:\\:adresses\\(\\)\\.$#"
			count: 1
			path: database/factories/CartFactory.php

		-
			message: "#^Parameter \\#1 \\$state of method Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory\\<App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\>\\:\\:state\\(\\) expects array\\<string, mixed\\>\\|\\(callable\\(array\\<string, mixed\\>, App\\\\Domain\\\\ECommerce\\\\Models\\\\CartItem\\|null\\)\\: array\\<string, mixed\\>\\), Closure\\(array, Illuminate\\\\Database\\\\Eloquent\\\\Model\\)\\: array\\{cart_id\\: mixed, address_id\\: int\\|null, sub_item_of\\: mixed, related_to\\: null, related_id\\: null, base_price\\: 5, amount\\: 5, quantity\\: 1, discounted_amount\\: 5, type\\: 'service', product_id\\: 4\\} given\\.$#"
			count: 1
			path: database/factories/CartItemFactory.php

		-
			message: "#^Cannot access property \\$id on App\\\\Models\\\\Entreprise\\|null\\.$#"
			count: 1
			path: database/factories/InvoiceFactory.php

		-
			message: "#^Cannot call method getMorphClass\\(\\) on App\\\\Models\\\\Entreprise\\|null\\.$#"
			count: 1
			path: database/factories/InvoiceFactory.php

		-
			message: "#^Parameter \\#1 \\$value of static method App\\\\Shared\\\\Amount\\:\\:fromFloat\\(\\) expects float, float\\|null given\\.$#"
			count: 1
			path: database/factories/OrderDiscountFactory.php

		-
			message: "#^Only numeric types are allowed in /, float\\|null given on the left side\\.$#"
			count: 1
			path: database/factories/OrderItemFactory.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo\\<Illuminate\\\\Database\\\\Eloquent\\\\Model,App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItem\\>\\:\\:associate\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model\\|null, App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemTicket\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Domain\\\\Accounting\\\\Orders\\\\Models\\\\OrderItemTicket\\> given\\.$#"
			count: 1
			path: database/factories/OrderItemFactory.php

		-
			message: "#^Parameter \\#1 \\$value of static method App\\\\Shared\\\\Amount\\:\\:fromFloat\\(\\) expects float, float\\|null given\\.$#"
			count: 1
			path: database/factories/OrderItemFactory.php

		-
			message: "#^Class Database\\\\Factories\\\\QuoteFactory extends generic class Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory but does not specify its types\\: TModel$#"
			count: 1
			path: database/factories/QuoteFactory.php

		-
			message: "#^Class Database\\\\Factories\\\\QuoteLineFactory extends generic class Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory but does not specify its types\\: TModel$#"
			count: 1
			path: database/factories/QuoteLineFactory.php

		-
			message: "#^Class Database\\\\Factories\\\\QuoteSlotFactory extends generic class Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory but does not specify its types\\: TModel$#"
			count: 1
			path: database/factories/QuoteSlotFactory.php

		-
			message: "#^Cannot access property \\$ville on App\\\\Models\\\\Lieu\\|null\\.$#"
			count: 1
			path: database/factories/WorkshopFactory.php

		-
			message: "#^Only booleans are allowed in a ternary operator condition, string\\|null given\\.$#"
			count: 1
			path: database/factories/WorkshopUpdateRequestFactory.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\Artisan\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Artisan\\>\\:\\:\\$id\\.$#"
			count: 1
			path: database/seeders/ContentSeeder.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\UserArtisan\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\UserArtisan\\>\\:\\:artisans\\(\\)\\.$#"
			count: 3
			path: database/seeders/ContentSeeder.php
