---
cta_book_my_place: 'Book my workshop'
cta_see_dates: 'See all availabilities'
date_proposition_accepted:
  artisan_message: 'Here is the message left by the artisan'
  new_slot: 'Following your suggestion, the artisan :craftsman has put a new slot online.|Following your suggestion, the artisan :craftsman has put new slots online.'
  new_slot_accepted: 'You can book your workshop by clicking on the slot and following the validation steps: |You can book your workshop by clicking on the slot that suits you and following the validation steps:'
  places_available: "Places are available online on the website, so don't delay - book them now!"
  title: 'Your proposed workshop date has been accepted!'
date_propositions_refused_or_expired:
  artisan_message: 'Here is the message left by the artisan'
  cta_change_workshop: 'Change my workshop'
  gift_owner: 'Do you have a gift voucher? 🎁 You can change workshop at any time with one click from your customer area.'
  next_date_newletter: 'In order to keep you informed of the next availability of this workshop, you will receive an email informing you of the new slots added.'
  see_slots: 'You can check availability at any time on our website.'
  see_slots_cta: 'See availability'
  title: 'Follow-up on your date request'
  unavailability_intro: 'We regret to inform you that the artisan has not accepted your suggested slots for the workshop. 😞 There could be several reasons for this:'
  workshop_exchange_process: 'Please refer to this article for more information:'
new_events_notification:
  already_booked: '✌ Have you already reserved your seat? You can unsubscribe from these reminders: '
  available_slots: '{1} seat available|[2,*] seats available'
  awaiting_new_availability: '{1} Good news, a new slot has been added for the "<b><a href=":url">:workshop</b></a>" workshop with <b><a href=":url_craftsman">:craftsman</b></a>!|[2,*]Good news, new slots have been added for the "<b><a href=":url">:workshop</b></a>" workshop with <b><a href=":url_craftsman">:craftsman</b></a>!'
  awaiting_replacement_workshop: 'You have a pending booking for the "<b><a href=":url">:workshop</a></b>" workshop with <b><a href=":url_craftsman">:craftsman</b></a>!'
  cta_here: 'HERE '
  cta_self_replacement: 'Manage my workshop'
  find_more: 'Find even more new slots on the "my workshops" page in your customer area!'
  good_news: '{1} Good news, a new slot has been added!|[2,*] Good news, new slots have been added!'
  like_bread: "💡 Don't wait too long, other people might be interested too!"
  more_availabilities: 'Click on the button below for even more slots!'
  next_dates: '{1} Here is the new slot :|[2,5] Here are the :count new slots :|[6,*] Here are the first 5 new slots :'
  reregistration: 'Would you still like to receive these alerts? If so: click <b><a href=":url">:here</a></b><br>If not, you will be automatically unsubscribed from this alert.'
  self_replacement: 'Go to "my workshops" in your customer area to choose a date for your workshop.'
  title: 'New dates for the workshop :workshop'
new_seat_available:
  cta: 'Reserve my place'
  good_news: '{1} Good news, :seats seat have become available for the workshop "<b><a href=":url">:workshop</a></b>" with <b><a href=":url_craftsman">:craftsman</b></a> from :date!|[2,*] Good news, :seats seats have become available for the workshop "<b><a href=":url">:workshop</a></b>" with <b><a href=":url_craftsman">:craftsman</b></a> from :date!'
  title: 'Availability for the workshop :workshop'
