---
information:
  header: Information
  info_1: '❌ Workshop is cancelled'
  info_2: '📍 Workshop venue is specific to this event'
  info_3: '🔑 Workshop is privatised'
  title: '<b>Legend</b>'
  workshop_edit_description: 'Please note that you can only move or delete a workshop if there are no active reservations for that time slot.'
  workshop_edit_title: '<b>Move/delete a workshop</b>.'
  workshop_superposition_description: 'You can overlap time slots for workshops. However, once a workshop is booked, it takes priority over others that are no longer available for booking.'
  workshop_superposition_title: '<b>Overlapping workshops</b>'
layout:
  add_event_btn_info: 'Add slots or periods of unavailability.'
  add_event_btn_label: Add
  add_event_info: 'You can also drag a workshop from the calendar and drop it into a desired time slot.'
  add_events: 'Add slots'
  artisan_select: 'Choose an artisan'
  artisan_select_title: "Artisan's Choice"
  select_artisan_before: 'Choose an artisan above.'
  workshop_move_error: 'The event has not been moved. Contact <NAME_EMAIL>.'
  workshop_move_forbidden: 'You cannot move this event. Contact <NAME_EMAIL>.'
  workshop_move_success: 'The event has been successfully moved.'
  workshop_move_validate: 'This event contains participants. Are you sure you want to move it?'
popover:
  cant_cancel:
    cancelled: 'The event cannot be cancelled because it has already been cancelled'
  cant_move:
    already_moved_by_artisan: 'The event has already been moved: no move action is possible. Please contact us via the online chat <NAME_EMAIL>.'
    cancelled: 'The event cannot be moved as it is cancelled'
    in_less_than_x_days: 'The event takes place in 7 days or less and therefore cannot be rescheduled. Contact <NAME_EMAIL> or via the online chat (button at the bottom right of your screen).'
    moved_and_in_less_than_x_days: 'The event has already been moved and is taking place in less than 7 days: no move action is possible. Please contact us via the online chat <NAME_EMAIL>.'
    ongoing_bookings: 'A reservation is being made for this event. Please come back later.'
  confirm_remove_recurring: 'Are you sure you want to remove recurring slots?'
  cta_cancel_event: 'Cancel event|Cancel events'
  cta_cancel_event_tooltip: '{1}The event takes place in 7 days or less and is therefore not cancelable. Contact us on the instant chatbox at the bottom right <NAME_EMAIL>|[2,*]Events take place in 7 days or less and are therefore not cancelable. Contact us on the instant chatbox at the bottom right <NAME_EMAIL>'
  cta_move_event: 'Move event|Move events'
  cta_remove: Delete
  cta_remove_recurring: 'Delete associated recurring slots'
  cta_remove_unavailability: 'Remove unavailability period'
  cta_show_event: 'See the event'
  event: 'Event #'
  event_place: ' - Specific location:'
  manage_event: 'Manage the event'
  remove_error_message: 'Deletion of the event is prohibited:'
  remove_success_message: 'The event has been deleted.'
  remove_unavailability_error_message: 'Impossible to delete'
  remove_unavailability_success_message: 'The period of unavailability has been removed.'
  unavailability:
    select_workshops: 'Select the periods you wish to delete:'
