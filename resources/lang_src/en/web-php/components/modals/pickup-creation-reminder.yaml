---
bookings_count: ':count booking|:count bookings'
bookings_selected_count: ':count selected booking|:count selected bookings'
btn:
  back: Return
  confirm: Confirm
  next: 'Complete the instructions'
explanation: 'All selected participants will receive the same alert by email and text message. If the instructions or deadlines are different, please deselect them and schedule another reminder.'
fields:
  due_date:
    label: 'What is the deadline after which creations cannot be picked up?'
  pickup_info:
    label: 'Do you have any specific recommendations for recovering creations?'
    placeholder: 'Indicate whether the participant need to bring a bag, a cool box... or any other useful information.'
  pickup_reception:
    label: 'When can participants come and pick up their creations?'
    placeholder: 'Indicate the days and times when it is possible for participants to pick up their creation.'
fill_fields: 'Fill in these inserts to send the recovery procedures to the participants:'
max_reminder_reach: 'No more reminder possible'
max_reminder_reach_warning: 'For selected events, :count booking has already been notified :maxReminder and cannot be reminded again. For selected events, :count bookings have already been notified :maxReminder and cannot be reminded again.'
messages:
  error:
    booking_cant_be_reminded: 'Impossible to create a reminder for one of the selected bookings'
    booking_required: 'You must select at least one booking.'
    booking_with_max_reminder_reach: 'You have selected a booking that has already been notified :maxReminder times, and cannot receive a new reminder.'
    pickup_due_date:
      after: 'The deadline must be in the future'
      required: 'You must fill in a deadline'
    pickup_reception: 'You must provide information about your availability'
  success:
    reminded: 'The emails and text messages have been sent to the participants.'
more_info: 'More information <a href=":url" target="_blank">here</a>'
notification_will_be_sent: 'An email and a text message will be sent to the previously selected participant so that they can pick up their creation at the address where they completed their workshop. An email and a text message will be sent to the previously selected <b>:count participants </b> so that they can pick up their creations at the address where they completed their workshop.'
reminder_count: ':count reminder|:count reminders'
seats_count: ':count place|:count places'
spoken_languages: 'The selected participants do not all speak the same language (:languages): we recommend that you use the languages used during the workshop.'
title: 'Managing reminders sent to participants'
