---
an_error_occured: "Une erreur s'est produite."
booking_service:
  booking:
    creation: 'Impossible de créer la réservation : :reason'
    not_found: 'La réservation est introuvable'
    update_event:
      different_price: "L'évènement sélectionné est d'un prix différent de l'évènement d'origine."
      event_already_billed: "L'évènement sélectionné a déjà été facturé"
      full: "L'évènement sélectionné est déjà complet"
      nd_origin: 'La réservation a pour origine Nature & Découvertes'
      passed: "L'évènement sélectionné a déjà eu lieu. Veuillez choisir un événement dans le futur."
      private: "L'évènement sélectionné est privé"
    update_status:
      disabled_event: "L'évènement est annulé. Vous devez le réactiver avant d'effectuer cette opération"
      not_enough_places_on_event: "Le changement de statut n'a pas fonctionné, le nombre de places disponibles sur l'atelier est insuffisant"
  event:
    not_found: "L'évènement est introuvable"
  reason_unavailable_stackable_event: 'Ce créneau n''est plus réservable. Vous pouvez consulter les autres disponibilités ici : <a href=":url">:workshop</a>'
  reason_unknown: 'Erreur inconnu'
  reason_wrong_capacity: "Il ne reste plus de place dans l'évènement"
class:
  not_found: 'La ressource demandée est introuvable.'
domain:
  box:
    bad_formatted_file: 'Contenu mal formaté'
  gift:
    already_associated: 'Ce Code est déjà présent dans vos Cadeaux'
    cant_be_transformed: 'La carte ne peut pas être transformée.'
event_service:
  stacked_same_workshop: "Impossible d'ajouter l'événement du <b>:date</b> car vous avez déjà un évènement identique dans votre calendrier pour l'atelier <b>:workshop</b>."
  stacked_same_workshop_list: "Les événements suivants n'ont pas pu être ajoutés car vous avez déjà des événements similaires planifiés :"
file:
  store: "L'enregistrement du fichier [:filename] a échoué."
models:
  not_found: ':0 est introuvable'
unknown: 'Une erreur est survenue, veuillez réessayer.'
user_service:
  not_deletable:
    has_future_confirmed_bookings: ':count réservation confirmée|:count réservations confirmées'
    has_standby_bookings: ':count réservation en attente de replacement|:count réservations en attente de replacement'
    title: 'Le compte ne peut pas être supprimé car il existe encore :'
