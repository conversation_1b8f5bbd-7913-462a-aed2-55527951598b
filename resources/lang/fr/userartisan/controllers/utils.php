<?php

return [
  'billing-informations' => [
    'created' => 'Vos informations de facturation ont bien été créées',
    'updated' => 'Vos informations de facturation ont bien été mises à jour',
  ],
  'booking' => [
    'artisan' => [
      'created' => 'Le créneau bloqué a bien été créé.',
    ],
    'cancelled' => 'La réservation a bien été annulée',
    'present' => [
      'id' => 'Réservation #:id',
    ],
  ],
  'event' => [
    'cancelled' => 'L\'évènement a bien été annulé',
    'cant-be-cancelled' => 'L\'évènement ne peut pas être annulé',
    'cant-be-deleted' => 'L\'évènement ne peut être supprimé',
    'created' => 'L\'évènement a été créé avec succès.|Les événements ont bien été créés',
    'deleted' => 'L\'évènement a bien été supprimé',
    'title' => [
      'updated' => 'Le titre de l\'événement a été changé avec succès.',
    ],
    'updated' => 'Evenement :id mis à jour.',
  ],
  'invoice' => [
    'checking' => 'Facture en cours de vérification',
  ],
  'privatization-request' => [
    'accepted' => 'Demande de privatisation acceptée et mise à jour',
    'refused' => 'Demande de privatisation refusée et mise à jour',
  ],
  'profile' => [
    'updated' => 'Votre profil a été mis à jour.',
  ],
  'rib' => [
    'created' => 'Votre RIB a bien été créé',
    'deleted' => 'Fichier supprimé',
    'updated' => 'Le RIB a bien été mis à jour',
  ],
  'update' => [
    'default' => 'Les informations ont été mises à jour avec succès',
  ],
  'workshop' => [
    'label' => 'Atelier : :workshopName',
  ],
];
