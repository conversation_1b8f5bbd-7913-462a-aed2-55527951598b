<?php

return [
  'booking' => [
    'calendar_event_status' => [
      'cancelled' => 'Annulé',
      'confirmed' => 'Confirmé',
      'pending' => 'En cours',
      'to_be_confirmed' => 'À confirmer',
    ],
    'calendar_multiple_events_type' => [
      'cumulative' => 'Cumulables',
      'stacked' => 'Superposables',
    ],
    'calendar_view_mode' => [
      'day' => 'Jour',
      'list' => 'Liste',
      'month' => 'Mois',
      'week' => 'Semaine',
    ],
    'event-maintain-status' => [
      'confirmation_not_required' => 'Confirmation non requise',
      'confirmed' => 'Confirmé',
      'refused' => 'Refusé',
      'to_be_confirmed' => 'À confirmer',
    ],
    'event-origins' => [
      'artisan' => 'Artisan',
      'artisan_move_event' => 'Artisan move event',
      'automatic_booking_transformation_command' => 'Automatic mass transform - GiftCardMassTransformStandbyBooking command',
      'automatic_clean_CancelRefusedEvents_job' => 'Automatic clean - CancelRefusedEvents job',
      'customer' => 'Customer',
      'cut_not_reach' => 'Cut not reached',
      'deactivated_workshop' => 'Deactivated workshop',
      'wecandoo' => 'Wecandoo',
    ],
    'event_cancellation_reason' => [
      'logistic' => 'Logistique',
      'not_enough_participants' => 'Nombre de participants insuffisant',
      'other' => 'Autre',
      'personal_constraint' => 'Contrainte personnelle',
      'sickness' => 'Maladie',
    ],
    'origins' => [
      'artisan' => 'Artisan',
      'customer' => 'Client',
      'nature_et_decouvertes' => 'Nature & Découvertes',
      'partner' => 'Partenaire',
      'wecandoo' => 'Wecandoo',
      'widget' => 'Widget',
    ],
    'origins_alternate' => [
      'direct' => 'En direct',
      'other' => 'Autre',
      'website' => 'Site Wecandoo',
      'widget' => 'Widget',
    ],
    'status' => [
      'awaiting_payment' => 'En attente de paiement',
      'billed' => 'Facturée',
      'cancelled' => 'Annulée',
      'confirmed' => 'Confirmée',
      'pending' => 'En cours',
      'standby' => 'À replacer',
    ],
    'types' => [
      'customer' => 'Demande client',
      'direct_booking' => 'Réservation en direct',
      'influencer' => 'Influenceur/Influenceuse',
      'invitation' => 'Invitation',
      'journalist' => 'Journaliste',
      'privatization' => 'Privatisation',
      'website' => 'Site Web',
      'widget' => 'Widget',
    ],
  ],
  'box' => [
    'slugs' => [
      'cosmetique' => 'Cosmétique',
      'exception' => 'Exception',
      'gourmand' => 'Gourmand',
      'poterie' => 'Poterie',
      'vegetal' => 'Végétal',
    ],
    'usage_status' => [
      'active' => 'Active',
      'claimed' => 'Réclamée',
      'inactive' => 'Inactive',
      'lost' => 'Perdue',
      'used' => 'Utilisée',
    ],
  ],
  'cart' => [
    'origin' => [
      'website' => 'Site web',
      'widget' => 'Widget',
    ],
    'status' => [
      'abandoned' => 'Abandonné',
      'new' => 'Nouveau',
      'trashed' => 'Supprimé',
    ],
  ],
  'category' => [
    'type' => [
      'drink' => 'Boire',
      'eat' => 'Manger',
      'material' => 'Matériel',
      'metier' => 'Métier',
      'object' => 'Objet',
      'technique' => 'Technique',
    ],
  ],
  'company' => [
    'merchant_type' => [
      'buyer' => 'Acheteur',
      'seller' => 'Vendeur',
    ],
  ],
  'contact_prochaines_dates' => [
    'status' => [
      'abandoned' => 'Abandonnée',
      'expired' => 'Expirée',
      'processed' => 'Validée',
      'to-process' => 'En attente',
    ],
  ],
  'content' => [
    'media' => [
      'sources' => [
        'artisan' => 'Artisan',
        'freelance' => 'Freelance',
        'other' => 'Autre',
        'service_provider' => 'Prestataire',
      ],
      'status' => [
        'available' => 'Disponible',
        'quality_improvement' => 'A/R Qualité',
        'requested' => 'Commandé',
        'to_request' => 'À commander',
        'to_review' => 'À revoir',
        'uploaded' => 'Uploadé BO',
      ],
      'types' => [
        'photo' => 'Photo',
        'video' => 'Vidéo',
      ],
    ],
    'page' => [
      'single' => [
        'sections' => [
          'types' => [
            'artisans' => 'Artisans',
            'cities' => 'Villes',
            'closest_experiences' => 'Experiences les plus proches',
            'collections' => 'Collections',
            'crafts' => 'Savoir-faire',
            'creations' => 'Créations',
            'news' => 'Nouveautés',
            'popular_experiences' => 'Experiences populaires',
            'search_preview' => 'Résultats de recherche',
            'techniques' => 'Techniques',
          ],
        ],
      ],
      'status' => [
        'in_progress' => 'En cours',
        'missing_information' => 'Infos manquantes',
        'standby' => 'Standby',
        'to_create' => 'A créer',
        'to_translate' => 'A traduire',
        'to_validate' => 'A valider',
        'validated' => 'Validée',
      ],
      'types' => [
        'artisan' => 'Artisan',
        'pillar' => 'Pillar',
        'search' => 'Recherche',
        'single' => 'Single',
        'workshop' => 'Atelier',
      ],
    ],
  ],
  'events' => [
    'type' => [
      'private' => 'privé',
      'private_client_managed_location' => 'privé chez le client',
      'public' => 'public',
    ],
  ],
  'gift' => [
    'card-status' => [
      'active' => 'Active',
      'cancelled' => 'Cancelled',
      'expired' => 'Expired',
      'refunded' => 'Refunded',
      'used' => 'Used',
    ],
    'card-type' => [
      'amount' => 'Montant',
      'ticket' => 'Place',
    ],
    'contact' => [
      'beneficiary' => 'Bénéficiaire',
      'buyer' => 'Acheteur',
      'giver' => 'Acheteur',
      'no-contact' => 'Aucun contact',
      'recipient' => 'Bénéficiaire',
    ],
    'creation-reason' => [
      'booking_transformation' => 'Transformation de réservation',
      'box_transformation' => 'Transformation Box',
      'customer_compensation' => 'Compensation client',
      'expiration' => 'Extension de carte cadeau périmée',
      'offline_purchase' => 'Achat hors ligne',
      'prepaid_batch' => 'Lot prépayé',
      'refund' => 'Remboursement',
      'reseller' => 'Revendeur (Nature & Découvertes, ...)',
      'reseller_deposit_sale_batch' => 'Dépot vente revendeur',
      'ticket_into_value_transformation' => 'Transformation ticket en value',
      'wallet_transformation' => 'Transformation de cagnotte',
      'website_purchase' => 'Achat en ligne',
      'wecandoo_gift' => 'Cadeau offert par Wecandoo (employés, partenaires, ...)',
    ],
    'theme' => [
      'flower' => 'Fleur',
      'gift' => 'Cadeau',
      'heart' => 'Coeur',
      'pottery' => 'Poterie',
      'tree' => 'Arbre',
    ],
  ],
  'invoice' => [
    'description' => [
      'facture_directe_artisan_wcd' => 'Facture directe de l\'artisan vers Wecandoo pour une carte cadeau ou un atelier offert ou un coffret généré par Wecandoo',
      'facture_directe_cartes_cadeau' => 'Facture directe des cartes cadeau et produits complémentaires de Wecandoo vers le client final',
      'facture_directe_invitation' => 'Facture directe de l\'artisan vers Wecandoo pour une invitation',
      'facture_directe_prestation' => 'Facture directe de la prestation de Wecandoo vers le client final',
      'facture_directe_prestation_achat_client_entreprise' => 'Facture directe vers un client sans informations connues (client entreprise partenaire ou autre)',
      'facture_directe_produits_comple' => 'Facture directe des produits complémentaires de Wecandoo vers le client final',
      'facture_mensuelle_commission' => 'Facture mensuelle de commission de Wecandoo vers l\'Artisan',
      'facture_prestation_achat_client_entreprise' => 'Facture artisan vers un client sans informations connues (client entreprise partenaire ou autre)',
      'facture_prestation_reservation' => 'Facture de la prestation de la réservation de l\'Artisan vers le client final',
    ],
    'status' => [
      'cancelled' => 'Annulée',
      'issued' => 'Facturée',
    ],
    'type' => [
      'facture_directe_artisan_wcd' => 'facture_directe_artisan_wcd',
      'facture_directe_invitation' => 'facture_directe_invitation',
      'facture_directe_prestation' => 'facture_directe_prestation',
      'facture_directe_prestation_achat_client_entreprise' => 'facture_directe_prestation_achat_client_entreprise',
      'facture_directe_produits_comple' => 'facture_directe_produits_comple',
      'facture_mensuelle_commission' => 'Commission',
      'facture_prestation_achat_client_entreprise' => 'Ventes Entreprise',
      'facture_prestation_reservation' => 'Ventes Particulier',
    ],
  ],
  'location' => [
    'type' => [
      'other' => 'Autre',
      'restaurant' => 'Restaurant',
      'user_custom_location' => 'Chez le client',
      'workshop' => 'Atelier',
      'workshop_with_pass' => 'Atelier avec pass',
    ],
  ],
  'moderation' => [
    'status' => [
      'approved' => 'Validé',
      'rejected' => 'Refusé',
      'to-review' => 'À valider',
    ],
  ],
  'onboarding' => [
    'status' => [
      'is_onboard' => 'A bord',
      'not_onboard' => 'Non onboardé(e)',
      'to_be_reviewed' => 'A revoir',
    ],
  ],
  'order' => [
    'origins' => [
      'nature_et_decouvertes' => 'Nature & Découvertes',
      'website' => 'Site-Web',
      'widget' => 'Widget',
    ],
    'status' => [
      'awaiting' => 'En attente de paiement',
      'cancelled' => 'Annulée',
      'delivered' => 'Livrée',
      'paid' => 'Payée',
      'pending' => 'En attente de validation du paiement',
      'refund' => 'Remboursée',
      'unpaid' => 'Non Payée',
    ],
  ],
  'payment' => [
    'methods' => [
      'leetchi' => 'Leetchi',
      'lpc' => 'Le pot Commun',
      'stripe' => 'Stripe',
    ],
  ],
  'physical_box' => [
    'lot' => [
      'type' => [
        'delivery' => 'Livraison',
        'production' => 'Production',
      ],
    ],
    'status' => [
      'claimed' => 'Réclamée',
      'expired' => 'Périmée',
      'in_delivering' => 'En cours de livraison',
      'in_shop' => 'En magasin',
      'in_stock' => 'En stock',
      'lost' => 'Perdue',
      'manufacturing' => 'En Production',
      'sold' => 'Vendue',
      'sold_forced' => 'Vente forcée',
      'used' => 'Utilisée',
    ],
  ],
  'private-booking-request' => [
    'refuse_reasons' => [
      'auto' => 'Refusé automatiquement',
      'availability' => 'Je ne suis pas disponible',
      'other' => 'Autres raisons',
      'participant' => 'Le nombre de participants ne convient pas',
      'price' => 'Le montant n\'est pas suffisamment élevé',
    ],
    'status' => [
      'accepted' => 'Acceptée',
      'cancelled' => 'Annulée',
      'expired' => 'Expirée',
      'new' => 'Créée',
      'other_proposal' => 'Contre proposition',
      'processed' => 'Confirmée',
      'quote_accepted' => 'Devis accepté',
      'refused' => 'Refusée',
    ],
    'status_label' => [
      'awaiting' => 'A traiter',
      'awaiting_payment' => 'En attente du paiement',
      'booking_confirmed' => 'Le client a payé sa réservation.',
      'cancelled' => 'Annulée',
      'expired' => 'Expirée',
      'no_payment' => 'Le client a validé le devis. Il doit encore payer sa réservation.',
      'quote_expired' => 'Devis expiré',
      'quote_need_confirmation' => 'Devis à confirmer',
      'refused' => 'Refusée',
    ],
  ],
  'privatization-request' => [
    'processus' => [
      'autonomous_process' => 'Processus autonome',
      'hubspot_process' => 'Processus Hubspot',
      'null' => 'Aucun',
    ],
    'state' => [
      'in_progress' => 'En cours',
      'lost' => 'Perdu',
      'null' => 'Aucun',
      'won' => 'Gagné',
    ],
    'status' => [
      'accepted' => 'Acceptée',
      'awaiting' => 'En attente',
      'awaiting_company' => 'En attente B2B',
      'cancelled' => 'Annulée',
      'outdated' => 'Expirée',
      'processed' => 'Traité',
      'refused' => 'Refusée',
    ],
  ],
  'processing' => [
    'status' => [
      'processed' => 'Traité',
      'to-process' => 'À traiter',
    ],
  ],
  'product' => [
    'status' => [
      'billed' => 'Facturé',
      'cancelled' => 'Annulée',
      'paid' => 'Payée',
      'pending' => 'En attente de validation du paiement',
      'refunded' => 'Remboursée',
    ],
  ],
  'propositions-dates' => [
    'status' => [
      'approved' => 'Validée',
      'expired' => 'Périmée',
      'pending' => 'En attente',
      'rejected' => 'Refusée',
    ],
  ],
  'unavailability' => [
    'origin' => [
      'admin' => 'Admin',
      'artisan' => 'Artisan',
    ],
  ],
];
