<?php

return [
  'booking_information' => [
    'ao_nb_slots_label' => 'Number of places:',
    'bc_value' => 'Gift card amount:',
    'beneficiary_email_label' => 'Recipient\'s email',
    'beneficiary_label' => 'To (feel free to use their nickname!):',
    'check_eligibility' => 'Check your eligibility on  ',
    'delivery_form_label' => 'Choose the format of your gift card:',
    'email_placeholder' => '<EMAIL>',
    'free_mail_delivery' => 'Included<br />Sent to your email box right after your purchase.',
    'from_date_to_date' => 'From :start to :end',
    'gift_date_label' => 'Date from which we can send an email to your loved one',
    'giver_label' => 'From:',
    'how_to_send' => 'How do you want to send it?',
    'min_char_placeholder' => ':number characters max.',
    'no_bc_sent' => '❗No gift voucher has been emailed to this address ❗',
    'numeric_card' => 'A digital card',
    'page_link' => '<b>this page</b>',
    'send_by_email_option' => [
      'automatically' => [
        'date_to_send_at' => 'Date we\'ll send the gift card',
        'date_to_send_at_extra' => '(sent by email between 10 and 11am on the chosen date)',
        'description' => 'In addition to sending you the PDF, we send
the gift card by email to your loved one 
on the date of your choice.',
        'title' => 'My loved one also receives it by email 🎁',
      ],
      'own_means' => [
        'date_to_send_at' => 'Date from which we can send an email to your loved one',
        'description' => 'The gift card will be sent to your e-mail directly after purchase.',
        'title' => 'I\'ll take care of it 😎',
      ],
    ],
    'send_email_to_beneficiary' => 'I want my loved one to receive the card by email on the chosen date.',
    'send_reminder_note' => 'Wecandoo does not send gift vouchers to the recipients',
    'step1' => 'Customize your gift card',
    'step2' => 'Shall we send reminders and workshop suggestions to the recipient of the gift card?',
    'step2-sub' => 'Share your friend\'s email address with us, and we\'ll send them a world of workshop ideas and suggestions once they receive the gift card.',
  ],
  'general_information' => [
    'choices' => 'A CHOICE OF<br> 2,500 WORKSHOPS ',
    'exchangeable' => 'CAN BE EXCHANGED<br> AS DESIRED',
    'validity' => 'GIFT VOUCHER <br> VALID FOR 12 MONTHS',
  ],
];
