<?php

return [
  'attributes' => [
    'adult-number' => 'number of adults',
    'child-number' => 'number of children',
    'participant-number' => 'Number of participants',
    'proposed-slots' => 'proposed date',
  ],
  'rules' => [
    'address1' => [
      'required' => 'Please provide the address where you want the artisan to host the workshop',
    ],
    'city' => [
      'required' => 'The city is required',
    ],
    'companyName' => [
      'required' => 'The company name is required',
    ],
    'country' => [
      'required' => 'The country is required',
    ],
    'email' => [
      'required' => 'The email is required',
    ],
    'firstName' => [
      'required' => 'The first name is required',
    ],
    'isOnTheGo' => [
      'cannot_be_at_artisan' => 'The workshop cannot be hosted at the artisan\'s location.',
      'cannot_be_at_client' => 'The workshop cannot be hosted at your location.',
    ],
    'lastName' => [
      'required' => 'The last name is required',
    ],
    'message' => [
      'required' => 'Please provide information regarding the location of the workshop',
    ],
    'participants-number-max' => 'This workshop can be booked for a maximum of :max people. Click here to see our workshops for groups of :input persons.',
    'participants-number' => [
      'max' => 'This workshop can be booked for a maximum of :max people. Click here to see our workshops for groups of :input people.',
    ],
    'phone' => [
      'required' => 'A phone number is required',
    ],
    'proposed-slots-date-after' => 'The date must be after :date',
    'proposed-slots-date-required' => 'The date is mandatory',
    'proposed-slots-end-after' => 'The end time must be greater than the start time plus the duration of the workshop.',
    'proposed-slots-start-before' => 'The start time must be less than the end time',
    'zipCode' => [
      'required' => 'The postal code is required',
    ],
    'zip_code' => [
      'required' => 'The postal code is required',
    ],
  ],
];
