<?php

return [
  'booking_with_bad_mood' => 'One or more people have had their previous reservation cancelled.',
  'confirm' => 'Confirm cancellation',
  'cumulative_title' => ':count cumulative workshops',
  'customer_notification' => 'An email and SMS will be sent to Wecandoo participants.',
  'customer_refund' => 'Customers can opt for a refund.',
  'multiple_title' => 'Event cancellations',
  'not_enough_participants_alert' => 'Give this event a chance: more participants might sign up.<br>If the minimum number of participants has not been reached 7 days before, we will ask you to confirm whether or not to keep this event (more information <a href=":url" target="_blank">here</a>).',
  'notification' => [
    'cant_open_modal' => 'An error has occurred: the modal cannot be opened.',
    'error' => 'Event could not be cancelled, <NAME_EMAIL>.|:count Events could not be cancelled, <NAME_EMAIL>.',
    'invalid_reason' => 'Please select a valid cancellation reason',
    'required_reason' => 'Please select a cancellation reason',
    'required_reason_other' => 'Please specify the reason',
    'success' => 'Event has been cancelled and bookings have been put on hold|Events have been cancelled and bookings have been put on hold',
  ],
  'reason' => 'Indicate the reason for your cancellation',
  'reason_other' => 'Specify the reason',
];
