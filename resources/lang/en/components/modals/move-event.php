<?php

return [
  'alert_move_days' => 'You have chosen to change the day of the workshop: all reservations will be put on hold. Customers will be notified and can choose to stay on this time slot, move to another time slot, or request a refund.',
  'choose_date' => 'Select the desired start date and time',
  'choose_date_tooltip' => 'To move the event within the next 7 days, contact us at <b><EMAIL></b> or via the online chat (button at the bottom right).',
  'confirm' => 'Confirm the move',
  'cumulative_title' => ':count cumulative workshops',
  'customer_notification' => 'An email and text message will be sent to Wecandoo participants to notify them of the modification.',
  'date_start_placeholder' => 'Your date',
  'notification' => [
    'error' => 'The event could not be moved|:count events could not be moved',
    'success' => 'The event has been moved|The events have been moved',
  ],
  'validation' => [
    'new_start_date_30_days' => 'The new start date must be within 30 days of the initial start date.',
    'new_start_date_7_days' => 'The entered date must be more than 7 days away.',
    'new_start_date_past' => 'The new date cannot be in the past.',
    'start_date_required' => 'Please select a new start date',
    'start_time_range' => 'The new start time must be between 5:00 and 21:00.',
    'start_time_required' => 'Please select a new start time',
  ],
];
