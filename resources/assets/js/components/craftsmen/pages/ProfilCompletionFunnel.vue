<template>
  <div
    v-if="!loading"
    class="full-width row"
  >
    <div
      class="col-xs-12 col-md-9 col-lg-8 no-padding"
      style="border-left: #003F46 8px solid"
    >
      <form-wizard
        ref="formWizard"
        :start-index="config.startIndex"
        step-size="sm"
        color="#003F46"
        error-color="#FF2D00"
        style="height: 100vh !important;padding-bottom:0px !important;background-color:white !important"
      >
        <div slot="title" />
        <button
          class="wcd-button-no-style hidden-xs is-onboarding-green mt-4"
          @click="goToArtisanSpace"
        >
          <i
            class="fas fa-arrow-left mr-2"
          /><b> {{ $t('espace-artisan.profil-completion-funnel.button-back-profile-label') }}</b>
        </button>
        <button
          class="wcd-button-no-style visible-xs is-onboarding-green mt-4"
          @click="goToArtisanSpace"
        >
          <i
            class="fas fa-arrow-left mr-2"
          /><b> {{ $t('espace-artisan.profil-completion-funnel.button-back-label') }}</b>
        </button>
        <tab-content
          v-for="(step, index) in config.steps"
          :key="'step-'+index"
          style="overflow-y:auto;height:75vh;"
          :before-change="() => validateTab({index: index, step: step})"
        >
          <component
            :is="step.component"
            :key="componentKey + 0"
            :ref="step.ref"
            :data="step"
          />
        </tab-content>
        <template
          slot="footer"
          slot-scope="props"
        >
          <div
            class="row w-100 hidden-xs hidden-sm"
            style="margin-top:auto;margin-bottom:auto;"
          >
            <div class="wizard-footer-left col-6">
              <wizard-button
                v-if="props.activeTabIndex > 0"
                :disabled="buttonState"
                :style="props.fillButtonStyle"
                class="wizard-btn prev-button-onboarding-funnel"
                @click.native="props.prevTab();refreshImage()"
              >
                {{ $t('espace-artisan.onboarding-funnel.wizard-button-precedent-label') }}
              </wizard-button>
            </div>
            <div
              class="wizard-footer-right col-6"
              style="text-align: right;"
            >
              <wizard-button
                v-show="!props.isLastStep"
                slot="custom-buttons-right"
                :disabled="buttonState"
                class="wizard-btn prev-button-onboarding-funnel mr-2"
                @click.native="ignoreStep"
              >
                {{ $t('espace-artisan.profil-completion-funnel.wizard-button-ignore-label') }}
              </wizard-button>
              <wizard-button
                v-if="!props.isLastStep"
                :disabled="buttonState"
                :style="props.fillButtonStyle"
                class="wizard-footer-right wizard-btn next-button-onboarding-funnel"
                @click.native="props.nextTab()"
              >
                {{ $t('espace-artisan.profil-completion-funnel.wizard-button-next-label') }}
              </wizard-button>
              <wizard-button
                v-else
                :style="props.fillButtonStyle"
                :disabled="buttonState"
                class="wizard-footer-right finish-button wizard-btn next-button-onboarding-funnel"
                @click.native="props.nextTab()"
              >
                {{ $t('espace-artisan.profil-completion-funnel.wizard-button-finish-label') }}
              </wizard-button>
            </div>
          </div>
          <div
            class="row w-100 hidden-md hidden-lg hidden-xl"
            style="margin-top:auto;margin-bottom:auto;"
          >
            <div
              class="wizard-footer-left col-auto"
              style="float: left"
            >
              <wizard-button
                v-if="props.activeTabIndex > 0"
                slot="prev"
                :disabled="buttonState"
                style="min-width: 50px;height: 37px"
                class="prev-button-onboarding-funnel"
                @click.native="props.prevTab()"
              >
                <i class="fas fa-arrow-left" />
              </wizard-button>
            </div>
            <div
              class="wizard-footer-right col"
              style="float: right"
            >
              <wizard-button
                v-show="!props.isLastStep"
                slot="custom-buttons-right"
                :disabled="buttonState"
                class="wizard-btn prev-button-onboarding-funnel mr-2"
                style="min-width: 50px;height: 37px;margin-top:0"
                @click.native="ignoreStep"
              >
                {{ $t('espace-artisan.profil-completion-funnel.wizard-button-ignore-label') }}
              </wizard-button>
              <wizard-button
                v-if="!props.isLastStep"
                slot="next"
                :disabled="buttonState"
                style="min-width: 50px;height: 37px;margin-top:0;"
                class="wizard-footer-right next-button-onboarding-funnel"
                @click.native="props.nextTab()"
              >
                <i class="fas fa-arrow-right" />
              </wizard-button>
              <wizard-button
                v-else
                :style="props.fillButtonStyle"
                :disabled="buttonState"
                style="height: 37px;float: right;margin-top:0"
                class="wizard-footer-right finish-button wizard-btn next-button-onboarding-funnel"
                @click.native="props.nextTab()"
              >
                {{ $t('espace-artisan.profil-completion-funnel.wizard-button-finish-label') }}
              </wizard-button>
            </div>
          </div>
        </template>
      </form-wizard>
    </div>
    <div
      class="col-xs-12 hidden-xs hidden-sm col-md-3 col-lg-4"
      :style="`border-left: #003F46 8px solid;background-image: url('/images/onboarding_funnel/funnel_image_${funnelImage}.jpg');background-position:center;background-size: cover;height: 100vh;`"
    />
  </div>
</template>

<script>
import LandingStep from '../onboarding/LandingStep.vue';
import CalendarStep from '../onboarding/CalendarStep.vue';
import FormStep from '../onboarding/FormStep.vue';
import VueFormGenerator from 'vue-form-generator';

export default {
  name: 'OnboardingFunnel',
  components: {
    FormStep, CalendarStep, LandingStep,
  },
  props: {
    uuid: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      componentKey: 1,
      loading: true,
      changing: true,
      config: [],
      funnelImage: 1,
      loader: null,
    };
  },
  computed: {
    buttonState() {
      return this.$store.state.fileUpload.changing || this.changing;
    },
  },
  watch: {
    changing(newValue) {
      if (!newValue) {
        this.loader.hide();
      } else {
        this.loader = this.$loading.show({
          loader: 'dots',
        });
      }
    },
  },
  async created() {
    this.loader = this.$loading.show({
      loader: 'dots',
    });
    await this.$store.dispatch('profilFunnel/getForm', {uuid: this.uuid}).then(res => {
      if (res.response && res.response.status != 200) {
        console.warn('Error: ', res);
        return null;
      }
      this.config = res.data;
    }).catch(error => {
      console.warn('Error: ', error);
      return null;
    });
    this.loading = false;
    setTimeout(() => {
      this.changing = false;
    }, 300);
  },
  methods: {
    refreshImage() {
      var random = Math.floor(Math.random() * Math.floor(5)) + 1;
      if (random == this.funnelImage) this.funnelImage++;
      else this.funnelImage = random;
    },
    goToArtisanSpace() {
      window.location.href = this.$localization.localizedPath('/artisan-hub/');
    },
    async ignoreStep() {
      this.changing = true;
      let formWizard = this.$refs.formWizard;
      if (formWizard.activeTabIndex + 1 < formWizard.tabCount) {
        this.refreshImage();
        formWizard.changeTab(formWizard.activeTabIndex, formWizard.activeTabIndex + 1);
      }
      setTimeout(() => {
        this.changing = false;
      }, 300);
    },
    async validateTab(data) {
      this.changing = true;
      let body = {
        ref: data.step.ref,
        index: data.index,
      };
      if (data.step.component == 'FormStep')  {
        var refToValidate = this.$refs[data.step.ref][0].$refs.form;
        body.model = refToValidate.model;
        if (refToValidate.validate()) {
          return await this.$store.dispatch('profilFunnel/validateStep', {uuid: this.uuid, body: body}).then(res => {
            if (res.response && res.response.status) {
              if (res.response.status == 422) {
                let ref = this.$refs[data.step.ref][0].$refs.form;
                VueFormGenerator.error422(res.response.data.errors, ref.schema, ref.errors);
              } else if (res.response.status == 500) {
                this.$noty.error(res.response.data, {
                  killer: true,
                  timeout: 8000,
                  layout: 'topCenter',
                });
              }
              setTimeout(() => {
                this.changing = false;
              }, 300);
              return false;
            }
            this.$noty.success(this.$t('onboarding.formsNotif.success'), {
              killer: true,
              timeout: 8000,
              layout: 'topCenter',
            });
            setTimeout(() => {
              this.changing = false;
            }, 300);
            if (data.step.ref == 'lastStep') this.goToArtisanSpace();
            else this.refreshImage();
            return true;
          }).catch(error => {
            if (error.response.status == 422) {
              let ref = this.$refs[data.step.ref][0].$refs.form;
              VueFormGenerator.error422(error.response.data.errors, ref.schema, ref.errors);
            } else if (error.response.status == 500) {
              this.$noty.error(error.response.data, {
                killer: true,
                timeout: 8000,
                layout: 'topCenter',
              });
            }
            setTimeout(() => {
              this.changing = false;
            }, 300);
            return false;
          });
        } else {
          this.$noty.error(this.$t('onboarding.formsNotif.error'), {
            killer: true,
            timeout: 8000,
            layout: 'topCenter',
          });
          setTimeout(() => {
            this.changing = false;
          }, 300);
          return false;
        }
      }
      setTimeout(() => {
        this.changing = false;
      }, 300);
      this.refreshImage();
      return true;
    },
  },
};
</script>
