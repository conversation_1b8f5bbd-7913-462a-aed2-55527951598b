<template>
	<div class="wrapper" style="width: 100%">
		<input
			ref="autocomplete"
			id="autocomplete"
			:placeholder="schema.placeholder"
			class="search-location form-control"
			:value="value"
			type="text"
			@change="clearModel()">
	</div>
</template>

<script>
import VueFormGenerator from 'vue-form-generator';

export default {
	name: 'FieldCustomGoogleAdress',
	mixins: [ VueFormGenerator.abstractField ],
	props: {
		schema: {
			required: true,
			type: Object
		}
	},
	data() {
		return {
			adress: {
				adresse1: '',
				adresse_ville: '',
				code_postal: '',
				pays: '',
        country_code: '',
				region: '',
				lat: null,
				lng: null
			}
		};
	},
	computed: {
		parentModel() {
			return this.$parent.model;
		}
	},
	mounted() {
		this.autocomplete = new google.maps.places.Autocomplete(
			(this.$refs.autocomplete),
			{types: ['geocode']}
		);
		this.autocomplete.addListener('place_changed', () => {
			let place = this.autocomplete.getPlace();
			let lat = place.geometry.location.lat();
			let lon = place.geometry.location.lng();
			let tmpAdress = {
				'street_number': '',
				'route': '',
				'locality': '',
				'administrative_area_level_1': '',
				'country': '',
        'country_code': '',
				'postal_code': ''
			};
			for(const component of place.address_components) {
        const componentType = component.types[0];

        switch (componentType) {
          case "street_number": {
            tmpAdress.street_number = component.long_name;
            break;
          }

          case "route": {
            tmpAdress.route += component.long_name;
            break;
          }

          case "postal_code": {
            tmpAdress.postal_code = component.long_name;
            break;
          }

          case "postal_code_suffix": {
            tmpAdress.postal_code = `${tmpAdress.postal_code}-${component.long_name}`;
            break;
          }
          case "locality":
            tmpAdress.locality = component.long_name;
            break;
          case "administrative_area_level_1": {
            tmpAdress.administrative_area_level_1 = component.short_name;
            break;
          }
          case "country":
            tmpAdress.country = component.long_name;
            tmpAdress.country_code = component.short_name.toLowerCase();
            break;
        }
			}
			this.adress = {
				adresse1: tmpAdress.street_number + ' ' + tmpAdress.route,
				adresse_ville: tmpAdress.locality,
				code_postal: tmpAdress.postal_code,
				pays: tmpAdress.country,
        country_code: tmpAdress.country_code,
				region: tmpAdress.administrative_area_level_1,
				lat: lat,
				lng: lon
			};
			VueFormGenerator.customHelpers.addAdressToModel(this.adress, this.parentModel, this.schema);
			this.value = tmpAdress.street_number + ' ' + tmpAdress.route + ', ' + tmpAdress.postal_code + ' - ' + tmpAdress.locality + ', ' + tmpAdress.administrative_area_level_1 + ' - ' + tmpAdress.country;
		});
		if (this.value) {
			this.adress = {
				adresse1: this.value,
				adresse_ville: this.value,
				code_postal: this.value,
				pays: this.value,
        country_code:this.value,
				region: this.value,
				lat: 0,
				lng: 0
			};
		}
	},
	methods: {
		isFunction(functionToCheck) {
			return functionToCheck && {}.toString.call(functionToCheck) === '[object Function]';
		},
		clearModel() {
			this.adress = {
				adresse1: '',
				adresse_ville: '',
				code_postal: '',
				pays: '',
        country_code: '',
				region: '',
				lat: null,
				lng: null
			};
			VueFormGenerator.customHelpers.addAdressToModel(this.adress, this.parentModel, this.schema);
		},
		validate(calledParent) {
			// disabled inputs should always be assumed
			// to be "valid" as they can not be changed
			if(this.disabled) return true;
			let isValid = false;
			// clear previous errors
			this.clearValidationErrors();
			// BE SURE TO IMPLEMENT THE "required" validation rules
			if(this.schema.required && !this.value) {
				isValid = false;
				this.errors.push(this.schema.errorText || this.$t('onboarding.validation.fieldIsRequired'));
			}
			// CUSTOM VALIDATION LOGIC HERE
			if (!this.value || this.adress.adresse1 === '') {
				this.errors.push(this.$t('onboarding.validation.validGoogleAdress') || this.schema.errorText);
			}
			if (this.isFunction(this.schema.validate)) {
				this.errors = this.errors.concat(this.schema.validate(this.schema, this.parentModel));
			}
			// SEND VALIDATION EVENT
			if (this.isFunction(this.schema.onValidated)) {
				this.schema.onValidated.call(this, this.parentModel, this.errors, this.schema);
			}
			if (!calledParent) {
				this.$emit('validated', isValid, this.errors, this);
			}
			return this.errors;
		}
	}
};
</script>
