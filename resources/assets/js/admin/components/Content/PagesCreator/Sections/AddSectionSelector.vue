<template>
  <div>
    <select v-model="sectionTypeToAdd">
      <option
        selected
        disabled
        :value="null"
      >
        Sélectionner une section à ajouter
      </option>
      <option
        v-for="(section, key) in sectionsList"
        :key="key"
        :value="section.type"
      >
        {{ section.label }}
      </option>
    </select>
    <button
      class="btn"
      type="button"
      :disabled="!sectionToAdd"
      @click="$emit('add', sectionToAdd)"
    >
      <i class="fa fa-plus-circle text-success" />
    </button>
  </div>
</template>

<script>
export default {
  name: 'AddSectionSelector',
  props: {
    sectionsList: {
      type: Array,
      required: true,
    },
  },
  data: () => ({
    sectionTypeToAdd: null,
  }),
  computed: {
    sectionToAdd() {
      if (this.sectionTypeToAdd) {
        return this.sectionsList.find(s => s.type === this.sectionTypeToAdd);
      }

      return null;
    },
  },
};
</script>