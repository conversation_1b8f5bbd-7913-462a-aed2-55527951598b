<template>
  <div class="well">
    <div
      class="display-inline-flex-field form-group"
      :class="{'has-error': datesError}"
    >
      <label
        class="col-sm-1 px-0"
        for="publish_at"
      >
        Date de publication
      </label>
      <div class="col-sm-11">
        <div class="input-group date">
          <div class="input-group-addon">
            <i class="fa fa-calendar" />
          </div>
          <input
            ref="publishAt"
            name="publish_at"
            :form="formId"
            type="text"
            class="form-control pull-right"
            autocomplete="off"
          >
        </div>
      </div>
    </div>

    <div
      class="display-inline-flex-field form-group"
      :class="{'has-error': datesError}"
    >
      <label
        class="col-sm-1 px-0"
        for="expires_at"
      >
        Date d'expiration
      </label>

      <div class="col-sm-11">
        <div class="input-group date">
          <div class="input-group-addon">
            <i class="fa fa-calendar" />
          </div>
          <input
            ref="expiresAt"
            name="expires_at"
            :form="formId"
            type="text"
            class="form-control pull-right"
            autocomplete="off"
          >
        </div>
      </div>
    </div>

    <div
      v-if="datesError"
      class="text-danger"
    >
      <ul>
        <li>La date de publication doit précéder la date d'expiration</li>
        <li>Si aucune date de publication ou d'expiration n'est spécifiée, la page avec la date de publication la plus proche et non expirée sera utilisée.</li>
      </ul>
    </div>

    <div class="display-inline-flex-field form-group">
      <label
        class="col-sm-1 px-0"
        for="country_code"
      >
        Pays
      </label>
      <select
        name="country_code"
        :value="form.country_code"
        :form="formId"
        @change="emitUpdate('country_code', $event.target.value)"
      >
        <option
          value
          selected
        >
          Tous
        </option>
        <option
          v-for="(country, code) in countries"
          :key="code"
          :value="code"
        >
          {{ country.label }}
        </option>
      </select>
    </div>

    <div
      class="display-inline-flex-field form-group"
    >
      <label
        class="col-sm-1 px-0"
        for="mode"
      >
        Mode
      </label>
      <select
        name="mode"
        :value="form.mode"
        :form="formId"
        @change="emitUpdate('mode', $event.target.value)"
      >
        <option
          v-for="(mode, key) in {standard: 'Standard', group: 'Groupe', company: 'Company'}"
          :selected="key === 'standard'"
          :key="key"
          :value="key"
        >
          {{ mode }}
        </option>
      </select>
      <small
        v-if="errors.mode"
        class="help-block col-sm-12"
      >{{ errors.mode.join('\n') }}</small>
    </div>

    <div
      class="display-inline-flex-field form-group"
    >
      <label
        class="col-sm-1 px-0"
        for="is_condensed"
      >
        Condensed page
      </label>
      <input
        :checked="form.is_condensed === true"
        type="checkbox"
        name="is_condensed"
        value="1"
        :form="formId"
        @change="emitUpdate('is_condensed', $event.target.checked)"
      >
      <input
        v-if="form.is_condensed !== true"
        type="hidden"
        name="is_condensed"
        :form="formId"
        value="0"
      >
    </div>

    <div
      class="display-inline-flex-field form-group"
      :class="{ 'has-error': errors.subject_type }"
    >
      <label
        class="col-sm-1 px-0"
        for="subject_type"
      >
        Type d'attribut
      </label>
      <div class="col-sm-11">
        <select
          :value="form.subject_type"
          name="subject_type"
          class="form-control"
          :form="formId"
          :readonly="isEdit"
          @change="emitUpdate('subject_type', $event.target.value)"
        >
          <option
            v-if="!isEdit"
            :selected="form.subject_type === null"
            disabled
            :value="null"
          >
            Type d'attribut
          </option>
          <template v-for="type of subjectTypes">
            <option
              v-if="!isEdit || type.value === form.subject_type"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </option>
          </template>
        </select>
      </div>
      <small
        v-if="errors.subject_type"
        class="help-block col-sm-12"
      >{{ errors.subject_type.join('\n') }}</small>
    </div>
    <div
      v-if="form.subject_type"
      class="display-inline-flex-field form-group mt-1"
      :class="{ 'has-error': errors.subject_id }"
    >
      <label
        class="col-sm-1 px-0"
        for="subject_id"
      >Attribut</label>
      <div class="col-sm-11">
        <multiselect
          name="subject_id"
          placeholder="Attribut"
          :multiple="false"
          :close-on-select="true"
          :clear-on-select="true"
          :options="availableSubjects"
          :value="availableSubjects.find((s) => s.id === form.subject_id)"
          :custom-label="getSubjectLabel"
          track-by="id"
          @input="emitUpdate('subject_id', parseInt($event.id))"
        />
        <input
          type="hidden"
          name="subject_id"
          :form="formId"
          :value="form.subject_id"
        >
      </div>
      <small
        v-if="errors.subject_id"
        class="help-block col-sm-12"
      >{{ errors.subject_id.join('\n') }}</small>
    </div>
    <div
      v-if="shouldDisplaySlug"
      class="display-inline-flex-field mt-1 form-group"
      :class="{ 'has-error': errors.slug }"
    >
      <label
        for="slug"
        class="col-sm-1 px-0"
      >Slug de la page</label>
      <div class="col-sm-11">
        <input
          :value="form.slug"
          :form="formId"
          type="text"
          name="slug"
          class="form-control ml-0"
          readonly="readonly"
          @change="emitUpdate('slug', $event.target.value)"
        >
      </div>
      <small
        v-if="errors.slug"
        class="help-block col-sm-12"
      >{{ errors.slug.join('\n') }}</small>
    </div>
  </div>
</template>

<script>
import { config } from '../../../../front/tools/config.js';
import { DateTime } from 'luxon';
import Multiselect from 'vue-multiselect';

export default {
  name: 'PageInformations',
  components: {
    Multiselect,
  },
  model: {
    prop: 'form',
    event: 'update',
  },
  props: {
    formId: {
      required: false,
      type: String,
      default: undefined,
    },
    subjects: {
      required: true,
      type: Object,
    },
    form: {
      required: true,
      type: Object,
    },
    isEdit: {
      required: false,
      type: Boolean,
      default: false,
    },
    errors: {
      required: false,
      type: Object,
      default: () => ({}),
    },
    locale: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      datesError: false,
    };
  },
  computed: {
    countries() {
      return config('countries', []);
    },
    subjectTypes() {
      return Object.keys(this.subjects).map((key) => {
        return {
          value: key,
          label: this.subjects[key].name,
        };
      });
    },
    availableSubjects() {
      return this.subjects[this.form.subject_type]?.items || [];
    },
    shouldDisplaySlug() {
      return this.form.subject_type && this.form.subject_id;
    },
    publishAt() {
      if (this.form.publish_at) {
        if (typeof this.form.publish_at === 'string') {
          return DateTime.fromISO(this.form.publish_at);
        } else if (this.form.publish_at instanceof Date) {
          return DateTime.fromJSDate(this.form.publish_at);
        }
      }

      return this.form.publish_at;
    },
    expiresAt() {
      if (this.form.expires_at) {
        if (typeof this.form.expires_at === 'string') {
          return DateTime.fromISO(this.form.expires_at);
        } else if (this.form.expires_at instanceof Date) {
          return DateTime.fromJSDate(this.form.expires_at);
        }
      }

      return this.form.expires_at;
    },
  },
  watch: {
    'form.expires_at': function (newVal, oldVal) {
      if (newVal !== null && newVal !== oldVal) {
        if (!this.checkDateValidity()) {
          this.changeDatetimepickerValue(this.$refs.expiresAt, null);
          this.datesError = true;
          return;
        }

        this.datesError = false;
      }
    },
    'form.publish_at': function (newVal, oldVal) {
      if (newVal !== null && newVal !== oldVal) {
        if (!this.checkDateValidity()) {
          this.changeDatetimepickerValue(this.$refs.publishAt, null);
          this.datesError = true;
          return;
        }

        this.datesError = false;
      }
    },
  },
  mounted() {
    this.setDatetimepicker(this.$refs.publishAt, 'publish_at', this.form.publish_at ?? null);
    this.setDatetimepicker(this.$refs.expiresAt, 'expires_at', this.form.expires_at ?? null);
  },
  methods: {
    config,
    setDatetimepicker(el, linkedAttribute, value) {
      // eslint-disable-next-line no-undef
      callJQuery(($) => {
        $(el).datetimepicker({
          locale: 'fr',
          showClear: true,
          useCurrent: false,
          sideBySide: true,
          date: value,
          format: 'YYYY-MM-DDTHH:mm:00Z',
        }).on('dp.change', this.getDatetimepickerChangedCallback(linkedAttribute));
      });
    },
    changeDatetimepickerValue(el, value) {
      // eslint-disable-next-line no-undef
      callJQuery(($) => {
        const dateTimePicker = $(el).data('DateTimePicker');
        if (value === null) {
          dateTimePicker.clear();
          return;
        }

        dateTimePicker.date(value);
      });
    },
    checkDateValidity() {
      const expiresAt = this.expiresAt;
      const publishAt = this.publishAt;

      if (
        (!expiresAt || !publishAt)
      ) {
        return true;
      }

      return publishAt && expiresAt && expiresAt > publishAt;
    },
    getDatetimepickerChangedCallback(attributeToChange) {
      return (event) => {
        const date = event.date ? DateTime.fromJSDate(event.date.toDate()).startOf('second') : null;
        if (date !== this.form[attributeToChange]) {
          this.emitUpdate(attributeToChange, date);
        }
      };
    },
    getSubjectLabel(subject) {
      return subject?.content?.[this.locale]?.name;
    },
    emitUpdate(attribute, value) {
      this.$emit('update', {...this.form, [attribute]: value});
    },
  },
};
</script>
