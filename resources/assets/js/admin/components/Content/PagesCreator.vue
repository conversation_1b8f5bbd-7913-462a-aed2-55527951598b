<template>
  <div class="row px-0 mx-0">
    <div class="">
      <form
        :id="form.id"
        :action="route"
        method="POST"
        enctype="multipart/form-data"
      />
      <input
        :form="form.id"
        type="hidden"
        name="_token"
        :value="csrfToken"
      >
      <page-informations
        v-model="form"
        :form-id="form.id"
        :subjects="subjects"
        :errors="errorsObject"
        :is-edit="isEdit"
        :locale="defaultLocale"
      />
      <tab-manager
        v-if="shouldDisplayContent"
        :tabs="tabs"
      />
    </div>
    <div>
      <nav class="navbar navbar-default navbar-fixed-bottom">
        <div class="container">
          <button
            class="btn navbar-btn btn-primary navbar-right margin-x-md"
            type="submit"
            form="page-creator-form"
            :disabled="!shouldDisplayContent"
          >
            Enregister
          </button>
        </div>
      </nav>
    </div>
    <page-already-exists-modal
      :show="modals.pageAlreadyExistsModal.show"
      :edit-page-route="editPageRoute"
      :item="modals.pageAlreadyExistsModal.item"
      :type="modals.pageAlreadyExistsModal.type"
      :reason="modals.pageAlreadyExistsModal.reason"
      :reason-payload="modals.pageAlreadyExistsModal.reasonPayload"
      @close="modals.pageAlreadyExistsModal.show = false"
    />
    <an-error-occurred-modal
      :show="modals.anErrorOccurredModal.show"
    />
  </div>
</template>

<script>
import TabManager from '../TabManager.vue';
import PageContent from './PagesCreator/PageContent.vue';
import HasCsrfToken from '../../../mixins/has-csrf-token.js';
import PageLinks from './PagesCreator/PageLinks/PageLinks.vue';
import PageInformations from './PagesCreator/PageInformations.vue';
import SectionsBuilder from './PagesCreator/Sections/SectionsBuilder.vue';
import AnErrorOccurredModal from '../Modals/Content/AnErrorOccurredModal.vue';
import PageAlreadyExistsModal from '../Modals/Content/PageAlreadyExistsModal.vue';
import {mapGetters} from 'vuex';
import {SEARCH_MODE_DEFAULT} from "@/features/algolia-search/constants/search-page-mode";

export default {
  name: 'PagesCreator',
  components: {
    PageAlreadyExistsModal,
    AnErrorOccurredModal,
    TabManager,
    PageInformations,
  },
  mixins: [HasCsrfToken],
  props: {
    subjects: {
      type: Object,
      required: true,
    },
    defaults: {
      type: Object,
      required: true,
    },
    page: {
      type: Object,
      required: false,
      default: null,
    },
    oldValues: {
      type: Object,
      required: false,
      default: null,
    },
    route: {
      type: String,
      required: true,
    },
    editPageRoute: {
      type: String,
      required: true,
    },
    errors: {
      type: Object,
      required: false,
      default: () => {},
    },
    locales: {
      type: Array,
      required: true,
    },
    defaultLocale: {
      type: String,
      required: true,
    },
    // Since vue is not loaded as an app on the backoffice, collections are collected via props, then saved in the store
    collections: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      form: {
        id: 'page-creator-form',
        slug: null,
        content: {},
        subject_id: null,
        subject_type: null,
        badge_image: null,
        hero_image: null,
        type: SEARCH_MODE_DEFAULT,
        is_condensed: false,
        sections: [],
        customLinkedPages: [],
        country_code: '',
        publish_at: null,
        expires_at: null,
      },
      modals: {
        pageAlreadyExistsModal: {
          show: false,
          reason: null,
          reasonPayload: {},
          item: null,
          type: null,
        },
        anErrorOccurredModal: {
          show: false,
        },
      },
      isLoading: false,
      loader: null,
    };
  },
  computed: {
    ...mapGetters('collections', [
      'getItems',
    ]),
    selectedSubject() {
      return (this.subjects[this.form.subject_type]?.items || [])
        .find(i => i.id === this.form.subject_id);
    },
    selectedSubjectLabel() {
      return this.selectedSubject?.content?.[this.defaultLocale]
        ?.name;
    },
    isEdit() {
      return !!this.page;
    },
    subjectSlug() {
      if (this.form.subject_type && this.form.subject_id) {
        const item = this.subjects[this.form.subject_type]?.items?.find(i => i.id === this.form.subject_id);
        if (item) {
          return item.slug;
        }
      }

      return '';
    },
    defaultValues() {
      return this.defaults;
    },
    shouldDisplayContent() {
      return (this.form.subject_type !== null && this.form.subject_id !== null);
    },
    errorsObject() {
      return Object.entries(this.errors || {}).reduce((acc, [key, value]) => {
        const parts = key.split('.');
        var target = acc;
        while (parts.length > 1) {
          const part = parts.shift();
          target = target[part] = target[part] || {};
        }

        target[parts[0]] = value;
        return acc;
      }, {});
    },
    tabs() {
      return this.singlePageTabs;
    },
    singlePageTabs() {
      return [
        {
          label: 'Contenu',
          component: PageContent,
          props: {
            formId: this.form.id,
            form: this.form,
            locales: this.locales,
            errors: this.errorsObject,
            namePrefix: 'content',
            showImages: true,
          },
          listeners: {
            update: (event) => {this.form = event;},
          },
        },
        {
          label: 'Sections',
          component: SectionsBuilder,
          props: {
            formId: this.form.id,
            errors: this.errorsObject.sections ? this.errorsObject.sections : {},
            locales: this.locales,
            sectionsList: this.defaultValues?.sections,
            enabledSections: this.form.sections,
          },
          listeners: {
            updated: (event) => {this.form.sections = event;},
          },
        },
        {
          label: 'Liens',
          component: PageLinks,
          props: {
            formId: this.form.id,
            editPageRoute: this.editPageRoute,
            customLinkedPages: this.form.customLinkedPages,
            currentPageId: this.page?.id ?? undefined,
          },
          listeners: {
            update: (event) => {this.form.customLinkedPages = event;},
          },
        },
      ];
    },
  },
  watch: {
    'form.subject_id'(newVal, oldVal) {
      if (newVal && newVal !== oldVal && this.form.subject_id !== (this.oldValues?.subject_id || this.page?.subject_id)) {
        this.doesPageAlreadyExists({
          subject_type: this.form.subject_type,
          subject_id: this.form.subject_id,
          expires_at: this.form.expires_at?.toISO({ suppressMilliseconds:true }),
          publish_at: this.form.publish_at?.toISO({ suppressMilliseconds:true }),
          country_code: this.form.country_code,
        }, 'subject', () => {
          this.form.subject_id = null;
        }, () => {
          if (!this.isEdit) {
            this.resetForm(['subject_type', 'subject_id']);
            this.setDefaults();
          }
        });
      }
    },
    'form.slug'(newVal, oldVal) {
      if (newVal && newVal !== oldVal && (this.page === null || this.page.slug !== this.form.slug)) {
        this.doesPageAlreadyExists({
          subject_type: this.form.subject_type,
          subject_id: this.form.subject_id,
          expires_at: this.form.expires_at?.toISO({ suppressMilliseconds:true }),
          publish_at: this.form.publish_at?.toISO({ suppressMilliseconds:true }),
          country_code: this.form.country_code,
          slug: this.form.slug,
        }, 'slug', () => {
          this.form.slug = null;
          this.form.subject_id = null;
        });
      }
    },
    isLoading(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (newVal) {
          this.loader = this.$loading.show();
        } else if (this.loader) {
          this.loader.hide();
        }
      }
    },
  },
  created() {
    this.$store.commit('collections/SET_ITEMS', this.collections);
    this.initFormValues();
  },
  methods: {
    initFormValues() {
      this.initLocales();
      [
        'subject_type',
        'subject_id',
        'slug',
        'mode',
        'is_condensed',
        'hero_image',
        'badge_image',
        'publish_at',
        'expires_at',
        'country_code',
      ].forEach(this.initFormField);
      [
        'title',
        'subtitle',
        'description',
        'meta_title',
        'meta_description',
        'seo_title',
        'seo_content',
      ].forEach(this.initTranslatedContentField);
      if (!this.form.hero_image) {
        this.form.hero_image = this.defaultValues.hero_image;
      }
      if (!this.form.badge_image) {
        this.form.badge_image = this.defaultValues.badge_image;
      }
      this.form.customLinkedPages = this.oldValues?.linked_pages || this.page?.custom_linked_pages || this.form.customLinkedPages;
      this.initFormSectionValues();
    },
    initLocales() {
      const blankContent = {
        title: null,
        subtitle: null,
        description: null,
        meta_title: null,
        meta_description: null,
        seo_title: '',
        seo_content: '',
      };

      for (var locale of this.locales) {
        if (typeof this.form.content[locale] === 'undefined') {
          this.$set(this.form.content, locale, {});
        }

        for (var [prop, value] of Object.entries(blankContent)) {
          this.$set(this.form.content[locale], prop, value);
        }
      }
    },
    initFormField(fieldName) {
      this.form[fieldName] = this.oldValues?.[fieldName] || this.page?.[fieldName] || this.form[fieldName];
    },
    initTranslatedContentField(fieldName) {
      for (var locale of this.locales) {
        this.form.content[locale][fieldName] =
            this.oldValues?.content?.[locale]?.[fieldName] ||
            this.page?.content?.[locale]?.[fieldName] ||
            this.form.content[locale][fieldName];
      }
    },
    initFormSectionValues() {
      const sections = this.oldValues?.sections || this.page?.sections || [];

      sections.forEach(this.initFormSectionField);
    },
    initFormSectionField(section) {
      const newSection = {
        type: section.type,
        label: section.label || this.defaultValues?.sections?.[section.type]?.label,
        content: this.getSectionInputs(section.type),
        data: section.data ?? {},
      };

      if (newSection.type === 'collections' && Array.isArray(newSection.data?.collections)) {
        newSection.data.collections = newSection.data.collections.map((collection) => {
          const found = this.getItems.find((storeCollection) => {
            return storeCollection.id === parseInt(collection.id);
          });

          if (typeof found === 'undefined') {
            console.error('Section collections : Couldn\'t retrieve the following collection', collection);
          }

          return found;
        }).filter((collection) => collection);
      }

      this.form.sections.push(newSection);
    },
    getSectionInputs(sectionType) {
      const oldValue = this.oldValues?.sections?.find(section => section.type === sectionType);
      const pageValue = this.page?.sections?.find(section => section.type === sectionType);

      return this.locales.reduce((acc, locale) => {
        let defaultValues = this.defaultValues?.sections?.[sectionType]?.inputs?.[locale];

        acc[locale] = {
          title: oldValue?.content?.[locale]?.title || pageValue?.content?.[locale]?.title || defaultValues?.title || null,
          description: oldValue?.content?.[locale]?.description || pageValue?.content?.[locale]?.description || defaultValues?.description || null,
        };
        return acc;
      }, {});
    },
    doesPageAlreadyExists(payload, reason, pageExistsCallback, pageDoesntExistsCallback = () => {}) {
      this.isLoading = true;

      this.$store.dispatch('pages/checkAvailability', payload).then((response) => {
        if (response.data && response.data.type !== null) {
          return response.data;
        }

        return null;
      }).catch(() => {
        return false;
      }).then((result) => {
        this.isLoading = false;
        if (result) {
          this.showPageAlreadyExistsModal(result, reason, pageExistsCallback);
        } else if (result === false) {
          this.showAnErrorOccurredModal();
        } else {
          pageDoesntExistsCallback();
        }
      });
    },
    showAnErrorOccurredModal() {
      this.modals.anErrorOccurredModal.show = true;
    },
    showPageAlreadyExistsModal(result, reason, pageExistsCallback) {
      this.modals.pageAlreadyExistsModal.item = result.item;
      this.modals.pageAlreadyExistsModal.type = result.type;
      this.modals.pageAlreadyExistsModal.reason = reason;
      this.modals.pageAlreadyExistsModal.reasonPayload = {
        subjectIdLabel: this.selectedSubjectLabel ?? '',
        slug: this.form.slug,
        country_code: this.form.country_code,
        expires_at: this.form.expires_at,
        publish_at: this.form.publish_at,
      };
      pageExistsCallback();
      this.modals.pageAlreadyExistsModal.show = true;
    },
    setDefaults() {
      const properties = [
        'title',
        'subtitle',
        'description',
        'meta_title',
        'meta_description',
      ];

      for (var prop of properties) {
        for (var locale of this.locales) {
          if ([null, ''].includes(this.form.content[locale][prop])) {
            this.form.content[locale][prop] = (this.defaultValues?.content?.[locale]?.[prop] || this.form[prop] || '')
              .replace(':subjectLabel', this.selectedSubject?.content?.[locale]?.name ?? this.selectedSubjectLabel);
          }
        }
      }

      this.setDefaultSlug();
      this.setDefaultOnImages();
      this.setDefaultOnSections();
    },
    setDefaultSlug() {
      this.form.slug = this.subjectSlug;
    },
    setDefaultOnImages() {
      const images = [
        'hero_image',
        'badge_image',
      ];

      for (var img of images) {
        this.form[img] = this.defaultValues[img];
      }
    },
    setDefaultOnSections() {
      this.form.sections = this.subjects[this.form.subject_type]?.sections?.map((item) => {
        return {
          label: this.defaultValues?.sections?.[item]?.label ?? '',
          type: item,
          content: this.locales.reduce((acc, locale) => {
            let defaultValues = this.defaultValues?.sections?.[item]?.inputs?.[locale];

            acc[locale] = {
              title: defaultValues?.title || '',
              description: defaultValues?.description || '',
            };

            return acc;
          }, {}),
          data: [],
        };
      }) || [];
    },
    resetForm(except = []) {
      const values = {
        slug: null,
        subject_id: null,
        subject_type: null,
        badge_image: null,
        hero_image: null,
        sections: [],
        customLinkedPages: [],
      };

      Object.entries(values).forEach(([key, value]) => {
        if (!except.includes(key)) {
          this.form[key] = value;
        }
      });

      this.initLocales();
    },
  },
};
</script>
