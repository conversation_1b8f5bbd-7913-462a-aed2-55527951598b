import Vue from 'vue';
import Vuex from 'vuex';

/*
 * Mo<PERSON>les
 */
import fileUpload from '../../common/store/fileUpload.js';
import auth from './modules/auth.js';
import seo from './modules/seo.js';
import seoList from './modules/seoList.js';
import onboarding from './modules/onboarding.js';
import onboardingList from './modules/onboardingList.js';
import pages from './modules/pages.js';
import collections from './modules/collections.js';

Vue.use(Vuex);

const debug = process.env.NODE_ENV !== 'production';

const store = new Vuex.Store({
  modules: {
    auth,
    seo,
    seoList,
    fileUpload,
    onboarding,
    onboardingList,
    pages,
    collections,
  },
  strict: debug,
});

export default store;
