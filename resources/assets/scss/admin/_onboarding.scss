#vfg-create-artisan-form-container {
  background-color: white;
  padding: 2rem;
  border-radius: 1rem;
}

/*
 * Create artisan form
 */
@media (max-width: 425px) {
  .vdp-datepicker__calendar {
    margin-left: -75px;
    width: 250px !important;
  }
}

/*
 * Fields
 */
.full-width-field > .field-wrap > .wrapper > input[type="number"] {
  width: 100% !important;
}

.custom-duration-select {
  border-style: solid;
  appearance: menulist !important;
  box-sizing: border-box;
}

.custom-select-menu {
  appearance: menulist !important;
}

.custom-select-menu > .field-wrap > select {
  appearance: menulist !important;
}

/*
 * Validation
 */
.nav-link-onboarding-validation {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.span-wrapper {
  position: relative;
  display: inline-block;
}

.badge-form-onboarding-validation {
  background-color: red;
}

.span-wrapper .badge-form-onboarding-validation {
  position: absolute;
  top: 0;
  right: 0;
}

.submit-container {
  justify-content: center;
  display: flex;
}

#onboarding-bo-validation-button {
  padding: 10px;
  margin: 10px;
}

/*
 * Lists
 */
.datatables-mail-button {
  display: block;
  height: 25px;
  color: white;
  font-weight: bold;
  line-height: 25px;
  text-align: center;
}
