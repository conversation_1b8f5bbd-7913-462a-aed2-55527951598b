@use(\App\Infrastructure\Auth\AuthFinder)
<script>
    var APP_ID = "{{config('services.intercom.key')}}";

    window.intercomSettings = {
        app_id: APP_ID,
        @if(AuthFinder::make()->tryCustomer())
        name: "{{ AuthFinder::make()->customer()->prenom." ".AuthFinder::make()->customer()->nom }}",
        email: "{{ AuthFinder::make()->customer()->email }}",
        created_at: "{{ AuthFinder::make()->customer()->created_at }}"
        @endif
    };

    var intercomLazyLoader = function () {
        !function () {
            var t = window, e = t.Intercom;
            if ("function" == typeof e) e("reattach_activator"), e("update", t.intercomSettings); else {
                var a = document, n = function () {
                    n.c(arguments)
                };
                n.q = [], n.c = function (t) {
                    n.q.push(t)
                }, t.Intercom = n;
                var c = function () {
                    var t = a.createElement("script");
                    t.type = "text/javascript", t.async = !0, t.src = "https://widget.intercom.io/widget/" + APP_ID;
                    var e = a.getElementsByTagName("script")[0];
                    e.parentNode.insertBefore(t, e)
                };
                "complete" === document.readyState ? c() : t.attachEvent ? t.attachEvent("onload", c) : t.addEventListener("load", c, !1)
            }
        }();
        window.removeEventListener('scroll', intercomLazyLoader);
        clearTimeout(intercomBackupTimer);
    };
    var intercomBackupTimer = null;
    window.addEventListener('scroll', intercomLazyLoader);
    intercomBackupTimer = setTimeout(intercomLazyLoader, 10000); // 10s
</script>
