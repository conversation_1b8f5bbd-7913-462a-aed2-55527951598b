<script src="{{ safeMix('/js/mailcheck.min.js') }}"></script>
<script>
    var domains = ['gmail.com', 'live.fr', 'wanadoo.fr', 'aol.com', 'icloud.com', 'orange.fr', 'free.fr', 'laposte.net', 'sfr.fr', 'edhec.com', 'me.com', 'neuf.fr', 'msn.com', 'bbox.fr', 'cegetel.net'];
    var secondLevelDomains = ['hotmail', 'outlook', 'yahoo']
    var topLevelDomains = ["com", "net", "org", "fr", "cc", "io", "edu"];

    $('#emailCheck').on('blur', function() {
      $(this).mailcheck({
        domains: domains,                       // optional
        secondLevelDomains: secondLevelDomains, // optional
        topLevelDomains: topLevelDomains,       // optional
        suggested: function(element, suggestion) {
          // callback code
          $('#mailCheckZone').append('\
          <div id="zonemailcheck" class="mt-3 alert alert-primary">\
              {{ __("scripts.mailchecker_script.correct") }}\
              <i id="emailsuggested">'+suggestion.full+'</i> ?<br/>\
              <a id="ouimailcheck" href="#">{{ __("scripts.yes") }}</a>\
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\
              <a id="nonmailcheck" href="#">{{ __("scripts.no") }}</a>\
          </div>');
        },
        empty: function(element) {
          // callback code
        }
      });
    });

    $(document.body).on('click', '#ouimailcheck', function (e){
        e.preventDefault();
        $('#emailCheck').val($('#emailsuggested').text());
        $('#zonemailcheck').remove();
        $('#password').focus();
    });
    $(document.body).on('click', '#nonmailcheck', function (e){
        e.preventDefault();
        $('#zonemailcheck').remove();
        $('#password').focus();
    });

    $("#emailCheck").focusin(function() {
        $('#zonemailcheck').remove();
    });
</script>
