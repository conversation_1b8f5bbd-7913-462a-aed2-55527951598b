@extends('admin.layouts._admin_layout', ['page_title' => 'Modifier une ville', 'page_description' => 'Formulaire de modification des villes', 'niveau1' => 'Modification ville'])

@section('content')
  <div class="row">
    <div class="col-lg-6 col-xs-12">
      {{ html()->modelForm($city, 'PUT', route('admin.cities.update', [$city->id]))->acceptsFiles()->open() }}
      <div class="box box-primary">
        <div class="box-header with-border">
          <h3 class="box-title">Edition d'une ville</h3>
        </div>
        <div class="box-body">
          {!! implode('<br/>', $errors->all()) !!}
          <div class="form-group col-xl-6 col-xs-12 {!! $errors->has('ville_nom') ? 'has-error' : '' !!}">
            <x-form.translated.text
              name="ville_nom"
              :values="$city->getFormLocalizedAttribute('ville_nom')"
              label="Nom de la ville (non modifiable)"
              disabled
            />
            {!! $errors->first('ville_nom', '<small class="help-block">:message</small>') !!}
          </div>
          <div class="form-group {!! $errors->has('permalien') ? 'has-error' : '' !!}">
            <label for="permalien">Permalien</label>
            <input type="text" name="permalien" class="form-control" disabled="true" value="{{ $city->permalien }}"/>
            {!! $errors->first('permalien', '<small class="help-block">:message</small>') !!}
          </div>
          <div class="form-group col-md-6 col-xs-12 {!! $errors->has('responsable_id') ? 'has-error' : '' !!}">
            {{ html()->label('Responsable de ville', 'responsable_id') }}
            <select class="form-control" id="responsable_id" name="responsable_id">
              @foreach($responsiblesList as $id => $name)
                <option value="{{$id}}" {{ $city->responsable_id == $id ? "selected" : "" }}>{{$name}}</option>
              @endforeach
            </select>
            {!! $errors->first('responsable_id', '<small class="help-block">:message</small>') !!}
          </div>
          <div class="form-group col-md-6 col-xs-12 {!! $errors->has('country_code') ? 'has-error' : '' !!}">
            {{ html()->label('Pays', 'country_code') }}
            <input type="hidden" name="country_code" value="{{$city->country_code}}"/>
            <select class="form-control" id="country_code" name="country_code" disabled>
              @foreach(config('countries.available_countries') as $host => $countryCode)
                <option
                  value="{{$countryCode}}" {{ $city->country_code == $countryCode ? "selected" : "" }}>{{frenchCountry($countryCode)}}</option>
              @endforeach
            </select>
            {!! $errors->first('country_code', '<small class="help-block">:message</small>') !!}
          </div>
          <div class="form-group col-xs-12 {!! $errors->has('thumbnail_image') ? 'has-error' : '' !!}">
            <label for="thumbnail_image">Image de la vignette</label>
            {!! $errors->first('thumbnail_image', '<small class="help-block">:message</small>') !!}
            <img
              id="thumbnail_image_preview"
              style="height: auto; width: 50%; display:block; margin: auto;"
              src="{{ \Illuminate\Support\Facades\Storage::disk(\App\Models\Ville::getStorageDisk())->url($city->thumbnail_image) }}"
            />
            <input type="file" name="thumbnail_image" class="form-control"
                   onchange="previewImage(event, 'thumbnail_image_preview')"/>
          </div>
          <div class="form-group text-center col-xs-12">
            {{ html()->submit('Editer')->class('btn btn-primary') }}
          </div>
        </div>
      </div>
      {{ html()->closeModelForm() }}
    </div>

    @if(!$city->active)
      <div class="col-lg-6 col-xs-12">
        <div class="box box-primary">
          <div class="box-header with-border">
            <h3 class="box-title">Activer la ville</h3>
          </div>
          <div class="box-body">
            <p>
              <i class="fas fa-exclamation-triangle" style="margin-right: 5px"></i> Attention ! <i
                class="fas fa-exclamation-triangle" style="margin-left: 5px"></i><br/> Lors de l'activation de la ville,
              les ateliers actifs et visibles seront indexés sur
              le site
              <br/>
              Cela créera également les pages P2 de la ville (Ville et Ville x Savoir-faire)
            </p>
            <a id="city-activate" href="{{route('admin.cities.activate', $city->id)}}"
               class="btn btn-primary">Activer {{$city->ville_nom}}</a>
            @can('hasAccess', [App\Models\Admin::class, 'admin.cities.destroy'])
              <form method="post" action="{{ route('admin.cities.destroy', $city) }}">
                @method('DELETE')
                @csrf
                <input type="submit" value="Supprimer" id="city-delete"
                   class="btn btn-danger mt-2"/>
              </form>
            @endCan

          </div>
        </div>
      </div>
    @endif
  </div>
@stop

@section('script')
  <script>
    $("#city-activate").on("click", function (e) {
      $.LoadingOverlay('show');
    });
    $("#city-delete").on("click", function (e) {
      $.LoadingOverlay('show');
    });

    function previewImage(event, previewTagId) {
      if (event.target.files.length > 0) {
        var previewElement = document.getElementById(previewTagId);
        if (previewElement) {
          previewElement.src = URL.createObjectURL(event.target.files[0]);
          previewElement.classList.remove('hidden');
        }
      }
    }
  </script>
@stop
