@extends('admin.layouts._admin_layout', ['page_title' => 'Informations user-artisan', 'page_description' => 'Affichage des informations et des comptes liés à un user-artisan', 'niveau1' => 'Informations user-artisan'])

@section('content')
    <div class="row">
          <div class="col-md-6 col-xs-12">
          <div class="box box-primary">
            <div class="box-body">
            <div class="box-header with-border">
                <h3 class="box-title">Informations utilisateur artisan</h3>
                <div style="display: flex; justify-content: flex-end">
                    <button id="regen"><i class="fa fa-edit"></i>Modifier</button>
                </div>
            </div>

            <div class="box-body">
              <table id="table-informations" class="table table-bordered table-hover">
                <tbody>
                    <tr>
                        <td>ID</td>
                        <td>{{ $userartisan->id }}</td>
                    </tr>
                    <tr>
                        <td>Prénom et nom</td>
                        <td>{{ $userartisan->prenom }} {{ $userartisan->nom }}</td>
                    </tr>
                    <tr>
                        <td>Email</td>
                        <td>{{ $userartisan->email }}</td>
                    </tr>
                    <tr>
                        <td>Locale</td>
                        <td>{{ frenchLanguage($userartisan->locale) }}</td>
                    </tr>
                </tbody>
              </table>
            </div>
            <div class="box-header with-border">
              <h3 class="box-title">Associer un artisan au compte</h3>
            </div>
            <div class="box-body">
                <form class="form" role="form" method="POST" action="{{ route('admin.usersartisans-artisans.create', $userartisan->id) }}">
                @csrf
                <div class="form-group col-xs-12 {!! $errors->has('artisan_id') ? 'has-error' : '' !!}">
                    {{ html()->label('Artisan à associer', 'artisan_id') }}
                     <select name="artisan_id" class="form-control select2">
                              @foreach(App\Models\Artisan::all() as $artisan)
                                  <option value="{{$artisan->id}}">{{$artisan->getNomEtDenomination()}}</option>
                              @endforeach
                    </select>
                    {!! $errors->first('artisan_id', '<small class="help-block">:message</small>') !!}
                </div>
                <div class="form-group text-center col-xs-12">
                    {{ html()->submit('Associer')->class('btn btn-primary') }}
                </div>
                {{ html()->form()->close() }}
            </div>
           </div>
        </div>

        </div>

@if($userartisan->artisans->count())
          <div class="col-md-6 col-xs-12">
          <div class="box box-primary">
            <div class="box-body">

              <div class="box-header">
                <h3 class="box-title">Profils artisans rattachés à l'utilisateur</h3>
                </div>
                <table id="table-profils" class="table table-bordered table-hover table-informations">
                <thead>
                <tr>
                    <th>ID et Nom artisan</th>
                    <th>Ateliers associés</th>
                    <th>Retirer la gestion du compte</th>
                </tr>
                </thead>
                <tbody>
                   @foreach($userartisan->artisans as $artisan)
                       <tr>
                         <td><a href="{{ route('admin.artisans.edit', ['artisan' => $artisan]) }}">{{ $artisan->id }} - {{ $artisan->prenom." ".$artisan->nom }}</a></td>
                         <td>
                         @foreach($artisan->ateliers as $atelier)
                         {{ $atelier->nom }}<br/>
                         @endforeach
                         </td>
                         <td>{{ html()->form('POST', '/wecanadmin/usersartisans/' . $userartisan->id . '/artisans/' . $artisan->id)->class('text-center')->open() }}
                                       {{ html()->hidden('_method', 'DELETE') }}
                                            <button class="btn btn-warning" type="submit" value="Dissocier"><i class="fa fa-trash" aria-hidden="true"></i></button>
                                </form>
                            </td>
                       </tr>
                   @endforeach
               </tbody>
              </table>
            </div>
            </div>
          </div>
@endif
    </div>

@stop

@push('modals')
    @include('admin.users.usersartisans.modals.edit')
@endpush

@section('script')
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.2/js/bootstrap-select.min.js"></script>
    <script>
        $(function () {
            $('.table-informations').DataTable({
              "paging": false,
              "lengthChange": false,
              "searching": false,
              "ordering": false,
              "info": false,
              "autoWidth": false,
              "language": {
                        "url": "{{ __('datatables.traduction_file_url') }}"
                    }
            });
        });

        $(function () {
          //Initialize Select2 Elements
          $('.select2').select2()
        });

        @if($errors->any())
            $("#modalEditUserArtisan").modal('toggle');
        @endif

        $(document.body).on('click', '#regen', function (e) {
            $("#modalEditUserArtisan").modal('toggle');
        });
        $("#closeModalEditUserArtisan").on('click', function() {
            $("#modalEditUserArtisan").modal('toggle');
        });
  </script>
@stop
