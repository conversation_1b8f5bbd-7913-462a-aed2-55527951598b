@php /** @var \App\Models\User $user */@endphp
<div class="col-md-6 col-xs-12">
    <div class="box box-primary">
        <div class="box-body">

            <div class="box-header">
                <h3 class="box-title">Bons cadeaux</h3>
            </div>
            <table id="gift-card-table" class="table table-bordered table-hover table-informations">
                <thead>
                <tr>
                    <th>#</th>
                    <th>Type</th>
                    <th>Atelier associé</th>
                    <th>Code cadeau</th>
                    <th>ID Commande associée</th>
                    <th>Date création</th>
                    <th>Bénéficiaire</th>
                    <th>Détail de la carte cadeau</th>
                </tr>
                </thead>
                <tbody>
                @foreach($user->giftCards as $giftCard)
                    <tr>
                        <td>{{ $giftCard->id }}</td>
                        <td>{!! $giftCard->type === \App\Domain\Gift\Enums\GiftCardType::TICKET ? '<i class="fas fa-ticket-alt text-primary"></i>' : '<i class="fas fa-euro-sign text-success"></i>';!!}</td>
                        <td>
                            @if($giftCard->type === \App\Domain\Gift\Enums\GiftCardType::TICKET && isset($giftCard->associatedWorkshop))
                                <a href="{{ route('admin.ateliers.edit', $giftCard->associatedWorkshop?->id)  }}">#{{ $giftCard->associatedWorkshop?->id }} - {{ $giftCard->AssociatedWorkshop?->nom }}</a>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('admin.gift-card.show', $giftCard->id) }}">
                                {{ $giftCard->code }}
                            </a>
                        </td>
                        <td>
                            <a href="{{ route('admin.commandes.show', $giftCard->order_id) }}">{{ $giftCard->order_id }}</a>
                        </td>
                        <td>{{ dateLongForTechFormat($giftCard->created_at) }}</td>
                        <td style="overflow: auto; text-overflow: ellipsis;max-width: 100px">
                            @if($giftCard->gift !== null)
                                {{ $giftCard->gift->to }}
                                <br/>
                                {{ $giftCard->contact_email }}
                            @endif
                        </td>
                        <td class="text-center">
                            <a href="{{ route('admin.gift-card.show', $giftCard->id) }}">
                                <i class="fa fa-eye" aria-hidden="true"></i>
                            </a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
