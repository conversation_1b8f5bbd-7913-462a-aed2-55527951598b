@extends('admin.layouts._admin_layout', ['page_title' => 'Liste des lots de bons cadeaux', 'page_description' => 'Gestion des lots de bons cadeaux', 'niveau1' => 'Lots de bons cadeaux management'])
@push('css')
    <link href="https://gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">
    <style>
      .d-none {
        display: none;
      }

      .select2-container {
        max-width: 350px !important;
      }

    </style>
@endpush
@section('content')
    <div id="async-alert"
         style="position: fixed; text-align: center; min-width: 300px; right:25px; top:75px; z-index: 999;">
    </div>
    <div class="panel panel-primary">
        <div class="panel-heading clearfix">
            Liste des lots
            <span class="pull-right">
                <button class="btn btn-success" id="btn-create-lot">C<PERSON>er un lot de bons cadeaux</button>
            </span>
        </div>
        <div class="panel-body">
            {{$dataTable->table()}}
        </div>
    </div>
@stop
@push('modals')
    @include('admin.gift-card.lot.manage.modals')
@endpush

@push('scripts')
    @include('admin.layouts.datatables-script')
    @include('admin.gift-card.lot.manage.scripts')
@endpush

<template id="gift-card-line">
    <tr class="form-inline">
        <td><label for="gift-card-pack">Sous&nbsp;Lot&nbsp;:</label></td>
      <td>
        <div class="input-group">
          <input type="number" min="1" max="2000" name="gift_card_pack[INDEX][price]" class="form-control" aria-describedby="euro-price" required />
          <span class="input-group-addon" id="euro-price">€/£</span>
        </div>
      </td>
      <td class="paid-price d-none">
        <div class="input-group">
          <input type="number" min="1" max="2000" name="gift_card_pack[INDEX][paid_price]" class="form-control" aria-describedby="euro" required />
          <span class="input-group-addon">€/£</span>
        </div>
      </td>
        <td>
          <div class="input-group">
            <input type="number" min="1" max="1000" maxlength="4" size="4" name="gift_card_pack[INDEX][quantity]" class="form-control" required />
          </div>
        </td>
        <td>
            <div class="input-group">
                <input type="number" min="1" name="gift_card_pack[INDEX][amount]"
                       class="form-control" readonly aria-describedby="euro"/>
                <span class="input-group-addon" id="euro">€/£</span>
            </div>
        </td>
    </tr>
</template>

<template id="ticket-gift-card-line">
  <tr class="form-inline">
    <td>
      <label for="gift-card-pack">Sous&nbsp;Lot&nbsp;:</label>
    </td>
    <td>
      <div class="input-group" style="width: 300px !important">
        <select id="select2-workshop" class="form-control select2" name="gift_card_pack[0][workshop_id]" required></select>
      </div>
    </td>
    <td class="paid-price d-none">
      <div class="input-group">
        <input type="number" min="1" max="2000" name="gift_card_pack[0][paid_price]" class="form-control" aria-describedby="euro" required />
        <span class="input-group-addon">€/£</span>
      </div>
    </td>
    <td>
      <div class="input-group">
        <input type="number" min="1" max="1000" maxlength="4" size="4" name="gift_card_pack[0][quantity]" class="form-control" required />
      </div>
    </td>
    <td>
      <div class="input-group">
        <input type="number" min="1" max="2000" name="gift_card_pack[0][tickets]" class="form-control" aria-describedby="euro-price" required />
      </div>
    </td>
  </tr>
</template>
