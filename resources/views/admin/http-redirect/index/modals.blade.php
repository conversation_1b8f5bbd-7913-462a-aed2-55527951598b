<!-- Create Modal -->
<div class="modal fade" id="modal-store-http-redirect" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Create redirect</h3>
            </div>
            <div class="modal-body">
                <form id="form-store-http-redirect" action="{{ route('admin.http-redirects.store') }}" method="POST">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <label for="store-http-redirect-source-uri">Source :</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="source_uri-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="store-http-redirect-source-uri" name="source_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="store-http-redirect-target-uri">Target (exemples : /ateliers/le-bois, https://wecandoo.fr/ateliers/le-bois, https://un.external-link.fr/wecandoo/le-bois)</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="target_uri-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="store-http-redirect-target-uri" name="target_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="store-http-redirect-is-permanent">Permanent redirect (Yes if unsure) ?</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="is_permanent-error"></ul>
                        </div>
                        <div class="radio">
                            <label>
                                <input id="store-http-redirect-is-permanent-yes" type="radio" name="is_permanent" value="1">
                                Yes (HTTP {{\Illuminate\Http\Response::HTTP_MOVED_PERMANENTLY}})
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input id="store-http-redirect-is-permanent-no" type="radio" name="is_permanent" value="0" checked>
                                No (HTTP {{\Illuminate\Http\Response::HTTP_FOUND}})
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="store-http-redirect-comment">Comment</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="comment-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="store-http-redirect-comment" name="comment" value=""/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary" form="form-store-http-redirect">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="modal-update-http-redirect" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Update redirect</h3>
            </div>
            <div class="modal-body">
                <form id="form-update-http-redirect" action="" method="POST">
                    {{ csrf_field() }}
                    {{ method_field('PATCH') }}
                    <input type="hidden" value="" name="id"/>
                    <div class="form-group">
                        <label for="update-http-redirect-source-uri">Source :</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="source_uri-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="update-http-redirect-source-uri" name="source_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="update-http-redirect-target-uri">Target (exemples : /ateliers/le-bois, https://wecandoo.fr/ateliers/le-bois, https://un.external-link.fr/wecandoo/le-bois)</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="target_uri-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="update-http-redirect-target-uri" name="target_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="update-http-redirect-is-permanent">Permanent redirect (Yes if unsure) ?</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="is_permanent-error"></ul>
                        </div>
                        <div class="radio">
                            <label>
                                <input id="update-http-redirect-is-permanent-yes" type="radio" name="is_permanent" value="1">
                                  Yes (HTTP {{\Illuminate\Http\Response::HTTP_MOVED_PERMANENTLY}})
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input id="update-http-redirect-is-permanent-no" type="radio" name="is_permanent" value="0" checked>
                                No (HTTP {{\Illuminate\Http\Response::HTTP_FOUND}})
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="update-http-redirect-comment">Comment</label>
                        <div class="callout callout-danger hidden">
                            <ul class="m-0" id="comment-error"></ul>
                        </div>
                        <input type="text" class="form-control" id="update-http-redirect-comment" name="comment" value=""/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary" form="form-update-http-redirect">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="modal-delete-http-redirect" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Delete redirect</h3>
            </div>
            <div class="modal-body">
                <form id="form-delete-http-redirect" action="" method="POST">
                    {{ csrf_field() }}
                    {{ method_field('DELETE') }}
                    You're about to delete this redirect
                    <div class="form-group">
                        <label for="delete-http-redirect-source-uri">Source :</label>
                        <input readonly type="text" class="form-control" id="delete-http-redirect-source-uri" name="source_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="delete-http-redirect-target-uri">Target (exemples : /ateliers/le-bois, https://wecandoo.fr/ateliers/le-bois, https://un.external-link.fr/wecandoo/le-bois) </label>
                        <input readonly type="text" class="form-control" id="delete-http-redirect-target-uri" name="target_uri" value=""/>
                    </div>
                    <div class="form-group">
                        <label for="delete-http-redirect-is-permanent">Permanent redirect (Yes if unsure) ?</label>
                        <div class="radio">
                            <label>
                                <input readonly id="delete-http-redirect-is-permanent-yes" type="radio" name="is_permanent" value="1">
                                  Yes (HTTP {{\Illuminate\Http\Response::HTTP_MOVED_PERMANENTLY}})
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input readonly id="delete-http-redirect-is-permanent-no" type="radio" name="is_permanent" value="0" checked>
                                  No (HTTP {{\Illuminate\Http\Response::HTTP_FOUND}})
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="delete-http-redirect-comment">Comment</label>
                        <input readonly type="text" class="form-control" id="delete-http-redirect-comment" name="comment" value=""/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Cacnel</button>
                <button type="submit" class="btn btn-danger" form="form-delete-http-redirect">Save</button>
            </div>
        </div>
    </div>
</div>
