@extends('admin.layouts._admin_layout', ['page_title' => isset($page) && $page ? 'Editer la page ' . $page->slug : 'Création des pages', 'page_description' => 'Création des pages'])

@push('css')
    <link href="/css/plugins/trumbowyg/trumbowyg.min.css" rel="stylesheet">
@endpush

@section('content')
    <pages-creator
            :subjects="{{json_encode($subjects)}}"
            :defaults="{{json_encode($defaults)}}"
            @if (!empty($pageAsProps))
                :page="{{json_encode($pageAsProps)}}"
            @endif
            @if (!empty(session()->getOldInput()))
                :old-values="{{json_encode(session()->getOldInput())}}"
            @endif
            @if (!empty($errors) && $errors->any())
                :errors="{{json_encode($errors->getMessages())}}"
            @endif
            edit-page-route="{{ route('admin.single-pages.edit', ['singlePage' => ':id']) }}"
            :locales="{{json_encode(app('translatable.locales')->all())}}"
            default-locale="{{app('translatable.locales')->current()}}"
            route="{{ isset($page) ? route('admin.single-pages.update', $page) : route('admin.single-pages.store') }}"
            :collections="{{json_encode($collections)}}"
    />
@stop

@push('scripts_for_vue')
    <script src="/js/plugins/trumbowyg/trumbowyg.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script>
        /* Dirty but it's the only way to get browser loaded jquery and not the one imported in VueJs */
        function callJQuery(callback) {
            callback($);
        }
    </script>
@endpush
