<div class="modal fade" id="modal-update-onboarding" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Editer le suivi onboarding</h3>
            </div>
            <div class="modal-body">
                <form action="{{route('admin.onboarding.update')}}" method="POST"
                      id="form-update-onboarding" name="form_update_onboarding">
                    {{ csrf_field() }}
                    <input type="hidden" name="onboarding_id" id="onboarding-id" value=""/>
                    <div class="form-group">
                        <label for="media_status">Statut du media</label>
                        <select class="form-control" name="media_status" required id="media-status">
                            @foreach (\App\Domain\Content\Enums\ContentMediaStatus::toArray() as $key => $value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="media_source">Source du media</label>
                        <select class="form-control" name="media_source" required id="media-source">
                            @foreach (\App\Domain\Content\Enums\MediaSources::toArray() as $key => $value)
                                <option value="{{$key}}">{{$value}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="media_notes">Notes du media</label>
                        <textarea name="media_notes" id="media-notes" class="form-control">
                        </textarea>
                    </div>
                    <div class="form-group">
                        <label for="priority">Onboarding prioritaire ?</label>
                        <input type="checkbox" name="priority" value="1" id="priority"
                               class="form-control checkbox-toggle" data-toggle="toggle" data-size="mini"/>
                    </div>
                    <div class="form-group" id="block-submit-placeholder">
                        <button class="btn btn-primary" id='update-onboarding-btn'>Enregistrer</button>
                    </div>
                    <div class="form-group block-info bg-warning" id="block-submit-validation">
                        <p class="text-muted">
                            ⚠️ Etes-vous sur de vouloir modifier ces informations d'onboarding ?
                        </p>
                        <input type="reset" class="btn btn-danger" id="btn-cancel-update" value="Annuler"/>
                        <button type="submit" data-form-id="form-update-onboarding"
                                class="btn btn-primary btn-form-validation"
                        >Valider
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

//Relance Modal
<div class="modal fade" id="modal-reminder-onboarding" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Relancer l'artisan</h3>
            </div>
            <div class="modal-body">
                <form action="{{route('admin.api.onboarding.reminder')}}" method="post"
                      id="form-reminder-onboarding" name="form_reminder_onboarding">
                    {{ csrf_field() }}
                    <input type="hidden" name="artisan_email" id="artisan-email-reminder" value=""/>
                    <div class="form-group block-info bg-warning">
                        <p class="text-muted">
                            ⚠️ Etes-vous sur de vouloir relancer l'artisan pour son onboarding ?
                        </p>
                        <input type="reset" class="btn btn-danger" id="btn-cancel-reminder" value="Annuler"/>
                        <button type="submit" data-form-id="form-reminder-onboarding"
                                class="btn btn-primary btn-form-validation"
                        >Valider
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

// Delete Modal
<div class="modal fade" id="modal-delete-onboarding" role="dialog">
    <div id="modal" class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h3 class="modal-title">Suppression d'un onboarding</h3>
            </div>
            <div class="modal-body">
                <p class="text-danger text-bold">Attention : la suppression est irréversible.</p>
                <p>Toutes les informations saisies lors de la création de l'onboarding seront perdues. Vous ne pourrez pas retrouver cet onboarding dans le back-office.</p>
                <ul>
                    <li><span class="text-bold">Artisan : </span><span id="artisan-name"></span></li>
                    <li><span class="text-bold">Date de début d'onboarding : </span><span id="begin"></span></li>
                    <li><span class="text-bold">Responsable : </span><span id="accountable"></span></li>
                </ul>
            </div>
            <div class="modal-footer">
                <form action="{{route('admin.onboarding.delete')}}" method="post"
                      id="form-delete-onboarding" name="form_delete_onboarding">
                    {{ csrf_field() }}
                    <input type="hidden" name="artisan-id" id="artisan-id" value=""/>
                    <div class="form-group">
                        <input type="reset" class="btn btn-danger" id="btn-cancel-delete" value="Annuler la suppression"/>
                        <button type="submit" data-form-id="form-delete-onboarding"
                                class="btn btn-primary btn-form-validation"
                        >Confirmer et supprimer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
