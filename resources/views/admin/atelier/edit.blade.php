@extends('admin.layouts._admin_layout', ['page_title' => 'Editer une page atelier','page_description' => $atelier->nom . ' - <a class="artisan-country-flag" href="' . route('admin.artisans.edit', ['artisan' => $atelier->artisan->id]) . '">' . $atelier->artisan->getDenomination() . ' ' . \App\Helpers\CountryHelper::getFlagOrCountryCode($atelier->artisan->getCountryCode()) . '</a>', 'niveau1' => 'Edition atelier'])


@push('css')
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.9/css/bootstrap-select.min.css">
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-slider/10.6.2/css/bootstrap-slider.min.css">
    <link href="{{ safeMix('/css/plugins/trumbowyg/trumbowyg.min.css') }}" rel="stylesheet">
    <link href="https://gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css" rel="stylesheet">
    <style>
        .dropzone {
            border: 2px dashed #337ab7;
            border-radius: 5px;
        }

        .image-upload-preview {
            max-height: 400px;
            overflow: scroll;
        }

        .kv-file-content {
            height: fit-content !important;
        }

        .file-thumbnail-footer {
            margin-top: -40px;
            background-color: rgba(255, 255, 255, 0.5);
            height: fit-content !important;
        }

        .file-thumbnail-footer .file-footer-caption {
            color: black;
            margin-bottom: 0;
        }

        .img-list-thumb {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }

        .datepicker {
            z-index: 1600 !important;
        }

        .block-info {
            padding: 10px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .tooltip-cs {
            position: relative;
            display: inline-block;
        }

        /* Tooltip text */
        .tooltip-cs .tooltiptext-cs {
            visibility: hidden;
            width: 120px;
            background-color: black;
            color: #fff;
            text-align: center;
            padding: 5px 0;
            border-radius: 6px;

            /* Position the tooltip text - see examples below! */
            position: absolute;
            z-index: 1;
        }

        .tooltip-cs .bottom-tooltiptext-cs {
            top: 100%;
            left: 50%;
            margin-left: -60px;
        }

        .tooltip-cs .top-tooltiptext-cs {
            top: -100%;
            left: 50%;
            margin-left: -60px;
        }

        /* Show the tooltip text when you mouse over the tooltip container */
        .tooltip-cs:hover .tooltiptext-cs {
            visibility: visible;
        }

        .tab-pane {
            background-color: white;
            border: 1px solid #ddd;
            border-top-color: transparent;
        }

        .fluid-content {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
        }

        .fluid-content .item {
            display: inline-block;
        }

        .progress-bar-low-warning {
            background-color: #f1c40f;
        }

        .d-none {
            display: none;
        }

        .d-block {
            display: block;
        }

        .text-warning {
            color: #f39c12 !important;
        }

        span.grippy {
            content: '....';
            width: 10px;
            height: 20px;
            display: inline-block;
            overflow: hidden;
            line-height: 5px;
            padding: 3px 4px;
            cursor: move;
            vertical-align: middle;
            margin-top: -.7em;
            margin-right: .3em;
            font-size: 12px;
            font-family: sans-serif;
            letter-spacing: 2px;
            color: #cccccc;
            text-shadow: 1px 0 1px black;
        }

        span.grippy::after {
            content: '.. .. .. ..';
        }

        .image-list {
            position: relative;
        }

        .image-list .actions {
            position: absolute;
            bottom: 5px;
            right: 5px;
        }
    </style>
@endpush

@section('content')
    {{-- Bottom action bar --}}
    <nav class="navbar navbar-default navbar-fixed-bottom">
        <div class="container">
            <button class="btn navbar-btn btn-primary navbar-right margin-x-md"
                    onclick="event.preventDefault();window.location='{{url('/wecanadmin/ateliers')}}'">
                <i class="fas fa-door-closed" aria-hidden="true"></i>
                Quitter
            </button>
            <button class="btn navbar-btn navbar-right btn-primary margin-x-md" id="saveButton">
                <i class="fas fa-save margin-right-sm" aria-hidden="true"></i>Enregistrer
            </button>
            @if($computedData['highlight'])
                <button class="btn navbar-btn btn-danger navbar-right  margin-x-md"
                        onclick="event.preventDefault();window.location='{{url('/wecanadmin/ateliers-misenavant/'.$atelier->id.'')}}'">
                    <i class="fas fa-calendar-times margin-right-sm" aria-hidden="true"></i>Retirer la mise en avant
                </button>
            @else
                <button id="miseEnAvant" class="btn navbar-btn btn-info navbar-right  margin-x-md"><i class="fas fa-sun"
                                                                                                      aria-hidden="true"></i>
                    Mettre en avant
                </button>
            @endif
            <a href="{{route('admin.calendriers.index', ['artisan' => $atelier->artisan->id])}}"
               class="btn navbar-btn btn-primary navbar-right margin-x-md"
            >
                <i class="fas fa-calendar margin-right-sm" aria-hidden="true"></i>Voir le calendrier de l'artisan
            </a>
            <a
              id="btn-stock-transfer"
              class="btn navbar-btn navbar-right btn-warning margin-x-md"
              href="{{ route('admin.ateliers.stock-transfer', $atelier) }}"
              target="_blank"
            >
                <i class="fas fa-arrows-alt-v"></i>
                Stock transfer
            </a>
        </div>
    </nav>

    {{-- Mirakl sync form located on first tab --}}
    <form id="mirakl_create" action="{{ route('admin.ateliers.mirakl.create', $atelier) }}" method="POST">
      @csrf
    </form>
    {{-- Mirakl sync form located on first tab --}}

    {{-- Mirakl deactivation form located on first tab --}}
    <form id="mirakl_deactivate" action="{{ route('admin.ateliers.mirakl.deactivate', $atelier) }}" method="POST">
      @csrf
    </form>
    {{-- Mirakl deactivation form located on first tab --}}

    {{-- Mirakl reactivation form located on first tab --}}
    <form id="mirakl_reactivate" action="{{ route('admin.ateliers.mirakl.reactivate', $atelier) }}" method="POST">
      @csrf
    </form>
    {{-- Mirakl reactivation form located on first tab --}}

    {{ html()->modelForm($atelier, 'PUT', route('admin.ateliers.update', [$atelier->id]))->acceptsFiles()->id('workshopForm')->acceptsFiles()->open() }}
    <input type="hidden" name="atelier_id" value="{{$atelier->id}}">
    <input type="hidden" id="currentTab" name="currentTab" value="#info">
    {{-- Status bar --}}
    <div>
        <div class="well well-sm clearfix full-width">
            <span>Statut : </span>
            <x-admin.status-display :flags="[
                'overloaded'=> $atelier->est_surcharge,
                'invisible'=> !$atelier->isVisible(),
                'inactive' => !$atelier->active,
                'highlighted' => $computedData['highlight'],
                'offline' => !$atelier->online_at
                ]"/>
            <h3 class="wcd-no-margin pull-right">
                @if($computedData['highlight'])
                    <span class="label label-danger">Mis en avant du
                {{dateShortFormat($atelier->debut_mise_en_avant)}}
                au
                {{dateShortFormat($atelier->fin_mise_en_avant)}}
            </span>
                @endif
            </h3>
        </div>
        <div>
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#info" aria-controls="info" role="tab" data-toggle="tab">
                        <i class="fas fa-info-circle margin-x-sm" aria-hidden="true"></i>
                        Informations
                        <span id="error-badge-info" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#craft" aria-controls="craft" role="tab" data-toggle="tab">
                        <i class="fas fa-hashtag margin-x-sm" aria-hidden="true"></i>
                        Attributs et Tags
                        <span id="error-badge-craft" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#location" aria-controls="location" role="tab" data-toggle="tab">
                        <i class="fas fa-map-marked-alt margin-x-sm" aria-hidden="true"></i>
                        Lieu
                        <span id="error-badge-location" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#tarif" aria-controls="tarif" role="tab" data-toggle="tab">
                        <i class="fas fa-tag margin-x-sm" aria-hidden="true"></i>
                        Offres & Tarification
                        <span id="error-badge-tarif" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#stock" aria-controls="stock" role="tab" data-toggle="tab">
                        <i class="fas fa-boxes margin-x-sm" aria-hidden="true"></i>
                        Gestion des stocks
                        <span id="error-badge-stock" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#content" aria-controls="content" role="tab" data-toggle="tab">
                        <i class="fas fa-file-image margin-x-sm" aria-hidden="true"></i>
                        Gestion des Contenus
                        <span id="error-badge-content" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#media" aria-controls="media" role="tab" data-toggle="tab">
                        <i class="fas fa-photo-video margin-x-sm" aria-hidden="true"></i>
                        Média
                        <span id="error-badge-media" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
                <li role="presentation">
                    <a href="#summary-sheet" aria-controls="summary-sheet" role="tab" data-toggle="tab">
                        <i class="fas fa-file margin-x-sm" aria-hidden="true"></i>
                        Fiche récap
                    </a>
                </li>
                <li role="presentation">
                    <a href="#history" aria-controls="history" role="tab" data-toggle="tab">
                        <i class="fas fa-history margin-x-sm" aria-hidden="true"></i>
                        Historique
                        <span id="error-badge-history" class="hidden label label-pill label-danger"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Il y a des erreurs à corriger sur cet onglet"></span>
                    </a>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content full-width">
                <!-- Info generale -->
                @include('admin.atelier.edit.tabs.informations')
                <!-- end tab info general -->

                <!-- savoir-faire -->
                @include('admin.atelier.edit.tabs.crafts')
                <!-- end tab savoir-faire -->

                <!-- locations -->
                @include('admin.atelier.edit.tabs.location')
                <!-- end tab Locations -->

                <!-- Tarification & offes-->
                @include('admin.atelier.edit.tabs.pricing')
                <!-- end tab tarif et kidz -->

                <!-- gestion des stocks -->
                @include('admin.atelier.edit.tabs.stock')
                <!-- end tab gestion des stocks -->

                <!-- gestion des contenus -->
                @include('admin.atelier.edit.tabs.content')
                <!-- end tab gestion contenus -->

                <!-- gestion des media -->
                @include('admin.atelier.edit.tabs.media')
                <!-- end tab gestion media -->

                <!-- Summary sheets -->
                @include('admin.atelier.edit.tabs.summarysheet')

                <!-- history -->
                @include('admin.atelier.edit.tabs.history')
                <!-- end history -->

            </div>
            <!-- end tab content -->
        </div>
    </div>
    {{ html()->closeModelForm() }}
@stop

@push('modals')
    <div class="modal fade" id="modalTooMuchImages" role="dialog">
        <div id="modal" class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h3 class="modal-title">Erreur, trop d'images</h3>
                </div>
                <div class="modal-body">
                    <p>
                        Vous allez ajouter des images à l'atelier, mais celui-ci contient déjà le nombre maximum
                        d'images possible.<br>
                        Veuillez d'abord supprimer/dissocier des images avant d'en ajouter des nouvelles.
                    </p>

                    <p>
                        Pour rappel, la limite est fixée à
                        <strong>{{ \App\Http\Requests\Admin\EditAtelierFormRequest::MAX_IMAGES_COUNT }}</strong>.
                    </p>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modalImagesDeleteValidation" role="dialog">
        <div id="modal" class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h3 class="modal-title">Dissocier une ou plusieurs images</h3>
                </div>
                <div class="modal-body">
                    <p>Vous êtes sur le point de <b>dissocier</b> une ou plusieurs images de l'atelier, merci de
                        confirmer.
                        Vous pouvez aussi complètement
                        <b>supprimer</b> les images du site si elles ne sont plus utilisées.
                    </p>
                    <div class="form-group block-info" id="block-status-validation">
                        <p class="text-muted">Êtes-vous sûr de vouloir dissocier cette image ?</p>
                        <button class="btn btn-secandary" id="btn-cancel-status"
                                onclick="hideModalImagesDeleteValidation()">
                            Annuler
                        </button>
                        <button class="btn btn-warning btn-form-validation margin-x-sm"
                                onclick="submitFormWorkshop(false)">
                            <span class="fa-stack" style="line-height:1.2em; height: 1.2em;">
                              <i class="far fa-image fa-stack-1x"></i>
                              <i class="fas fa-slash fa-stack-1x"></i>
                            </span> Dissocier
                        </button>
                        <button class="btn btn-danger btn-form-validation margin-x-sm pull-right"
                                onclick="submitFormWorkshop(true)">
                            <i class="fas fa-trash-alt"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modalPutOnline" role="dialog" reason="">
        <div class="modal-dialog" style="width: 900px;">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Vous souhaitez mettre en ligne cet atelier ?</h4>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        @if ($atelier->artisan->isOnboard() && !$atelier->artisan->active )
                            <p>
                                Cet atelier est relié à un artisan n'étant pas encore actif/ayant été désactivé.<br/>
                                Si vous cliquez sur "Forcer la mise en ligne" l'atelier ainsi que l'artisan relié à ce
                                premier seront tous deux mis en ligne.<br/><br/>
                                <b>Veillez à vérifier que l'artisan relié à cet atelier possède toutes les informations
                                    nécessaires à cette mise en ligne.</b>
                            </p>
                            <br><br>
                        @endif
                        @if (count($computedData['errorsToCorrect']) == 0)
                            <p>
                                Cet atelier possède des dates dans le futur.<br>
                                La photo de sa vignette, ainsi que ses propres photos sont présentes.<br>
                                La punchline de la vignette est défini.<br>
                                L'artisan relié possède une photo de profil.
                            </p>
                        @else
                            <p>
                                Cette atelier possède des informations manquantes :
                            </p>
                            <ul>
                                @foreach($computedData['errorsToCorrect'] as $error)
                                    <li style="color:red;">
                                        <b>{{ $error }}</b>
                                    </li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                </div>
                <div class="modal-footer" style="text-align: center">
                    <button
                            id="putOnlineModal"
                            class="btn btn-primary">
                        @if (count($computedData['errorsToCorrect']) == 0)
                            Mettre en ligne
                        @else
                            Forcer la mise en ligne
                        @endif
                    </button>
                    <button id="closePutOnlineModal" class="btn btn-danger">Annuler</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modalMiseEnAvant" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Mettre en avant l'atelier dans la recherche</h4>
                </div>
                <div class="modal-body">
                    <form id="miseenavant-form" action="{{ route('admin.ateliers-misenavant.create') }}" method="POST">
                        {{ csrf_field() }}
                        <input type="hidden" name="atelier_id" value="{{$atelier->id}}">
                        <div class="container-fluid">
                            <div class="row">
                                <div id="container-datepicker-miseenavant-debut"
                                     class="form-group col-xs-12 {!! $errors->has('debut_mise_en_avant') ? 'has-error' : '' !!}">
                                    <span style="color: #FF5851;"><sup>*</sup></span>&nbsp;{{ html()->label('Début de la mise en avant', 'debut_mise_en_avant') }}
                                    <div class="input-group date">
                                    <span class="input-group-addon"
                                          style="border-radius: 4px 0 0 4px; background-color:#eee;">
                                        <i class="fas fa-btn fa-calendar"></i></span>
                                        {{ html()->date('debut_mise_en_avant')->class('form-control') }}
                                    </div>
                                    {!! $errors->first('debut_mise_en_avant', '<small class="help-block">:message</small>')
                                    !!}
                                </div>
                                <div id="container-datepicker-miseenavant-fin"
                                     class="form-group col-xs-12 {!! $errors->has('fin_mise_en_avant') ? 'has-error' : '' !!}">
                                    <span style="color: #FF5851;"><sup>*</sup></span>&nbsp;{{ html()->label('Fin de la mise en avant', 'fin_mise_en_avant') }}
                                    <div class="input-group date">
                                    <span class="input-group-addon"
                                          style="border-radius: 4px 0 0 4px; background-color:#eee;">
                                        <i class="fa fa-btn fa-calendar"></i></span>
                                        {{ html()->date('fin_mise_en_avant')->class('form-control') }}
                                    </div>
                                    {!! $errors->first('fin_mise_en_avant', '<small class="help-block">:message</small>')
                                    !!}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button id="mettreenavant" type="submit" class="btn btn-primary">Mettre en avant</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="modal-update-capacity-validation" role="dialog">
        <div id="modal" class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h3 class="modal-title">Mettre à jour la capacité</h3>
                </div>
                <div class="modal-body">
                    <p>⚠️ les évènements futurs du calendrier qui ont déjà un minimum/maximum de participants
                        différents du minimum/maximum de l'atelier ne seront pas modifiés. Tous les autres évènements du
                        calendrier de l'artisan seront modifiés avec le nouveau minimum/maximum de participants.
                    </p>
                    <div class="form-group block-info">
                        <p class="text-muted">Etes vous sur de vouloir modifier la capacité ?</p>
                        <button class="btn btn-secandary" data-dismiss="modal">Annuler</button>
                        <button class="btn btn-success margin-x-sm"
                                id="btn-capacity-update-validated"
                                data-capacity="test"
                                data-type=""
                                onclick="">
                            Confirmer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('admin.summary-sheet.modals')
@endpush

@section('script')
    <script src="{{ safeMix("js/wecandoo-admin.js") }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.9/js/bootstrap-select.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-slider/10.6.2/bootstrap-slider.min.js"></script>
    <script src="{{ safeMix('/js/plugins/trumbowyg/trumbowyg.min.js') }}"></script>
    <script src="https://gitcdn.github.io/bootstrap-toggle/2.2.2/js/bootstrap-toggle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/piexifjs@1.0.6/piexif.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.5.2/js/fileinput.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.5.2/js/locales/fr.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-fileinput/5.5.2/themes/fas/theme.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js"></script>
    <script type="text/javascript">
        $('#saveButton').on('click', function () {
            let totalImages = $(document).find('#images-list .image-list').length;
            let totalImagesToRemove = $(document).find('input[name="image_delete[]"]:checked').length;
            let totalImagesToUpload = $(document).find('#workshop-image-upload-part .kv-preview-thumb').length;

            if ((totalImages - totalImagesToRemove + totalImagesToUpload) > {{ \App\Http\Requests\Admin\EditAtelierFormRequest::MAX_IMAGES_COUNT }}) {
                $('#modalTooMuchImages').modal('toggle');

                return;
            }

            if ($(document).find('input[name="image_delete[]"]').is(':checked')) {
                $('#modalImagesDeleteValidation').modal('toggle');

                return;
            }

            $('#workshopForm').submit();
        });

        //Begin sortable images script
        const el = document.getElementById("images-list");
        const sortable = Sortable.create(el);
        //End sortable images script

        //Begin image file upload script
        $("#workshop-images").fileinput({
            theme: 'fas',
            allowedFileExtensions: ['jpg', 'jpeg', 'png'],
            language: 'fr',
            previewClass: 'image-upload-preview',
            showUpload: false,
            showCaption: false,
            browseOnZoneClick: true,
            autoReplace: false,
            showClose: false,
            showBrowse: false,
            dropZoneTitle: "Déposez vos images ",
            dropZoneClickTitle: "ou cliquez ici",
            fileActionSettings: {
                showZoom: false,
            },
            minImageWidth: {{ \App\Infrastructure\Image\ImageOptimizer::MIN_SIZE }},
            minImageHeight: {{ \App\Infrastructure\Image\ImageOptimizer::MIN_SIZE }},
            maxFileCount: {{ \App\Http\Requests\Admin\EditAtelierFormRequest::MAX_IMAGES_COUNT }},
            autoOrientImage: false,
        });
        $("#workshop-vignette").fileinput({
            theme: 'fas',
            allowedFileExtensions: ['jpg', 'jpeg', 'png'],
            overwriteInitial: true,
            language: 'fr',
            showUpload: false,
            showCaption: false,
            browseOnZoneClick: true,
            autoReplace: true,
            showClose: false,
            showBrowse: false,
            dropZoneTitle: "Déposez la vignette ",
            dropZoneClickTitle: "ou cliquez ici",
            fileActionSettings: {
                showZoom: false
            },
            minImageWidth: {{ \App\Infrastructure\Image\ImageOptimizer::MIN_SIZE }},
            minImageHeight: {{ \App\Infrastructure\Image\ImageOptimizer::MIN_SIZE }},
            maxFileCount: 1,
            autoOrientImage: false,
        });
        //End image file upload script

        //Begin remove image process with validation
        $(document).on('click', '#delete-all-images', function (e) {
            e.preventDefault();
            e.stopPropagation();

            let $imageDelete = $(document).find('.image-delete');
            $imageDelete.find('input').prop('checked', true);
            $imageDelete.addClass('btn-danger').removeClass('btn-default');
        });

        $(document).on('click', '.image-delete', function () {
            if ($(this).find('input').is(':checked')) {
                $(this).addClass('btn-danger').removeClass('btn-default');
            } else {
                $(this).addClass('btn-default').removeClass('btn-danger');
            }
        });

        function submitFormWorkshop(force) {
            $(document).find('#images_hard').val(force === true ? '1' : '0');
            $('#modalImagesDeleteValidation').modal('toggle');
            $('#workshopForm').submit();
        }

        function hideModalImagesDeleteValidation() {
            $('#modalImagesDeleteValidation').modal('toggle');
        }

        //End remove image process with validation

        $('#ex2-privat').on('slide', function (e) {
            $('#pers-min-privat').text(e.value[0]);
            $('#pers-max-privat').text(e.value[1]);
            $('#nb-pers-min-privat').val(e.value[0]);
            $('#nb-pers-max-privat').val(e.value[1]);
        });

        addEventOnReplacementWorkshopTrash();
        $('body').on('DOMSubtreeModified', '#replacementWorkshopContainer', function () {
            addEventOnReplacementWorkshopTrash();
            console.log('replacementWorkshopContainer changed');
        });

        $('#btn-add-replacementWorkshop').on('click', function (e) {
            e.preventDefault();
            var id = {{$atelier->id}};
            var replacementId = $('#replacementWorkshop').val();
            var ajaxurl = '{{route('admin.ateliers-replacement.add', [':workshop', ':replacementWorkshop'])}}';
            ajaxurl = ajaxurl.replace(':workshop', id);
            ajaxurl = ajaxurl.replace(':replacementWorkshop', replacementId);
            if ($('#replacementWorkshop').val() != 'choix' && $('#replacementWorkshop').val() != null) {
                axios.post(ajaxurl, {})
                    .then(function (response) {
                        $("#replacementWorkshopContainer").html(response.data);
                        $("#replacementWorkshop :selected").remove();
                        $("#replacementWorkshop").selectpicker('refresh');
                    })
                    .catch(function (error) {
                            console.log(error);
                        }
                    );
                addAltWorkshop(id, replacementId).then(() => {
                    addAltWorkshop(replacementId, id);
                });
            }
        });

        function addEventOnReplacementWorkshopTrash() {
            $('.replacement-workshop-trash').on('click', function (e) {
                e.preventDefault();
                var id = {{$atelier->id}};
                var workshopId = e.currentTarget.dataset.workshopid;
                var workshopName = e.currentTarget.dataset.workshop;
                var artisan = e.currentTarget.dataset.artisan;
                var isOverloaded = e.currentTarget.dataset.overloaded;
                var isVisible = e.currentTarget.dataset.visible;
                var ajaxurl = '{{route('admin.ateliers-replacement.remove', [':workshop', ':replacementWorkshop'])}}';
                ajaxurl = ajaxurl.replace(':workshop', id);
                ajaxurl = ajaxurl.replace(':replacementWorkshop', workshopId);
                axios.get(ajaxurl, {})
                    .then((response) => {
                        let option = addOption(workshopId, workshopName, artisan, isOverloaded, isVisible);
                        $('#replacementWorkshopContainer').html(response.data);
                        $('#replacementWorkshop').append(option);
                        $('#replacementWorkshop').selectpicker('refresh');
                    })
                    .catch(function (error) {
                            console.log(error);
                        }
                    );
            });
        };

        function addOption(id, workshopName, artisan, isOverloaded, isVisible) {
            let option = `<option`;
            if (isOverloaded == 1) {
                option += ` data-icon="fas fa-bolt" style="color:red;"`;
            }
            if (isVisible == 1) {
                option += ` data-icon="fas fa-low-vision" style="color:darkorange;"`;
            }
            option += ` data-tokens="${workshopName} ${artisan}"`;
            option += ` value="${id}"`;
            option += ` data-subtext="${artisan}">`;
            option += `${workshopName}</option>`;

            return option;
        }

        $(document).ready(function () {
            $tab = window.location.hash;
            if ($tab != null) {
                $('a[href="' + $tab + '"]').tab('show');
            }
            $('#replacementWorkshop').selectpicker({
                dropdownAlignRight: 'auto',
                dropupAuto: true,
                liveSearch: true,
                width: '300px',
                size: 15
            });
        });
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            var target = $(e.target).attr("href");
            $('#currentTab').val(target);
        });
    </script>
    <script>
        $(function () {
            $('.select2').select2({width: '100%'});
        });
        $(function () {
            $('.checkbox-toggle').bootstrapToggle();
        });
    </script>
    <script>
        $('#putOnline').on('click', function (e) {
            e.preventDefault();
            $('#modalPutOnline').modal('toggle');
        });
        $('#closePutOnlineModal').on('click', function (e) {
            e.preventDefault();
            $('#modalPutOnline').modal('hide');
        });
        $('#putOnlineModal').on('click', function (e) {
            e.preventDefault();
            window.location = "{{url('wecanadmin/ateliers/'.$atelier->id.'/put-online/')}}";
        });
        $('#setVisible').on('click', function (e) {
            e.preventDefault();
            window.location = "{{url('wecanadmin/ateliers/'.$atelier->id.'/set-visible/1')}}";
        });
        $('#setInvisible').on('click', function (e) {
            e.preventDefault();
            window.location = "{{url('wecanadmin/ateliers/'.$atelier->id.'/set-visible/0')}}";
        });
        $('#setActive').on('click', function (e) {
            e.preventDefault();
            window.location = "{{url('wecanadmin/ateliers/'.$atelier->id.'/set-active/1')}}";
        });
        $('#setInactive').on('click', function (e) {
            e.preventDefault();
            window.location = "{{url('wecanadmin/ateliers/'.$atelier->id.'/set-active/0')}}";
        });
    </script>
    <script>
        $('#miseEnAvant').on('click', function (e) {
            e.preventDefault();
            $('#modalMiseEnAvant').modal('toggle');
        });
    </script>
    <script>
        $('#mettreenavant').on('click', function (e) {
            e.preventDefault();
            document.getElementById('miseenavant-form').submit();
            $('#modalMiseEnAvant').modal('hide');
            $.LoadingOverlay("show");
        });
    </script>
    <script>

        $('#city-input').on('input', function () {
            let city = $(this).val();
            let district = $('#district-input').val();
            let innerHtml = city + ', ' + district;
            $('#location-helper').html(innerHtml);
        });
        $('#district-input').on('input', function () {
            let city = $('#city-input').val();
            let district = $(this).val();
            let innerHtml = city + ', ' + district;
            $('#location-helper').html(innerHtml);
        });

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });

        $('#pedagogie-prix-1').on('input', function () {
            $('#pedagogie-prix-1-view').text($(this).val());
        });
        $('#pedagogie-prix-2').on('input', function () {
            $('#pedagogie-prix-2-view').text($(this).val());
        });
        $('#pedagogie-prix-3').on('input', function () {
            $('#pedagogie-prix-3-view').text($(this).val());
        });

        // stock management checking
        function willBeOverloaded(maxGiftNumber, giftCodeNumber) {
            let stockUsed = (giftCodeNumber / maxGiftNumber);
            if (stockUsed > 0.9) {
                return true;
            }
            return false;
        }

        function sendAlert(message) {
            $('#stockAlert-text').text(message);
            $('#stockAlert').removeClass('d-none');
            setTimeout(function () {
                $('#stockAlert').addClass('d-none');
            }, 8000);
        }

        $('#isOverloaded').on('input', function () {
            if ($(this).is(':checked') && !willBeOverloaded($('#maxGiftStock').val(), $('#giftCodeNumber').text())) {
                sendAlert("Attention : Le stock est suffisant pour ne pas déclarer l'atelier en surcharge, " +
                    "mettez à jour le stock sinon l'atelier ne restera pas surchargé à la prochaine màj.");
            }
            if (!$(this).is(':checked') && willBeOverloaded($('#maxGiftStock').val(), $('#giftCodeNumber').text())) {
                sendAlert("Attention : Le stock est insuffisant pour retirer la surcharge, " +
                    "mettez à jour le stock sinon l'atelier rebasculera en surcharge à la prochaine màj.");
            }
        });
        $('#maxGiftStock').on('input', function () {
            if ($('#isOverloaded').is(':checked') && !willBeOverloaded($(this).val(), $('#giftCodeNumber').text())) {
                sendAlert("Attention : Le stock est suffisant pour ne pas déclarer l'atelier en surcharge, " +
                    "la surcharge va être retirée à la prochaine màj.");
            }
            if (!$('#isOverloaded').is(':checked') && willBeOverloaded($(this).val(), $('#giftCodeNumber').text())) {
                sendAlert("Attention : Le stock est insuffisant pour ne pas déclarer l'atelier en surcharge, " +
                    "il sera indiqué comme surchargé à la prochaine màj.");
            }
        });

        $('input[name=is_online]').change(function () {
            if ($(this).prop('checked')) {
                setOnlineOn();
            } else {
                setOnlineOff();
            }
        });

        function setOnlineOn() {
            $('#online-workshop-panel .panel-body').show(1000);
            $('input[name=is_online_with_kit]').bootstrapToggle('enable');
            $('input[name=is_only_available_in_city]').bootstrapToggle('enable');
            $('input[name=online_url]').removeAttr('disabled');
            $('#online-workshop-panel').removeClass('panel-primary');
            $('#online-workshop-panel').addClass('panel-success');
        };

        function setOnlineOff() {
            $('input[name=is_online_with_kit]').bootstrapToggle('off');
            $('input[name=is_online_with_kit]').bootstrapToggle('disable');
            $('input[name=is_only_available_in_city]').bootstrapToggle('off');
            $('input[name=is_only_available_in_city]').bootstrapToggle('disable');
            $('input[name=online_url]').attr('disabled', 'disabled');
            $('#online-workshop-panel').addClass('panel-primary');
            $('#online-workshop-panel').removeClass('panel-success');
            $('#online-workshop-panel .panel-body').hide(1000);
        }

        $(function () {
            if ($('input[name=is_online]').prop('checked')) {
                setOnlineOn();
            } else {
                setOnlineOff();
            }
        });

        function updatePriceWithFormat() {
            let formatsInfo = {!! $computedData['formatsInfoJS'] !!};
            let formatId = $('select[name="format_id"] option:selected').val();
            $('.price-format-display').html($('input[name="prix"]').val() * formatsInfo[formatId]['priceFor']);
            $('#price-format-texte').html(' / ' + formatsInfo[formatId]['priceForTexte']);
        }

        $('select[name="format_id"]').on('change', function () {
            updatePriceWithFormat();
        })
        $('input[name="prix"]').on('change', function () {
            updatePriceWithFormat();
        })

        if ($('.has-error').length) {
            if ($('#info').find('.has-error').length) {
                $('#error-badge-info').html($('#info').find('.has-error').length);
                $('#error-badge-info').removeClass('hidden');
            }
            if ($('#craft').find('.has-error').length) {
                $('#error-badge-craft').html($('#info').find('.has-error').length);
                $('#error-badge-info').removeClass('hidden');
            }
            if ($('#location').find('.has-error').length) {
                $('#error-badge-location').html($('#info').find('.has-error').length);
                $('#error-badge-location').removeClass('hidden');
            }
            if ($('#tarif').find('.has-error').length) {
                $('#error-badge-tarif').html($('#info').find('.has-error').length);
                $('#error-badge-tarif').removeClass('hidden');
            }
            if ($('#stock').find('.has-error').length) {
                $('#error-badge-stock').html($('#info').find('.has-error').length);
                $('#error-badge-stock').removeClass('hidden');
            }
            if ($('#content').find('.has-error').length) {
                $('#error-badge-content').html($('#content').find('.has-error').length);
                $('#error-badge-content').removeClass('hidden');
            }
            if ($('#media').find('.has-error').length) {
                $('#error-badge-media').html($('#invoice').find('.has-error').length);
                $('#error-badge-media').removeClass('hidden');
            }
        }
    </Script>
    <script>
        //capacity script
        $('#capacity-min').on('change', function (e) {
            capacityAlert();
            $('#btn-capacity-min').removeAttr('disabled');
        });
        $('#capacity-max').on('change', function (e) {
            capacityAlert();
            $('#btn-capacity-max').removeAttr('disabled');
        });
        $('#btn-capacity-min').on('click', function (e) {
            e.preventDefault();
            if (!e.target.hasAttribute('disabled')) {
                let capacity = $('#capacity-min option').filter(":selected").val();
                $('#btn-capacity-update-validated').data('capacity', capacity);
                $('#btn-capacity-update-validated').data('type', "min");
                $('#modal-update-capacity-validation').modal('toggle');
            }
        })
        $('#btn-capacity-max').on('click', function (e) {
            e.preventDefault();
            if (!e.target.hasAttribute('disabled')) {
                let capacity = $('#capacity-max option').filter(":selected").val();
                $('#btn-capacity-update-validated').data('capacity', capacity);
                $('#btn-capacity-update-validated').data('type', "max");
                $('#modal-update-capacity-validation').modal('toggle');
            }
        })

        $('#btn-capacity-update-validated').on('click', function (e) {
            let capacity = $('#btn-capacity-update-validated').data('capacity');
            let type = $('#btn-capacity-update-validated').data('type');
            $.LoadingOverlay('show');
            updateCapacity(capacity, type);
        });

        function capacityAlert() {
            $('#capacity-alert').removeClass('d-none');
            setTimeout(function () {
                $('#capacity-alert').addClass('d-none');
            }, 8000);
        }

        function updateCapacity(capacity, type) {
            let ajaxUrl = '{{route('admin.ateliers.capacity.update', $atelier->id)}}';
            axios.post(ajaxUrl, {
                capacity: capacity,
                type: type,
                _token: "{{csrf_token()}}"
            }, {
                headers: {
                    Accept: 'application/json',
                    contentType: 'application/json'
                }
            })
                .then((response) => {
                    if (response.status === 200) {
                        console.log(response)
                        reloadTab('#tarif');
                    } else {
                        console.error(response.statusText);
                    }
                })
        }

        function reloadTab(tab) {
            location.href = '{{url()->current()}}' + tab;
            location.reload(true);
        }

    </script>

    @include('admin.summary-sheet.scripts')
@stop
