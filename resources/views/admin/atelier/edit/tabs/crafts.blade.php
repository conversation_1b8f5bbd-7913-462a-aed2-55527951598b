<div role="tabpanel" class="padding-y-sm tab-pane clearfix" id="craft">
  <!-- colonne 1 -->
  <div class="col-md-6 col-xs-12">
    <div class="panel panel-primary margin-y-md" style="padding:0px;">
      <div class="panel-heading">
        Attributs
      </div>
      <div class="panel-body">
        <div class="form-group {!! $errors->has('craft_id') ? 'has-error' : '' !!}">
          {{ html()->label('Savoir-faire de l\'atelier', 'craft_id') }}
          {{ html()->select('craft_id', \App\Domain\Content\Models\Craft::where('is_visible', true)->pluck('name', 'id'), $atelier->craft_id)->class('form-control') }}
          {!! $errors->first('craft_id', '<small
              class="help-block">:message</small>') !!}
        </div>
        <div class="form-group col-xs-12 {!! $errors->has('techniques') ? 'has-error' : '' !!}">
          {{ html()->label('Techniques de l\'atelier', 'techniques') }}
          <select name="techniques[]" class="form-control select2" multiple>
            @php($atelierTechniques = $atelier->techniques()->pluck('id')->toArray())
            @foreach(\App\Domain\Content\Models\Craft::whereHas('techniques')->with(['techniques'])->get() as $craft)
              <optgroup label="{{$craft->name}}">
                @foreach ($craft->techniques as $technique)
                  <option
                    value="{{$technique->id}}" {{in_array($technique->id, old("techniques") ?? $atelierTechniques) ? 'selected' : ''}}>{{$technique->name}}</option>
                @endforeach
              </optgroup>
            @endforeach
          </select>
          {!! $errors->first('techniques', '<small class="help-block">:message</small>') !!}
        </div>
        <div class="form-group col-xs-12 {!! $errors->has('creations') ? 'has-error' : '' !!}">
          {{ html()->label('Créations de l\'atelier', 'creations') }}
          {{ html()->multiselect('creations[]', \App\Domain\Content\Models\Creation::pluck('name', 'id'), $atelier->creations()->pluck('id'))->class('form-control select2') }}
          {!! $errors->first('creations', '<small class="help-block">:message</small>') !!}
        </div>
      </div>
    </div>
  </div>
  <!-- colonne 2 -->
  <div class="col-md-6 col-xs-12">
    <div class="panel panel-primary margin-y-md" style="padding:0px;">
      <div class="panel-heading">
        Tags
      </div>
      <div class="panel-body">
        <div class="form-group">
          {{ html()->label('Tags de l\'atelier', 'tags_checkboxes[]') }}<br/>
          @foreach(App\Models\Tag::pluck('name', 'id') as $tagId => $tagName)
            <div class="checkbox" style="display: inline-block !important;">
              <label>
                &nbsp;&nbsp;&nbsp;<input type="checkbox"
                                         id="inlineCheckboxTag{{ $tagId }}"
                                         name="tags_checkboxes[]"
                                         value="{{ $tagId }}"
                                         @if($atelier->tags->contains($tagId)) checked @endif>{{ $tagName }}
                &nbsp;&nbsp;&nbsp;|
              </label>
            </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</div>
