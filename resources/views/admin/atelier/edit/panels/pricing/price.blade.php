@php /** @var \App\Models\Atelier $atelier */@endphp
<div class="panel panel-primary margin-y-md" style="padding:0px;">
    <div class="panel-heading">
        Tarification
    </div>
    <div class="panel-body">
        {{-- TODO_REFACTO_FORMAT --}}
        @if(! $atelier->hasBeenBooked())
            <div class="form-group {!! $errors->has('format_id') ? 'has-error' : '' !!}">
                {{ html()->label('Format', 'format_id') }}
                {{ html()->select('format_id', $computedData['formats'], $atelier->format_id)->class('form-control select2 col-md-8') }}
                {!! $errors->first('format_id', '<small class="help-block">:message</small>') !!}
            </div>
        @else
            <input type="hidden" name="format_id" value="{{$atelier->format_id}}">
            <div class="well well-sm text-muted">
                Le format associé à cet atelier est : <b>{{$atelier->format->name}}</b>
                <br/>Vous ne pouvez pas le modifier car l'atelier a déjà eu des achats
                <br/>Sa modification pourrait engendrer des problèmes de facturation
                <br/>(Contacter le service technique pour plus d'informations)
            </div>
        @endif

        <div class="form-group {!! $errors->has('prix') ? 'has-error' : '' !!}">
            {{ html()->label('Prix par personne', 'prix') }}
            <div class="input-group">
                {{ html()->text('prix')->class('form-control')->attribute('placeholder', 'Prix de l\'atelier')->attribute('onwheel', 'blur(); return;') }}
                <span class="input-group-addon" style="border-radius: 0 4px 4px 0; background-color:#eee;">
                    {{ getCurrencySymbol($atelier->currency) }}
                </span>
            </div>
            {!! $errors->first('prix', '<small class="help-block">:message</small>') !!}
        </div>
        <div class="well well-sm bg-primary" style="background-color: #07ABD6;">
            Prix d'affichage sur la page atelier (fonction du format) :
            <b><span class="price-format-display">{{ currencyFormat($atelier->format->priceDisplay($atelier->getUnitPriceIncludeVat())) }}</span></b>
        </div>
        <div class="form-group col-md-6 {!! $errors->has('age_min') ? 'has-error' : '' !!}">
            {{ html()->label('Âge minimum pour participer à l\'atelier', 'age_min') }}
            <div class="input-group">
                {{ html()->number('age_min')->class('form-control')->attribute('onwheel', 'blur(); return;')->attribute('min', 3)->attribute('placeholder', 'Âge minimum pour participer à l\'atelier') }}
                <span class="input-group-addon"
                      style="border-radius: 0 4px 4px 0; background-color:#eee;"> Ans </span>
            </div>
            {!! $errors->first('age_min', '<small class="help-block">:message</small>') !!}
        </div>
        <div class="form-group col-md-6 {!! $errors->has('age_max') ? 'has-error' : '' !!}">
            {{ html()->label('Âge maximum pour participer à l\'atelier', 'age_max') }}
            <div class="input-group">
                {{ html()->number('age_max')->class('form-control')->attribute('onwheel', 'blur(); return;')->attribute('min', 3)->attribute('placeholder', 'Âge maximum pour participer à l\'atelier') }}
                <span class="input-group-addon"
                      style="border-radius: 0 4px 4px 0; background-color:#eee;"> Ans </span>
            </div>
            {!! $errors->first('age_max', '<small class="help-block">:message</small>') !!}
        </div>
        <div class="form-group">
            <input type="hidden" name="are_accompanists_allowed" value="0">
            {{ html()->label('Accepte les accompagnateurs ?', 'are_accompanists_allowed') }}
            @if ($atelier->are_accompanists_allowed)
                {{ html()->checkbox('are_accompanists_allowed', 1, 1)->class('col-sm-1') }}
            @else
                {{ html()->checkbox('are_accompanists_allowed', 0, 1)->class('col-sm-1') }}
            @endif
        </div>
        <div
                class="form-group">
            {{ html()->label('Si oui, combien d\'accompagnateurs maximum ?', 'nb_accompanists_max') }}
            {{ html()->select('nb_accompanists_max', options_range(0, 10)) }}
        </div>
        <div class="form-group">
            <input type="hidden" name="is_press_interested" value="0">
            {{ html()->label('Accepte d\'accueillir des influenceurs au sein de l\'atelier pour un tarif réduit ?', 'is_press_interested') }}
            @if ($atelier->is_press_interested)
                {{ html()->checkbox('is_press_interested', 1, 1)->class('col-sm-1') }}
            @else
                {{ html()->checkbox('is_press_interested', 0, 1)->class('col-sm-1') }}
            @endif
        </div>
        <div
                class="form-group">
            {{ html()->label('Si oui, combien de pourcentage de réduction maximum ?', 'possible_press_discount') }}
            {{ html()->select('possible_press_discount', options_range(0, 100)) }} %
        </div>
        <div
                class="form-group {!! $errors->has('nb_jours') ? 'has-error' : '' !!} {!! $errors->has('nb_heures') ? 'has-error' : '' !!} {!! $errors->has('nb_minutes') ? 'has-error' : '' !!}">
            {{ html()->label('Durée de l\'atelier', 'nb_heures') }}
            <div class="fluid-content">
                                    <span class="item">
                                        {{ html()->select('nb_jours', options_range(0, 60)) }}
                                        {{ html()->label('jours', 'nb_jours') }}
                                    </span>
                <span class="item">
                                        {{ html()->select('nb_heures', options_range(0, 15)) }}
                    {{ html()->label('heures', 'nb_heures') }}
                                    </span>
                <span class="item">
                                        {{ html()->select('nb_minutes', options_range(0, 60)) }}
                    {{ html()->label('minutes', 'nb_minutes') }}
                                    </span>
            </div>
            {!! $errors->first('nb_heures', '<small class="help-block">:message</small>') !!}
        </div>
        <div class="form-group">
            {{ html()->label('Ateliers cumulables (ateliers dont les places peuvent être cumulées en transverse quand les créneaux commencent en même temps) :', 'ateliers_cumulables_checkboxes[]') }}
            @foreach($atelier->getAteliersCumulablesPotentiels() as $atelierCumulable)
                <div class="checkbox">
                    <label>
                        <input type="checkbox"
                               id="inlineCheckbox{{ $atelierCumulable->id }}"
                               name="ateliers_cumulables_checkboxes[]"
                               value="{{ $atelierCumulable->id }}"
                               @if($atelier->isAtelierCumulable($atelierCumulable)) checked @endif>
                        {{ $atelierCumulable->nom }}
                    </label>
                </div>
            @endforeach
        </div>
    </div>
</div>
