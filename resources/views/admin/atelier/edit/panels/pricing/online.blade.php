<div id="online-workshop-panel"
     class="panel {{$atelier->is_online ? 'panel-success' : 'panel-primary'}} margin-y-md"
     style="padding:0;">
    <div class="panel-heading">
        Atelier en ligne
        <span class="pull-right">{{ html()->checkbox('is_online', $atelier->is_online, 1)->class('checkbox-toggle')->data('toggle', 'toggle')->data('onstyle', "success")->data('size', 'mini') }}</span>
    </div>
    <div class="panel-body">
        <div class="form-group col-xs-12 {!! $errors->has('is_online_with_kit') ? 'has-error' : '' !!}">
            {{ html()->checkbox('is_online_with_kit', $atelier->is_online_with_kit, 1)->class('checkbox-toggle')->data('toggle', 'toggle')->data('size', 'mini') }}
            {!! $errors->first('is_online_with_kit', '<small class="help-block">:message</small>') !!}
            {{ html()->label('Un kit est fourni ?', 'is_online_with_kit') }}
        </div>
        <div class="form-group col-xs-12 {!! $errors->has('is_only_available_in_city') ? 'has-error' : '' !!}">
            {{ html()->checkbox('is_only_available_in_city', $atelier->is_only_available_in_city, 1)->class('checkbox-toggle')->data('toggle', 'toggle')->data('size', 'mini') }}
            {!! $errors->first('is_only_available_in_city', '<small class="help-block">:message</small>') !!}
            {{ html()->label('Seulement disponible dans la ville de l\'atelier ?', 'is_only_available_in_city') }}
        </div>
        <div class="form-group {!! $errors->has('online_url') ? 'has-error' : '' !!}">
            {{ html()->label(' Url des information de l\'atelier en ligne (kit, ingrédients, adresse de visioconférence)', 'online_url') }}
            {!! $errors->first('online_url', '<small class="help-block">:message</small>') !!}
            {{ html()->text('online_url', $atelier->online_url)->class('form-control')->required() }}
        </div>
    </div>
</div>
