<x-admin.layouts.app>
  <x-common.page-header
    title="Artisan permalink"
    class="border-b border-primary"
  />


  <div class="text-center mb-4">
    <h1 class="text-xl font-semibold">{{$artisanTitle}}</h1>
  </div>

  <div class="mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
    <h1 class="text-1xl my-4">Update permalink</h1>
    <x-common.form.errors :errors="$errors"/>
    <x-common.alert.show-more class="mt-6 mb-4 md:mb-8">
      <x-slot:title>
        SEO Impact
      </x-slot:title>
      <x-slot:content>
        Changing an artisan permalink can impact its performance, you may create a redirection if it hasn't been updated for a while.
      </x-slot:content>
    </x-common.alert.show-more>
    <form method="post" action="{{ route('admin.artisan.permalink.update', $artisanId) }}">
      @method('PUT')
      @csrf
      <div class="md:flex items-center rounded">
        <div class="bg-gray-200 text-gray-500 px-4 py-2 rounded-l select-none">
          {{ $urlRoot }}
        </div>
        <input type="text"
               name="permalink"
               value="{{old('permalink', $artisanPermalink)}}"
               class="block w-full border-gray-300 @error('permalink') border-red-300 @enderror"
        />
      </div>
      <x-common.form.toggle
        name="addHttpRedirect"
        label="Automatically create a redirection with the previous url?"
        value="1"
        checked="{{$artisanRecentlyOnline === false}}"
      />
      <div>
        <label for="comment">Comment:</label>
        <x-common.form.textarea
          name="comment"
        >{{old('comment')}}</x-common.form.textarea>
      </div>
      <div class="mt-6">
        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
          Submit
        </button>
      </div>
    </form>
  </div>

  <div class="mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
    <h1 class="text-2xl my-4">Http redirections</h1>
    @if(count($httpRedirects) === 0)
      There are no known http redirections for this artisan.
    @else
      <x-common.tables>
        <x-slot:columns>
          <x-common.tables.th>From</x-common.tables.th>
          <x-common.tables.th>To</x-common.tables.th>
          <x-common.tables.th>Comment</x-common.tables.th>
        </x-slot:columns>

        @foreach($httpRedirects as $httpRedirect)
          <x-common.tables.tr key="{{ $httpRedirect['id'] }}">
            <x-common.tables.td
              class="font-extrabold md:mr-0 md:font-normal !px-2"
            >
              {{ $httpRedirect['source_uri'] }}
            </x-common.tables.td>
            <x-common.tables.td
              class="basis-full break-all"
            >
              {{ $httpRedirect['target_uri'] }}
            </x-common.tables.td>
            <x-common.tables.td
              class="basis-full break-all"
            >
              {{ $httpRedirect['comment'] }}
            </x-common.tables.td>
          </x-common.tables.tr>
        @endforeach
      </x-common.tables>
    @endif

  </div>
</x-admin.layouts.app>
