@extends('admin.layouts._admin_layout', ['page_title' => 'Les pages artisans', 'page_description' => 'Liste complète pour recherche puis ouverture de la page artisan ou édition', 'niveau1' => 'Liste artisans'])

@section('content')
    <div class="row">
            <div class="col-xs-12">
              <div class="box">
                <div class="box-header">
                  <h3 class="box-title">Liste des artisans</h3>
                </div>
                <!-- /.box-header -->
                <div class="box-body">
                    <table id="table-artisan" class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>#</th>
                            <th>Nom et prénom ou nom alternatif (si existant)</th>
                            <th>Url</th>
                            <th><PERSON><PERSON>tier</th>
                            <th>Email</th>
                            <th>Statut</th>
                            <th>Editer</th>
                        </tr>
                        </thead>
                    </table>
                    <div>
                        Légende :
                        <x-admin.status-display :flags="[
                        'inactive' => true,
                        'notOnline' => true
                        ]" />
                    </div>
                </div>
                <!-- /.box-body -->
              </div>
              <!-- /.box -->
            </div>
            <!-- /.col -->
          </div>

@stop

@section('script')
<script>
  $(function () {
    $('#table-artisan').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ route('admin.artisans.all') }}",
            "dataType": "json",
            "type": "POST",
            "data": {_token: "{{csrf_token()}}"}
        },
        "columns": [
            {"data": "artisan_id"},
            {"data": "name", "orderable": false},
            {"data": "permalink", "orderable": false},
            {"data": "profession", "orderable": false},
            {"data": "email", "orderable": false},
            {"data": "status", "orderable": false },
            {"data": "actions", "orderable": false},
        ],
        "paging": true,
        "responsive": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "language": {
            "url": "{{ __('datatables.traduction_file_url') }}"
        }
    });
  });
</script>
@stop
