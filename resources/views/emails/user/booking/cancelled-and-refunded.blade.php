@use(App\Enums\Payment\PaymentMethod)
@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])

# {{ __('emails/common.hello.who', ['who' => ucwords($user->prenom)]) }},

{!! __('emails/user/booking/cancelled-and-refunded.body', [
    'workshopName' => $workshopName,
    'artisanName' => $artisanName,
    'eventStartDate' => $eventStartDate,
    'eventStartTime' => $eventStartTime,
])!!}

@if($stripeAmount !== null)
@if($paymentMethod === PaymentMethod::CustomerBalance)
{!! __('emails/user/booking/cancelled-and-refunded.stripe_customer_balance_refund', [
    'amount' => currencyFormat($stripeAmount, null, $locale, false),
])!!}
@else
{!! __('emails/user/booking/cancelled-and-refunded.stripe_card_refund', [
    'amount' => currencyFormat($stripeAmount, null, $locale, false),
])!!}
@endif
@endif

@if($giftCardAmount !== null)
<br/>
{!! __('emails/guest/gift.gift_transformed.bc_value', ['value' => currencyFormat($giftCardAmount, null,  $locale, false)]) !!}
<br> <div style="text-align: center;"><b style="font-size: 24px; color: {{config('global.couleur_primaire')}};">{{ $giftCardCode }}</b></div>
<br/>
{!! __('emails/guest/gift.gift_transformed.code_usage') !!}

@component('mail::button', ['url' => frontUrl(route(localizedRouteName('utiliser-boncadeau.index', $locale), [
  'code_reduction' => $giftCardCode,
  'utm_medium' => 'email',
  'utm_source' => 'site',
  'utm_campaign' => 'transfobc'
], false), $country)])
  {{ __('emails/guest/gift.gift_transformed.cta_use_gift') }}
@endcomponent

{!! __('emails/guest/gift.gift_transformed.coupon_expire', ['expired_date' => dateShortFormat($giftCardExpirationDate)]) !!}
@endif
@endcomponent
