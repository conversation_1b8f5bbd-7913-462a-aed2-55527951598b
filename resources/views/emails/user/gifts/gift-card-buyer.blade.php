@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])

# {{ __('emails/common.hello.who', ['who' => $buyerName]) }},

@if(!empty($to))
{!! __('emails/guest/gift.gift_card.buyer.intro_with_to', ['to' => $to]) !!}
@else
{!! __('emails/guest/gift.gift_card.buyer.intro_without_to') !!}
@endif

@if($withPhysical)
@component('mail::panel')
{!! __('emails/guest/gift.gift_card.buyer.physical') !!}
@endcomponent
@endif

@if($digitalLink !== null)
@component('mail::panel')
{!! __('emails/guest/gift.gift_card.buyer.share_intro') !!}
@component('mail::button', ['url' => $digitalLink])
  {{__('emails/guest/gift.gift_card.buyer.cta_open_gift')}}
@endcomponent

{!! $withPhysical
  ? __('emails/guest/gift.gift_card.buyer.pdf_also_available')
  : __('emails/guest/gift.gift_card.buyer.pdf_available')
!!}
@endcomponent
@endif

@component('mail::panel')
{!! __('emails/guest/gift.gift_card.buyer.recap_title') !!}

@if($giftCardType === \App\Domain\Gift\Enums\GiftCardType::TICKET)
{!! trans_choice('emails/guest/gift.gift_card.buyer.recap_ticket', $giftCardTickets, [
  'seatsLeft' => $giftCardTickets,
  'workshopName' => $workshopName,
  'artisanName' => $artisanName,
]) !!}
@endif
@if($giftCardType === \App\Domain\Gift\Enums\GiftCardType::VALUE)
  {!! __('emails/guest/gift.gift_card.buyer.recap_value', [
  'value' => $giftCardValue,
]) !!}
@endif
<br /><br />
<div style="text-align: center;"><b style="font-size: 24px; color: {{config('global.couleur_primaire')}};">{{$giftCardCode}}</b></div>
<br />
{!! __('emails/guest/gift.gift_card.buyer.expires_at', [
  'expiresAt' => dateShortFormat($giftCardExpiresAt)
]) !!}
@if($giftCardType === \App\Domain\Gift\Enums\GiftCardType::VALUE)
{!! __('emails/guest/gift.gift_card.buyer.also_available_for_others') !!}
@endif

@endcomponent

{{ __('emails/guest/gift.gift_card.buyer.end_message') }}
@endcomponent
