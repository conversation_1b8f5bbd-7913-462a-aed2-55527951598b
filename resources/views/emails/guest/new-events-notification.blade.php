@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/common.hello.simple') }},

@if($reservation != null)
{!! __(
    'emails/guest/events.new_events_notification.awaiting_replacement_workshop',
    [
        'workshop' => $workshopName,
        'url' => $workshopUrlNonGift,
        'url_craftsman' => $artisanUrl,
        'craftsman' => $artisanName,
    ]
) !!}

{!! trans_choice('emails/guest/events.new_events_notification.good_news', $eventsCount) !!}

{{ __('emails/guest/events.new_events_notification.like_bread') }}

{{ __('emails/guest/events.new_events_notification.self_replacement') }}
@component('mail::button', ['url' => frontUrl(route(localizedRouteName('evenements.index', $locale), [], false), $country).'?utm_medium=email&utm_source=site&utm_campaign=prochdatesreplacement'])
{{ __('emails/guest/events.new_events_notification.cta_self_replacement') }}
@endcomponent
@else
{!! trans_choice(
    'emails/guest/events.new_events_notification.awaiting_new_availability',
    $eventsCount,
    [
        'workshop' => $workshopName,
        'url' => $workshopUrlNonGift,
        'url_craftsman' => $artisanUrl,
        'craftsman' => $artisanName,
    ]
) !!}

{{ __('emails/guest/events.new_events_notification.like_bread') }}
@endif

@component('mail::panel')
## {!! trans_choice('emails/guest/events.new_events_notification.next_dates', $eventsCount, ['count' => $eventsCount]) !!}

@foreach($events as $event)
+ <b>{{ $event['openingHours'] }}</b> - {{ $event['availableSlotsCount'] }} {{ trans_choice( 'emails/guest/events.new_events_notification.available_slots', $event['availableSlotsCount']) }}
@endforeach

@if($reservation)
@if($eventsCount >= App\Mail\Guest\AvailabilitySubscriberSeatsAvailableMail::EVENTS_LIMIT)
{{ __('emails/guest/events.new_events_notification.find_more') }}
@endif
@else
@if($isGift == 1)
@component('mail::button', ['url' => $workshopUrlGift.'?utm_medium=email&utm_source=site&utm_campaign=prochdates'])
@if($eventsCount >= App\Mail\Guest\AvailabilitySubscriberSeatsAvailableMail::EVENTS_LIMIT)
{{ __('emails/guest/events.cta_see_dates') }}
@else
{{ __('emails/guest/events.cta_book_my_place') }}
@endif
@endcomponent
@else
@if($eventsCount >= App\Mail\Guest\AvailabilitySubscriberSeatsAvailableMail::EVENTS_LIMIT)
<span style="font-size: 14px;">**{{ __('emails/guest/events.new_events_notification.more_availabilities') }}**</span>
@endif
@component('mail::button', ['url' => $workshopUrlNonGift.'?utm_medium=email&utm_source=site&utm_campaign=prochdates'])
@if($eventsCount >= App\Mail\Guest\AvailabilitySubscriberSeatsAvailableMail::EVENTS_LIMIT)
{{ __('emails/guest/events.cta_see_dates') }}
@else
{{ __('emails/guest/events.cta_book_my_place') }}
@endif
@endcomponent
@endif
@endif
@endcomponent

@component('mail::panel')
@if(isset($sentMailCount) && $sentMailCount >= 5)
<div style="text-align: center;">
<small><b>{!! __(
    'emails/guest/events.new_events_notification.reregistration',
    [
        'url' => frontUrl(route(LocalizedRouteName('reinscription-prochainesdates', $locale), $formId, false), $country),
        'here' => __('emails/guest/events.new_events_notification.cta_here'),
    ]
) !!}</b></small>
</div>
@else
<div style="text-align: center;">
<small><b>{{ __('emails/guest/events.new_events_notification.already_booked') }}<a href="{{frontUrl(route(LocalizedRouteName('availability-subscriber.unsubscribe', $locale), $formId, false), $country)}}">{{ __('emails/guest/events.new_events_notification.cta_here') }}</a></b></small>
</div>
@endif

@endcomponent


@endcomponent

