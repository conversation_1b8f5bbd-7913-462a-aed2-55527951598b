@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/common.hello.good_news') }}

{{ trans_choice('emails/guest/events.date_proposition_accepted.new_slot', count($events), ['craftsman' => $dateRequest->atelier->artisan->getDenomination()]) }}

@component('mail::panel')
{{ trans_choice('emails/guest/events.date_proposition_accepted.new_slot_accepted', count($events)) }}

<br/>
@foreach($events as $event)
<a href="{{ frontUrl(route(localizedRouteName('reservation', $locale), [
    'event' => $event->id,
    'utm_medium' => 'email',
    'utm_source' => 'site',
    'utm_campaign' => 'autre-date-en-ligne',
  ], false), $country)}}">
{{ dateLongFormat($event->start->format('Y-m-d H:i:s'), $event->timezone) .' ' . schedulesShortFormat($event->start->format('Y-m-d H:i:s'), $event->end->format('Y-m-d H:i:s'), $event->timezone) }}
</a><br/><br/>
@endforeach

@if(!empty($dateRequest->artisan_message))
<b>{{ __('emails/guest/events.date_proposition_accepted.artisan_message') }}:</b><br/>
{!! nl2br($dateRequest->artisan_message) !!}
@endif


@endcomponent

@endcomponent
