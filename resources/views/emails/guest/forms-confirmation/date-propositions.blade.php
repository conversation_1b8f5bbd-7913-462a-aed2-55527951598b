@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])

  # {{ __('emails/common.hello.simple') }},

{!! __('emails/guest/forms-confirmation.date_propositions.introduction', ['workshop' => $atelier, 'url' => $url]) !!}

@component('mail::panel')
  ## {{__('emails/guest/forms-confirmation.utils.summary')}}

  **{{__('emails/guest/forms-confirmation.utils.sending_date')}} :** {{ $date_envoi }}

  **{{__('emails/guest/forms-confirmation.utils.name')}} :** {{ $nom }}

  **{{__('emails/guest/forms-confirmation.utils.email')}} :** {{ $email }}

  **{{__('emails/guest/forms-confirmation.utils.phone')}} :** {{ $phone }}

  **{{__('emails/guest/forms-confirmation.date_propositions.workshop')}} :** {{ $atelier }}

  **{{__('emails/guest/forms-confirmation.date_propositions.proposed_slots')}} :**
@foreach($dateRequests as $dateRequest)
- {{$dateRequest->getPropositionDate()}}
@endforeach

**{{__('emails/guest/forms-confirmation.utils.additional_message')}} :**

  {{ $message }}
@endcomponent

@endcomponent
