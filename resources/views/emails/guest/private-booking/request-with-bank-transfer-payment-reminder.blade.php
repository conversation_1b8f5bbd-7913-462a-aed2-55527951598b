@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/common.hello.who', ['who' => ucwords($firstName)]) }},

{!! __('emails/guest/private-booking.request-with-bank-transfer-payment-reminder.explanation', ['workshopName' => $workshopName, 'url' => $workshopUrl]) !!}

@component('mail::panel')
{!! __('emails/guest/private-booking.request-with-bank-transfer-payment-reminder.bank_transfer') !!}

@component('mail::button', ['url' => $btnUrl])
{{ __('emails/guest/private-booking.request-with-bank-transfer-payment-reminder.cta_bank_transfer') }}
@endcomponent

{!! __('emails/guest/private-booking.quote-validated.we_may_cancel') !!}
@endcomponent

@include('emails.participant.components.manage-booking', [
  'limitDate' => $expiresAt,
  'eventStart' => (clone $reservation->evenement->start),
  'event' => $reservation->evenement,
  'isPrivate' => true,
  'url' => $btnUrl,
])

@include('emails.participant.components.event-practical-informations', ['link' => $reservation->getCalendarLink() ])


@endcomponent
