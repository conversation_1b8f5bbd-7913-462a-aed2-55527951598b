@component('emails.layouts.client.message', ['country' => $country, 'locale' => $locale])

# {{__('emails/common.hello.who', ['who' => ucwords($reservation->getParticipantFirstname()) ]) }},

<strong>{{ $title }}.</strong>

@if($reservation->evenement->atelier->is_online)
@include('emails.participant.components.event-online', ['url' => $reservation->evenement->atelier->online_url])
@endif
@include('emails.participant.components.event-practical-informations', ['link' => $reservation->getCalendarLink() ])

@if($reservation->evenement->start > \Carbon\Carbon::now()->addHours(config('private-booking.manage_booking.customer')))
@include('emails.participant.components.manage-booking', [
  'limitDate' => dateAndTimeLongFormat($reservation->evenement->start->subHours(config('private-booking.manage_booking.customer')), $reservation->evenement->timezone),
  'eventStart' => (clone $reservation->evenement->start),
  'event' => $reservation->evenement,
  'isPrivate' => $reservation->evenement->isPrivate(),
  'url' => $url,
])
@endif
@if($reservation->evenement->cut > 1 && !$reservation->evenement->willTakePlace())
{!! __('emails/participant/booking-management.booking_confirmed.reminder', ['nbPartMin' => $reservation->evenement->cut]) !!}
@endif

@endcomponent
