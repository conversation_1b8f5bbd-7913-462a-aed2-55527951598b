@component('emails.layouts.artisan.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/artisan/booking-management.reservation-annulee.greetings', ['name' => $name]) }}

@if(!$is_bank_transfer_expiration)
@if($isPrivateBookingRequest)
{!! __('emails/artisan/booking-management.reservation-annulee.content_privat', [
  'customer' => $customer,
  'workshop' => $workshop,
  'datetime' => $datetime,
  'places' => $places,
]) !!}
@else
{!! __('emails/artisan/booking-management.reservation-annulee.content', [
  'customer' => $customer,
  'workshop' => $workshop,
  'datetime' => $datetime,
  'places' => $places,
]) !!}
@endif
<br/><br />
{!! __('emails/artisan/booking-management.reservation-annulee.help') !!}
@else
{!! __('emails/artisan/booking-management.reservation-annulee.privatisation_content', ['workshop' => $evenement->atelier->nom, 'datetime' => $evenement->present()->openingHoursWithDate()]) !!}
<br /><br />
{!! __('emails/artisan/booking-management.reservation-annulee.privatisation_content_policy') !!}
@endif
<br/>
@component('mail::panel')
{{ __('emails/artisan/booking-management.reservation-annulee.panel.title') }}
------
+ {!! __('emails/artisan/booking-management.reservation-annulee.panel.date', ['date' => $datetime]) !!}
+ {!! __('emails/artisan/booking-management.reservation-annulee.panel.places_count', ['places' => $totalPlaces]) !!}
@if(!$isPrivateBookingRequest)
@if($isCutReached)
  {{ trans_choice('emails/artisan/booking-management.reservation-annulee.panel.cut_reached', $cut, [
    'cut' => $cut
    ]) }}
@else
  {{ trans_choice('emails/artisan/booking-management.reservation-annulee.panel.cut_not_reached', $cut, [
    'cut' => $cut
    ]) }}
@endif
+ {!! __('emails/artisan/booking-management.reservation-annulee.panel.remaining_places', ['places' => $remainingPlaces]) !!}
@endif
+ {!! __('emails/artisan/booking-management.reservation-annulee.panel.place', ['place' => $location]) !!}
@endcomponent

@component('mail::button', ['url' => $url])
  {{__('emails/artisan/booking-management.cta_manage_event')}}
@endcomponent

___

@endcomponent
