@component('emails.layouts.artisan.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/artisan/booking-management.nouvelle-resa-evenement.greetings', ['name' => $name]) }}

{{ __('emails/artisan/booking-management.nouvelle-resa-evenement.content', [
  'places' => $displayPlaces,
  'datetime' => $dateTime,
  'workshop' => $workshop
  ]) }}

@component('mail::panel')
{{ __('emails/artisan/booking-management.nouvelle-resa-evenement.panel.title') }}
------
+ {{ trans_choice('emails/artisan/booking-management.nouvelle-resa-evenement.panel.places_count', $placeNumber, [
  'places_count' => $placeNumber
] ) }}
@if($isCutReached)
  {{ trans_choice('emails/artisan/booking-management.nouvelle-resa-evenement.panel.cut_reached', $cut, [
    'cut' => $cut
    ]) }}
@else
  {{ trans_choice('emails/artisan/booking-management.nouvelle-resa-evenement.panel.cut_not_reached', $cut, [
    'cut' => $cut
    ]) }}
@endif
+ {{ __('emails/artisan/booking-management.nouvelle-resa-evenement.panel.contact', [
  'fullname' => $fullname,
  'email' => $email,
  'phone' => $phone
]) }}
+ {!! trans_choice('emails/artisan/booking-management.nouvelle-resa-evenement.panel.remaining_places', $availableSlots) !!}
+ {!! __('emails/artisan/booking-management.nouvelle-resa-evenement.panel.date', ['date' => $dateTime]) !!}
+ {!! __('emails/artisan/booking-management.nouvelle-resa-evenement.panel.place', ['place' => $place]) !!}
@endcomponent
@component('mail::button', ['url' => frontUrl(route(localizedRouteName('artisan.events.show', $locale), ['event' => $eventId], false), $country)])
  {{__('emails/artisan/booking-management.cta_manage_event')}}
@endcomponent

___

@endcomponent
