@component('emails.layouts.artisan.message', ['country' => $country, 'locale' => $locale])
# {{ __('emails/artisan/booking-management.modification-resa-evenement.greetings', ['name' => $name]) }}

{{ __('emails/artisan/booking-management.modification-resa-evenement.content', ['places' => $places]) }}
<br/>
@component('mail::panel')
{{ __('emails/artisan/booking-management.modification-resa-evenement.panel.title') }}
------
+ {{ __('emails/artisan/booking-management.modification-resa-evenement.panel.moved_places', ['places' => $places]) }}
+ {!! __('emails/artisan/booking-management.modification-resa-evenement.panel.old_workshop', ['datetime' => $initialDatetime, 'workshop' => $initialWorkshop, 'url' => $initialUrl]) !!}
+ {!! __('emails/artisan/booking-management.modification-resa-evenement.panel.new_workshop', ['datetime' => $newDatetime, 'workshop' => $newWorkshop, 'url' => $newUrl]) !!}
@if($hasParticipant)
+ {{ __('emails/artisan/booking-management.modification-resa-evenement.panel.participant_name', ['name' => $participantFullname]) }}
+ {{ __('emails/artisan/booking-management.modification-resa-evenement.panel.email', ['email' => $participantEmail]) }}
@endif
@if(\is_string($participantPhone))
+ {{ __('emails/artisan/booking-management.modification-resa-evenement.panel.phone', ['phone' => $participantPhone]) }}
@endif
@endcomponent

{!! __('emails/artisan/booking-management.modification-resa-evenement.find_all_information', ['url' => $newUrl]) !!}<br>
<br>

@endcomponent
