<x-artisan.layouts.app>
  <x-common.page-header :title="__('userartisan/view/performances.title')"/>
  <x-common.nav>
      @foreach($tabs as $key => $tab)
        <x-common.nav.link
          :name="$tab"
          :label="__('userartisan/view/performances.tabs.' . $key)"
          :active="$tab === $currentTab"
          :url="route(localizedRouteName('artisan.performances.'.$tab))"
          heading_style="text-xl"
        />
      @endforeach
  </x-common.nav>

  <div
    class="p-4"
  >
    <x-artisan.performances.satisfaction
      :data="$data"
    />
  </div>
</x-artisan.layouts.app>
