@use(App\Enums\Event\EventType)
@use(App\Domain\Workshop\Enums\BookingOptionType)
<x-artisan.layouts.app>
  <x-artisan.workshop.page-header
    :workshop-name="$workshop->nom"
    :is-active="$workshop->active"
    :is-group-only="$workshop->isGroupOnly()"
    :workshop-url="frontUrl($workshop->getPageUrl(false))"
  />

  <x-artisan.workshop.navigation
    tab-active="content"
    :workshop-id="$workshop->getKey()"
  />

  <div class="p-4">
    @if ($workshop->active)
      <x-common.card
        reduce-padding="true"
        class="my-2 w-full mt-4"
      >
        <x-slot:header>
          <div class="md:flex md:justify-between md:items-center p-4">
            <div>{{__('userartisan/view/workshops.show.update_request.content')}}</div>
            <div>
              @if($workshop->isVisible())
                @if($updateRequestPending === false)
                  <x-common.button
                    x-data
                    x-on:click="$modal('workshop-update-request').open()"
                    class="btn-goto-workshop-update-request"
                    size="small"
                  >
                    {{ __('userartisan/view/workshops.table.update_request_cta') }}
                  </x-common.button>
                @endif
                @if($updateRequestCount > 0)
                  <x-common.button
                    class="btn-show-update-request-overview-modal"
                    size="small"
                    x-data
                    x-on:click="$modal('show-update-request-overview-modal').open()"
                    outlined
                    x-tooltip="{{ $updateRequestPending ? __('userartisan/view/workshops.show.update_request.pending_request_content') : '' }}"
                  >
                    {{ __('userartisan/view/workshops.table.update_request_overview_cta') }}
                  </x-common.button>
                  <livewire:artisan.workshop.update-request-overview-modal
                    :workshop="$workshop"
                    :wire:key="$workshop->id"
                  />
                @endif
              @endif
            </div>
          </div>
        </x-slot:header>
        <livewire:artisan.workshop.update-request-form-modal :workshop="$workshop"/>
      </x-common.card>
    @endif
  </div>
</x-artisan.layouts.app>
