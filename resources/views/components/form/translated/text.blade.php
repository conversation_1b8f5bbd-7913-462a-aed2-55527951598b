@props([
    'name' => '',
    'values' => [],
    'label' => '',
    'placeholder' => '',
    'disabled' => false,
    'required' => false,
    'prefix' => null,
])

@push('css')
	<style>
		.translated-field .inactiv img{
			filter: grayscale(100%);
		}
	</style>
@endpush
<div id="translated-field-{{ $name }}" class="translated-field form-group col-xs-12 {{ $errors->has($prefix ? $prefix. '.' . config('translatable.locales')[0]. '.' . $name : config('translatable.locales')[0]. '.' . $name) ? 'has-error' : '' }}">
	<x-master.label :name="$name" id="{{ $name }}-label" :label="$label" />

	<span class="pull-right {{ $name }}-lang-links flex">
		@foreach(config('translatable.locales') as $locale)
			<a href="#" data-locale="{{ $locale }}" class="flags-link {{ $name }}-locale-link mx-1">
				<img src="{{safeMix('/images/flags/'.$locale.'.png')}}" width="16px">
			</a>
		@endforeach
	</span>

	@foreach(config('translatable.locales') as $locale)
		<div id="{{ $name }}-{{ $locale }}" data-locale="{{ $locale }}" class="{{ $name }}-lang-field">
      @php
        $value = old($prefix ? "$prefix.$locale.$name" : "$locale.$name", $values[$locale] ?? '');
      @endphp
			<x-master.input
        :name="$prefix ? $prefix.'['.$locale.']'.'['.$name.']' : $locale.'['.$name.']'"
        class="block form-control"
        value="{{$value}}"
        type="text"
        placeholder="{{ $placeholder }}"
        :disabled="$disabled ?? false"
        :required="$locale === 'fr' && $required"
      />
		</div>
	@endforeach

  @foreach(config('translatable.locales') as $locale)
    <div class="text-danger">{{ $errors->first($prefix ? "$prefix.$locale.$name" : "$locale.$name") }}</div>
  @endforeach
</div>

@push('scripts')
	<script>
		(function() {
			let $translatedField = $(document).find('#translated-field-{{ $name }}');
			$translatedField.find('a.flags-link.{{ $name }}-locale-link').each(function (index, elem) {
				if ($(elem).attr('data-locale') !== 'fr') {
					$(elem).addClass('inactiv');
				}

				$(elem).on('click', function (event) {
					event.preventDefault();

					let currentLocale = $(this).attr('data-locale');

					$translatedField.find('a.flags-link.{{ $name }}-locale-link').addClass('inactiv');
					$(this).removeClass('inactiv');

					$translatedField.find('div.{{ $name }}-lang-field').hide();
					$translatedField.find('div.{{ $name }}-lang-field[data-locale="'+currentLocale+'"]').show();
				});
			});

			$translatedField.find('div.{{ $name }}-lang-field').each(function (index, elem) {
				if ($(elem).attr('data-locale') !== 'fr') {
					$(elem).hide();
				}
			});
		})();
	</script>
@endpush
