@props([
  'required' => false,
  'value' => null,
  'showErrorInline' => false,
])
@aware(['name'])
<div class="w-full">
  <textarea
    {{ $attributes->merge([
          'class' => 'bw-input',
          'rows' => 4
        ])
        ->class(["!border-danger" => $errors->has($name)])
    }}
    @required($required)
  >
    {{ $slot }}
  </textarea>
  @if($showErrorInline)
    <div>
      @error($name) <span class="text-danger text-sm">{{ $message }}</span> @enderror
    </div>
  @endif
</div>
