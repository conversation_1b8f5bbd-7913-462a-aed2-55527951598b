@use('App\Domain\Booking\Enums\CalendarMultipleEventsType')

@props([
    'type' => CalendarMultipleEventsType::Stacked,
    'status' => null,
    'events' => [],
    'start' => null,
    'end' => null,
    'timezone' => null,
    'participants' => null,
    'view' => null,
])

<div {{ $attributes->merge(['class' => 'text-sm border rounded-lg border-gray-300 p-5 bg-white'])}}>
    <div class="flex justify-between items-start gap-2">
        <div>
            <x-common.calendar.multiple-events-title
                :type="$type"
                :start="$start"
                :end="$end"
                :timezone="$timezone"
                :participants="$participants"
            />
        </div>
        @if ($type === CalendarMultipleEventsType::Cumulative)
            <x-common.calendar.event-status
                class="shrink-0"
                size="medium"
                :status="$status"
            />
        @endif
    </div>
    @if(count($events) < 3)
        <div class="flex flex-col gap-4 mt-5">
            @foreach($events as $event)
                <x-common.calendar.list-event-card
                    class="w-full"
                    :id="$event['id']"
                    :status="$event['status']"
                    :color="$event['color']"
                    :title="$event['title']"
                    :thumbnail="null"
                    :start="$event['start']"
                    :end="$event['end']"
                    :timezone="$event['timezone']"
                    :participants="$event['participants']"
                    :max-participants="$event['maxParticipants']"
                    :is-private="$event['isPrivate']"
                    :is-unavailable-stacked="$event['isUnavailableStacked']"
                    :is-recurring="$event['isRecurring']"
                    :recurring-id="$event['recurringId']"
                />
            @endforeach
        </div>
    @else
        <div class="grid grid-cols-2 gap-3 mt-5">
            @foreach($events as $event)
                <x-common.calendar.simple-list-event-card
                    :id="$event['id']"
                    :title="$event['title']"
                    :color="$event['color']"
                    :is-private="$event['isPrivate']"
                    :is-unavailable-stacked="$event['isUnavailableStacked']"
                    :is-recurring="$event['isRecurring']"
                    :recurring-id="$event['recurringId']"
                    :start="$event['start']"
                    :end="$event['end']"
                    :timezone="$event['timezone']"
                    :participants="$event['participants']"
                    :max-participants="$event['maxParticipants']"
                />
            @endforeach
        </div>
    @endif
</div>
