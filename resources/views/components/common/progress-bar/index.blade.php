{{--
    Component: Input
    Purpose: Main component for input field.
    Props:
        - transparent (bool, default: false): if the background is transparent
        - percentage (int, default: 0): percentage of progression
        - color (string, default: 'primary'): the progress bar color
        - showPercentageLabel (bool, default: false): show percentage label
        - showPercentageLabelInline (bool, default: true): show percentage label in inline
        - percentageLabelPosition (string, default: 'top left'): position of the percentage label
        - percentagePrefix (string, default: ''):
        - percentageSuffix (string, default: ''):
        - cssOverride (string, default: ''):
        - percentageLabelOpacity (string, default: '100'):
--}}
@php use Illuminate\Support\Str; @endphp
@props([
    'transparent' => false,
    'percentage' => 0,
    'color' => 'primary',
    'showPercentageLabel' => false,
    'showPercentageLabelInline' => true,
    'percentageLabelPosition' => 'top left',
    'color_weight' => 300,
    'text_color_weight' => 600,
    'percentagePrefix' => '',
    'percentageSuffix' => '',
    'barClass' => '',
    'cssOverride' => '',
    'percentageLabelOpacity' => '100',
    'caretPercentage' => null,
    'caretColor' => 'green',
])

@php
  $showPercentageLabel = filter_var($showPercentageLabel, FILTER_VALIDATE_BOOLEAN);
  $showPercentageLabelInline = filter_var($showPercentageLabelInline, FILTER_VALIDATE_BOOLEAN);
  $showPercentageLabelInline = filter_var($showPercentageLabelInline, FILTER_VALIDATE_BOOLEAN);
  $transparent = filter_var($transparent, FILTER_VALIDATE_BOOLEAN);
  //-----------------------------------------------------------------------

  if ($color == 'gray') $cssOverride = '!bg-slate-300';
  if(! is_numeric($percentageLabelOpacity*1)) $percentageLabelOpacity = '100';
@endphp

<div>
  <span class=" bg-primary-300 bg-primary-600 hidden"></span>
  <div {{ $attributes->merge(['class' => 'bw-progress-bar']) }}>
    @if($showPercentageLabel &&
        !$showPercentageLabelInline &&
        Str::contains($percentageLabelPosition, 'top'))
      <div class="text-xs tracking-wider {{str_replace('top ','text-', $percentageLabelPosition)}}">
        {{$percentagePrefix}} <span class="opacity-{{$percentageLabelOpacity}}">{{ $percentage}}%</span> {{$percentageSuffix}}
      </div>
    @endif
    <div class="@if(!$transparent) bg-slate-200/70 w-full @endif mt-1 my-2 rounded-full">
      <div style="width: {{$percentage}}%" class="text-center py-1 bg-{{$color}}-{{$color_weight}} {{$cssOverride}} rounded-full bar-width animate__animated animate__fadeIn {{$barClass}}">
        @if($showPercentageLabel && $showPercentageLabelInline)<span class="text-{{$color}}-{{$text_color_weight}} px-2 text-xs">
              {{$percentagePrefix}} <span class="opacity-{{$percentageLabelOpacity}}">{{ $percentage}}%</span> {{$percentageSuffix}}
              </span>@endif
      </div>
    </div>
    @if($showPercentageLabel &&
        !$showPercentageLabelInline &&
        Str::contains($percentageLabelPosition, 'bottom'))
      <div class="text-xs tracking-wider {{str_replace('bottom ','text-', $percentageLabelPosition)}}">
        {{$percentagePrefix}} <span class="opacity-{{$percentageLabelOpacity}}">{{ $percentage}}%</span> {{$percentageSuffix}}
      </div>
    @endif
  </div>
  @if($caretPercentage !== null)
    <div class="-mt-4">
      <div class="border-r-2 border-{{ $caretColor }}-600" style="height: 8px; width: {{ $caretPercentage }}%;">
      </div>
    </div>
  @endif
</div>
