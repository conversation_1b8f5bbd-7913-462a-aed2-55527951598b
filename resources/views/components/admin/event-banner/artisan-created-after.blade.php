@props([
  'data' => null,
  'selected' => null,
])

@php
  if (!empty($selected)) {
    $selected = \Carbon\Carbon::createFromFormat(\App\Livewire\Admin\EventBanner\ManageBanner::DATE_FORMAT, $selected);

    if ($selected instanceof \Carbon\CarbonInterface) {
      $selected = $selected->format(\App\Livewire\Admin\EventBanner\ManageBanner::DATE_FORMAT);
    } else {
      $selected = null;
    }
  }
@endphp

<div wire:key="artisan-created-after">
  <h3 class="text-primary text-lg">
    Artisan created after (UTC)
  </h3>

  <x-common.form.input
    name="artisanCreatedAt"
    type="datetime-local"
    :selected-value="$selected"
    wire:model="rules.artisan-created-after"
  />
</div>
