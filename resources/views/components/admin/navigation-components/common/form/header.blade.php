@props([
  'formId',
  'navigationComponent' => null,
  'isDuplicate' => false
])
@if ($errors->has('type'))
  <p class="text-center text-danger">
    Une version existe déjà pour les dates et le pays sélectionné(e)s
  </p>
@endif
<div class="well">
  <div id="{{$formId}}-component-label-form-group" class="display-inline-flex-field form-group @error('label') has-error @enderror">
    @error('label')
    <small
      class="help-block">
      {{ $message }}
    </small>
    @enderror
    <label
      class="col-sm-1 px-0"
      for="label">
      Label
    </label>
    <div class="col-sm-11">
      <input name="label"
             type="text"
             class="form-control pull-right"
             autocomplete="off"
             id="{{$formId}}-component-label-input"
             @if(old('label'))
               value="{{ old('label') }}"
             @elseif(!empty($navigationComponent['label']) && $isDuplicate === false)
               value="{{ $navigationComponent['label'] }}"
             @endif
      >
    </div>
  </div>
  <div id="{{$formId}}-publish-at-form-group" class="display-inline-flex-field form-group @error('publish_at') has-error @enderror">
    @error('publish_at')
      <small
        class="help-block">
        {{ $message }}
      </small>
    @enderror
    <label
      class="col-sm-1 px-0"
      for="publish_at">
      Date de publication
    </label>
    <div class="col-sm-11">
      <div class="input-group date">
        <div class="input-group-addon">
          <i class="fa fa-calendar"></i>
        </div>
        <input name="publish_at" type="text" class="form-control pull-right"
               autocomplete="off"
               id="{{$formId}}-publish-at-datepicker">
      </div>
    </div>
  </div>
  <div id="{{$formId}}-expires-at-form-group" class="display-inline-flex-field form-group @error('expires_at') has-error @enderror">
    @error('expires_at')
      <small
        class="help-block">
        {{ $message }}
      </small>
    @enderror
    <label
      class="col-sm-1 px-0"
      for="expires_at">
      Date d'expiration
    </label>
    <div class="col-sm-11">
      <div class="input-group date">
        <div class="input-group-addon">
          <i class="fa fa-calendar"></i>
        </div>
        <input name="expires_at" type="text" class="form-control pull-right"
               autocomplete="off"
               id="{{$formId}}-expires-at-datepicker">
      </div>
    </div>
  </div>
  <div id="{{$formId}}-datepickers-error" class="hidden text-danger">
    <ul>
      <li>La date de publication doit précéder la date d'expiration</li>
      <li>Si aucune date de publication ou d'expiration n'est spécifiée, la page avec la date de publication la plus proche et non expirée sera utilisée.</li>
    </ul>
  </div>
  <div class="display-inline-flex-field form-group @error('country_code') has-error @enderror">
    @error('country_code')
      <small
        class="help-block">
        {{ $message }}
      </small>
    @enderror
    <label
      class="col-sm-1 px-0"
      for="country_code">
      Pays
    </label>
    <div class="col-sm-11">
      <select class="form-control" name="country_code">
        <option
          @if (
            (hasOldInput('country_code') && old('country_code') === null) ||
            (
                !hasOldInput('country_code') && (
                    !isset($navigationComponent['country_code']) ||
                    $navigationComponent['country_code'] === null
                )
            )
          )
            selected
          @endif
          value>Tous</option>
        @foreach(config('countries.available_countries', []) as $country)
          <option
            value="{{$country}}"
            @if (
                (hasOldInput('country_code') && old('country_code') === $country) ||
                (!hasOldInput('country_code') && isset($navigationComponent['country_code']) && $navigationComponent['country_code'] === $country)
            )
              selected
            @endif
          >
            {{ config('iso-countries', [])[$country][config('app.locale')] ?? $country }}
          </option>
        @endforeach
      </select>
    </div>
  </div>
</div>
