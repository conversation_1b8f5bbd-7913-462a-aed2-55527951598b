@props([
    'id' => null,
    'checked' => false,
    'name' => null,
    'value' => null,
])

<div id="{{ $id }}" class="panel {{ $checked ? 'panel-success' : 'panel-primary' }} margin-y-md" style="padding:0;">
    <div class="panel-heading">
        {!! $title ?? '' !!}

        <span class="pull-right">
            <label class="sr-only" for="input-{{ $name }}">{{ $name }}</label>
            <input type="checkbox" name="{{ $name }}" id="input-{{ $name }}" value="{{ $value }}" @checked($checked)
                   class="checkbox-toggle" data-toggle="toggle" data-onstyle="success" data-size="mini"
            >
        </span>
    </div>
    @if(!empty(trim($slot)))
        <div class="panel-body">{!! $slot !!}</div>
    @endif
</div>

@push('scripts')
    <script>
        $('input[name={{ $name }}]').change(function () {
            if ($(this).prop('checked')) {
                $('#{{ $id }} .panel-body').show(1000);
                $('#{{ $id }}').toggleClass('panel-success panel-primary');
            } else {
                $('#{{ $id }} .panel-body').hide(1000);
                $('#{{ $id }}').toggleClass('panel-success panel-primary');
            }
        });

        $(function () {
            if ($('input[name={{ $name }}]').prop('checked')) {
                $('#{{ $id }} .panel-body').show();
            } else {
                $('#{{ $id }} .panel-body').hide();
            }
        });
    </script>
@endpush
