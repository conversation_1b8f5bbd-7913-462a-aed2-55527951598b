@props([
  'data',
])

@if(count($data['activeWorkshops']) === 0)
  <x-common.alert show-close-icon="false" class="my-2 text-sm" show-icon="false">
    <p>{{__('userartisan/view/performances.no_active_workshop')}}</p>
  </x-common.alert>
@else
  <x-common.card>
    <x-slot:title>
      <x-common.card.header :title="__('userartisan/view/performances.sales_capacity.title')"/>
    </x-slot:title>

    <div class="flex flex-col gap-4">
      @foreach($data['activeWorkshops'] as $workshop)
        <div>
          <div class="mb-2 flex gap-2 items-center">
            <x-common.workshop.color :color="$workshop->background_color" :name="$workshop->nom"/>
          </div>
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <x-common.statistics.card
              :title="__('userartisan/view/performances.sales_capacity.futur_events.title')"
              :statisticsTooltip="$data['stats'][$workshop->id]['futurEvents']['indicator'] ? null : __('userartisan/view/performances.sales_capacity.futur_events.tooltip')"
              :indicatorTooltip="$data['stats'][$workshop->id]['futurEvents']['indicator'] ? __('userartisan/view/performances.sales_capacity.futur_events.tooltip') : null"
              statistics="{{ trans_choice('userartisan/view/performances.sales_capacity.futur_events.stat', $data['stats'][$workshop->id]['futurEvents']['stat']) }}"
              indicator="{{ $data['stats'][$workshop->id]['futurEvents']['indicator'] }}"
            />
            <x-common.statistics.card
              :title="__('userartisan/view/performances.sales_capacity.available_seats.title')"
              :indicatorTooltip="__('userartisan/view/performances.sales_capacity.available_seats.tooltip')"
              statistics="{{ trans_choice('userartisan/view/performances.sales_capacity.available_seats.stat', $data['stats'][$workshop->id]['availableSeats']['stat']) }}"
              explanations="{{ trans_choice('userartisan/view/performances.sales_capacity.available_seats.standby', $data['stats'][$workshop->id]['availableSeats']['standby']) . ', ' . trans_choice('userartisan/view/performances.sales_capacity.available_seats.giftcard', $data['stats'][$workshop->id]['availableSeats']['giftcard'])}}"
              indicator="{{ $data['stats'][$workshop->id]['availableSeats']['indicator'] }}"
            />
            <x-common.statistics.card
              :title="__('userartisan/view/performances.sales_capacity.last_event.title')"
              :indicatorTooltip="__('userartisan/view/performances.sales_capacity.last_event.tooltip')"
              statistics="{{ $data['stats'][$workshop->id]['lastEvent']['stat'] }}"
              explanations="{{ trans_choice('userartisan/view/performances.sales_capacity.last_event.explanations', $data['stats'][$workshop->id]['lastEvent']['days'])}}"
              indicator="{{ $data['stats'][$workshop->id]['lastEvent']['indicator'] }}"
            />
          </div>
        </div>
      @endforeach
    </div>
    <div class="mt-4 flex items-center justify-center">
      <x-common.button
        :href="route(localizedRouteName('artisan.calendar')) . '?open=add-slot-calendar-modal'"
      >
        {{__('userartisan/view/performances.sales_capacity.add_slot_cta')}}
      </x-common.button>
    </div>
    <x-common.alert show-close-icon="false" class="mt-4 my-2 text-sm" show-icon="true">
      <p class="[&>a]:underline [&>a]:text-primary">
        {!! __('userartisan/view/performances.sales_capacity.organize_calendar', ['url' => config('global.artisan_manage_slots')]) !!}
      </p>
    </x-common.alert>
  </x-common.card>
@endif
