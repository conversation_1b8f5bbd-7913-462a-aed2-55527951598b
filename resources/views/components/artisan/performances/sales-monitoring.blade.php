@if(count($data['activeWorkshops']) === 0)
  <x-common.alert show-close-icon="false" class="my-2 text-sm" show-icon="false">
    <p>{{__('userartisan/view/performances.no_active_workshop')}}</p>
  </x-common.alert>
@else
  <x-common.card class="mt-4">
    <x-slot:title>
      <x-common.card.header :title="__('userartisan/view/performances.sales_monitoring.title')"/>
    </x-slot:title>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <x-common.statistics.card
        :statistics="number_format($data['eventsDone'],0, ',', ' ')"
        :explanations="trans_choice('userartisan/view/homepage.results.events_done', $data['eventsDone'])"
      />
      <x-common.statistics.card
        :statistics="number_format($data['attendees'],0, ',', ' ')"
        :explanations="trans_choice('userartisan/view/homepage.results.participants', $data['attendees'])"
        :statisticsTooltip="__('userartisan/view/performances.sales_monitoring.participants.tooltip')"
      />
      <x-common.statistics.card
        statistics="{{ currencyFormat(round($data['turnover']), getCurrencyForCountry($data['artisan']->country_code ?? config('app.country')), null, false) }}"
        :explanations="__('userartisan/view/homepage.results.turnover')"
        :statisticsTooltip="__('userartisan/view/homepage.results.turnover_tooltip')"
      />
    </div>
    <x-common.collapse
      :open="true"
      chevron_position="right"
      icon="information-circle"
    >
      <x-slot:title>
        {{ __('userartisan/view/performances.charts.sales.title') }}
      </x-slot:title>
      <livewire:artisan.performances.sales-chart :artisan="$data['artisan']"/>
    </x-common.collapse>
  </x-common.card>
@endif
