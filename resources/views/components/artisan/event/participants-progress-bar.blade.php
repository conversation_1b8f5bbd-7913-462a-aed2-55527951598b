@props([
    'participants' => null,
    'cut' => null,
    'capacity' => null,
    'needActionBefore' => null,
    'timezone' => null,
    'maintainStatus' => null,
    'canDelete' => true,
])

@php
  $progressbarColor = 'primary';
  if ($needActionBefore !== null) {
      $progressbarColor = 'orange';
  } elseif ($participants >= $cut || $maintainStatus === App\Domain\Booking\Enums\EventMaintainStatus::EVENT_MAINTAINED_CONFIRMED) {
      $progressbarColor = 'green';
  }

@endphp

<div {{ $attributes->merge(['class' => "rounded-lg border border-gray-200 p-3"])}}>

  @if($participants > 0 && $needActionBefore !== null)
    <div class="flex items-center gap-2">
      <x-common.icons name="exclamation-triangle" class="!h-8 !w-8 text-amber-500"/>
      <p class="text-sm font-extrabold">
        {{ __('userartisan/view/events/pages.display.cut_not_reached_message') }}
      </p>
    </div>
  @endif

    <div class="flex justify-between items-center gap-2 mb-4">
      <p class="text-lg w-1/2">
        <span class="font-bold">{{ $participants }}</span> {{ trans_choice('userartisan/view/events/pages.display.participants', $participants) }}
      </p>
      @if($participants !== 0 && $needActionBefore === null)
        @if($participants >= $cut)
          <p class="text-sm text-green-500 w-1/2 text-right">
            {{ $capacity === $participants ? __('userartisan/view/events/pages.display.maximum_participants_reached') : __('userartisan/view/events/pages.display.minimum_participants_reached') }}
          </p>
        @elseif($maintainStatus === App\Domain\Booking\Enums\EventMaintainStatus::EVENT_MAINTAINED_TO_BE_CONFIRMED)
          <p class="text-sm w-1/2 text-right">
            {{ trans_choice('userartisan/view/events/pages.display.cut_reached_in_x_participants', $cut - $participants, ['participants' => $cut - $participants]) }}
          </p>
        @endif
      @endif
    </div>

    @if($participants === 0)
      <p class="font-bold text-sm">
        {{ __('userartisan/view/events/pages.display.no_participant') }}
        @if($canDelete)
          {{ __('userartisan/view/events/pages.display.you_can_delete_it') }}
        @endif
      </p>
    @else

      <x-common.progress-bar
        :percentage="$participants / $capacity * 100"
        :caretPercentage="$participants >= $cut ? null : $cut / $capacity * 100"
        :color="$progressbarColor"
      />

      @if($needActionBefore !== null)
        <div class="border-t border-gray-200 mt-2 pt-2">
          <p>{!!  __('userartisan/view/events/pages.display.need_action_before') !!}</p>
          <p class="font-bold text-amber-500">{{ dateAndTimeLongFormat($needActionBefore, $timezone) }}</p>
        </div>
      @endif
    @endif

</div>
