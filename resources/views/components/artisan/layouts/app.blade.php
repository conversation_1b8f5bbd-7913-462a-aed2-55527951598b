<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <meta name="robots" content="noindex">

  <title>{{ $page_title ?? "Wecandoo Artisan Application" }}</title>

  <!-- Theme style -->
  <x-tracking.gtm_header :app-id="config('services.gtag.artisan_app_id')"/>

  @include('userartisan.scripts.tailwind-config')
  <link href="{{ safeMix('css/common/animate.min.css') }}" rel="stylesheet"/>
  <link href="{{ safeMix('css/common/bladewind-ui.min.css') }}" rel="stylesheet"/>
  <script src="{{ safeMix('js/tinymce/tinymce.min.js') }}" referrerpolicy="origin"></script>
  <script src="{{ safeMix('js/common/forms.js') }}"></script>
  <script src="{{ safeMix('js/artisan.js') }}"></script>
  <script>
    window.t.setLocale('{{ config('app.locale') }}')
  </script>
  <link href="{{ safeMix('css/tom-select.css') }}" rel="stylesheet">
  @stack('styles')
  @livewireStyles
</head>
<body class="lining-nums">
<div class="antialiased bg-gray-50 overflow-x-hidden min-h-[100svh]">
  <x-artisan.layouts.navigation/>
  <x-artisan.sidebar/>
  <main class="px-2 pt-[60px] md:ml-64 h-auto pb-8">
    <livewire:common.alerting-system :modals="true" />
    @if(empty($noScroll))
      <livewire:artisan.event-banner/>
    @endif
    {{ $slot }}
  </main>
</div>
<x-common.notification/>
<x-common.overlay/>
<!-- REQUIRED JS SCRIPTS -->
<script src="{{ safeMix("js/flowbite/flowbite.js") }}"></script>
<x-artisan.tracking.gtm-script :app-id="config('services.gtag.artisan_app_id')" />
<script src="{{ safeMix('js/cookie.js') }}"></script>
@include('front.components.tracking.intercom')
@stack('scripts')
@livewireScripts
<script>
  document.addEventListener('livewire:init', () => {
    Livewire.on('reloadPage', (event) => {
      const delay = event.delay === undefined ? 0 : event.delay;

      setTimeout(function () {
        window.location.reload();
      }, delay);
    });
  });
</script>
</body>
</html>
