<div>
  <x-common.modal
    size="big"
    name="event-banner-manage-form"
    :title="$name === '' ? 'Create new Event Banner' : ('Event Banner: '.$name)"
  >
    <x-common.form.errors :errors="$errors" />

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div>
        <x-common.form.label
          name="name"
          required="true"
        >
          Name
        </x-common.form.label>
        <x-common.form.input
          name="name"
          wire:model="name"
          required
        />
      </div>

      <div>
        <x-common.form.label
          name="icon"
        >
          Icon

          <a
            class="relative inline-block"
            href="https://heroicons.com/"
            target="_blank"
            x-tooltip="Open Heroicons library"
          >
            <x-common.icons name="information-circle" class="!h-5 !w-5"/>
          </a>
        </x-common.form.label>
        <x-common.form.input
          name="icon"
          wire:model.live.debounce.250ms="icon"
        />
      </div>

      <div>
        <x-common.form.label
          name="type"
          required="true"
        >
          Type
        </x-common.form.label>
        <x-common.form.select
          name="type"
          class="w-full bw-raw-select"
          wire:model.live="type"
          required
        >
          @foreach(\App\Infrastructure\EventBanner\Enums\BannerType::cases() as $t)
            <x-common.form.select.option :value="$t->value">
              {{ $t->name }}
            </x-common.form.select.option>
          @endforeach
        </x-common.form.select>
      </div>

      <div>
        <x-common.form.label
          name="countryCode"
          required="true"
        >
          Country
        </x-common.form.label>
        <x-common.form.select
          name="country"
          class="w-full bw-raw-select"
          wire:model.live="countryCode"
          required
        >
          <x-common.form.select.option value="all">
            All
          </x-common.form.select.option>

          @foreach(\App\Enums\Country::cases() as $country)
            <x-common.form.select.option :value="$country->value">
              {{ Str::headline($country->name) }}
            </x-common.form.select.option>
          @endforeach
        </x-common.form.select>
      </div>

      <div>
        <x-common.form.label
          name="publishAt"
        >
          Publish at (UTC)
        </x-common.form.label>
        <x-common.form.input
          name="publishAt"
          wire:model="publishAt"
          type="datetime-local"
        />
      </div>

      <div>
        <x-common.form.label
          name="expiresAt"
        >
          Expires at (UTC)
        </x-common.form.label>
        <x-common.form.input
          name="expiresAt"
          wire:model="expiresAt"
          type="datetime-local"
        />
      </div>
    </div>

    <div class="py-2">
      <x-common.form.translated-text
        :default-locale="$defaultLocale"
        :translatable-locales="$translatableLocales"
        :is-live="true"
        label="Title"
        name="title"
        model="title"
        placeholder="Christmas is coming and it represents 50% of our annual sales!"
        required
      />
    </div>

    <div class="py-2">
      <x-common.form.translated-text
        :is-textarea="true"
        rows="4"
        :default-locale="$defaultLocale"
        :translatable-locales="$translatableLocales"
        :is-live="true"
        label="Text"
        name="text"
        model="text"
        placeholder="Craft lovers are already buying their presents. For you to maximise sales, we recommend adding slots at least for the next 4 months so clients are reassured about your availability, and that their loved ones can attend the workshop they have been gifted!"
        required
      />
    </div>

    <div>
      <x-common.collapse class="!bg-gray-100 rounded-lg p-2 mb-2 my-5">
        <x-slot:title>CTA (optional)</x-slot:title>

        @foreach($cta as $key => $c)
          <div class="grid md:flex w-full mt-3 gap-2 md:gap-4">
            <div class="w-full">
              <x-common.form.translated-text
                :default-locale="$defaultLocale"
                :translatable-locales="$translatableLocales"
                :is-live="true"
                label="URL"
                name="url_{{ $key }}"
                model="cta.{{ $key }}.url"
                required
              />
            </div>

            <div class="w-full">
              <x-common.form.translated-text
                :default-locale="$defaultLocale"
                :translatable-locales="$translatableLocales"
                :is-live="true"
                label="Label"
                name="label_{{ $key }}"
                model="cta.{{ $key }}.label"
                required
              />
            </div>

            <div class="mt-0 md:mt-2 w-full">
              <x-common.form.label
                name="trackingId_{{ $key }}"
                required="true"
              >
                Tracking #ID
              </x-common.form.label>
              <x-common.form.input
                name="trackingId_{{ $key }}"
                wire:model="cta.{{ $key }}.tracking-id"
                required
              />
            </div>

            <div class="mt-0 md:mt-12 text-right">
              <x-common.button.circle
                color="danger"
                size="tiny"
                icon="trash"
                wire:click="removeCta({{ $key }})"
                loader="circle"
              />
            </div>
          </div>
        @endforeach

        <div>
          <x-common.button.alternate
            type="button"
            icon="plus"
            :rounded="false"
            wire:click="addCta"
            class="mt-3"
            loader="circle"
          >
            Add a CTA
          </x-common.button.alternate>
        </div>
      </x-common.collapse>

      <x-common.collapse class="!bg-gray-100 rounded-lg p-2 mb-2 my-5">
        <x-slot:title>Exclusion rules (optional)</x-slot:title>

        <x-common.alert
          type="info"
          show-close-icon="false"
        >
          If at least one rule is true, the banner won't be displayed to the artisan.<br>
          Leave a rule empty, so it won't be used.
        </x-common.alert>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
          @foreach(\App\Infrastructure\EventBanner\Enums\Rules::cases() as $rule)
            <x-dynamic-component
              component="admin.event-banner.{{ $rule->value }}"
              :data="$rulesData[$rule->value] ?? $rules[$rule->value] ?? null"
              :selected="$rules[$rule->value] ?? null"
            />
          @endforeach
        </div>
      </x-common.collapse>
    </div>

    <div class="text-center">
      <x-common.button
        :rounded="false"
        loader="circle"
        wire:click="save"
        class="mb-4"
      >
        Save
      </x-common.button>
    </div>

    <div class="sticky bottom-0 bg-white border-t z-10">
      <h3 class="text-primary text-lg mt-4">
        Demo
      </h3>

      <x-common.event-banner
        :type="\App\Infrastructure\EventBanner\Enums\BannerType::from($type)"
        :icon="$icon"
        :title="$title[$defaultLocale]"
        :cta="$this->getCta($defaultLocale)"
        :name="$name"
        :showCloseButton="false"
      >
        {!! $text[$defaultLocale] !!}
      </x-common.event-banner>
    </div>
  </x-common.modal>
</div>
