<div>
  <x-common.card
    class="bg-white rounded-lg shadow-md space-y-6 border"
  >
    <x-common.card.header
      title="Stock Transfer (active GC tickets and standby bookings) into GC amount"
    />
    <div
      x-data="{ isToday: @entangle('isNow'), showLoader: false }"
      class="space-y-6"
    >
      <div>
        <h2 class="font-bold">Current Stock</h2>
        <p>{{ $giftCardNumber }} places with a gift card tickets</p>
        <p>{{ $standbyNumber }} places with a standby booking</p>
      </div>

      <div>
        <x-common.form.label
          label="When the transfer should be done?"
          required
          name="when"
          class="!font-bold !text-base"
          :sublabel="$transferAtInfoSubLabel"
        />
        <x-form.input.radio.simple
          name="when"
          value="1"
          wire:model="isNow"
          label="Now"
          required
          x-on:click="isToday = true; $refs.transferAt.value = ''; showLoader = false;"
        />
        <div class="flex align-center">
          <x-form.input.radio.simple
            name="when"
            value="0"
            wire:model="isNow"
            label=""
            required
            x-on:click="showLoader = true"
          />
          <x-common.form.input
            name="transferAt"
            wire:model="transferAt"
            type="date"
            :addClearing="false"
            :min="\Carbon\Carbon::now()->addDay()->toDateString()"
            wire:change="updateSeats"
            x-ref="transferAt"
          />
        </div>
      </div>
      <div x-show="showLoader && isToday">
        <x-common.icons.loader
          trigger="livewire"
        />
      </div>
      <div
        x-show="!isToday"
        x-cloak
      >
        <x-common.form.label
          label="Should customers receive a last call?"
          name="withLastCall"
          class="!font-bold !text-base"
          :sublabel="$this->getLastCallLabel()"
        />
        <x-common.form.toggle
          class="text-sm"
          label_class="!my-0"
          label_position="right"
          wire:model="withLastCall"
          :disabled="$seatsAvailable === 0"
        />
      </div>
      <div>
        <x-common.form.label
          label="What do you want to suggest to customers who will receive a GC amount?"
          name="workshopsSelected"
          class="!font-bold !text-base"
          sublabel="An email will be send to the customers at the transfer date. If you select only one workshop, it will be presented as an extremely similar workshop. Otherwise, they will be suggested as workshops that could interest customers."
        />
        <div class="space-y-1">
          @foreach (range(0, 2) as $index)
            <livewire:common.form.workshop-select
              :selected-workshop="$workshopsSelected[$index]['id'] ?? null"
              :selected-workshop-name="$workshopsSelected[$index]['name'] ?? ''"
              :index="$index"
              :exceptions="[$workshopId]"
              wire:key="workshop-select-{{ $index }}"
            />
          @endforeach
        </div>
      </div>
      <div>
        <x-common.form.label
          label="Please explain why this workshop needs a stock transfer"
          required
          name="reason"
          class="!font-bold !text-base"
          sublabel="Please tell if a deactivation is needed and the reason of the stock transfer. Write down any information that might come in handy (why, deactivation/offboard needed…)"
        />
        <x-common.form.input
          required
          name="reason"
          wire:model="reason"
          placeholder="Explain the reason"
        />
      </div>
      <div class="flex justify-center">
        <x-common.button
          wire:click="validateTransferInfo"
          loader="circle"
        >
          Confirm
        </x-common.button>
      </div>
    </div>
  </x-common.card>

  <x-common.modal
    name="stock-transfer-confirmation-modal"
  >
    <x-slot:title>
      Stock transfer (active GC tickets and standby bookings) into GC amount
    </x-slot:title>
    <h1 class="mb-2 text-lg font-semibold">Do you confirm those actions?</h1>
    <ul class="space-y-1 list-disc list-inside">
      @if($isNow)
        <li>The transfer will be done immediately</li>
      @else
        <li>A task will be assigned to you on the {{ $transferAt }}</li>
      @endif

      @if($withLastCall)
        <li>An email will be send to the current stock (<b>{{ $giftCardNumber + $standbyNumber }}</b> customer's) to invite
          them to book a seat among the <b>{{ $seatsAvailable }}</b> available's.
        </li>
      @endif
      @if(!empty($workshopsSelected))
        <li>
          Those workshops will be recommended to the remaining stock at the transfer date:
          <ul class="ps-5 space-y-1 list-disc list-inside">
            @foreach($workshopsSelected as $index => $workshop)
              <li>{{ $workshop['name'] }}</li>
            @endforeach
          </ul>
        </li>
      @endif
      <li>Why this transfer: {{ nl2br($reason) }}</li>
    </ul>
    <x-slot:footer>
      <div
        class="w-full flex flex-col-reverse md:flex-row justify-center items-center space-y-2 md:space-y-0 space-y-reverse space-x-0 md:space-x-2"
      >
        <x-common.button.alternate
          type="button"
          size="small"
          x-on:click="$modal('stock-transfer-confirmation-modal').close()"
        >
          Cancel
        </x-common.button.alternate>
        <x-common.button
          type="button"
          size="small"
          wire:click="transfer"
          loader="circle"
        >
          Confirm
        </x-common.button>
      </div>
    </x-slot:footer>
  </x-common.modal>
</div>
