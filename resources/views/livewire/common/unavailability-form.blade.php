<div>
  @if(count($workshops) === 0)
    <x-common.alert
      type="warning"
      show-close-icon="false"
      class="my-4"
    >
      <p>{{ __('components/modals/add-event.no_workshop') }}</p>
      <p>{{  __('userartisan/unavailability.modal.unavailability.no_workshop') }}</p>
    </x-common.alert>
  @else
    <div class="overflow-y-auto max-h-[55svh]">
      <x-common.collapse
        chevron_position="right"
        icon="information-circle"
        class="bg-info-100 rounded-lg p-2 mb-2 my-5"
        title_class="pl-3 md:pl-1"
        icon_class="md:inline-block hidden"
      >
        <x-slot:title>
          {{__('userartisan/unavailability.modal.unavailability.help_message.title')}}
        </x-slot:title>

        <p class="mt-4">
          {!! __('userartisan/unavailability.modal.unavailability.help_message.message') !!}
        </p>
      </x-common.collapse>
      <x-common.form id="unavailability-form">
        <h4 class="my-3 font-semibold">{{ __('components/modals/add-event.explaination') }}</h4>
        @foreach($workshops as $workshop)
          <div class="flex items-center my-2">
            <x-master.input
              id="unavailability-workshop-{{$workshop['id']}}"
              class="block text-blue-500 my-0 accent-primary"
              type="checkbox"
              wire:model="selectedWorkshops.{{$workshop['id']}}"
            />
            <x-common.form.label
              name="unavailability-workshop-{{$workshop['id']}}"
              class="ml-5 !my-0"
            >
              <x-common.workshop.color
                :color="$workshop['backgroundColor']"
                :name="$workshop['name']"
                :duration="$workshop['duration']"
              />
            </x-common.form.label>
          </div>
        @endforeach

        <div class="grid grid-cols-2 gap-4 mt-5">
          <div>
            <x-common.form.label name="startAt">
              {{ __('userartisan/unavailability.modal.unavailability.form.date_start_label') }}
            </x-common.form.label>
            <x-common.form.date-range-picker
              datepicker datepicker-autohide datepicker-buttons
              wire:model.live="startAt"
              name="startAt"
              :placeholder="__('userartisan/unavailability.modal.unavailability.form.date_start_placeholder')"
            />
          </div>

          <div>
            <x-common.form.label name="end_at">
              {{ __('userartisan/unavailability.modal.unavailability.form.date_end_label') }}
            </x-common.form.label>
            <x-common.form.date-range-picker
              datepicker datepicker-autohide datepicker-buttons
              wire:model.live="endAt"
              name="endAt"
              min="{{ $startAt }}"
              :placeholder="__('userartisan/unavailability.modal.unavailability.form.date_end_placeholder')"
            />
          </div>
        </div>

        <x-common.form.label
          name="message"
          :sublabel="__('userartisan/unavailability.modal.unavailability.form.reason_message_add_info')"
          sublabel_class="!text-xs italic !text-slate-600"
        >
          {{ __('userartisan/unavailability.modal.unavailability.form.reason_message_label') }}
        </x-common.form.label>
        <x-common.form.textarea
          name="message"
          wire:model="message"
          rows="1"
          :placeholder="__('userartisan/unavailability.modal.unavailability.form.reason_message_placeholder')"
        />
      </x-common.form>
    </div>
    <div class="text-center">
      @if(!empty($info))
        <x-common.alert class="w-full my-1 md:my-3 !text-left" show-close-icon="false">
          <div>{{ $info }}</div>
        </x-common.alert>
      @endif

      <x-common.form.errors :errors="$errors" />

      <x-common.button
        name="submit-unavailability-form"
        loader="circle"
        wire:click="create"
        id="gtm_clic_cta_confirmer_modale_calendrier"
      >
        {{ __('userartisan/unavailability.modal.unavailability.form.cta_confirm') }}
      </x-common.button>
    </div>
  @endif
</div>
