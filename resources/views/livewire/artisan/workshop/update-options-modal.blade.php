@use(App\Enums\Event\EventType)
<x-common.modal
  name="workshop-update-options-modal"
  :title="__('components/modals/workshop/update-options.title')"
  gtmCloseClass="gtm-workshop-update-options-modal"
>
  <x-common.tab name="private-booking-requests">
    <x-slot name="headings">
      <x-common.tab.heading
        name="private-booking"
        active="true"
        :label="__('components/modals/workshop/update-options.tabs.group_booking.title')"
        heading_style="text-xl"
      />
      @if(!$isGroupOnly)
        <x-common.tab.heading
          name="date-request"
          :label="__('components/modals/workshop/update-options.tabs.date_request.title')"
          heading_style="text-xl"
        />
      @endif
    </x-slot>
    <x-common.tab.body>
      <x-common.tab.content
        name="private-booking"
        active="true"
        class="!px-0 !pb-4 !pt-8"
      >
        <div x-data="{ isPrivateArtisanManagedLocation: {{ $isPrivateArtisanManagedLocation ? 'true' : 'false' }} }">
          <x-common.form.toggle
            class="text-base font-bold"
            label_class="!my-0"
            wire:model="isPrivateArtisanManagedLocation"
            x-on:click="isPrivateArtisanManagedLocation = !isPrivateArtisanManagedLocation"
          >
            <x-slot:label>
              {{ __('components/modals/workshop/update-options.tabs.group_booking.artisan_managed_location') }}
              <x-common.tooltip>
                {{ __('components/modals/workshop/update-options.once_a_day') }}
              </x-common.tooltip>
            </x-slot:label>
          </x-common.form.toggle>
          <div x-show="isPrivateArtisanManagedLocation" x-transition>
            <label for="minParticipantsPrivateArtisanManagedLocation" class="block mb-3 text-sm font-medium text-gray-900 mt-4">
              {{ __('components/modals/workshop/update-options.tabs.group_booking.min_participants_private_artisan_managed_location') }}
            </label>
            <x-common.form.select
              name="minParticipantsPrivateArtisanManagedLocation"
              wire:model="minParticipantsPrivateArtisanManagedLocation"
            >
              @for ($i = min($minParticipants, $minParticipantsForPrivateEvent); $i <= 200; $i++)
                <x-common.form.select.option
                  :value="$i"
                  :selected="$i === $minParticipantsPrivateArtisanManagedLocation"
                >
                  {!! trans_choice('userartisan/view/workshops.common.participants_option', $i, ['participants' => $i]) !!}
                </x-common.form.select.option>
              @endfor
            </x-common.form.select>

            <label for="maxParticipantsPrivateArtisanManagedLocation" class="block mb-3 text-sm font-medium text-gray-900 mt-4">
              {{ __('components/modals/workshop/update-options.tabs.group_booking.max_participants_private_artisan_managed_location') }}
            </label>
            <x-common.form.select
              name="maxParticipantsPrivateArtisanManagedLocation"
              wire:model="maxParticipantsPrivateArtisanManagedLocation"
            >
              @for ($i = $minParticipantsForPrivateEvent; $i <= 200; $i++)
                <x-common.form.select.option
                  :value="$i"
                  :selected="$i === $maxParticipantsPrivateArtisanManagedLocation"
                >
                  {!! trans_choice('userartisan/view/workshops.common.participants_option', $i, ['participants' => $i]) !!}
                </x-common.form.select.option>
              @endfor
            </x-common.form.select>
          </div>
        </div>

        <div x-data="{ isPrivateClientManagedLocation: {{ $isPrivateClientManagedLocation ? 'true' : 'false' }} }" class="mt-8">
          <x-common.form.toggle
            class="text-base font-bold"
            label_class="!my-0"
            wire:model="isPrivateClientManagedLocation"
            x-on:click="isPrivateClientManagedLocation = !isPrivateClientManagedLocation"
          >
            <x-slot:label>
              {{ __('components/modals/workshop/update-options.tabs.group_booking.client_managed_location') }}
              <x-common.tooltip>
                {{ __('components/modals/workshop/update-options.once_a_day') }} <br/>
                {{ __('components/modals/workshop/update-options.tabs.group_booking.client_managed_location_tooltip', ['amount' => currencyFormat(\App\Shared\Amount::make(config('private-booking.private_client_managed_location.travel_expenses'), $currency))]) }}
              </x-common.tooltip>
            </x-slot:label>
          </x-common.form.toggle>
          <div x-show="isPrivateClientManagedLocation" x-transition>
            <label for="minParticipantsPrivateClientManagedLocation" class="block mb-3 text-sm font-medium text-gray-900 mt-4">
              {{ __('components/modals/workshop/update-options.tabs.group_booking.min_participants_private_client_managed_location') }}
            </label>
            <x-common.form.select
              name="minParticipantsPrivateClientManagedLocation"
              wire:model="minParticipantsPrivateClientManagedLocation"
            >
              @for ($i = min($minParticipants, $minParticipantsForPrivateClientManagedLocationEvent); $i <= 200; $i++)
                <x-common.form.select.option
                  :value="$i"
                  :selected="$i === $minParticipantsPrivateClientManagedLocation"
                >
                  {!! trans_choice('userartisan/view/workshops.common.participants_option', $i, ['participants' => $i]) !!}
                </x-common.form.select.option>
              @endfor
            </x-common.form.select>

            <label for="maxParticipantsPrivateClientManagedLocation" class="block mb-3 text-sm font-medium text-gray-900 mt-4">
              {{ __('components/modals/workshop/update-options.tabs.group_booking.max_participants_private_client_managed_location') }}
            </label>
            <x-common.form.select
              name="maxParticipantsPrivateClientManagedLocation"
              wire:model="maxParticipantsPrivateClientManagedLocation"
            >
              @for ($i = $minParticipantsForPrivateClientManagedLocationEvent; $i <= 200; $i++)
                <x-common.form.select.option
                  :value="$i"
                  :selected="$i === $maxParticipantsPrivateClientManagedLocation"
                >
                  {!! trans_choice('userartisan/view/workshops.common.participants_option', $i, ['participants' => $i]) !!}
                </x-common.form.select.option>
              @endfor
            </x-common.form.select>
          </div>
        </div>
      </x-common.tab.content>

      @if(!$isGroupOnly)
        <x-common.tab.content
          name="date-request"
          class="!px-0 !pb-4 !pt-8"
        >
          <x-common.form.toggle
            class="text-base font-bold"
            label_class="!my-0"
            wire:model="dateRequestActivated"
          >
            <x-slot:label>
              {{ __('components/modals/workshop/update-options.tabs.date_request.label') }}
              <x-common.tooltip>
                {{ __('components/modals/workshop/update-options.once_a_day') }}
              </x-common.tooltip>
            </x-slot:label>
          </x-common.form.toggle>
        </x-common.tab.content>
      @endif
    </x-common.tab.body>
  </x-common.tab>

  @if($errors->any())
    <div id="update-options-errors" x-init="scrollToErrors" class="mb-4">
      <x-common.form.errors :errors="$errors"/>
    </div>
  @endif

  <x-slot:footer class="gap-2">
    <x-common.button
      outlined
      class="w-1/2 md:w-1/3"
      size="small"
      x-on:click="$modal('workshop-update-options-modal').close()"
    >
      {{ __('components/modals/workshop/update-options.btns.cancel') }}
    </x-common.button>
    <x-common.button
      class="w-1/2 md:w-1/3 submit-update-options-form"
      size="small"
      loader="circle"
      x-on:click="scrollToErrors"
      wire:click="update"
    >
      {{ __('components/modals/workshop/update-options.btns.confirm') }}
    </x-common.button>
  </x-slot:footer>

  @push('scripts')
    <script type="text/javascript">
      function scrollToErrors() {
        const errors = document.getElementById('update-options-errors');

        if (errors !== null) {
          errors.scrollIntoView({behavior: 'smooth', block: 'end'});
        }
      }
    </script>
  @endpush
</x-common.modal>
