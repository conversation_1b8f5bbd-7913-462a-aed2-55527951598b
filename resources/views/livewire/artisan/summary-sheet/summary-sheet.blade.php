<div>
  <x-common.card
    reduce-padding="true"
    class="my-2 w-full mt-4"
  >
    <x-slot:header>
      <x-common.page-header :title="__('userartisan/view/workshops.show.summary_sheet.title')" :btnsAbove="true">
        <x-slot:buttons>
          @if($workshop->getLastSummarySheet() !== null)
            <x-common.button
              size="small"
              @click="$modal('summary-sheet-publish-modal').open()"
              icon="arrow-up-on-square"
            >
              {{ __('userartisan/view/summary-sheet.publish_version') }}
            </x-common.button>

            <x-common.button
              size="small"
              href="{{ $workshop->getLastSummarySheet()->getGoogleDocsEditUrl() }}"
              target="_blank"
              outlined
              icon="pencil"
            >
              {{ __('userartisan/view/summary-sheet.edit') }}
            </x-common.button>

            @if($workshop->getLastSummarySheet() !== null)
              <x-common.button
                size="small"
                color="danger"
                @click="$modal('summary-sheet-remove-modal').open()"
                icon="trash"
              >
                {{ __('userartisan/view/summary-sheet.remove_version') }}
              </x-common.button>
            @endif
          @else
            <x-common.button
              size="small"
              @click="$modal('summary-sheet-create-modal').open()"
              icon="document-plus"
            >
              {{ __('userartisan/view/summary-sheet.create') }}
            </x-common.button>
          @endif
        </x-slot:buttons>
      </x-common.page-header>
    </x-slot:header>

    <div>
      @if($workshop->getLastSummarySheet() !== null)
        @if($workshop->getLastPublishedSummarySheet() === null)
          {{ __('userartisan/view/summary-sheet.not_published_yet') }}
        @else
          {{ __('userartisan/view/summary-sheet.last_published_version') }}
          <x-common.link
            href=""
            wire:click="download"
            target="_blank"
          >
            {{ dateAndTimeLongFormat($workshop->getLastPublishedSummarySheet()->created_at) }}
          </x-common.link>
        @endif
      @endif
    </div>
  </x-common.card>

  {{-- Modals --}}
  <x-common.modal
    name="summary-sheet-publish-modal"
    :title="__('userartisan/view/summary-sheet.publish_version')"
  >
    <p>{{ __('userartisan/view/summary-sheet.confirm_publish') }}</p>

    <x-slot:footer>
      <x-common.button
        loader="circle"
        wire:click="publish"
      >
        {{ __('userartisan/view/summary-sheet.publish_version') }}
      </x-common.button>
    </x-slot:footer>
  </x-common.modal>

  <x-common.modal
    name="summary-sheet-remove-modal"
    :title="__('userartisan/view/summary-sheet.remove_version')"
  >
    <p>{{ __('userartisan/view/summary-sheet.confirm_remove') }}</p>

    <x-slot:footer>
      <x-common.button
        wire:click="delete"
        loader="circle"
      >
        {{ __('userartisan/view/summary-sheet.remove_version') }}
      </x-common.button>
    </x-slot:footer>
  </x-common.modal>

  <x-common.modal
    name="summary-sheet-create-modal"
    :title="__('userartisan/view/summary-sheet.create')"
  >
    <p>{{ __('userartisan/view/summary-sheet.create_information') }}</p>

    <x-slot:footer>
      <x-common.button
        wire:click="create"
        loader="circle"
      >
        {{ __('userartisan/view/summary-sheet.create') }}
      </x-common.button>
    </x-slot:footer>
  </x-common.modal>
</div>

@script
<script type="text/javascript">
  $wire.on('summary-sheet-create-success', (event) => {
    let link = document.createElement('a');
    link.href = event.url;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
  });
</script>
@endscript
