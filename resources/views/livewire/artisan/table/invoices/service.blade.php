<div class="bg-white relative shadow-md sm:rounded-lg">
  <x-common.tables
    :links="$links"
  >
    <x-slot:columns>
      <x-common.tables.th>
        {{ __('userartisan/view/billing.service_table.date') }}
      </x-common.tables.th>

      <x-common.tables.th>
        {{ __('userartisan/view/billing.service_table.booking') }}
      </x-common.tables.th>

      <x-common.tables.th>
        {{ __('userartisan/view/billing.service_table.total_amount') }}
      </x-common.tables.th>

      <x-common.tables.th>
        {{ __('userartisan/view/billing.service_table.bill') }}
      </x-common.tables.th>
    </x-slot:columns>

    @foreach($invoices as $invoice)
      <x-common.tables.tr key="{{ time().$invoice['id'] }}">
        <x-common.tables.td
          class="font-extrabold"
        >
          #{{ $invoice['event_id'] }} - {{ $invoice['event_date'] }}
        </x-common.tables.td>
        <x-common.tables.td>
          {{ __('userartisan/controllers/utils.booking.present.id', ['id' => $invoice['booking_id']]) }}<br>
          {{ $invoice['booking_client_name'] }} - {{ $invoice['booking_seats'] }}
        </x-common.tables.td>
        <x-common.tables.td>{{ $invoice['amount'] }}</x-common.tables.td>
        <x-common.tables.td>
          <x-common.button
            color="primary-500" id="btn-download-service-invoice"
            size="tiny"
            class="m-2"
            :href="route(localizedRouteName('artisan.invoices.services.download'), $invoice['uuid'] ?? $invoice['id'])"
            target="_blank"
          >
            <x-common.icons name="arrow-down-tray" class="!h-4 !w-4 md:mr-2" />
            <span class="hidden md:inline">{{ $invoice['number'] }}</span>
          </x-common.button>
        </x-common.tables.td>
      </x-common.tables.tr>
    @endforeach
  </x-common.tables>

  <livewire:common.event.cancel-event-modal/>
</div>
