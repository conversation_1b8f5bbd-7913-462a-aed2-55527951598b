@use(App\Domain\PrivateBooking\Enums\PrivateBookingRequestLocation)
@use(App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep)
<div x-data="{step: @entangle('step').live}">
  <x-common.modal
    size="big"
    name="private-booking-request-modal"
    :title="__('components/modals/private-booking-requests.title')"
    gtmCloseClass="gtm-close-group-booking-request-modal"
    body-css="!p-0 !m-0"
  >
    @isset($requestId)
      <div class="flex flex-col md:flex-row min-h-full p-0">
        <!-- left -->
        <div
          class="w-full md:w-1/2 px-4 py-4 md:px-10 md:py-10 text-gray-900 flex flex-col space-y-4 md:space-y-6
        border-b-2 md:border-b-0 border-gray-100 bg-secondary-50"
        >
          <x-artisan.private-booking.request-information
            :workshop-name="$this->workshopName"
            :estimate-price-incl-vat="$estimatedPriceInclVat"
            :currency="$workshopCurrency"
            :participants="$participantsDisplay"

          />

          <!-- slots display -->
          @if(in_array($step, [
            PrivateBookingRequestManagementStep::FIRST_STEP->value,
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP->value,
            PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP->value
          ], true))
            <x-artisan.private-booking.customer-slots
              :proposed-slots="$proposedSlots"
            />
          @endif

          <!-- TODO: could be one condition after backend rework -->
          @if(in_array( $step, [
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP->value,
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP->value,
          ], true))
            <x-artisan.private-booking.selected-slots
              :title="__('components/modals/private-booking-requests.steps.add_message.accepted_slot')"
              :selected-slots="array_filter($selectedSlots)"
              :timezone="$workshopTimezone"
              :duration-day="$workshopDurationDay"
              :duration-hour="$workshopDurationHour"
              :duration-minute="$workshopDurationMinute"
            />
          @endif

          @if(in_array( $step, [
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP->value,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP->value,
          ], true))
            <x-artisan.private-booking.selected-slots
              :title="__('components/modals/private-booking-requests.steps.add_message.proposed_slot')"
              :selected-slots="array_filter($otherProposalSlot, fn ($slot) => !empty($slot))"
              :timezone="$workshopTimezone"
              :duration-day="$workshopDurationDay"
              :duration-hour="$workshopDurationHour"
              :duration-minute="$workshopDurationMinute"
            />
          @endif

          <!-- customer location -->
          <x-artisan.private-booking.customer-location
            :customer-location="$customerLocation"
          />

          <!-- customer message -->
          <x-artisan.private-booking.customer-message
            :message="$customerMessage"
            :customer="$customerDisplay"
          />
        </div>


        <!-- right -->
        <div class="w-full md:w-1/2 px-4 py-4 md:px-10 md:py-10 text-gray-900 bg-white">
          @if($step === PrivateBookingRequestManagementStep::FIRST_STEP->value )
            <div>
              <h3 class="text-base">
                {{ __('components/modals/private-booking-requests.steps.first.your_choice') }}
              </h3>
              <x-common.alert
                class="mt-3"
                show-close-icon="false"
              >
                {{ __('components/modals/private-booking-requests.steps.first.information') }}
              </x-common.alert>
              <div class="flex flex-col mt-3 space-y-2">
                <x-form.input.radio.simple
                  wire:model="responseChoice"
                  id="chose-proposed-slot-step-{{$requestId}}"
                  name="responseChoice-{{$requestId}}"
                  :value="PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP->value"
                  :label="__('components/modals/private-booking-requests.steps.first.chose_proposed_slot')"
                  :disabled="empty($artisanAvailability)"
                />
                @empty($artisanAvailability)
                  <small
                    class="text-gray-500 ml-5 !mt-0 leading-tight"><em>{{ __('components/modals/private-booking-requests.has_no_available_slots') }}</em></small>
                @endempty

                <x-form.input.radio.simple
                  wire:model="responseChoice"
                  id="other-proposal-step-{{$requestId}}"
                  name="responseChoice-{{$requestId}}"
                  :value="PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP->value"
                  :label="__('components/modals/private-booking-requests.steps.first.other_proposal')"
                />
                <x-form.input.radio.simple
                  wire:model="responseChoice"
                  id="refuse-step-{{$requestId}}"
                  name="responseChoice-{{$requestId}}"
                  :value="PrivateBookingRequestManagementStep::REFUSE_STEP->value"
                  :label="__('components/modals/private-booking-requests.steps.first.refuse')"
                />
              </div>
            </div>
          @endif

          @if($step === PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP->value )
            <div>
              <h3 class="text-base">
                {{ __('components/modals/private-booking-requests.chose_date') }}
              </h3>

              @if($hasSlotNotAvailable || $hasSlotAvailableWithEvent)
                <x-common.alert
                  class="mt-3"
                  show-close-icon="false"
                >
                  @if($hasSlotNotAvailable)
                    <p>{{ __('components/modals/private-booking-requests.has_slot_not_available') }}</p>
                  @endif
                  @if($hasSlotAvailableWithEvent)
                    <p>{!! __('components/modals/private-booking-requests.has_slot_available_with_event') !!}</p>
                  @endif
                </x-common.alert>
              @endif

              @foreach($artisanAvailability as $index => $availability)
                <div class="mt-4 border rounded-2xl border-gray-300 md:border-none md:rounded-none p-4">
                  <div class="flex flex-col space-y-4 md:flex-row md:space-y-0 items-center">
                    <div class="flex items-center w-full md:w-1/2">
                      <x-master.input
                        id="date-{{$requestId}}-{{$index}}"
                        class="block peer text-blue-500 my-0 accent-primary"
                        type="checkbox"
                        wire:model.live="selectedDays.{{$index}}"
                      />
                      <x-master.label
                        name="date-{{$requestId}}-{{$index}}"
                        :label="dateLongFormat(\Carbon\Carbon::parse($index), $workshopTimezone)"
                        class="text-gray-500 peer-checked:text-gray-900 mx-5 my-0"
                      />
                    </div>
                    <x-common.form.select
                      name="selectedSlots_{{$index}}"
                      :allow_empty_option="true"
                      wire:model.live="selectedSlots.{{$index}}"
                    >
                      <x-common.form.select.option>
                        {{ __('components/modals/private-booking-requests.slot') }}
                      </x-common.form.select.option>
                      @foreach($availability['slots'] as $key => $slot)
                        @php
                          // Livewire as some issue with Carbon serialization
                          $slot['slot'] = $slot['slot'] instanceof \Carbon\Carbon ? $slot['slot'] : \Carbon\Carbon::parse($slot['slot']);
                        @endphp
                        <x-common.form.select.option
                          :value="$key"
                          :disabled="$slot['available'] === false"
                        >
                          {{ timeShort($slot['slot'], $workshopTimezone) }}
                          - {{ timeShort($slot['slot']->copy()->addHours($workshopDurationHour)->addMinutes($workshopDurationMinute), $workshopTimezone) }}
                        </x-common.form.select.option>
                      @endforeach
                    </x-common.form.select>
                  </div>
                </div>
                @if(count($availability['events']))
                  <div class="mt-2 md:mt-0">
                    <x-common.icons name="information-circle"/>
                    {{ __('components/modals/private-booking-requests.has_booking') }}
                  </div>
                @endif
              @endforeach
            </div>
          @endif

          @if($step === PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP->value )
            <div>
              <h3 class="text-base">
                {{ __('components/modals/private-booking-requests.chose_date') }}
              </h3>
              <div class="flex flex-col space-y-2 mt-4">
                @if(count($otherProposalSlot) < 1)
                  <x-artisan.date-request.steps.other-proposal
                    :index="0"
                    :otherProposalSlot="$otherProposalSlot"
                    :availableSlots="$availableSlots"
                    :request="$request"
                  />
                @else
                  @foreach($otherProposalSlot as $key => $otherProposal)
                    <x-artisan.date-request.steps.other-proposal
                      :index="$key"
                      :otherProposalSlot="$otherProposalSlot"
                      :availableSlots="$availableSlots"
                      :request="$request"
                    />
                  @endforeach
                @endif
              </div>
              @if(count($otherProposalSlot) < 30)
                <div class="flex flex-row-reverse mt-3">
                  <a
                    class="text-blue-600 hover:underline hover:cursor-pointer"
                    wire:click="addOtherProposal"
                  >
                    {{ __('components/modals/private-booking-requests.steps.other_proposal.add_slot') }}
                    <x-common.icons name="plus-circle"/>
                  </a>
                </div>
              @endif
            </div>
          @endif

          @if($step === PrivateBookingRequestManagementStep::REFUSE_STEP->value )
            <div>
              <h3 class="text-base">
                {{ __('components/modals/private-booking-requests.steps.refuse.title') }}
              </h3>
              <x-common.alert
                class="mt-3"
                show-close-icon="false"
              >
                {{ __('components/modals/private-booking-requests.steps.refuse.information') }}
              </x-common.alert>
              <div class="flex flex-col mt-3 space-y-2" x-data="{refuseReason: @entangle('refuseReason').live}" x-cloak>
                <x-form.input.radio.simple
                  wire:model="refuseReason"
                  id="availability-{{$requestId}}"
                  name="refuseReason-{{$requestId}}"
                  :value="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Availability->value"
                  :label="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Availability->getLabel()"
                />
                <x-form.input.radio.simple
                  wire:model="refuseReason"
                  id="price-{{$requestId}}"
                  name="refuseReason-{{$requestId}}"
                  :value="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Price->value"
                  :label="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Price->getLabel()"
                />
                <x-form.input.radio.simple
                  wire:model="refuseReason"
                  id="participant-{{$requestId}}"
                  name="refuseReason-{{$requestId}}"
                  :value="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Participant->value"
                  :label="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Participant->getLabel()"
                />
                <x-form.input.radio.simple
                  wire:model="refuseReason"
                  id="other-{{$requestId}}"
                  name="refuseReason-{{$requestId}}"
                  :value="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Other->value"
                  :label="\App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Other->getLabel()"
                />
                <x-common.form.textarea
                  x-show="refuseReason === '{{ \App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason::Other->value }}'"
                  :placeholder="__('components/modals/private-booking-requests.steps.refuse.other')"
                  rows="4"
                  wire:model="refuseReasonOther">
                </x-common.form.textarea>
              </div>
            </div>
          @endif

          @if(in_array($step, [
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP->value,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP->value,
          ], true))
            <x-artisan.private-booking.quote-form
              :title="__('components/modals/private-booking-requests.steps.quote.title')"
              :currency="$currency"
              :estimate-price="$bookingPrice"
              :location="$location"
              :customer-location-price="$customerLocationPrice"
            />
          @endif

          @if(in_array($step, [
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP->value,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP->value,
          ], true))
            <x-artisan.date-request.steps.add-message
              :title="__('components/modals/private-booking-requests.steps.add_message.title')"
            >
              <x-slot:tooltip>
                <x-common.tooltip>
                  {!! __('userartisan/view/private-booking-requests.bypass') !!}
                </x-common.tooltip>
              </x-slot:tooltip>
            </x-artisan.date-request.steps.add-message>
          @endif

          @if($step === PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP->value)
            <x-artisan.date-request.steps.add-message
              :title="__('components/modals/private-booking-requests.steps.add_message.refuse')"
            >
              <x-slot:tooltip>
                <x-common.tooltip>
                  {!! __('userartisan/view/private-booking-requests.bypass') !!}
                </x-common.tooltip>
              </x-slot:tooltip>
            </x-artisan.date-request.steps.add-message>
          @endif

          @if($errors->any())
            <div id="errors-messages-{{$requestId}}" class="mb-4">
              <x-common.form.errors :errors="$errors"/>
            </div>
          @endif
        </div>
      </div>
    @endisset

    <x-slot:footer class="!justify-end space-x-2">
      @if($step !== PrivateBookingRequestManagementStep::FIRST_STEP->value )
        <x-common.button
          class="w-1/2 md:w-80"
          size="small"
          icon="arrow-small-left"
          icon-position="left"
          x-on:click="scrollToErrors"
          wire:click="backStep"
          loader="circle"
        >
          {{ __('components/modals/private-booking-requests.btn.back') }}
        </x-common.button>
      @endif
      <x-common.button
        class="w-1/2 md:w-80 {{ PrivateBookingRequestManagementStep::getGtmTag($step) }}"
        size="small"
        icon="arrow-small-right"
        loader="circle"
        x-on:click="scrollToErrors"
        wire:click="nextStep"
        :disabled="!isset($requestId)"
      >
        @if(PrivateBookingRequestManagementStep::isLastStep($step))
          {{ __('components/modals/private-booking-requests.btn.confirm') }}
        @else
          {{ __('components/modals/private-booking-requests.btn.next') }}
        @endif
      </x-common.button>
    </x-slot:footer>
  </x-common.modal>
</div>
