<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Admin;
use App\Models\EvenementRecurrent;
use App\Models\User;
use App\Models\UserArtisan;

class RecurringEventPolicy
{
    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User|Admin|UserArtisan $user, EvenementRecurrent $recurringEvent): bool
    {
        if ($user instanceof User) {
            return false;
        }

        if ($user instanceof Admin) {
            return true;
        }

        return $user->can('manage', $recurringEvent);
    }

    /**
     * Determine whether the user can manage the model.
     */
    public function manage(User|Admin|UserArtisan $user, EvenementRecurrent $recurringEvent): bool
    {
        if ($user instanceof User) {
            return false;
        }

        if ($user instanceof Admin) {
            return true;
        }

        return $user->can('manage', $recurringEvent->atelier->artisan);
    }
}
