<?php

declare(strict_types=1);

namespace App\Livewire\Common;

use App\Domain\Booking\Enums\UnavailabilityOrigin;
use App\Domain\Booking\Exceptions\Unavailability\AlreadyUnavailable;
use App\Domain\Booking\Exceptions\Unavailability\EventAlreadyBooked;
use App\Domain\Booking\Exceptions\Unavailability\EventWithoutBooking;
use App\Domain\Booking\Services\UnavailabilityCreator;
use App\Domain\Content\Repositories\WorkshopRepository;
use App\Models\Artisan;
use App\Models\Atelier;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class UnavailabilityForm extends Component
{
    public int $artisanId;
    private Artisan $artisan;
    /** @var array<int, bool> */
    public array $selectedWorkshops = [];
    public string $startAt;
    public string $endAt;
    public string $message;
    public string $info = '';

    protected UnavailabilityCreator $creator;

    public function boot(UnavailabilityCreator $creator): void
    {
        $this->creator = $creator;
        $this->artisan = Artisan::findOrFail($this->artisanId);
    }

    public function render(WorkshopRepository $workshopRepository): View
    {
        $this->message = __('userartisan/unavailability.modal.unavailability.form.reason_message_default');

        $workshops = $workshopRepository->getWorkshopsForUnavailabilityCreation($this->artisan)->map(fn (Atelier $workshop) => [
            'id' => $workshop->id,
            'name' => $workshop->nom,
            'backgroundColor' => $workshop->background_color,
            'duration' => $workshop->present()->durationShort(),
        ]);

        return view('livewire.common.unavailability-form', ['workshops' => $workshops]);
    }

    public function create(): void
    {
        $this->validate([
            'selectedWorkshops' => 'required|array',
            'startAt' => 'required|date|after:today',
            'endAt' => 'required|date|after_or_equal:startAt',
            'message' => 'required|max:300',
        ], [
            'selectedWorkshops.required' => __('components/modals/add-event.messages.error.selected_workshops_required'),
            'startAt.required' => __('userartisan/unavailability.modal.unavailability.validation_message.start_at_required'),
            'startAt.after' => __('userartisan/unavailability.modal.unavailability.validation_message.start_at_after'),
            'endAt.required' => __('userartisan/unavailability.modal.unavailability.validation_message.end_at_required'),
            'endAt.after_or_equal' => __('userartisan/unavailability.modal.unavailability.validation_message.end_at_after'),
            'message.required' => __('userartisan/unavailability.modal.unavailability.validation_message.message_required'),
            'message.max' => __('userartisan/unavailability.modal.unavailability.validation_message.message_max'),
        ]);

        /** @var Collection<int, Atelier> $workshops */
        $workshops = Atelier::findMany($this->getSelectedWorkshopIds());

        if ($workshops->count() === 0) {
            $this->addError(
                'selected_workshops_required',
                __('components/modals/add-event.messages.error.selected_workshops_required')
            );

            return;
        }

        try {
            $this->creator->createUnavailabilityForArtisan(
                $this->artisanId,
                $workshops->pluck('id')->toArray(),
                Carbon::parse($this->startAt)->startOfDay(),
                Carbon::parse($this->endAt)->endOfDay(),
                $this->message,
                UnavailabilityOrigin::Artisan
            );

            $this->notify(__('userartisan/unavailability.modal.unavailability.form.success'));
            $this->dispatch('refreshCalendar');
            $this->modal('add-slot-calendar')->close();
        } catch (AlreadyUnavailable) {
            $this->addError('date', __('userartisan/unavailability.exception.already_unavailable'));
        } catch (EventAlreadyBooked) {
            $this->addError('date', __('userartisan/unavailability.exception.event_already_booked'));
        } catch (\Throwable $exception) {
            Log::error('An error occurred while trying to add unavailability for artisan', [
                'exception' => $exception,
                'artisanId' => $this->artisanId,
            ]);
            $this->addError('generic_error', __('userartisan/unavailability.exception.event_already_booked'));
        }
    }

    /**
     * livewire hook to listen startAt changes.
     */
    public function updatedStartAt(): void
    {
        $this->resetErrorBag();
        $this->checkFormInput();
    }

    public function updatedEndAt(): void
    {
        $this->resetErrorBag();
        $this->checkFormInput();
    }

    protected function checkFormInput(): void
    {
        if (empty($this->startAt) || empty($this->endAt)) {
            return;
        }

        try {
            $this->creator->checkUnavailabilityBeCreated(
                $this->getSelectedWorkshopIds(),
                Carbon::parse($this->startAt)->startOfDay(),
                Carbon::parse($this->endAt)->endOfDay()
            );
        } catch (AlreadyUnavailable) {
            $this->addError('dates', __('userartisan/unavailability.exception.already_unavailable'));
        } catch (EventAlreadyBooked) {
            $this->addError('dates', __('userartisan/unavailability.exception.event_already_booked'));
        } catch (EventWithoutBooking) {
            $this->info = __('userartisan/unavailability.exception.event_without_booking');
        } catch (\Throwable $exception) {
            Log::error($exception->getMessage(), [
                'exception' => $exception,
                'artisanId' => $this->artisanId,
            ]);
            $this->addError('date', 'generic exception message TO CHANGE');
        }
    }

    /**
     * @return int[]
     */
    private function getSelectedWorkshopIds(): array
    {
        return array_keys(array_filter($this->selectedWorkshops, function ($isSelected) {
            return $isSelected === true;
        }));
    }
}
