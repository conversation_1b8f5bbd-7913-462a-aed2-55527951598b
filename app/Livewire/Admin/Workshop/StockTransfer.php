<?php

declare(strict_types=1);

namespace App\Livewire\Admin\Workshop;

use App\Domain\Workshop\Jobs\SendReminderBeforeStockTransfer;
use App\Domain\Workshop\Jobs\StockTransfer as StockTransferJob;
use App\Enums\NotificationEnum;
use App\Infrastructure\Crm\Hubspot\DTO\Task;
use App\Infrastructure\Crm\Hubspot\Jobs\CreateTaskForStockTransfer;
use App\Infrastructure\Data\BigQuery\Repositories\BigQueryWorkshopRepository;
use App\Models\Admin;
use App\Models\Atelier;
use App\Models\Evenement;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Auth\AuthManager;
use Illuminate\Bus\Dispatcher as BusDispatcher;
use Illuminate\Contracts\Bus\Dispatcher;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\View;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Attributes\Validate;
use Livewire\Component;

/**
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class StockTransfer extends Component
{
    #[Locked]
    public Atelier $workshop;
    public int $workshopId;

    // form
    public bool $withLastCall = false;
    /** @var array<int, array<string, mixed>> */
    public array $workshopsSelected = [];
    #[Url]
    #[Validate('required', message: 'You must explain why this stock is transferred', translate: false)]
    public string $reason = '';
    public bool $isNow = true;
    #[Validate('required_if:isNow,false', message: 'Some information is missing.', translate: false)]
    public string $transferAt = '';
    public string $transferAtInfoSubLabel = '';

    // info Data
    public int $giftCardNumber = 0;
    public int $standbyNumber = 0;
    public int $seatsAvailable = 0;
    public ?string $furthestEventWithParticipant = null;
    /** @var array<int, string> */
    public array $workshopsSuggestion = [];
    /** @var array<int, string> $workshopsInQuery */
    #[Url(as: 'workshops')]
    public array $workshopsInQuery = [];

    private AuthManager $authManager;
    private Dispatcher $dispatcher;
    private BusDispatcher $busDispatcher;

    #[On('removeWorkshopSelected')]
    public function removeWorkshopSelected(int $index): void
    {
        unset($this->workshopsSelected[$index]);
    }

    public function workshopSelected(int $index, ?int $workshopId, string $workshopName): void
    {
        $this->workshopsSelected[$index] = ['id' => $workshopId, 'name' => $workshopName];
    }

    public function getLastCallLabel(): string
    {
        return $this->seatsAvailable === 0
            ? "There are currently <b>$this->seatsAvailable seats</b> available, sending last call is disabled"
            : "There are currently <b>$this->seatsAvailable seats</b> available, do you want to send a last call to the customers (it will be sent right away)?";
    }

    public function boot(
        AuthManager $authManager,
        Dispatcher $dispatcher,
        BusDispatcher $busDispatcher,
    ): void {
        $this->authManager = $authManager;
        $this->dispatcher = $dispatcher;
        $this->busDispatcher = $busDispatcher;
    }

    public function mount(
        Atelier $workshop,
        BigQueryWorkshopRepository $statisticsRepository,
    ): void {
        $this->workshop = $workshop;
        $this->workshopId = $workshop->getKey();

        $workshopStatistics = $statisticsRepository->getWorkshopsStatisticsFromGoogleBigQuery([$workshop->getKey()])->first();
        $this->giftCardNumber = $this->workshop->giftCardsWithStatusActive->count();
        $this->standbyNumber = (int) $this->workshop->getBookingStatistics()->preBookedPlaces;
        $this->furthestEventWithParticipant = $workshopStatistics?->furthestFutureEventDate?->toDateString();

        $transferAtMessage = $this->furthestEventWithParticipant !== null
            ? "⚠️ Be careful, there are still events with participants until {$this->furthestEventWithParticipant}."
            : 'There is no event with participants.';
        $this->transferAtInfoSubLabel = 'If not now, you will be assigned a Hubspot task on this date to confirm this form.<br>'.$transferAtMessage;

        $this->workshopsSuggestion = Atelier::query()
            ->where('active', true)
            ->whereNull('invisible_until')
            ->selectRaw("CONCAT(id, ' - ', nom) as id_nom, id")
            ->pluck('id_nom', 'id')
            ->toArray();

        foreach ($this->workshopsInQuery as $key => $id) {
            $this->workshopSelected($key, (int) $id, $this->workshopsSuggestion[$id]);
        }
    }

    public function render(): View
    {
        return view('livewire.admin.workshop.stock-transfer');
    }

    public function validateTransferInfo(): void
    {
        $this->validate();
        if ($this->isNow) {
            $this->reset('transferAt', 'withLastCall');
        }
        $this->modal('stock-transfer-confirmation')->open();
    }

    public function transfer(): void
    {
        $this->validate();
        $adminId = $this->authManager->guard('admin')->id();
        if (empty($adminId)) {
            $this->notify("You're not an admin, you can't transfer stock", NotificationEnum::Error);
            $this->modal('stock-transfer-confirmation')->close();

            return;
        }

        if ($this->isNow) {
            $this->dispatcher->dispatch(
                new StockTransferJob(
                    $this->workshopId,
                    $this->reason,
                    (int) $adminId,
                    $this->getSuggestedWorkshopsForEmail(),
                )
            );
            $this->notify('Stock transfer started. You will receive a mail when transfer is done.');
            $this->modal('stock-transfer-confirmation')->close();

            $this->redirectRoute('admin.ateliers.edit', ['atelier' => $this->workshopId]);

            return;
        }

        $this->scheduleTransfer();
    }

    public function updateSeats(): void
    {
        $end = Carbon::createFromFormat('Y-m-d', $this->transferAt);
        if (!$end instanceof Carbon) {
            $end = null;
        }

        $events = $this->workshop
            ->getEvenements(Carbon::now()->endOfDay(), $end)
            ->filter(fn (Evenement $event) => !$event->isCancelled());

        $this->seatsAvailable = $events->sum(fn (Evenement $event) => $event->getAvailableSlotsCount(false));
        $this->isNow = false;
    }

    private function scheduleTransfer(): void
    {
        $admin = $this->authManager->guard('admin')->user();
        if (!$admin instanceof Admin) {
            $this->notify("You're not an admin, you can't create a stock transfer task", NotificationEnum::Error);
            $this->modal('stock-transfer-confirmation')->close();

            return;
        }

        $transferDate = CarbonImmutable::createFromFormat('Y-m-d', $this->transferAt);
        if (!$transferDate instanceof CarbonImmutable) {
            $this->notify('It seems that the date given is erroneous, please review it and retry.', NotificationEnum::Error);
            $this->addError('transferAt', 'Bad date or format');

            return;
        }

        $task = new Task(
            "Stock transfer for workshop: {$this->workshop->nom}",
            body: 'Follow this <a href="'.$this->createLink().'" target="_blank">LINK</a> to go to stock transfer page',
            dueDate: $transferDate->copy()->endOfDay(),
        );

        $this->dispatcher->dispatch(new CreateTaskForStockTransfer($admin->id, $task, $this->workshopId, $this->reason));
        $this->notify('Stock transfer is scheduled on '.dateShortFormat($this->transferAt).'.');

        if ($this->withLastCall) {
            $this->busDispatcher->dispatch(new SendReminderBeforeStockTransfer($this->workshopId));
            $this->notify('People with gift cards or bookings in standby will receive a last call');
        }

        $this->modal('stock-transfer-confirmation')->close();
        $this->redirectRoute('admin.ateliers.edit', ['atelier' => $this->workshopId]);
    }

    private function createLink(): string
    {
        $reason = 'reason='.urlencode($this->reason);
        $workshops = '';
        foreach ($this->workshopsSelected as $key => $workshop) {
            $workshops .= "&workshops[{$key}]={$workshop['id']}";
        }

        return route('admin.ateliers.stock-transfer', ['workshop' => $this->workshopId]).'?'.$reason.$workshops;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    private function getSuggestedWorkshopsForEmail(): array
    {
        $suggestedWorkshop = [];
        /** @var Collection<int, Atelier> $workshops */
        $workshops = Atelier::query()
            ->whereIn('id', array_map(fn (array $workshop) => $workshop['id'], $this->workshopsSelected))
            ->get();

        foreach ($workshops as $workshop) {
            $suggestedWorkshop[] = [
                'id' => $workshop->id,
                'name' => $workshop->nom,
                'url' => $workshop->getUrl(),
            ];
        }

        return $suggestedWorkshop;
    }
}
