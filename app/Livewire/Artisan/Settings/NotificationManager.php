<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\Settings;

use App\Enums\NotificationEnum;
use App\Infrastructure\NotificationSettings\Notification;
use App\Infrastructure\NotificationSettings\NotificationSettings;
use App\Models\Artisan;
use Illuminate\View\View;
use Livewire\Component;

class NotificationManager extends Component
{
    public Artisan $artisan;
    private NotificationSettings $notificationSettings;
    /**
     * @var array<string, array<string, mixed>>
     */
    private array $artisanSettings = [];
    /*
     * modal properties
     */
    public string $modalTitle = '';
    public string $modalGroup = '';
    /**
     * @var array<int, Notification>
     */
    private array $modalNotifications = [];

    /**
     * @var array<string, array<string, bool>>
     */
    public array $notificationsField = [];

    public function boot(NotificationSettings $notificationSettings): void
    {
        $this->notificationSettings = $notificationSettings;
        $this->artisanSettings = $this->notificationSettings->getSettingsForArtisan($this->artisan);
    }

    public function render(): View
    {
        return view('livewire.artisan.settings.notification-manager', [
            'notificationSettings' => $this->artisanSettings,
            'modalNotifications' => $this->modalNotifications,
        ]);
    }

    public function edit(string $group): void
    {
        $this->modalTitle = __("userartisan/view/settings/pages.notification_manager.{$group}.label");
        $this->modalGroup = $group;
        $this->modalNotifications = $this->artisanSettings[$group]['notifications'];
        $this->setNotificationsField($this->modalNotifications);
        $this->modal('notification-setting-edit')->open();
    }

    public function save(string $group): void
    {
        $this->modalGroup = $group;
        $canBeFullyDisabled = (bool) $this->artisanSettings[$group]['canBeFullyDisabled'];
        /*
         * in this if statement we want to check that in each group of notifications we have at least one notification
         * that is checked (turn on) for groups that need that
         */
        if (!$canBeFullyDisabled && !$this->hasAtLeastOneNotificationActive()) {
            $this->modalNotifications = $this->artisanSettings[$group]['notifications'];
            $this->setNotificationsField($this->modalNotifications);
            $this->addError(
                'one_notification_required',
                __('userartisan/view/settings/pages.notification_manager.notify.one_active_required')
            );

            return;
        }

        try {
            foreach ($this->notificationsField as $notification => $types) {
                $this->notificationSettings->updateNotificationSettingsForArtisan($notification, $types, $this->artisan);
            }
            $this->reset('modalTitle', 'notificationsField');
            $this->notify(__('userartisan/view/settings/pages.notification_manager.notify.success'));
        } catch (\Throwable $exception) {
            \Log::error('An error occurred while trying to save notification settings for artisan', [
                'exception' => $exception,
                'artisan' => $this->artisan->id,
            ]);
            $this->reset('modalTitle', 'notificationsField');
            $this->notify(__('userartisan/view/settings/pages.notification_manager.notify.error'), NotificationEnum::Error);
        }

        $this->artisanSettings = $this->notificationSettings->getSettingsForArtisan($this->artisan);
        $this->modal('notification-setting-edit')->close();
    }

    /**
     * @param array<int, Notification> $notifications
     */
    private function setNotificationsField(array $notifications): void
    {
        foreach ($notifications as $notification) {
            foreach ($notification->types as $type => $isActive) {
                $this->notificationsField[$notification->class][$type] = $isActive;
            }
        }
    }

    private function hasAtLeastOneNotificationActive(): bool
    {
        /*
         * In the context of array_reduce() function, carry (or sometimes referred to as $carry) is a variable that
         * holds the intermediate result as the reduction progresses through the array.
         *
         * Here's how it works:
         *
         * The array_reduce() function iterates over the elements of an array and applies a callback function against
         * an accumulator and each value of the array.
         * The callback function takes two parameters: the accumulator (carry) and the current value.
         * The callback function returns the new value of the accumulator, which becomes the carry for the next iteration.
         * The final value of the accumulator after all iterations becomes the result of the reduction.
         */
        $fields = $this->notificationsField;

        return array_reduce($fields, function ($carry, $item) {
            return $carry || \in_array(true, $item, true);
        }, false);
    }
}
