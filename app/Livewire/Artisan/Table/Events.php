<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\Table;

use App\Domain\Booking\Services\EventRemover;
use App\Domain\Booking\Services\PickupCreationReminderCreator;
use App\Enums\NotificationEnum;
use App\Livewire\Attributes\Cookie;
use App\Livewire\Common\Table\AbstractDataTablesComponent;
use App\Livewire\Common\Table\WithPageLength;
use App\Livewire\Common\Table\WithSearch;
use App\Livewire\Common\Table\WithSelectableElements;
use App\Livewire\Common\Table\WithSortBy;
use App\Models\Evenement;
use App\Models\UserArtisan;
use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Livewire\Attributes\Locked;

class Events extends AbstractDataTablesComponent
{
    use WithPageLength;
    use WithSearch;
    use WithSelectableElements;
    use WithSortBy;

    #[Locked]
    public int $artisanId;
    /** @var int[] */
    public array $workshopsFilters = [];
    #[Cookie]
    public bool $withParticipants = true;
    #[Cookie]
    public bool $withoutParticipants = true;
    #[Cookie]
    public bool $withPastEvents = false;
    #[Cookie]
    public bool $withFutureEvents = true;
    public string $sortBy = 'start';
    public string $sortDir = 'ASC';

    protected EventRemover $eventRemover;
    protected PickupCreationReminderCreator $pickupCreationReminderCreator;
    protected AuthManager $authManager;

    public function boot(
        EventRemover $eventRemover,
        PickupCreationReminderCreator $pickupCreationReminderCreator,
        AuthManager $authManager,
    ): void {
        $this->eventRemover = $eventRemover;
        $this->pickupCreationReminderCreator = $pickupCreationReminderCreator;
        $this->authManager = $authManager;
    }

    public function render(): View
    {
        $page = Paginator::resolveCurrentPage();
        $events = $this->getEvents($page)
            ->get();

        $pagination = new LengthAwarePaginator(
            $events,
            \DB::query()->fromSub($this->getEvents(), 'subquery')->count(),
            $this->perPage,
            $page,
        );

        $workshops = $this->getEvents(null, true)
            ->get()
            ->groupBy('atelier_id')
            ->map(function (Collection $workshopEvents) {
                /** @var Collection<int, Evenement> $workshopEvents */
                return [
                    'id' => $workshopEvents->first()?->atelier_id,
                    'name' => $workshopEvents->first()?->atelier->nom,
                    'count' => $workshopEvents->count(),
                ];
            })->values();

        return view('livewire.artisan.table.events', [
            'workshops' => $workshops,
            'events' => $events,
            'links' => $this->getLinks($pagination),
        ]);
    }

    public function toggleWithPastEvents(): void
    {
        $this->resetPage();

        $this->withPastEvents = !$this->withPastEvents;
        // we want to change order direction when only past events are displayed
        if ($this->withPastEvents && !$this->withFutureEvents) {
            $this->sortDir = 'DESC';
        }
    }

    public function toggleWithFutureEvents(): void
    {
        $this->resetPage();

        $this->withFutureEvents = !$this->withFutureEvents;
        // we want to change order direction when only past events are displayed
        if ($this->withPastEvents && !$this->withFutureEvents) {
            $this->sortDir = 'DESC';
        }
    }

    public function toggleWithoutParticipants(): void
    {
        $this->resetPage();

        $this->withoutParticipants = !$this->withoutParticipants;
    }

    public function toggleWithParticipants(): void
    {
        $this->resetPage();

        $this->withParticipants = !$this->withParticipants;
    }

    public function pickupCreationReminder(): void
    {
        if (!$this->canPerformOrNotify()) {
            return;
        }

        $this->retrieveElements();

        if (!$this->pickupCreationReminderCreator->canCreateReminderForEvents($this->elements)) {
            $this->notify(__('userartisan/view/events/pages.coming_events.tooltip.cant_remind'), NotificationEnum::Error);

            return;
        }

        $this->dispatch('openPickupCreationReminderModal', eventIds: $this->elements);
    }

    public function removeEvents(): void
    {
        if (!$this->canPerformOrNotify()) {
            return;
        }

        $this->retrieveElements();

        /** @var Evenement[] $events */
        $events = Evenement::findMany($this->elements);
        foreach ($events as $event) {
            // TODO: need to be confirmed by the product, it's just a temp fix
            if (!$event->canBeDeleted() || $event->isPrivate()) {
                $this->notify(__('userartisan/view/events/pages.coming_events.tooltip.cant_remove'), NotificationEnum::Error);

                return;
            }
        }

        $this->dispatch('openRemoveEventsModal', ids: $this->elements);
    }

    public function cancelEvents(): void
    {
        if (!$this->canPerformOrNotify()) {
            return;
        }

        $this->retrieveElements();

        /** @var Evenement[] $events */
        $events = Evenement::findMany($this->elements);

        /** @var ?UserArtisan $userArtisan */
        $userArtisan = $this->authManager->guard('artisan')->user();

        if ($userArtisan === null) {
            return;
        }

        foreach ($events as $event) {
            if (!$userArtisan->can('cancel', $event) || !$event->canBeCancelled()) {
                $this->notify(__('userartisan/view/events/pages.coming_events.tooltip.cant_cancel'), NotificationEnum::Error);

                return;
            }
        }

        $this->dispatch('openCancelMultiplesEventsModal', eventsIds: $this->elements);
    }

    private function canPerformOrNotify(): bool
    {
        if (!$this->canPerform()) {
            $this->notify(__('userartisan/view/events/pages.coming_events.select_at_least_one'), NotificationEnum::Error);

            return false;
        }

        return true;
    }

    private function retrieveElements(): void
    {
        if (!$this->selectAllElements || \count($this->elements) !== 0) {
            return;
        }

        $page = Paginator::resolveCurrentPage();
        $events = $this->getEvents($page)->get();
        $this->elements = $events->pluck('id')->toArray();
    }

    /**
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     * @SuppressWarnings(PHPMD.LongVariable)
     *
     * @return Builder<Evenement>
     */
    private function getEvents(?int $page = null, bool $excludeWorkshopsFiltersAndSearch = false): Builder
    {
        $tempo = match (true) {
            $this->withPastEvents && $this->withFutureEvents => null,
            $this->withPastEvents => UserArtisan::WITH_PAST_EVENTS,
            $this->withFutureEvents => UserArtisan::WITH_FUTURE_EVENTS,
            default => null
        };

        /** @var UserArtisan $userArtisan */
        $userArtisan = $this->authManager->guard('artisan')->user();

        $events = $userArtisan->getEvents(
            $tempo,
            $this->withParticipants,
            $this->withoutParticipants,
        )->whereHas('atelier', function (Builder $query): void {
            $query->where('artisan_id', $this->artisanId);
        });

        if ($page !== null) {
            $events->limit($this->perPage);
            $events->offset(($page - 1) * $this->perPage);
        }

        if (!$excludeWorkshopsFiltersAndSearch) {
            if (!empty($this->search)) {
                $events->where('ateliers.nom', 'LIKE', "%{$this->search}%");
            }
            if (!empty($this->workshopsFilters)) {
                $events->whereIn('evenements.atelier_id', $this->workshopsFilters);
            }
        }
        $events->orderBy($this->sortBy, $this->sortDir);

        return $events;
    }
}
