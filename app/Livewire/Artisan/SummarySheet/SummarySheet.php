<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\SummarySheet;

use App\Domain\Content\Models\SummarySheet as SummarySheetModel;
use App\Domain\Content\Models\SummarySheetTemplate;
use App\Enums\Locale;
use App\Enums\NotificationEnum;
use App\Infrastructure\Auth\AuthFinder;
use App\Infrastructure\Interfaces\DocumentManagerInterface;
use App\Infrastructure\Services\SummarySheetService;
use App\Models\Atelier;
use App\Models\Translations\AtelierTranslation;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Locked;
use Livewire\Component;
use Symfony\Component\HttpFoundation\Response;

final class SummarySheet extends Component
{
    #[Locked]
    public int $workshopId;

    private Atelier $workshop;
    private AuthFinder $authFinder;

    public function boot(AuthFinder $authFinder): void
    {
        $this->workshop = Atelier::findOrFail($this->workshopId);
        $this->authFinder = $authFinder;
    }

    public function render(): View
    {
        return view(
            'livewire.artisan.summary-sheet.summary-sheet',
            [
                'workshop' => $this->workshop,
            ]
        );
    }

    public function publish(SummarySheetService $sheetService): void
    {
        $summarySheet = $this->workshop->getLastSummarySheet();

        if ($summarySheet === null) {
            abort(Response::HTTP_NOT_FOUND);
        }

        $sheetService->publish($summarySheet);

        $this->modal('summary-sheet-publish-modal')->close();
        $this->notify(__('userartisan/view/summary-sheet.publish_success'));
    }

    public function delete(SummarySheetService $sheetService): void
    {
        $summarySheet = $this->workshop->getLastSummarySheet();

        if ($summarySheet === null) {
            abort(Response::HTTP_NOT_FOUND);
        }

        $sheetService->delete($summarySheet);

        $this->modal('summary-sheet-remove-modal')->close();
        $this->notify(__('userartisan/view/summary-sheet.remove_with_success'));
    }

    public function download(SummarySheetService $sheetService): Response
    {
        $summarySheet = $this->workshop->getLastSummarySheet();

        if ($summarySheet === null) {
            abort(Response::HTTP_NOT_FOUND);
        }

        return $sheetService->download($summarySheet);
    }

    public function create(DocumentManagerInterface $googleDocs): void
    {
        if ($this->workshop->getSummarySheetVersions()->count() > 0) {
            $this->notify(__('userartisan/view/summary-sheet.already_exists'), NotificationEnum::Error);

            return;
        }

        $userArtisan = $this->authFinder->tryArtisan();

        if ($userArtisan === null) {
            abort(Response::HTTP_FORBIDDEN);
        }

        /** @var ?SummarySheetTemplate $summarySheetTemplate */
        $summarySheetTemplate = SummarySheetTemplate::where('locale', $this->workshop->artisan->locale)->first();

        if ($summarySheetTemplate === null) {
            $this->notify(__('utils.generic_error'), NotificationEnum::Error);
            report(new \InvalidArgumentException("Unable to find summary sheet template for locale '{$this->workshop->artisan->locale}'"));

            return;
        }

        $summarySheet = new SummarySheetModel();
        $summarySheet->atelier_id = $this->workshop->getKey();
        $summarySheet->template_id = $summarySheetTemplate->getKey();
        $summarySheet->locale = Locale::from($this->workshop->artisan->locale);

        $workshopTitle = $this->workshop->nom;
        if ($summarySheet->locale !== Locale::French) {
            $translation = $this->workshop->translate($summarySheet->locale->value);
            if ($translation instanceof AtelierTranslation && \is_string($translation->nom)) {
                $workshopTitle = $translation->nom;
            }
        }

        $newFile = $googleDocs->duplicateFile(
            $summarySheetTemplate->docs_id,
            __('userartisan/view/summary-sheet.title', ['workshop' => $workshopTitle]),
        );
        $googleDocs->allowPublicEdit($newFile->getId());
        $summarySheet->docs_id = $newFile->getId();
        $summarySheet->save();

        $googleDocs->replacePlaceholdersInDocument(
            $newFile->getId(),
            [
                'workshop_title' => $workshopTitle,
                'artisan_name' => $this->workshop->artisan->getDenominationFront(),
            ],
        );

        $this->modal('summary-sheet-create-modal')->close();
        $this->dispatch('summary-sheet-create-success', url: $summarySheet->getGoogleDocsEditUrl());
        $this->notify(__('userartisan/view/summary-sheet.create_success'));
    }
}
