<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Actions;

use App\Domain\Booking\Exceptions\Event\StackedSameWorkshopException;
use App\Domain\PrivateBooking\Exceptions\PrivateBookingRequestException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Notifications\Emails\PrivateBookingRequestOtherProposal;
use App\Domain\PrivateBooking\Notifications\Sms\SmsOtherProposal;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestState;
use App\Domain\PrivateBooking\Services\PrivateBookingService;
use App\Domain\PrivateBooking\Services\QuoteCreator;
use App\Shared\Percent;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * Action for proposing other slots for private booking requests
 * Encapsulates the business logic for other proposal workflow
 */
final readonly class ProposeOtherSlotsAction
{
    public function __construct(
        private PrivateBookingRequestState $privateBookingRequestState,
        private PrivateBookingService $privateBookingService,
        private QuoteCreator $quoteCreator,
    ) {
    }

    /**
     * @param array<string> $proposalSlots
     * @param array<int, array<string, mixed>> $lines
     */
    public function execute(
        PrivateBookingRequest $request,
        array $proposalSlots,
        ?string $artisanMessage = null,
        array $lines = [],
        float $discountPercent = 0,
    ): ActionResult {
        try {
            DB::beginTransaction();

            $this->privateBookingRequestState->proposeOtherSlots(
                $request,
                $proposalSlots,
                $artisanMessage
            );

            if ($request->is_company) {
                $this->quoteCreator->createFromPrivateBookingRequest(
                    $request,
                    $lines,
                    $this->getSlots($request, $proposalSlots),
                    Percent::fromFloat($discountPercent),
                );
            }

            DB::commit();

            $this->sendNotifications($request);

            return ActionResult::success(
                __('components/modals/private-booking-requests.messages.success.accepted', [
                    'delay' => $request->is_company
                        ? config()->integer('private-booking.response_delay.company') / 24
                        : config()->integer('private-booking.response_delay.customer') / 24,
                ])
            );

        } catch (StackedSameWorkshopException) {
            DB::rollBack();
            return ActionResult::error(
                'same_slot',
                __('components/modals/private-booking-requests.messages.error.same_slot')
            );

        } catch (PrivateBookingRequestException $e) {
            DB::rollBack();
            Log::error("Can't propose one or more slots", [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id
            ]);

            return ActionResult::error(
                'cant_propose',
                __('components/modals/private-booking-requests.messages.error.cant_propose')
            );

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('An error occurred while proposing other slot', [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id
            ]);

            return ActionResult::error(
                'error_on_propose',
                __('components/modals/private-booking-requests.messages.error.error')
            );
        }
    }

    private function sendNotifications(PrivateBookingRequest $request): void
    {
        Mail::to($request->email)
            ->locale($request->locale)
            ->send((new PrivateBookingRequestOtherProposal($request))->country($request->country_code));

        $this->privateBookingService->sendSmsToCustomer(
            $request,
            new SmsOtherProposal($request)
        );
    }

    /**
     * @param array<string> $slots
     * @return array<int, array<string, mixed>>
     */
    private function getSlots(PrivateBookingRequest $request, array $slots): array
    {
        $quoteSlots = [];
        foreach ($slots as $slot) {
            $quoteSlots[] = [
                'start' => Carbon::parse($slot),
                'end' => Carbon::parse($slot)
                    ->addDays($request->workshop->nb_jours)
                    ->addHours($request->workshop->nb_heures)
                    ->addMinutes($request->workshop->nb_minutes),
                'timezone' => $request->timezone,
            ];
        }

        return $quoteSlots;
    }
}
