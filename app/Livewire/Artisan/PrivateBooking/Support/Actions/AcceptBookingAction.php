<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Actions;

use App\Domain\PrivateBooking\Exceptions\PrivateBookingRequestException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Notifications\Emails\PrivateBookingRequestAccepted;
use App\Domain\PrivateBooking\Notifications\Sms\SmsPrivateBookingRequestAccepted;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestState;
use App\Domain\PrivateBooking\Services\PrivateBookingService;
use App\Domain\PrivateBooking\Services\QuoteCreator;
use App\Shared\Percent;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * Action for accepting private booking requests
 * Encapsulates the business logic for acceptance workflow.
 */
final readonly class AcceptBookingAction
{
    public function __construct(
        private PrivateBookingRequestState $privateBookingRequestState,
        private PrivateBookingService $privateBookingService,
        private QuoteCreator $quoteCreator,
    ) {
    }

    /**
     * @param array<string> $selectedSlots
     * @param array<int, array<string, mixed>> $lines
     */
    public function execute(
        PrivateBookingRequest $request,
        array $selectedSlots,
        ?string $artisanMessage = null,
        array $lines = [],
        float $discountPercent = 0,
    ): ActionResult {
        try {
            DB::beginTransaction();

            $this->privateBookingRequestState->accept(
                $request,
                array_filter($selectedSlots),
                $artisanMessage
            );

            if ($request->is_company) {
                $this->quoteCreator->createFromPrivateBookingRequest(
                    $request,
                    $lines,
                    $this->getSlots($request, array_filter($selectedSlots)),
                    Percent::fromFloat($discountPercent),
                );
            }

            DB::commit();

            $this->sendNotifications($request);

            return ActionResult::success(
                __('components/modals/private-booking-requests.messages.success.accepted', [
                    'delay' => $request->is_company
                        ? config()->integer('private-booking.response_delay.company') / 24
                        : config()->integer('private-booking.response_delay.customer') / 24,
                ])
            );
        } catch (PrivateBookingRequestException $e) {
            DB::rollBack();
            Log::error("Can't accept one or more of proposed slots", [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id,
            ]);

            return ActionResult::error(
                'cant_accept',
                __('components/modals/private-booking-requests.messages.error.cant_accept')
            );
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('An error occurred while accepting request', [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id,
            ]);

            return ActionResult::error(
                'error_on_accept',
                __('components/modals/private-booking-requests.messages.error.error')
            );
        }
    }

    private function sendNotifications(PrivateBookingRequest $request): void
    {
        Mail::to($request->email)
            ->locale($request->locale)
            ->send((new PrivateBookingRequestAccepted($request))->country($request->country_code));

        $this->privateBookingService->sendSmsToCustomer(
            $request,
            new SmsPrivateBookingRequestAccepted($request)
        );
    }

    /**
     * @param array<string> $slots
     *
     * @return array<int, array<string, mixed>>
     */
    private function getSlots(PrivateBookingRequest $request, array $slots): array
    {
        $quoteSlots = [];
        foreach ($slots as $slot) {
            $quoteSlots[] = [
                'start' => Carbon::parse($slot),
                'end' => Carbon::parse($slot)
                    ->addDays($request->workshop->nb_jours)
                    ->addHours($request->workshop->nb_heures)
                    ->addMinutes($request->workshop->nb_minutes),
                'timezone' => $request->timezone,
            ];
        }

        return $quoteSlots;
    }
}
