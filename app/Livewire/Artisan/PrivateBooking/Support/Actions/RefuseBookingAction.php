<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Actions;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason;
use App\Domain\PrivateBooking\Exceptions\PrivateBookingRequestException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Notifications\Emails\PrivateBookingRequestRefused;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestState;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * Action for refusing private booking requests
 * Encapsulates the business logic for refusal workflow
 */
final readonly class RefuseBookingAction
{
    public function __construct(
        private PrivateBookingRequestState $privateBookingRequestState
    ) {
    }

    public function execute(
        PrivateBookingRequest $request,
        PrivateBookingRequestRefuseReason $refuseReason,
        ?string $artisanMessage = null,
        ?string $refuseReasonOther = null,
    ): ActionResult {
        try {
            $this->privateBookingRequestState->refuse(
                $request,
                $refuseReason,
                $artisanMessage,
                $refuseReasonOther
            );

            $this->sendNotifications($request);

            return ActionResult::success(
                __('components/modals/private-booking-requests.messages.success.refused')
            );

        } catch (PrivateBookingRequestException $e) {
            Log::error("try to refuse request but can't", [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id
            ]);

            return ActionResult::error(
                'cant_refuse',
                __('components/modals/private-booking-requests.messages.error.cant_refuse')
            );

        } catch (\Throwable $e) {
            Log::error('An error occurred while refusing request', [
                'exception' => $e,
                'PrivateBookingRequest' => $request->id
            ]);

            report($e);

            return ActionResult::error(
                'error_on_refuse',
                __('components/modals/private-booking-requests.messages.error.error')
            );
        }
    }

    private function sendNotifications(PrivateBookingRequest $request): void
    {
        Mail::to($request->email)
            ->locale($request->locale)
            ->send((new PrivateBookingRequestRefused($request))->country($request->country_code));
    }
}
