<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Actions;

/**
 * Value object for action execution results
 * Encapsulates success/failure state and messages
 */
final readonly class ActionResult
{
    public function __construct(
        private bool $success,
        private ?string $message = null,
        private ?string $errorKey = null,
    ) {
    }

    public static function success(?string $message = null): self
    {
        return new self(true, $message);
    }

    public static function error(string $errorKey, string $message): self
    {
        return new self(false, $message, $errorKey);
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function isError(): bool
    {
        return !$this->success;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getErrorKey(): ?string
    {
        return $this->errorKey;
    }
}
