<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\AbstractWizardStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\AcceptStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\FirstStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\OtherProposalStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\QuoteStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\RefuseStep;

/**
 * Wizard manager for private booking request flow
 * Implements Strategy pattern for step management
 */
final class PrivateBookingWizard
{
    /** @var array<string, AbstractWizardStep> */
    private array $steps = [];

    public function __construct(
        private PrivateBookingModalData $data,
        private StepFactory $stepFactory,
    ) {
        $this->initializeSteps();
    }

    public function getCurrentStep(string $stepName): AbstractWizardStep
    {
        if (!isset($this->steps[$stepName])) {
            throw new \InvalidArgumentException("Unknown step: {$stepName}");
        }

        return $this->steps[$stepName];
    }

    public function processStep(string $stepName, array $formData): array
    {
        $step = $this->getCurrentStep($stepName);
        return $step->process($formData);
    }

    public function canNavigateBack(string $stepName): bool
    {
        $step = $this->getCurrentStep($stepName);
        return $step->getPreviousStep() !== null;
    }

    public function getPreviousStep(string $stepName): ?PrivateBookingRequestManagementStep
    {
        $step = $this->getCurrentStep($stepName);
        return $step->getPreviousStep();
    }

    public function getValidationRules(string $stepName): array
    {
        $step = $this->getCurrentStep($stepName);
        return $step->getValidationRules();
    }

    public function getValidationMessages(string $stepName): array
    {
        $step = $this->getCurrentStep($stepName);
        return $step->getValidationMessages();
    }

    private function initializeSteps(): void
    {
        $stepClasses = [
            PrivateBookingRequestManagementStep::FIRST_STEP->value => FirstStep::class,
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP->value => AcceptStep::class,
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP->value => AcceptStep::class,
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP->value => QuoteStep::class,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP->value => OtherProposalStep::class,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP->value => OtherProposalStep::class,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP->value => QuoteStep::class,
            PrivateBookingRequestManagementStep::REFUSE_STEP->value => RefuseStep::class,
            PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP->value => RefuseStep::class,
        ];

        foreach ($stepClasses as $stepName => $stepClass) {
            $this->steps[$stepName] = $this->stepFactory->create($stepClass, $this->data);
        }
    }
}
