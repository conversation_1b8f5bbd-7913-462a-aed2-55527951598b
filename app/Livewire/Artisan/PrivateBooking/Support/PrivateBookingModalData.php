<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestLocation;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Enums\Currency;
use App\Models\Atelier;
use Carbon\CarbonInterface;

/**
 * Data Transfer Object for Private Booking Modal state
 * Encapsulates all modal data to improve maintainability and reduce coupling.
 */
final readonly class PrivateBookingModalData
{
    public function __construct(
        public PrivateBookingRequest $request,
        public Atelier $workshop,
        public bool $isCompany,
        public string $location,
        public Currency $currency,
        public ?string $companyName,
        public float $customerLocationPrice,
        public ?string $customerLocation,
        public float $estimatedPriceInclVat,
        public string $participantsDisplay,
        /** @var CarbonInterface[][] */
        public array $proposedSlots,
        public ?string $customerMessage,
        public string $customerDisplay,
        public float $bookingPrice,
        /** @var array<string, mixed> */
        public array $artisanAvailability,
        public string $workshopName,
        public string $workshopCurrency,
        public string $workshopTimezone,
        public int $workshopDurationDay,
        public int $workshopDurationHour,
        public int $workshopDurationMinute,
    ) {
    }

    public static function fromRequest(
        PrivateBookingRequest $request,
        array $artisanAvailability,
        float $estimatedPrice,
        ?string $customerMessage,
    ): self {
        $workshop = $request->workshop;
        $isCompany = $request->is_company;
        $location = $request->location->value;
        $currency = $request->getCurrency();

        $customerLocationPrice = $location === PrivateBookingRequestLocation::Customer->value
            ? self::calculateCustomerLocationPrice($request, $currency, $workshop)
            : 0;

        $customerLocation = $location === PrivateBookingRequestLocation::Customer->value
            ? self::formatCustomerLocation($request)
            : null;

        return new self(
            request: $request,
            workshop: $workshop,
            isCompany: $isCompany,
            location: $location,
            currency: $currency,
            companyName: $request->company_name,
            customerLocationPrice: $customerLocationPrice,
            customerLocation: $customerLocation,
            estimatedPriceInclVat: $estimatedPrice,
            participantsDisplay: $request->present()->participantsNumber(),
            proposedSlots: $request->getProposedSlotsByDay(),
            customerMessage: $customerMessage,
            customerDisplay: $request->present()->customerName(),
            bookingPrice: self::calculateBookingPrice($request, $workshop),
            artisanAvailability: $artisanAvailability,
            workshopName: $workshop->nom,
            workshopCurrency: $workshop->currency,
            workshopTimezone: $workshop->timezone,
            workshopDurationDay: $workshop->nb_jours,
            workshopDurationHour: $workshop->nb_heures,
            workshopDurationMinute: $workshop->nb_minutes,
        );
    }

    private static function calculateCustomerLocationPrice(
        PrivateBookingRequest $request,
        Currency $currency,
        Atelier $workshop
    ): float {
        $travelExpenses = config(
            'private-booking.private_client_managed_location.travel_expenses',
            0,
            $request->country_code
        );

        return \App\Shared\Amount::fromFloatCents($travelExpenses, $currency)
            ->addPercent($workshop->artisan->getVatRate())
            ->floatValue();
    }

    private static function formatCustomerLocation(PrivateBookingRequest $request): string
    {
        return $request->address_1.', '.$request->zip_code.', '.$request->city.'<br>'.$request->address_2;
    }

    private static function calculateBookingPrice(PrivateBookingRequest $request, Atelier $workshop): float
    {
        $minParticipants = $workshop->getMinParticipantsForEventType(
            \App\Enums\Event\EventType::fromLocation($request->location)
        );

        return $workshop->prix * max($request->participants_number, $minParticipants);
    }
}
