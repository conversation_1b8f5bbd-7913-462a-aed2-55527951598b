<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;

/**
 * Step for quote management
 * Handles quote line items and discount validation.
 */
final class QuoteStep extends AbstractWizardStep
{
    public function getStepName(): PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP;
    }

    public function canNavigateNext(array $formData): StepValidationResult
    {
        return $this->validateQuote($formData);
    }

    public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        return match ($currentStep) {
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP => PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP => PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP,
            default => null,
        };
    }

    public function getPreviousStep(): ?PrivateBookingRequestManagementStep
    {
        $currentStep = $this->getStepName();

        return match ($currentStep) {
            PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP => PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP,
            PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP => PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP,
            default => PrivateBookingRequestManagementStep::FIRST_STEP,
        };
    }

    public function execute(array $formData): bool
    {
        // Quote step doesn't execute business logic, just validates and continues
        return false;
    }

    public function getValidationRules(): array
    {
        return [
            'lines.*.label' => 'string|max:191',
            'lines.*.price' => 'numeric|min:0',
            'discountPercent' => 'numeric|min:0|max:25',
        ];
    }

    public function getValidationMessages(): array
    {
        return [
            'lines.*.label' => __('components/modals/private-booking-requests.steps.quote.form_errors.label'),
            'lines.*.price' => __('components/modals/private-booking-requests.steps.quote.form_errors.price'),
            'discountPercent' => __('components/modals/private-booking-requests.steps.quote.form_errors.discount'),
        ];
    }

    private function validateQuote(array $formData): StepValidationResult
    {
        $lines = $formData['lines'] ?? [];
        $discountPercent = $formData['discountPercent'] ?? 0;
        $errors = [];

        // Validate lines
        foreach ($lines as $index => $line) {
            if (isset($line['label']) && (empty($line['label']) || \mb_strlen($line['label']) > 191)) {
                $errors["lines.{$index}.label"] = __('components/modals/private-booking-requests.steps.quote.form_errors.label');
            }

            if (isset($line['price']) && (!is_numeric($line['price']) || $line['price'] < 0)) {
                $errors["lines.{$index}.price"] = __('components/modals/private-booking-requests.steps.quote.form_errors.price');
            }
        }

        // Validate discount
        if (!is_numeric($discountPercent) || $discountPercent < 0 || $discountPercent > 25) {
            $errors['discountPercent'] = __('components/modals/private-booking-requests.steps.quote.form_errors.discount');
        }

        return empty($errors) ? StepValidationResult::valid() : StepValidationResult::invalid($errors);
    }
}
