<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestRefuseReason;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\RefuseBookingAction;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;
use Illuminate\Validation\Rule;

/**
 * Step for refusing private booking requests
 * Handles reason selection and final refusal
 */
final class RefuseStep extends AbstractWizardStep
{
    public function __construct(
        PrivateBookingModalData $data,
        private RefuseBookingAction $refuseAction
    ) {
        parent::__construct($data);
    }

    public function getStepName(): PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::REFUSE_STEP;
    }

    public function canNavigateNext(array $formData): StepValidationResult
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP) {
            // Final step - validate refusal data
            return $this->validateRefusalData($formData);
        }

        // Initial step - validate reason selection
        return $this->validateRefusalData($formData);
    }

    public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::REFUSE_STEP) {
            return PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP;
        }

        return null; // Final step
    }

    public function getPreviousStep(): ?PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::FIRST_STEP;
    }

    public function execute(array $formData): bool
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::REFUSE_MESSAGE_STEP) {
            // Execute the refusal
            $refuseReason = PrivateBookingRequestRefuseReason::from($formData['refuseReason']);
            
            $result = $this->refuseAction->execute(
                $this->data->request,
                $refuseReason,
                $formData['artisanMessage'] ?? null,
                $formData['refuseReasonOther'] ?? null
            );

            if ($result->isError()) {
                throw new \Exception($result->getMessage());
            }

            return true; // Workflow completed
        }

        return false; // Continue to next step
    }

    public function getValidationRules(): array
    {
        return [
            'refuseReason' => ['required', Rule::enum(PrivateBookingRequestRefuseReason::class)],
            'refuseReasonOther' => [Rule::requiredIf(fn() => 
                isset($this->data) && 
                ($this->data->request->refuseReason ?? null) === PrivateBookingRequestRefuseReason::Other->value
            )],
        ];
    }

    public function getValidationMessages(): array
    {
        return [
            'refuseReason.required' => __('components/modals/private-booking-requests.messages.error.refuse_reason'),
            'refuseReasonOther.required' => __('components/modals/private-booking-requests.messages.error.refuse_reason_other'),
        ];
    }

    private function validateRefusalData(array $formData): StepValidationResult
    {
        $refuseReason = $formData['refuseReason'] ?? null;

        if (empty($refuseReason)) {
            return StepValidationResult::invalidWithMessage(
                'refuseReason',
                __('components/modals/private-booking-requests.messages.error.refuse_reason')
            );
        }

        if (!PrivateBookingRequestRefuseReason::tryFrom($refuseReason)) {
            return StepValidationResult::invalidWithMessage(
                'refuseReason',
                __('components/modals/private-booking-requests.messages.error.refuse_reason')
            );
        }

        // Check if "Other" reason requires additional text
        if ($refuseReason === PrivateBookingRequestRefuseReason::Other->value) {
            $refuseReasonOther = $formData['refuseReasonOther'] ?? null;
            if (empty($refuseReasonOther)) {
                return StepValidationResult::invalidWithMessage(
                    'refuseReasonOther',
                    __('components/modals/private-booking-requests.messages.error.refuse_reason_other')
                );
            }
        }

        return StepValidationResult::valid();
    }
}
