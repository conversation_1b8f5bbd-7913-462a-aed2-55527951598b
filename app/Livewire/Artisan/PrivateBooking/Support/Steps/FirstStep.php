<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;

/**
 * First step of the private booking wizard
 * Handles initial response choice selection
 */
final class FirstStep extends AbstractWizardStep
{
    public function getStepName(): PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::FIRST_STEP;
    }

    public function canNavigateNext(array $formData): StepValidationResult
    {
        $responseChoice = $formData['responseChoice'] ?? null;

        if ($responseChoice === null || !PrivateBookingRequestManagementStep::isValidSecondStep($responseChoice)) {
            return StepValidationResult::invalidWithMessage(
                'one_response_choice_required',
                __('components/modals/private-booking-requests.messages.error.responce_choice')
            );
        }

        return StepValidationResult::valid();
    }

    public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep
    {
        $responseChoice = $formData['responseChoice'] ?? null;
        
        return $responseChoice ? PrivateBookingRequestManagementStep::from($responseChoice) : null;
    }

    public function getPreviousStep(): ?PrivateBookingRequestManagementStep
    {
        return null; // First step has no previous step
    }

    public function execute(array $formData): bool
    {
        // First step doesn't execute business logic, just validates choice
        return false;
    }
}
