<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\AcceptBookingAction;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;

/**
 * Step for accepting proposed slots
 * Handles both slot selection and final acceptance.
 */
final class AcceptStep extends AbstractWizardStep
{
    public function __construct(
        PrivateBookingModalData $data,
        private readonly AcceptBookingAction $acceptAction
    ) {
        parent::__construct($data);
    }

    public function getStepName(): PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP;
    }

    public function canNavigateNext(array $formData): StepValidationResult
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP) {
            return $this->validateSelectedSlots($formData);
        }

        return $this->validateSelectedSlots($formData);
    }

    public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_STEP) {
            return $this->requiresQuoteStep()
                ? PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_QUOTE_STEP
                : PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP;
        }

        return null;
    }

    public function getPreviousStep(): ?PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::FIRST_STEP;
    }

    public function execute(array $formData): bool
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP) {
            $result = $this->acceptAction->execute(
                $this->data->request,
                array_filter($formData['selectedSlots'] ?? []),
                $formData['artisanMessage'] ?? null,
                $formData['lines'] ?? [],
                $formData['discountPercent'] ?? 0
            );

            if ($result->isError()) {
                throw new \Exception($result->getMessage() ?? 'Unknown error');
            }

            return true;
        }

        return false;
    }

    private function validateSelectedSlots(array $formData): StepValidationResult
    {
        $selectedSlots = $formData['selectedSlots'] ?? [];

        if (empty($selectedSlots)) {
            return StepValidationResult::invalidWithMessage(
                'selectedSlots',
                __('components/modals/private-booking-requests.messages.error.selected_day_required')
            );
        }

        $hasValidSlot = false;
        foreach ($selectedSlots as $key => $value) {
            if (!empty($value)) {
                $hasValidSlot = true;
                break;
            }
        }

        if (!$hasValidSlot) {
            return StepValidationResult::invalidWithMessage(
                'selectedSlots',
                __('components/modals/private-booking-requests.messages.error.selected_day_required')
            );
        }

        return StepValidationResult::valid();
    }
}
