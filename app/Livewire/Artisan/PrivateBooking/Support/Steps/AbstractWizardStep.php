<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;

/**
 * Abstract base class for wizard steps
 * Implements Template Method pattern for step processing
 */
abstract class AbstractWizardStep
{
    public function __construct(
        protected PrivateBookingModalData $data
    ) {
    }

    abstract public function getStepName(): PrivateBookingRequestManagementStep;

    abstract public function canNavigateNext(array $formData): StepValidationResult;

    abstract public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep;

    abstract public function getPreviousStep(): ?PrivateBookingRequestManagementStep;

    /**
     * Execute the step's business logic
     * Returns true if the step completes the wizard, false if it continues
     */
    abstract public function execute(array $formData): bool;

    /**
     * Get validation rules for this step
     */
    public function getValidationRules(): array
    {
        return [];
    }

    /**
     * Get validation messages for this step
     */
    public function getValidationMessages(): array
    {
        return [];
    }

    /**
     * Check if this step requires company-specific handling
     */
    protected function requiresQuoteStep(): bool
    {
        return $this->data->isCompany;
    }

    /**
     * Template method for step processing
     */
    final public function process(array $formData): array
    {
        $validationResult = $this->canNavigateNext($formData);
        
        if (!$validationResult->isValid()) {
            return [
                'success' => false,
                'errors' => $validationResult->getErrors(),
                'completed' => false,
            ];
        }

        $completed = $this->execute($formData);
        $nextStep = $completed ? null : $this->getNextStep($formData);

        return [
            'success' => true,
            'errors' => [],
            'completed' => $completed,
            'nextStep' => $nextStep,
        ];
    }
}
