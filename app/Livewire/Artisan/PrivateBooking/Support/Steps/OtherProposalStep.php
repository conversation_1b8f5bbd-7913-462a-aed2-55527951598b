<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support\Steps;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Domain\PrivateBooking\Services\ArtisanAvailability;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\ProposeOtherSlotsAction;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\StepValidationResult;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

/**
 * Step for proposing other slots
 * Handles slot selection and proposal submission
 */
final class OtherProposalStep extends AbstractWizardStep
{
    public function __construct(
        PrivateBookingModalData $data,
        private ArtisanAvailability $artisanAvailabilityService,
        private ProposeOtherSlotsAction $proposeAction
    ) {
        parent::__construct($data);
    }

    public function getStepName(): PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP;
    }

    public function canNavigateNext(array $formData): StepValidationResult
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP) {
            // Final step - validate proposal data
            return $this->validateOtherProposal($formData);
        }

        // Initial step - validate proposal slots
        return $this->validateOtherProposal($formData);
    }

    public function getNextStep(array $formData): ?PrivateBookingRequestManagementStep
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::OTHER_PROPOSAL_STEP) {
            return $this->requiresQuoteStep() 
                ? PrivateBookingRequestManagementStep::OTHER_PROPOSAL_QUOTE_STEP
                : PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP;
        }

        return null; // Final step
    }

    public function getPreviousStep(): ?PrivateBookingRequestManagementStep
    {
        return PrivateBookingRequestManagementStep::FIRST_STEP;
    }

    public function execute(array $formData): bool
    {
        $currentStep = PrivateBookingRequestManagementStep::from($formData['currentStep'] ?? $this->getStepName()->value);

        if ($currentStep === PrivateBookingRequestManagementStep::OTHER_PROPOSAL_MESSAGE_STEP) {
            // Execute the proposal
            $proposalSlots = array_filter($formData['otherProposalSlot'] ?? [], function ($slot) {
                return !empty($slot);
            });

            $result = $this->proposeAction->execute(
                $this->data->request,
                $proposalSlots,
                $formData['artisanMessage'] ?? null,
                $formData['lines'] ?? [],
                $formData['discountPercent'] ?? 0
            );

            if ($result->isError()) {
                throw new \Exception($result->getMessage());
            }

            return true; // Workflow completed
        }

        return false; // Continue to next step
    }

    private function validateOtherProposal(array $formData): StepValidationResult
    {
        $otherProposalDay = $formData['otherProposalDay'] ?? [];
        $otherProposalSlot = $formData['otherProposalSlot'] ?? [];

        $safeDays = $this->data->isCompany ?
            config()->integer('private-booking.days_before_first_date.for_companies_last_minute') :
            config()->integer('private-booking.days_before_first_date.other_than_companies');

        $daySelected = false;
        $errors = [];

        foreach ($otherProposalDay as $key => $day) {
            if (empty($day)) {
                continue;
            }

            $daySelected = true;

            // Validate date format and future date
            $dayCarbon = Carbon::createFromFormat('Y-m-d', $day);
            if (!$dayCarbon || $dayCarbon->isBefore(Carbon::now()->addDays($safeDays)->endOfDay())) {
                $errors["otherProposalDay.{$key}"] = __('components/modals/private-booking-requests.messages.error.after', [
                    'date' => dateShortFormat(Carbon::now()->addDays($safeDays)->endOfDay()),
                ]);
            }

            // Validate corresponding slot
            if (empty($otherProposalSlot[$key] ?? null)) {
                $errors["otherProposalSlot.{$key}"] = __('components/modals/private-booking-requests.messages.error.time_required');
            }
        }

        if (!$daySelected) {
            $errors['otherProposalSlot'] = __('components/modals/private-booking-requests.messages.error.date_required');
        }

        // Check for duplicate slots
        $nonEmptySlots = array_filter($otherProposalSlot);
        if (count($nonEmptySlots) !== count(array_unique($nonEmptySlots))) {
            $errors['otherProposalSlot'] = __('components/modals/private-booking-requests.messages.error.distinct');
        }

        return empty($errors) ? StepValidationResult::valid() : StepValidationResult::invalid($errors);
    }
}
