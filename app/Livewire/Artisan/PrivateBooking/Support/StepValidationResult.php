<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support;

/**
 * Value object for step validation results
 * Encapsulates validation state and error messages.
 */
final readonly class StepValidationResult
{
    public function __construct(
        private bool $valid,
        private array $errors = []
    ) {
    }

    public static function valid(): self
    {
        return new self(true);
    }

    public static function invalid(array $errors): self
    {
        return new self(false, $errors);
    }

    public static function invalidWithMessage(string $key, string $message): self
    {
        return new self(false, [$key => $message]);
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
}
