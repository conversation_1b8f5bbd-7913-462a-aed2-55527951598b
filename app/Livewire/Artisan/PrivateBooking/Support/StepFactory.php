<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking\Support;

use App\Domain\PrivateBooking\Services\ArtisanAvailability;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestState;
use App\Domain\PrivateBooking\Services\PrivateBookingService;
use App\Domain\PrivateBooking\Services\QuoteCreator;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\AcceptBookingAction;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\ProposeOtherSlotsAction;
use App\Livewire\Artisan\PrivateBooking\Support\Actions\RefuseBookingAction;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\AbstractWizardStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\AcceptStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\FirstStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\OtherProposalStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\QuoteStep;
use App\Livewire\Artisan\PrivateBooking\Support\Steps\RefuseStep;

/**
 * Factory for creating wizard steps with their dependencies
 * Implements Factory pattern for step creation.
 */
final readonly class StepFactory
{
    public function __construct(
        private ArtisanAvailability $artisanAvailabilityService,
        private PrivateBookingService $privateBookingService,
        private PrivateBookingRequestState $privateBookingRequestState,
        private QuoteCreator $quoteCreator,
    ) {
    }

    public function create(string $stepClass, PrivateBookingModalData $data): AbstractWizardStep
    {
        return match ($stepClass) {
            FirstStep::class => new $stepClass($data),

            AcceptStep::class => new $stepClass(
                $data,
                new AcceptBookingAction(
                    $this->privateBookingRequestState,
                    $this->privateBookingService,
                    $this->quoteCreator
                )
            ),

            OtherProposalStep::class => new $stepClass(
                $data,
                $this->artisanAvailabilityService,
                new ProposeOtherSlotsAction(
                    $this->privateBookingRequestState,
                    $this->privateBookingService,
                    $this->quoteCreator
                )
            ),

            RefuseStep::class => new $stepClass(
                $data,
                new RefuseBookingAction($this->privateBookingRequestState)
            ),

            QuoteStep::class => new $stepClass($data),

            default => throw new \InvalidArgumentException("Unknown step class: {$stepClass}")
        };
    }
}
