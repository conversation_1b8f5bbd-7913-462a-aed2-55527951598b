<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestStatus;
use App\Domain\PrivateBooking\Exceptions\ArtisanAvailabilityException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Services\ArtisanAvailability;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPriceResolver;
use App\Domain\PrivateBooking\Services\PrivateBookingThread;
use App\Enums\NotificationEnum;
use App\Livewire\Artisan\DateRequest\AbstractManagementModalComponent;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingWizard;
use App\Livewire\Artisan\PrivateBooking\Support\StepFactory;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;

class PrivateBookingModal extends AbstractManagementModalComponent
{
    #[Locked]
    public PrivateBookingRequest $request;

    public int $requestId;
    public string $step = PrivateBookingRequestManagementStep::FIRST_STEP->value;

    // Refusal-specific properties
    public ?string $refuseReason = null;
    public ?string $refuseReasonOther = null;

    // Other proposal properties
    /** @var array<int, string> */
    public array $otherProposalDay = [];
    /** @var array<int, string> */
    public array $otherProposalSlot = [];

    // Quote properties
    /** @var array<int, array<string, mixed>> */
    public array $lines = [];
    public float $discountPercent = 0;

    // Modal data (populated from PrivateBookingModalData)
    public ?PrivateBookingModalData $modalData = null;

    private ?PrivateBookingWizard $wizard = null;

    public function render(): View
    {
        return view('livewire.artisan.private-booking.private-booking-modal');
    }

    public function boot(StepFactory $stepFactory): void
    {
        if ($this->modalData !== null) {
            $this->wizard = new PrivateBookingWizard($this->modalData, $stepFactory);
        }
    }

    #[On('private-booking-request-modal-opened')]
    public function init(?int $id = null): void
    {
        if ($id === null) {
            return;
        }

        $this->requestId = $id;
        $this->request = PrivateBookingRequest::findOrFail($this->requestId);

        if ($this->request->status !== PrivateBookingRequestStatus::New) {
            $this->notify(
                __('components/modals/private-booking-requests.messages.error.already_answered'),
                type: NotificationEnum::Error
            );
            $this->modal('private-booking-request')->close();
            return;
        }

        $this->initializeModalData();
        $this->initArtisanAvailability();
        $this->modal('private-booking-request')->open();
    }

    #[On('private-booking-request-modal-closed')]
    public function cleanData(): void
    {
        $this->reset();
        $this->step = PrivateBookingRequestManagementStep::FIRST_STEP->value;
        $this->modalData = null;
        $this->wizard = null;
    }

    private function initializeModalData(): void
    {
        $artisanAvailabilityService = app(ArtisanAvailability::class);
        $priceResolver = app(PrivateBookingRequestPriceResolver::class);
        $privateBookingThread = app(PrivateBookingThread::class);

        $artisanAvailability = $artisanAvailabilityService->getArtisanAvailability($this->request);
        $estimatedPrice = $priceResolver->getCurrentPrice($this->request)->floatValue();
        $customerMessage = $privateBookingThread->getCustomerFirstMessage($this->request);

        $this->modalData = PrivateBookingModalData::fromRequest(
            $this->request,
            $artisanAvailability,
            $estimatedPrice,
            $customerMessage
        );
    }

    /**
     * @throws ArtisanAvailabilityException
     */
    public function updatedOtherProposalDay(string $value, string $key): void
    {
        $this->otherProposalSlot[(int) $key] = '';
        if (empty($value)) {
            $this->availableSlots[(int) $key] = null;
            return;
        }

        $artisanAvailabilityService = app(ArtisanAvailability::class);
        $start = Carbon::createFromFormat('Y-m-d H:i:s', $value.' '.ArtisanAvailability::SLOT_START.':00', $this->request->timezone);
        $end = Carbon::createFromFormat('Y-m-d H:i:s', $value.' '.ArtisanAvailability::SLOT_END.':00', $this->request->timezone);

        if ($start === false || $end === false) {
            throw new \InvalidArgumentException('Start and end dates for other proposal day are inconsistent');
        }

        $this->availableSlots[(int) $key] = $artisanAvailabilityService->getArtisanAvailabilityForSlot($this->request, $start->toPeriod($end));
    }

    public function addOtherProposal(): void
    {
        $this->otherProposalSlot[] = '';
        $this->otherProposalDay[] = '';

        // By default, we show an "other proposal slot form" even if otherProposalSlot is empty
        // So if a user click on "add another proposal" we need to add twice proposal
        if (\count($this->otherProposalSlot) === 1) {
            $this->addOtherProposal();
        }
    }

    public function removeOtherProposal(int $index): void
    {
        unset($this->otherProposalSlot[$index]);
        unset($this->otherProposalDay[$index]);
    }

    public function nextStep(): void
    {
        $this->resetErrorBag();

        if ($this->wizard === null) {
            $this->addError('wizard_not_initialized', 'Wizard not properly initialized');
            return;
        }

        $formData = $this->getFormData();
        $result = $this->wizard->processStep($this->step, $formData);

        if (!$result['success']) {
            foreach ($result['errors'] as $key => $message) {
                $this->addError($key, $message);
            }
            return;
        }

        if ($result['completed']) {
            $this->dispatch('refresh-private-booking-list');
            $this->modal('private-booking-request')->close();
            return;
        }

        if ($result['nextStep'] !== null) {
            $this->step = $result['nextStep']->value;
        }
    }

    public function backStep(): void
    {
        $this->resetErrorBag();

        if ($this->wizard === null) {
            return;
        }

        $previousStep = $this->wizard->getPreviousStep($this->step);
        if ($previousStep !== null) {
            $this->step = $previousStep->value;
        }
    }

    private function getFormData(): array
    {
        return [
            'currentStep' => $this->step,
            'responseChoice' => $this->responseChoice,
            'selectedSlots' => $this->selectedSlots,
            'artisanMessage' => $this->artisanMessage,
            'refuseReason' => $this->refuseReason,
            'refuseReasonOther' => $this->refuseReasonOther,
            'otherProposalDay' => $this->otherProposalDay,
            'otherProposalSlot' => $this->otherProposalSlot,
            'lines' => $this->lines,
            'discountPercent' => $this->discountPercent,
        ];
    }

    // Computed properties for template access
    public function getIsCompanyProperty(): bool
    {
        return $this->modalData?->isCompany ?? false;
    }

    public function getWorkshopProperty(): ?\App\Models\Atelier
    {
        return $this->modalData?->workshop;
    }

    public function getEstimatedPriceInclVatProperty(): float
    {
        return $this->modalData?->estimatedPriceInclVat ?? 0;
    }

    public function getCustomerDisplayProperty(): string
    {
        return $this->modalData?->customerDisplay ?? '';
    }

    public function getParticipantsDisplayProperty(): string
    {
        return $this->modalData?->participantsDisplay ?? '';
    }

    public function getProposedSlotsProperty(): array
    {
        return $this->modalData?->proposedSlots ?? [];
    }

    public function getCustomerMessageProperty(): ?string
    {
        return $this->modalData?->customerMessage;
    }

    public function getCustomerLocationProperty(): ?string
    {
        return $this->modalData?->customerLocation;
    }

    public function getCurrencyProperty(): ?\App\Enums\Currency
    {
        return $this->modalData?->currency;
    }

    public function getLocationProperty(): string
    {
        return $this->modalData?->location ?? '';
    }

    public function getArtisanAvailabilityProperty(): array
    {
        return $this->modalData?->artisanAvailability ?? [];
    }

    public function getWorkshopNameProperty(): string
    {
        return $this->modalData?->workshopName ?? '';
    }

    public function getWorkshopCurrencyProperty(): string
    {
        return $this->modalData?->workshopCurrency ?? 'EUR';
    }

    public function getWorkshopTimezoneProperty(): string
    {
        return $this->modalData?->workshopTimezone ?? 'Europe/Paris';
    }

    public function getWorkshopDurationDayProperty(): int
    {
        return $this->modalData?->workshopDurationDay ?? 0;
    }

    public function getWorkshopDurationHourProperty(): int
    {
        return $this->modalData?->workshopDurationHour ?? 0;
    }

    public function getWorkshopDurationMinuteProperty(): int
    {
        return $this->modalData?->workshopDurationMinute ?? 0;
    }

    public function getCompanyNameProperty(): ?string
    {
        return $this->modalData?->companyName;
    }

    public function getCustomerLocationPriceProperty(): float
    {
        return $this->modalData?->customerLocationPrice ?? 0;
    }

    public function getBookingPriceProperty(): float
    {
        return $this->modalData?->bookingPrice ?? 0;
    }
}
