<?php

declare(strict_types=1);

namespace App\Livewire\Artisan\PrivateBooking;

use App\Shared\Amount;
use App\Models\Atelier;
use App\Enums\Currency;
use Carbon\CarbonInterface;
use InvalidArgumentException;
use App\Enums\Event\EventType;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestLocation;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestManagementStep;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestStatus;
use App\Domain\PrivateBooking\Exceptions\ArtisanAvailabilityException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Services\ArtisanAvailability;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPriceResolver;
use App\Domain\PrivateBooking\Services\PrivateBookingThread;
use App\Enums\NotificationEnum;
use App\Livewire\Artisan\DateRequest\AbstractManagementModalComponent;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingModalData;
use App\Livewire\Artisan\PrivateBooking\Support\PrivateBookingWizard;
use App\Livewire\Artisan\PrivateBooking\Support\StepFactory;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use function count;

class PrivateBookingModal extends AbstractManagementModalComponent
{
    #[Locked]
    public PrivateBookingRequest $request;

    public ?int $requestId = null;
    public string $step = PrivateBookingRequestManagementStep::FIRST_STEP->value;

    // Refusal-specific properties
    public ?string $refuseReason = null;
    public ?string $refuseReasonOther = null;

    // Other proposal properties
    /** @var array<int, string> */
    public array $otherProposalDay = [];
    /** @var array<int, string> */
    public array $otherProposalSlot = [];

    // Quote properties
    /** @var array<int, array<string, mixed>> */
    public array $lines = [];
    public float $discountPercent = 0;

    // Workshop and booking data properties
    public bool $isCompany = false;
    public string $workshopName = '';
    public string $workshopCurrency = 'EUR';
    public string $workshopTimezone = 'Europe/Paris';
    public int $workshopDurationDay = 0;
    public int $workshopDurationHour = 0;
    public int $workshopDurationMinute = 0;
    public float $estimatedPriceInclVat = 0;
    public string $customerDisplay = '';
    public string $participantsDisplay = '';
    public ?string $customerMessage = null;
    public ?string $customerLocation = null;
    public ?string $companyName = null;
    public float $customerLocationPrice = 0;
    public float $bookingPrice = 0;
    public string $location = '';

    public Currency $currency;

    #[Locked]
    public Atelier $workshop;

    /** @var array<string, mixed> */
    public array $artisanAvailability = [];

    /** @var CarbonInterface[][] */
    public array $proposedSlots = [];

    private ?PrivateBookingWizard $wizard = null;

    public function render(): View
    {
        return view('livewire.artisan.private-booking.private-booking-modal');
    }

    public function boot(StepFactory $stepFactory): void
    {
        if ($this->requestId === null) {
            return;
        }
        $modalData = $this->createModalData();
        $this->wizard = new PrivateBookingWizard($modalData, $stepFactory);
    }

    #[On('private-booking-request-modal-opened')]
    public function init(?int $id = null): void
    {
        if ($id === null) {
            return;
        }

        $this->requestId = $id;
        $this->request = PrivateBookingRequest::findOrFail($this->requestId);

        if ($this->request->status !== PrivateBookingRequestStatus::New) {
            $this->notify(
                __('components/modals/private-booking-requests.messages.error.already_answered'),
                type: NotificationEnum::Error
            );
            $this->modal('private-booking-request')->close();
            return;
        }

        $this->populateComponentProperties();
        $this->initArtisanAvailability();
    }

    #[On('private-booking-request-modal-closed')]
    public function cleanData(): void
    {
        $this->reset();
        $this->step = PrivateBookingRequestManagementStep::FIRST_STEP->value;
        $this->wizard = null;
    }

    private function populateComponentProperties(): void
    {
        $artisanAvailabilityService = app(ArtisanAvailability::class);
        $priceResolver = app(PrivateBookingRequestPriceResolver::class);
        $privateBookingThread = app(PrivateBookingThread::class);

        // Populate component properties directly
        $this->workshop = $this->request->workshop;
        $this->isCompany = $this->request->is_company;
        $this->location = $this->request->location->value;
        $this->currency = $this->request->getCurrency();
        $this->companyName = $this->request->company_name;

        // Calculate customer location price
        $this->customerLocationPrice = $this->location === PrivateBookingRequestLocation::Customer->value
            ? Amount::fromFloatCents(
                config('private-booking.private_client_managed_location.travel_expenses', 0, $this->request->country_code),
                $this->currency
            )->addPercent($this->workshop->artisan->getVatRate())->floatValue()
            : 0;

        // Format customer location
        $this->customerLocation = $this->location === PrivateBookingRequestLocation::Customer->value
            ? $this->request->address_1 . ', ' . $this->request->zip_code . ', ' . $this->request->city . '<br>' . $this->request->address_2
            : null;

        // Set other properties
        $this->estimatedPriceInclVat = $priceResolver->getCurrentPrice($this->request)->floatValue();
        $this->participantsDisplay = $this->request->present()->participantsNumber();
        $this->proposedSlots = $this->request->getProposedSlotsByDay();
        $this->customerMessage = $privateBookingThread->getCustomerFirstMessage($this->request);
        $this->customerDisplay = $this->request->present()->customerName();

        // Calculate booking price
        $minParticipants = $this->workshop->getMinParticipantsForEventType(
            EventType::fromLocation($this->request->location)
        );
        $this->bookingPrice = $this->workshop->prix * max($this->request->participants_number, $minParticipants);

        // Set artisan availability
        $this->artisanAvailability = $artisanAvailabilityService->getArtisanAvailability($this->request);

        // Set workshop properties
        $this->workshopName = $this->workshop->nom;
        $this->workshopCurrency = $this->workshop->currency;
        $this->workshopTimezone = $this->workshop->timezone;
        $this->workshopDurationDay = $this->workshop->nb_jours;
        $this->workshopDurationHour = $this->workshop->nb_heures;
        $this->workshopDurationMinute = $this->workshop->nb_minutes;
    }

    private function createModalData(): PrivateBookingModalData
    {
        return PrivateBookingModalData::fromRequest(
            $this->request,
            $this->artisanAvailability,
            $this->estimatedPriceInclVat,
            $this->customerMessage
        );
    }

    /**
     * @throws ArtisanAvailabilityException
     */
    public function updatedOtherProposalDay(string $value, string $key): void
    {
        $this->otherProposalSlot[(int)$key] = '';
        if (empty($value)) {
            $this->availableSlots[(int)$key] = null;
            return;
        }

        $artisanAvailabilityService = app(ArtisanAvailability::class);
        $start = Carbon::createFromFormat('Y-m-d H:i:s', $value . ' ' . ArtisanAvailability::SLOT_START . ':00', $this->request->timezone);
        $end = Carbon::createFromFormat('Y-m-d H:i:s', $value . ' ' . ArtisanAvailability::SLOT_END . ':00', $this->request->timezone);

        if ($start === false || $end === false) {
            throw new InvalidArgumentException('Start and end dates for other proposal day are inconsistent');
        }

        $this->availableSlots[(int)$key] = $artisanAvailabilityService->getArtisanAvailabilityForSlot($this->request, $start->toPeriod($end));
    }

    public function addOtherProposal(): void
    {
        $this->otherProposalSlot[] = '';
        $this->otherProposalDay[] = '';

        // By default, we show an "other proposal slot form" even if otherProposalSlot is empty
        // So if a user click on "add another proposal" we need to add twice proposal
        if (count($this->otherProposalSlot) === 1) {
            $this->addOtherProposal();
        }
    }

    public function removeOtherProposal(int $index): void
    {
        unset($this->otherProposalSlot[$index]);
        unset($this->otherProposalDay[$index]);
    }

    public function nextStep(): void
    {
        $this->resetErrorBag();

        if ($this->wizard === null) {
            $this->addError('wizard_not_initialized', 'Wizard not properly initialized');
            return;
        }

        $formData = $this->getFormData();
        $result = $this->wizard->processStep($this->step, $formData);

        if (!$result['success']) {
            foreach ($result['errors'] as $key => $message) {
                $this->addError($key, $message);
            }
            return;
        }

        if ($result['completed']) {
            $this->dispatch('refresh-private-booking-list');
            $this->modal('private-booking-request')->close();
            return;
        }

        if ($result['nextStep'] !== null) {
            $this->step = $result['nextStep']->value;
        }
    }

    public function backStep(): void
    {
        $this->resetErrorBag();

        if ($this->wizard === null) {
            return;
        }

        $previousStep = $this->wizard->getPreviousStep($this->step);
        if ($previousStep !== null) {
            $this->step = $previousStep->value;
        }
    }

    private function getFormData(): array
    {
        return [
            'currentStep' => $this->step,
            'responseChoice' => $this->responseChoice,
            'selectedSlots' => $this->selectedSlots,
            'artisanMessage' => $this->artisanMessage,
            'refuseReason' => $this->refuseReason,
            'refuseReasonOther' => $this->refuseReasonOther,
            'otherProposalDay' => $this->otherProposalDay,
            'otherProposalSlot' => $this->otherProposalSlot,
            'lines' => $this->lines,
            'discountPercent' => $this->discountPercent,
        ];
    }
}
