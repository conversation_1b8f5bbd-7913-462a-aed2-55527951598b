<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Services\HttpRedirector;
use App\Services\UriLocaleRemover;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

readonly class GuardHttpRedirectMiddleware
{
    public function __construct(
        private HttpRedirector $httpRedirector,
        private UriLocaleRemover $uriLocaleRemover,
    ) {
    }

    public function handle(Request $request, \Closure $next): Response
    {
        $uri = $this->uriLocaleRemover->fromRequest($request);
        $httpRedirect = $this->httpRedirector->findBySourceUri($uri);

        if ($httpRedirect === null) {
            return $next($request);
        }

        return $this->httpRedirector->toRedirectResponse($httpRedirect);
    }
}
