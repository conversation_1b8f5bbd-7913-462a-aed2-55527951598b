<?php

declare(strict_types=1);

namespace App\Http\Transformers;

use App\Domain\Content\Abstracts\CategorizationModel;
use App\Domain\Content\Enums\SinglePageSectionTypes;
use App\Domain\Content\Models\SinglePage;
use App\Domain\Content\Models\SinglePageSection;
use App\Domain\Content\Repositories\SinglePageRepository;
use App\Domain\Content\SinglePageService;
use App\Domain\Content\SingleSectionService;
use App\Infrastructure\Search\Query\SearchService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Storage;
use League\Fractal;

class FrontSinglePageTransformer extends Fractal\TransformerAbstract
{
    private SearchService $searchService;
    private SinglePageService $singlePageService;
    private SingleSectionService $singleSectionService;
    private SinglePageRepository $singlePageRepository;
    private FilesystemAdapter $imageStorage;

    public function __construct(
        private readonly bool $isSsr,
        private readonly ?string $ipAddress = null,
    ) {
        $this->searchService = \App::make(SearchService::class);
        $this->singlePageService = \App::make(SinglePageService::class);
        $this->singleSectionService = \App::make(SingleSectionService::class);
        $this->singlePageRepository = \App::make(SinglePageRepository::class);
        $this->imageStorage = Storage::disk(CategorizationModel::getStorageDisk());
    }

    /**
     * @return array<string, mixed>
     */
    public function transform(SinglePage $singlePage): array
    {
        $subject = $singlePage->subject;

        if ($subject instanceof CategorizationModel) {
            $subjectIdentifier = $this->searchService->getSearchEngineIdentifierForCategory($subject);
            $subjectFacet = $subject::getFacetName();
            $subjectLabel = $subject->getLabel();
            $subjectSlug = $subject->getSlug();
            $subjectQuery = [
                'filters' => $this->searchService->buildQueryFromArray(
                    $this->searchService->categoryToSearchFilterArray($subject)
                ),
            ];
        }

        $sections = $this->getSections($singlePage);
        $subjectLinkedSinglePages = $this->singlePageRepository->getSubjectLinkedSinglePages($singlePage->getSubjectOrFail());
        $customLinkedPages = $singlePage->customLinkedPages;

        $technique = $singlePage->getTechnique();

        return [
            'mode' => $singlePage->mode->value,
            'is_condensed' => $singlePage->is_condensed,
            'hero_image' => Storage::disk(CategorizationModel::getStorageDisk())->url($singlePage->hero_image),
            'badge_image' => Storage::disk(CategorizationModel::getStorageDisk())->url($singlePage->badge_image),
            'title' => $singlePage->title,
            'description' => $singlePage->description,
            'meta_title' => $singlePage->meta_title,
            'meta_description' => $singlePage->meta_description,
            'seo_title' => $singlePage->seo_title,
            'seo_content' => $singlePage->seo_content,
            'subject' => [
                'label' => $subjectLabel ?? '',
                'slug' => $subjectSlug ?? '',
                'type' => $singlePage->subject_type,
                'id' => $singlePage->subject_id,
                'identifier' => $subjectIdentifier ?? '',
                'algolia' => [
                    'facet' => $subjectFacet ?? '',
                    'query' => $subjectQuery ?? '',
                ],
            ],
            'city' => $singlePage->city !== null ? [
                'id' => $singlePage->city->getKey(),
                'name' => $singlePage->city->ville_nom,
                'slug' => $singlePage->city->getSlug(),
            ] : null,
            'craft' => $singlePage->craft !== null ? [
                'id' => $singlePage->craft->getKey(),
                'name' => $singlePage->craft->name,
                'slug' => $singlePage->craft->getSlug(),
            ] : null,
            'technique' => $technique !== null ? [
                'craft' => [
                    'id' => $technique->craft->getKey(),
                    'name' => $technique->craft->name,
                    'slug' => $technique->craft->getSlug(),
                ],
            ] : null,
            'sections' => $sections->map(fn (SinglePageSection $item) => [
                'id' => $item->getKey(),
                'title' => $item->title,
                'description' => $item->description,
                'type' => $item->type,
                ...$this->singleSectionService->getSectionData($item, $this->ipAddress, $this->isSsr),
            ]),
            'linked_pages' => $subjectLinkedSinglePages->map(fn (SinglePage $singlePage) => [
                'title' => $singlePage->subject?->getLabel(),
                'slug' => $singlePage->getUrl(),
                'image' => $this->imageStorage->url($singlePage->hero_image),
            ]),
            'custom_linked_pages' => $customLinkedPages->map(fn (SinglePage $singlePage) => [
                'title' => $singlePage->getSubject()?->getLabel() ?? $singlePage->title,
                'slug' => $singlePage->getUrl(),
                'image' => $this->imageStorage->url($singlePage->hero_image ?? $this->singlePageService->getDefaultHeroImage()),
            ]),
        ];
    }

    /**
     * @return Collection<int,SinglePageSection>
     */
    private function getSections(SinglePage $singlePage): Collection
    {
        if ($this->isSsr) {
            return $singlePage->sections()
                ->with(['singlePage', 'singlePage.subject'])
                ->where('type', '!=', SinglePageSectionTypes::CREATIONS)
                ->get();
        }

        return $singlePage->sections;
    }
}
