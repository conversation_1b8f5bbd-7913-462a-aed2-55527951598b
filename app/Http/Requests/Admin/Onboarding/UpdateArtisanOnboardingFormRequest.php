<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Onboarding;

use App\Rules\Onboarding\AtelierOnboardingUpdate;
use Illuminate\Foundation\Http\FormRequest;

class UpdateArtisanOnboardingFormRequest extends FormRequest
{
    /**
     * @return array<string, array<string|\App\Rules\Onboarding\AtelierOnboardingUpdate>>
     */
    public function rules(): array
    {
        return [
            'model' => [
                'required',
            ],
            'model.atelier' => [new AtelierOnboardingUpdate()],
            'model.lieu' => [
                'required',
            ],
            'model.lieu.google_adress' => [
                'required',
            ],
            'model.lieu.id' => [
                'required',
            ],
            'model.artisan' => [
                'required',
            ],
            'model.artisan.nom' => [
                'required',
            ],
            'model.artisan.email' => [
                'required',
            ],
            'model.artisan.phone' => [
                'required',
            ],
            'model.artisan.prenom' => [
                'required',
            ],
            'model.artisan.id' => [
                'required',
            ],
        ];
    }

    /** @return array<string, string> */
    public function messages(): array
    {
        return [
            'model.required' => "Le model de l'artisan à valider n'est pas renseigné.",
            'model.lieu.required' => "Le lieu à mettre à jour n'est pas renseigné.",
            'model.lieu.id' => "L'id du lieu à mettre à jour n'est pas renseigné.",
            'model.lieu.google_adress' => "L'adresse du lieu n'est pas renseignée.",
            'model.artisan.required' => "L'artisan à mettre à jour n'est pas renseigné.",
            'model.artisan.nom' => 'Nom non renseigné.',
            'model.artisan.email' => 'Email non renseigné.',
            'model.artisan.phone' => 'Téléphone non renseigné.',
            'model.artisan.prenom' => 'Prénom non renseigné.',
            'model.artisan.id' => "L'id de l'artisan à mettre à jour n'est pas renseigné.",
        ];
    }
}
