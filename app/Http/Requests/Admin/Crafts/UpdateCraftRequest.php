<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Crafts;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCraftRequest extends FormRequest
{
    /**
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'fr.name' => [
                'required',
                'string',
            ],
            'en.name' => [
                'required',
                'string',
            ],
            'nl.name' => [
                'required',
                'string',
            ],
            'fr.description' => [
                'required',
                'string',
            ],
            '*.description' => [
                'nullable',
                'string',
            ],
            'thumbnail_image' => [
                'nullable',
                'image',
                'max:6144',
            ],
        ];
    }
}
