<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Companies;

use App\Domain\Company\Enums\MerchantType;
use App\Models\Entreprise;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

class StoreCompanyRequest extends FormRequest
{
    /**
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        $company = $this->route('company') instanceof Entreprise ? $this->route('company') : null;

        return [
            'nom' => [
                'required',
                'string',
                'max:191',
                Rule::unique(Entreprise::class, 'nom')
                    ->when($company, fn (Unique $rule) => $rule->ignore((int) $company?->id)),
            ],
            'trigram' => [
                'nullable',
                'string',
                'size:3',
                'uppercase',
                'regex:/^[A-Z]{3}$/',
                Rule::requiredIf($this->isMethod('POST')),
                Rule::excludeIf($this->isMethod('PUT')),
                Rule::unique(Entreprise::class, 'trigram'),
            ],
            'merchant_type' => [
                'required',
                Rule::enum(MerchantType::class),
            ],
            'gift_card_commission_percent' => [
                'nullable',
                'numeric',
                'decimal:0,2',
                'min:0',
                'max:100',
            ],
        ];
    }
}
