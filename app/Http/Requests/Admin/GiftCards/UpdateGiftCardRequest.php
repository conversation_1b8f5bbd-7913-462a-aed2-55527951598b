<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\GiftCards;

use App\Rules\ValidEmail;
use Illuminate\Foundation\Http\FormRequest;

class UpdateGiftCardRequest extends FormRequest
{
    /**
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            'contactEmail' => [
                'nullable',
                new ValidEmail(),
            ],
            'giftFrom' => [
                'nullable',
                'string',
                'max:191',
            ],
            'giftTo' => [
                'nullable',
                'string',
                'max:191',
            ],
            'deliveryEmail' => [
                'nullable',
                'sometimes',
                'array',
            ],
            'deliveryEmailDate' => [
                'required_with:deliveryEmail',
                'after:today',
                'date_format:d/m/Y',
            ],
            'deliveryEmailEmail' => [
                'required_with:deliveryEmail',
                new ValidEmail(),
            ],
        ];
    }
}
