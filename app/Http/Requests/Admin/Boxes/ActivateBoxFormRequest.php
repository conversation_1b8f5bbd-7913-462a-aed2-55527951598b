<?php

declare(strict_types=1);

namespace App\Http\Requests\Admin\Boxes;

use App\Models\Entreprise;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;

class ActivateBoxFormRequest extends FormRequest
{
    /**
     * @return array<string, string|array<int, string|Exists>>
     */
    public function rules(): array
    {
        return [
            'activation_code' => [
                'required',
                'string',
            ],
            'company_id' => [
                'required',
                Rule::exists(Entreprise::class, 'id'),
            ],
        ];
    }
}
