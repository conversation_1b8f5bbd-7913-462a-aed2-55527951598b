<?php

declare(strict_types=1);

namespace App\Http\Requests\UserArtisan\ProfileCompletion;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\RequiredIf;

class ProfileCompletionExternalStepFormRequest extends FormRequest
{
    /**
     * @return array<string, mixed[]>
     */
    public function rules(): array
    {
        return [
            'atelier' => [
                'required',
            ],
            'atelier.*.id' => [
                'required',
            ],
            'atelier.*.is_press_interested' => [
                'required',
            ],
            'atelier.*.possible_press_discount' => [
                'nullable',
                new RequiredIf($this['atelier.*.is_press_interested'] == 1),
            ],
        ];
    }

    /** @return array<string, string> */
    public function messages(): array
    {
        return [
            'atelier.required' => __('userartisan/controllers/errors.workshop_multiple.required'),
            'atelier.*.id.required' => __('userartisan/controllers/errors.workshop_multiple.id.required'),
            'atelier.*.is_press_interested.required' => __('userartisan/controllers/errors.workshop_multiple.is_press_interested.required'),
            'atelier.*.possible_press_discount' => __('userartisan/controllers/errors.workshop_multiple.possible_press_discount.required'),
        ];
    }
}
