<?php

declare(strict_types=1);

namespace App\Http\Requests\UserArtisan\Invoices;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;

class MonthlyInvoicesRequest extends FormRequest
{
    /**
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'month' => [
                'required',
                'date_format:m-Y',
            ],
        ];
    }

    protected function failedValidation(Validator $validator): void
    {
        Session::flash('flash_message', __('userartisan/view/billing.service.flash_message'));
        Session::flash('flash_message_danger');

        parent::failedValidation($validator);
    }
}
