<?php

declare(strict_types=1);

namespace App\Http\Requests\Front\API\Cart;

use App\Domain\Gift\Enums\GiftTheme;
use App\Models\Evenement;
use App\Rules\ValidEmail;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CartBookingStoreRequest extends FormRequest
{
    /**
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            // Booking details
            'eventId' => [
                'required',
                Rule::exists(Evenement::class, 'id'),
            ],
            'phoneNumber' => [
                'required',
                'phone:INTERNATIONAL',
            ],
            'adultCount' => [
                'required',
                'numeric',
                'gte:0',
            ],
            'childCount' => [
                'required',
                'numeric',
                'gte:0',
            ],
            'bookingCode' => [
                'nullable',
            ],
            // Gift details
            'gift' => [
                'nullable',
                'sometimes',
                'array',
            ],
            'gift.message' => [
                Rule::requiredIf($this->isGift()),
                'string',
            ],
            'gift.theme' => [
                Rule::requiredIf($this->isGift()),
                Rule::enum(GiftTheme::class),
            ],
            'gift.from' => [
                Rule::requiredIf($this->isGift()),
                'string',
                'max:191',
            ],
            'gift.to' => [
                Rule::requiredIf($this->isGift()),
                'string',
                'max:191',
            ],
            // Gift delivery emails details
            'gift.deliveryEmail' => [
                'nullable',
                'sometimes',
                'array',
            ],
            'gift.deliveryEmail.email' => [
                Rule::requiredIf($this->hasDeliveryEmail()),
                new ValidEmail(),
                'max:191',
            ],
            'gift.deliveryEmail.date' => [
                Rule::requiredIf($this->hasDeliveryEmail()),
                'date_format:Y-m-d',
                'after_or_equal:tomorrow',
            ],
        ];
    }

    public function isGift(): bool
    {
        return $this->get('gift') !== null;
    }

    public function hasDeliveryEmail(): bool
    {
        return $this->input('gift.deliveryEmail') !== null;
    }

    /**
     * @return array<string, mixed>
     */
    public function messages(): array
    {
        return [
            'gift.from.required' => __('api/booking.gift.validation.from.required'),
            'gift.to.required' => __('api/booking.gift.validation.to.required'),
            'gift.message.required' => __('validation.messages.required'),
            'gift.theme.required' => __('validation.messages.required'),
            'giftTheme' => __('front/validation/attributes.physicalGiftCard.unavailable'),
            'gift.deliveryEmail.email.required' => __('api/booking.gift.validation.deliveryEmail.email.required'),
            'gift.deliveryEmail.date.required' => __('api/booking.gift.validation.deliveryEmail.date.required'),
        ];
    }
}
