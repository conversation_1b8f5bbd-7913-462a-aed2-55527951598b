<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front;

use App\Enums\Country;
use App\Enums\Locale;
use App\Http\Controllers\Controller;
use App\Services\CountryRetriever;
use App\Services\LocaleRetriever;
use App\Services\LocalizedConfigService;
use Illuminate\Http\RedirectResponse;

class InternationalClosingController extends Controller
{
    public function __construct(
        private readonly CountryRetriever $countryRetriever,
        private readonly LocaleRetriever $localeRetriever,
        private readonly LocalizedConfigService $localizedConfig
    ) {
    }

    public function redirectLanding(): RedirectResponse
    {
        $currentCountry = $this->countryRetriever->getCurrentCountry();

        $url = match ($currentCountry) {
            Country::Netherlands => $this->localizedConfig->string('app.url', country: Country::Belgium->value).'/'.Locale::Dutch->value,
            default => $this->localizedConfig->string('app.url', country: Country::France->value),
        };

        return redirect()->away($url, 301);
    }

    public function redirectDomain(): RedirectResponse
    {
        $currentCountry = $this->countryRetriever->getCurrentCountry();
        $currentLocale = $this->localeRetriever->getCurrentLocale();

        $url = match ([$currentCountry, $currentLocale]) {
            [Country::Netherlands, Locale::English],
            [Country::GreatBritain, Locale::English] => $this->localizedConfig->string('app.url', country: Country::Belgium->value).'/'.Locale::English->value,
            [Country::Netherlands, Locale::Dutch] => $this->localizedConfig->string('app.url', country: Country::Belgium->value).'/'.Locale::Dutch->value,
            default => $this->localizedConfig->string('app.url', country: Country::France->value),
        };

        return redirect()->away($url, 301);
    }

    public function hardRedirect(string $to): RedirectResponse
    {
        return redirect()->away($to, 301);
    }
}
