<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front;

use App\Domain\Accounting\Orders\Models\OrderDiscount;
use App\Domain\Discount\Sponsorship;
use App\Domain\ECommerce\CartService;
use App\Domain\ECommerce\Exceptions\Coupon\InvalidCouponException;
use App\Http\Controllers\Controller;
use App\Infrastructure\Auth\AuthFinder;
use App\Mail\Guest\SponsorshipInvitation;
use App\Models\Parrainage\CodeParrainageInvitation;
use App\Models\User;
use App\Rules\ValidEmail;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

/** @SuppressWarnings(PHPMD.CouplingBetweenObjects)  */
class SponsorshipController extends Controller
{
    public function __construct(private readonly AuthFinder $authFinder)
    {
    }

    public function display(Sponsorship $sponsorship): View
    {
        $user = $this->authFinder->tryCustomer();
        $sponsorshipUses = collect();
        if ($user instanceof User) {
            $sponsorshipUses = OrderDiscount::query()
                ->havingSuccessfulOrder()
                ->where('discount_type', (new CodeParrainageInvitation())->getMorphClass())
                ->where('discount_id', $user->codeparrainageinvitation?->id)
                ->get();
        }

        return view('front.user.sponsorship.display', [
            'user' => $user,
            'sponsorshipUses' => $sponsorshipUses,
            'sponsorshipInvitationPercentage' => $sponsorship->getInvitationPercentage(),
            'sponsorshipUserBonusAmount' => $sponsorship->getUserBonusAmount(),
            'sponsorshipOriginalUserBonusAmount' => $sponsorship->getOriginalUserBonusAmount(),
            'sponsorOverrideTo' => $sponsorship->getOverrideTo(),
            'sponsorOverride' => $sponsorship->isUserBonusAmountOverride(),
        ]);
    }

    public function accueil(Request $request): View|RedirectResponse
    {
        if ($request->has('code')) {
            if (!CodeParrainageInvitation::where('code_reduction', '=', request('code'))->exists()) {
                flashMessage('sponsorship.failure_does_not_exist', 'danger');

                return redirect()->route(localizedRouteName('home'));
            }

            $codeparrainageinvitation = CodeParrainageInvitation::where('code_reduction', '=', request('code'))->firstOrFail();
            $user = $codeparrainageinvitation->getUser();
            if (\Auth::id() === $user?->id) {
                return redirect()->route(localizedRouteName('parrainage.display'));
            }

            try {
                $codeparrainageinvitation->check();
            } catch (InvalidCouponException) {
                flashMessage('coupon.failure_invalid_coupon', 'danger');

                return redirect()->route(localizedRouteName('home'));
            }

            $cart = CartService::applyDiscountOnCart(CartService::getCart(), $codeparrainageinvitation);
            session(['discount' => $codeparrainageinvitation]);
            CartService::store($cart);

            flashMessage('discount.application_success', 'success');

            return view('front.user.sponsorship.accueil', ['user' => $user]);
        }

        return redirect()->route(localizedRouteName('home'));
    }

    public function mail(Request $request): RedirectResponse
    {
        $emails = $request->input('mail');
        $emails = array_map('trim', explode(';', (string) $emails));

        try {
            $request->merge(['mail' => $emails]);
            $request->validate([
                'mail' => ['required', 'array'],
                'mail.*' => [new ValidEmail()],
            ]);

            $user = $this->authFinder->tryCustomer();
            if (!$user instanceof User) {
                throw new \LogicException('Invalid User instance');
            }
            foreach ($emails as $email) {
                Mail::to($email)->locale($user->preferredLocale())->send((new SponsorshipInvitation($user))->country($user->country_code));
                if ($user->codeparrainageuser !== null) {
                    ++$user->codeparrainageuser->nb_mails;
                }
            }

            $user->codeparrainageuser?->save();
        } catch (\Exception) {
            flashMessage('sponsorship.mail_failure', 'danger');

            return redirect()->route(localizedRouteName('parrainage.display'))->withInput(['mail' => implode(';', $emails)]);
        }

        flashMessage('sponsorship.mail_success', 'success');

        return redirect()->route(localizedRouteName('parrainage.display'));
    }
}
