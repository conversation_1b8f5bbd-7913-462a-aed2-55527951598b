<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front\SinglePages;

use App\Domain\Content\Repositories\PageRepository;
use App\Domain\Content\Services\Statistics;
use App\Enums\Categorization\FacetType;
use App\Http\Controllers\Controller;
use App\Http\Middleware\Crawler;
use App\Http\Transformers\FrontSinglePageTransformer;
use App\Infrastructure\Search\Query\CategorizationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Spatie\Fractal\Fractal;

class SinglePageController extends Controller
{
    public function __construct(
        private readonly PageRepository $pageRepository,
        private readonly CategorizationService $categorizationService,
        private readonly Statistics $statictics,
    ) {
    }

    public function __invoke(Request $request, string $slug): View|Response
    {
        try {
            return $this->getSinglePageView(
                $slug,
                $request->ip(),
                $request->attributes->getBoolean(Crawler::IS_CRAWLER)
            );
        } catch (\InvalidArgumentException) {
            return response()->view('front.errors.404', [], 404);
        }
    }

    private function getSinglePageView(string $slug, ?string $ipAddress, bool $isSsr): View
    {
        $singlePage = $this->pageRepository->findActivePageBySlugAndCountry($slug, config('app.country', 'fr'));

        if ($singlePage === null) {
            throw new \InvalidArgumentException("Single page with slug [$slug] not found");
        }

        $pageData = [
            'pageProps' => Fractal::create(
                $singlePage,
                new FrontSinglePageTransformer($isSsr, $ipAddress)
            )->toArray()['data'] ?? [],
        ];

        if ($isSsr) {
            $pageData['cities'] = $this->categorizationService->getHydratedFacet(
                FacetType::Ville,
                ['filters' => 'country:'.config('app.country')]
            );

            $pageData['crafts'] = $this->categorizationService->getHydratedFacet(
                FacetType::Craft,
                ['filters' => 'country:'.config('app.country')]
            );

            $pageData['pageProps']['total_experiences'] = $this->statictics->countWorkshop();
        }

        return view($this->getTemplate($isSsr), $pageData);
    }

    private function getTemplate(bool $isSsr): string
    {
        if ($isSsr) {
            return 'front.content.single.template-ssr';
        }

        return 'front.content.single.template';
    }
}
