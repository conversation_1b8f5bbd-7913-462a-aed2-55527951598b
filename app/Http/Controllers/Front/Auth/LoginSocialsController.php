<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front\Auth;

use App\Domain\ECommerce\CartService;
use App\Domain\User\UserService;
use App\Enums\SocialProviderTrackers;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSocial;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Two\AbstractProvider;
use Laravel\Socialite\Two\User as UserSocialite;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/** @SuppressWarnings(PHPMD.CouplingBetweenObjects) */
class LoginSocialsController extends Controller
{
    public function redirect(Request $request, string $provider): RedirectResponse
    {
        try {
            /**
             * @throws \InvalidArgumentException
             *
             * @var AbstractProvider $driver
             */
            $driver = \Socialite::driver($provider);
        } catch (\InvalidArgumentException $e) {
            // Abort if an unknown provider is given
            throw new NotFoundHttpException();
        }

        $driver = $driver
            ->stateless()
            ->redirectUrl(route(localizedRouteName('thirdparty.auth.callback'), ['provider' => $provider]));

        if ($request->has('redirect')) {
            $driver = $this->addRedirectUrlForCallback($provider, $driver, $request->get('redirect'));
        }

        return $driver->redirect();
    }

    protected function addRedirectUrlForCallback(
        string $provider,
        AbstractProvider $driver,
        string $redirect
    ): AbstractProvider {
        if (\in_array($provider, ['google', 'facebook'], true)) {
            $driver->with([
                'state' => $redirect,
            ]);
        }

        return $driver;
    }

    public function callback(Request $request, string $provider): RedirectResponse|Redirector
    {
        try {
            /**
             * @throws \InvalidArgumentException
             *
             * @var AbstractProvider $driver
             */
            $driver = \Socialite::driver($provider);
        } catch (\InvalidArgumentException $e) {
            // Abort if an unknown provider is given
            throw new NotFoundHttpException(previous: $e);
        }
        $url = $this->retrieveRedirectUrl($request, $provider);

        // Facebook error
        if ($provider === 'facebook' && $request->has('error_reason')) {
            // Avoid logging the error if the user clicked on "Deny", or "Cancel"
            if (!str_contains($request->get('error_reason'), 'user_denied')) {
                report("Social callback error for provider [$provider] {$request->getQueryString()}");
            }

            \Session::flash('flash_message', __('auth.social_failed'));
            \Session::flash('flash_message_danger');

            return redirect($this->addTrackingParametersToUrl($url, $provider, SocialProviderTrackers::ActionAbort));
        }

        try {
            /** @var UserSocialite $userSocialite */
            $userSocialite = $driver->stateless()->user();
        } catch (\Exception $exception) {
            report($exception);
            \Session::flash('flash_message', __('auth.social_failed'));
            \Session::flash('flash_message_danger');

            return redirect($this->addTrackingParametersToUrl($url, $provider, SocialProviderTrackers::ActionFailed));
        }

        if (empty($userSocialite->getEmail())) {
            \Session::flash('flash_message', __('auth.social_email_missing'));
            \Session::flash('flash_message_danger');

            return redirect($this->addTrackingParametersToUrl(
                route(localizedRouteName('login.show')),
                $provider,
                SocialProviderTrackers::ActionEmailMissing
            ));
        }

        /** @var string $provider */
        $user = $this->attemptLogin($userSocialite, $provider);
        $action = SocialProviderTrackers::ActionLogin;

        if ($user === null) {
            $user = $this->registerUser($userSocialite, $provider);
            $action = SocialProviderTrackers::ActionRegister;
        }

        Auth::loginUsingId($user->getKey(), true);
        CartService::initUserSession($user->getKey());

        $url = $this->addTrackingParametersToUrl($url, $provider, $action);

        return redirect($url);
    }

    protected function associateUserToSocialiteProvider(User $user, string $provider, string $providerId): void
    {
        /** @var UserSocial $userSocial */
        $userSocial = $user->userSocials()->make();
        $userSocial->provider = $provider;
        $userSocial->provider_id = $providerId;
        $userSocial->save();
    }

    /**
     * Adds into the url, parameters used to track authentication
     * activities through social providers.
     */
    protected function addTrackingParametersToUrl(string $url, string $provider, SocialProviderTrackers $action): string
    {
        $parsedUrl = parse_url($url);
        $queryParameters = $parsedUrl['query'] ?? null;

        if ($queryParameters !== null) {
            parse_str($queryParameters, $queryParameters);
        }

        $queryParameters[SocialProviderTrackers::SocialProviderFrom->value] = $provider;
        $queryParameters[SocialProviderTrackers::SocialProviderAction->value] = $action->value;

        $path = $parsedUrl['path'] ?? '/';

        return $path.'?'.http_build_query($queryParameters);
    }

    /**
     * Retrieve the url to redirect to from the social provider.
     */
    protected function retrieveRedirectUrl(Request $request, string $provider): string
    {
        return match (true) {
            $provider === 'google',
            $provider === 'facebook' => $request->get('state', '/'),
            default => '/',
        };
    }

    protected function attemptLogin(UserSocialite $userSocialite, string $provider): ?User
    {
        // First we'll check if the user has already connected
        // with this provider, if so, we can match it with
        // an existing user in our database and return it
        $userSocial = UserSocial::query()
            ->where('provider', $provider)
            ->where('provider_id', $userSocialite->id)
            ->first();

        if ($userSocial instanceof UserSocial && $userSocial->user instanceof User) {
            return $userSocial->user;
        }

        // Reaching this step means the user has never used this social
        // provider, let's check if we already have a user with
        // the same email, if so we'll use that user.
        /** @var ?User $user */
        $user = User::query()->where('email', $userSocialite->email)->first();

        if ($user !== null) {
            $this->associateUserToSocialiteProvider($user, $provider, $userSocialite->id);

            return $user;
        }

        // User doesn't exist
        return null;
    }

    protected function registerUser(UserSocialite $userSocialite, string $provider): User
    {
        // Finally, the user doesn't exist in our database,
        // so we'll quickly register the user to have
        // a record and return it
        $user = UserService::register([
            'prenom' => $userSocialite->first_name ?? '',
            'nom' => $userSocialite->last_name ?? '',
            'email' => $userSocialite->email,
            'password' => null,
            'locale' => config('app.locale'),
            'country_code' => config('app.country'),
        ]);

        $this->associateUserToSocialiteProvider($user, $provider, $userSocialite->id);

        return $user;
    }
}
