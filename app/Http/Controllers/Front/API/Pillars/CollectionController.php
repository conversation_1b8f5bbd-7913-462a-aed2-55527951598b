<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front\API\Pillars;

use App\Domain\Content\Models\Collection;
use App\Domain\Content\Repositories\CollectionRepository;
use App\Http\Controllers\Controller;
use App\Http\Requests\Front\Search\SearchFormRequest;
use App\Infrastructure\Search\Query\SearchService;
use Illuminate\Pagination\LengthAwarePaginator;

class CollectionController extends Controller
{
    /**
     * @return LengthAwarePaginator<Collection>
     */
    public function search(SearchFormRequest $request, CollectionRepository $collectionRepository): LengthAwarePaginator
    {
        return $collectionRepository
            ->fetch(
                $request->get('query'),
                $request->integer('hitsPerPage', 20),
                $request->integer('page', 1)
            )->through(fn (Collection $collection) => [
                'identifier' => $collection->identifier,
                'label' => $collection->name,
                'thumbnail_image' => $collection->getThumbnailImageUrl(),
                'redirectUrl' => $collection->singlePage?->getUrl(),
            ]);
    }

    /**
     * @return array{identifier: string, label: string, thumbnail_image: string, redirectUrl: string, filters: string}
     */
    public function find(
        string $identifier,
        CollectionRepository $collectionRepository,
        SearchService $searchService
    ): array {
        $collection = $collectionRepository->findByIdentifier($identifier);

        return [
            'identifier' => $collection->identifier,
            'label' => $collection->name,
            'thumbnail_image' => $collection->getThumbnailImageUrl(),
            'redirectUrl' => $collection->singlePage?->getUrl() ?? '',
            'filters' => $searchService->buildQueryFromArray($collection->filters),
        ];
    }
}
