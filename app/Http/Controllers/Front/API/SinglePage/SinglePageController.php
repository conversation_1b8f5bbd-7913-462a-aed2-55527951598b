<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front\API\SinglePage;

use App\Domain\Content\Enums\SinglePageMode;
use App\Domain\Content\Models\SinglePage;
use App\Domain\Content\Repositories\PageRepository;
use App\Http\Controllers\Controller;
use App\Http\Resources\SinglePage\SinglePageResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SinglePageController extends Controller
{
    public function __construct(
        private readonly PageRepository $repository
    ) {
    }

    public function listByMode(SinglePageMode $mode): AnonymousResourceCollection
    {
        $singlePages = $this->repository->findActivePageByModeAndCountry($mode, config('app.country', 'fr'));

        if ($mode === SinglePageMode::Company) {
            // Hotfix for companies landing page:
            $singlePages = $singlePages->filter(fn (SinglePage $singlePage): bool => str_starts_with($singlePage->slug, 'teambuiling-offer-'));
        }

        return SinglePageResource::collection($singlePages);
    }
}
