<?php

namespace App\Http\Controllers\Front\API\Facets;

use App\Domain\Content\Models\Craft;
use App\Enums\Categorization\FacetType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Front\Search\SearchQueryFormRequest;
use App\Infrastructure\Search\Query\CategorizationService;
use App\Models\Ville;
use Illuminate\Http\JsonResponse;

class FacetsController extends Controller
{
    public function index(
        SearchQueryFormRequest $request,
        FacetType $facetType,
        CategorizationService $categorizationService
    ): JsonResponse {
        $validated = $request->validated();

        $type = $request->get('type');
        $slug = $request->get('slug');

        $city = $type === 'ville'
            ? Ville::query()->where(Ville::$slugAttributeName, $slug)->firstOrFail()
            : null;

        $craft = $type === 'craft'
            ? Craft::query()->where(Craft::$slugAttributeName, $slug)->firstOrFail()
            : null;

        $data = $categorizationService->getHydratedFacet(
            $facetType,
            array_filter(['filters' => $validated['filters'] ?? null]),
            $city,
            $craft
        );

        return json_response()->data($data);
    }
}
