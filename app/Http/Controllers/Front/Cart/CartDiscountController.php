<?php

declare(strict_types=1);

namespace App\Http\Controllers\Front\Cart;

use App\Abstracts\Reduction;
use App\Domain\Box\Exceptions\BoxBadFunnelException;
use App\Domain\Box\Exceptions\BoxDiscountCodeNotFoundException;
use App\Domain\ECommerce\CartService;
use App\Domain\ECommerce\DiscountService;
use App\Domain\ECommerce\Exceptions\ApplicableOnFirstCommandException;
use App\Domain\ECommerce\Exceptions\Coupon\CouponException;
use App\Domain\Gift\Enums\GiftCardUsageOrigin;
use App\Domain\Gift\Exceptions\GiftCardAssociatedToStandByBooking;
use App\Domain\Gift\Exceptions\GiftCardHasAlreadyBeenUsed;
use App\Domain\Gift\Exceptions\GiftCardHasBeenCancelled;
use App\Domain\Gift\Exceptions\GiftCardHasBeenTransformed;
use App\Domain\Gift\Exceptions\GiftCardHasExpired;
use App\Domain\Gift\Exceptions\GiftCardHasNoRemainingTickets;
use App\Domain\Gift\Exceptions\GiftCardHasNoRemainingValue;
use App\Domain\Gift\Exceptions\GiftCardIsInactive;
use App\Domain\Gift\Exceptions\GiftCardIsInvalid;
use App\Domain\Gift\GiftCardService;
use App\Domain\Gift\Models\GiftCard;
use App\Exceptions\Business\BusinessClassNotFoundException;
use App\Http\Controllers\Controller;
use App\Infrastructure\Auth\AuthFinder;
use App\Models\Parrainage\CodeParrainageInvitation;
use App\Models\Parrainage\CodeParrainageUser;
use App\Models\Reductions\CadeauBox;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.CyclomaticComplexity)
 * @SuppressWarnings(PHPMD.NPathComplexity)
 * @SuppressWarnings(PHPMD.ElseExpression)
 */
class CartDiscountController extends Controller
{
    public function __construct(private readonly AuthFinder $authFinder)
    {
    }

    /**
     * @throws BoxBadFunnelException
     * @throws BoxDiscountCodeNotFoundException
     * @throws BusinessClassNotFoundException
     */
    public function applyDiscountCode(Request $request, GiftCardService $giftCardService): mixed
    {
        $user = $this->authFinder->tryCustomer();
        $code = str_replace(' ', '', $request->get('code_reduction') ?? '');
        $cart = CartService::getCart();

        if (empty($code)) {
            flashMessage('discount.failure_input_empty', 'danger');

            return redirect()->back();
        }

        $discount = DiscountService::getDiscountFromCode($code);
        if ($discount instanceof CadeauBox) {
            if ($discount->canBeClaimed()) {
                CartService::cleanCart($cart);

                $request->session()->forget('panier');
                throw new BoxBadFunnelException();
            }
            $discount = null;
        } elseif ($discount instanceof Reduction) {
            try {
                if ($discount instanceof CodeParrainageInvitation) {
                    CartService::checkWhetherSponsorshipInvitationCanBeApplied($user);
                }
                $discount->check($cart->getTotal('discountedAmount'));

                if ($discount instanceof CodeParrainageUser) {
                    CartService::applyUserWalletOnCart($cart, $discount);
                    session(['wallet' => $discount]);
                } else {
                    CartService::applyDiscountOnCart($cart, $discount);
                    session(['discount' => $discount]);
                }

                flashMessage('discount.application_success', 'success');
            } catch (AuthenticationException) {
                flashMessage('discount.failure_need_auth', 'danger');
            } catch (ApplicableOnFirstCommandException) {
                flashMessage('discount.applicable_on_first_command_only', 'danger');
            } catch (CouponException $e) {
                \Log::warning('Error when applying a discount', ['exception' => $e]);

                return $e->render($request);
            } catch (\Exception $e) {
                report($e);
                flashMessage('discount.application_failed', 'danger');

                return redirect()->back();
            }
        }

        $giftCard = GiftCard::where('code', $code)->first();
        if ($giftCard !== null) {
            try {
                $giftCardService->useGiftCard(
                    $giftCard,
                    GiftCardUsageOrigin::Cart,
                    $user,
                    cart: $cart,
                );
            } catch (GiftCardAssociatedToStandByBooking $e) {
                $flashMessage = __('flash-messages.coupon.failure_standby_booking_exists');
                $flashMessage .= '<a class="btn btn-sm btn-primary" href="'.route(localizedRouteName('evenements.index')).'">'.trans_choice('flash-messages.booking.my_bookings', 2).'</a>';

                \Session::flash('flash_message', $flashMessage);
                \Session::flash('flash_message_warning');
                \Session::flash('flash_message_important');

                return redirect()->back();
            } catch (GiftCardHasAlreadyBeenUsed|GiftCardHasNoRemainingValue $e) {
                flashMessage('coupon.failure_coupon_already_used', 'danger');

                return redirect()->back();
            } catch (GiftCardHasBeenCancelled $e) {
                flashMessage('coupon.failure_coupon_cancelled', 'danger');

                return redirect()->back();
            } catch (GiftCardHasBeenTransformed $e) {
                flashMessage('coupon.failure_coupon_transformed', 'danger');

                return redirect()->back();
            } catch (GiftCardHasExpired $e) {
                if ($giftCard->expired_at !== null) {
                    flashMessage(
                        'coupon.failure_coupon_expired_from',
                        'danger',
                        false,
                        ['date' => $giftCard->expired_at->isoFormat('L')],
                    );

                    return redirect()->back();
                }

                flashMessage(
                    'coupon.failure_coupon_expired',
                    'danger',
                );

                return redirect()->back();
            } catch (GiftCardHasNoRemainingTickets $e) {
                flashMessage('coupon.failure_no_slot_left', 'danger');

                return redirect()->back();
            } catch (GiftCardIsInvalid $e) {
                flashMessage('coupon.failure_invalid_coupon', 'danger');
            } catch (GiftCardIsInactive $e) {
                flashMessage('coupon.failure_coupon_not_activated', 'danger');

                return redirect()->back();
            }
        }

        if ($discount !== null || $giftCard !== null) {
            return redirect()->back();
        }

        flashMessage('coupon.failure_invalid_coupon', 'danger');

        return redirect()->back();
    }

    public function removeDiscount(): RedirectResponse
    {
        try {
            $cart = CartService::getCart();
            CartService::removeDiscount($cart);
        } catch (ModelNotFoundException) {
            flashMessage('cart.not_found', 'warning');

            return redirect()->back();
        }

        session(['discount' => null]);

        return redirect()->back();
    }

    public function removeCoupon(string $code): RedirectResponse
    {
        CartService::removeCoupon(CartService::getCart(), $code);

        return redirect()->back();
    }

    public function removeWallet(): RedirectResponse
    {
        try {
            $cart = CartService::getCart();
            CartService::removeWallet($cart);
        } catch (ModelNotFoundException) {
            flashMessage('cart.not_found', 'warning');

            return redirect()->back();
        }

        session(['wallet' => null]);

        return redirect()->back();
    }
}
