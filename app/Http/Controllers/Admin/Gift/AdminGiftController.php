<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\Gift;

use App\Domain\Gift\GiftPdfResolver;
use App\Domain\Gift\Models\Gift;
use App\Http\Controllers\AdminController;
use App\Infrastructure\Pdf\Services\PdfGenerator;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * @SuppressWarnings(PHPMD)
 */
class AdminGiftController extends AdminController
{
    public function __construct(
        private readonly GiftPdfResolver $pdfResolver,
        private readonly PdfGenerator $pdfGenerator,
    ) {
    }

    public function physical(Request $request, Gift $gift): View|Response
    {
        $pdf = $this->pdfResolver->getPhysicalGiftcardPdf($gift);

        if ($request->boolean('html')) {
            return $pdf->getView();
        }

        return $this->pdfGenerator->stream($pdf, force: $request->boolean('force'));
    }

    public function digital(Request $request, Gift $gift): View|Response
    {
        $pdf = $this->pdfResolver->getDigitalGiftcardPdf($gift);

        if ($request->boolean('html')) {
            return $pdf->getView();
        }

        return $this->pdfGenerator->stream($pdf, force: $request->boolean('force'));
    }
}
