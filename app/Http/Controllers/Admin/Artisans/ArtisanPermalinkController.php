<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\Artisans;

use App\Domain\Content\Services\PermalinkChanger;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Artisans\UpdateArtisanPermalinkRequest;
use App\Models\Artisan;
use App\Models\HttpRedirect;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ArtisanPermalinkController extends Controller
{
    public function edit(Request $request, Artisan $artisan): View
    {
        return view('admin.artisans.permalinks.edit', [
            'artisanId' => $artisan->getKey(),
            'artisanTitle' => $artisan->getFullName(),
            'artisanPermalink' => $artisan->permalien,
            'artisanRecentlyOnline' => $artisan->online_at !== null && $artisan->online_at->addDays(15)->isFuture(),
            'urlRoot' => $request->root().'/artisan/',
            'httpRedirects' => $artisan->httpRedirects()->latest()->get()->map(fn (HttpRedirect $redirect) => [
                'id' => $redirect->getKey(),
                'source_uri' => $redirect->source_uri,
                'target_uri' => $redirect->target_uri,
                'comment' => $redirect->comment,
            ])->toArray(),
        ]);
    }

    public function update(
        UpdateArtisanPermalinkRequest $request,
        Artisan $artisan,
        PermalinkChanger $permalinkChanger,
    ): RedirectResponse {
        \DB::transaction(
            static fn () => $permalinkChanger->forArtisan(
                $artisan,
                $request->string('permalink')->toString(),
                $request->boolean('addHttpRedirect'),
                $request->get('comment')
            )
        );

        \Session::flash('flash_message', 'The permalink has been updated');
        \Session::flash('flash_message_success');

        return redirect()->back();
    }
}
