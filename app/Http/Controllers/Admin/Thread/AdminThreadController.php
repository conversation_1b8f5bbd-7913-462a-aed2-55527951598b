<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\Thread;

use App\Domain\Thread\Models\ThreadMessageAttachment;
use App\Domain\Thread\Services\ThreadAttachment;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\StreamedResponse;

class AdminThreadController extends Controller
{
    public function downloadAttachment(ThreadMessageAttachment $attachment, ThreadAttachment $service): StreamedResponse
    {
        return $service->downloadAttachment($attachment);
    }
}
