<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\PrivateBooking;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestLocation;
use App\Domain\PrivateBooking\Enums\PrivateBookingRequestStatusLabel;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Models\Quote;
use App\Domain\PrivateBooking\Models\QuoteLine;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPaymentManager;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPriceResolver;
use App\Domain\Thread\Models\Thread;
use App\Domain\Thread\Repositories\ThreadMessageRepository;
use App\Domain\Thread\Repositories\ThreadRepository;
use App\Domain\Thread\Transformers\ThreadMessageTransformer;
use App\Http\Controllers\AdminController;
use App\Models\Evenement;
use App\Services\DateFormatter;
use App\Shared\Amount;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

/**
 * @SuppressWarnings(PHPMD)
 */
class AdminPrivateBookingShowController extends AdminController
{
    public function __construct(
        private readonly DateFormatter $dateFormatter,
        private readonly ThreadRepository $threadRepository,
        private readonly ThreadMessageRepository $threadMessageRepository,
        private readonly ThreadMessageTransformer $threadMessageTransformer,
        private readonly PrivateBookingRequestPriceResolver $priceResolver,
        private readonly PrivateBookingRequestPaymentManager $paymentManager,
    ) {
    }

    public function __invoke(
        PrivateBookingRequest $privateRequest,
        PrivateBookingRequestPaymentManager $paymentManager,
    ): View {
        $privateRequest->load([
            'quotes.quoteLines',
            'proformaInvoices',
            'activities.causer',
            'activities.impersonator',
            'event' => [
                'reservations' => [
                    'commande',
                ],
            ],
            'awaitingEvents' => [
                'reservations' => [
                    'commande',
                ],
            ],
        ]);

        $extraFees = $this->getQuoteLines($privateRequest);
        $validatedQuote = $paymentManager->getLastAcceptedQuote($privateRequest);
        $quotes = $privateRequest->quotes->sortByDesc('created_at')->map(fn (Quote $quote) => [
            'id' => $quote->id,
            'created_at' => $quote->created_at,
            'is_validated' => $validatedQuote?->id === $quote->id,
        ]);

        $thread = $privateRequest->thread;
        $artisanParticipant = null;
        $threadMessages = [];

        if ($thread instanceof Thread) {
            $thread->load([
                'participants.participant',
                'messages.sender.participant',
                'messages.attachments',
            ]);

            $artisanParticipant = $this->threadRepository->getParticipant($privateRequest->workshop->artisan, $thread);
            $messagesBuilder = $this->threadMessageRepository->getMessagesBuilder($thread, null);
            $threadMessages = $this->threadMessageTransformer->transform($messagesBuilder, $thread->participants, null)->toArray();
        }

        $expirationDate = \in_array(
            $privateRequest->getStatusLabel(),
            [
                PrivateBookingRequestStatusLabel::Awaiting,
                PrivateBookingRequestStatusLabel::QuoteNeedConfirmation],
            true,
        )
            ? $this->dateFormatter->dateAndTimeLongFormat($privateRequest->expires_at)
            : null;

        $currentPrice = $this->priceResolver->getCurrentPrice($privateRequest);

        return view('admin.private-booking.show', [
            'id' => $privateRequest->getKey(),
            'status' => $privateRequest->getStatusLabel(),
            'isCompany' => $privateRequest->is_company,
            'location' => $privateRequest->location,
            'participants' => $privateRequest->present()->participantsNumber(),
            'canBeCancelled' => $privateRequest->canBeCancelled(),
            'quotes' => $quotes,
            'lastQuoteId' => $privateRequest->getLastQuote()?->getKey(),
            'hasProformaInvoice' => $this->paymentManager->hasProformaInvoice($privateRequest),
            'extraFees' => $extraFees,
            'totalAmountInclVat' => $currentPrice,
            'totalAmountExclVat' => $currentPrice->excludePercent($privateRequest->workshop->getVat()),
            'discount' => $privateRequest->getLastQuote()?->discount_percent->floatValue(),
            'timeslots' => $this->getSlotsBasedOnStatus($privateRequest),
            'timeslotsHeader' => $this->getSlotHeaderMessage($privateRequest),
            'messages' => $threadMessages,
            'artisanParticipant' => $artisanParticipant,
            'activities' => $privateRequest->activities->reverse(),
            'expirationDate' => $expirationDate,
        ]);
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    private function getSlotsBasedOnStatus(PrivateBookingRequest $privateRequest): array
    {
        return match ($privateRequest->getStatusLabel()) {
            PrivateBookingRequestStatusLabel::Awaiting,
            PrivateBookingRequestStatusLabel::Refused,
            PrivateBookingRequestStatusLabel::Expired => $this->getCustomerTimeslots($privateRequest),
            PrivateBookingRequestStatusLabel::QuoteNeedConfirmation,
            PrivateBookingRequestStatusLabel::AwaitingPayment,
            PrivateBookingRequestStatusLabel::BookingConfirmed,
            PrivateBookingRequestStatusLabel::QuoteExpired => $this->getArtisanProposedSlots($privateRequest),
            PrivateBookingRequestStatusLabel::Cancelled,
            PrivateBookingRequestStatusLabel::NoPayment => $this->getValidatedSlots($privateRequest),
            default => [],
        };
    }

    private function getSlotHeaderMessage(PrivateBookingRequest $privateRequest): string
    {
        $today = Carbon::today();
        $paymentDeadline = $privateRequest->expires_at;
        $header = '';

        switch ($privateRequest->getStatusLabel()) {
            case PrivateBookingRequestStatusLabel::Awaiting:
            case PrivateBookingRequestStatusLabel::Expired:
            case PrivateBookingRequestStatusLabel::Refused:
                $header = 'Timeslots proposed by customer:';
                break;

            case PrivateBookingRequestStatusLabel::QuoteExpired:
            case PrivateBookingRequestStatusLabel::QuoteNeedConfirmation:
                $header = 'Timeslots proposed by artisan:';
                break;

            case PrivateBookingRequestStatusLabel::AwaitingPayment:
                $daysLeft = $today->diffInDays(Carbon::parse($paymentDeadline), false);
                $header = $daysLeft > 10
                    ? "📅 Payment must be received in $daysLeft days maximum"
                    : "⚠️ Payment must be received in $daysLeft days maximum";
                break;

            case PrivateBookingRequestStatusLabel::BookingConfirmed:
                $eventDate = Carbon::parse($privateRequest->event?->start ?? null);
                $daysUntil = $today->diffInDays($eventDate, false);
                if ($daysUntil > 10) {
                    $header = "📅 The event takes place in $daysUntil days";
                } elseif ($daysUntil > 0) {
                    $header = "⚠️ The event takes place in $daysUntil days";
                } elseif ($daysUntil === 0) {
                    $header = '⚠️ The event takes place today';
                } else {
                    $header = 'ℹ️ The event is over';
                }
                break;

            case PrivateBookingRequestStatusLabel::NoPayment:
            case PrivateBookingRequestStatusLabel::Cancelled:
                $header = '📅 The event has been cancelled';
                break;

            default:
                $header = 'No information available.';
                break;
        }

        return $header;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    private function getCustomerTimeslots(PrivateBookingRequest $privateRequest): array
    {
        return array_map(function ($slots) {
            $start = $slots->start;
            $end = $slots->end;

            return [
                'date' => $this->dateFormatter->dateAndTimeLongFormatStartAndEnd($start, $end),
                'eventId' => null,
            ];
        }, $privateRequest->proposed_slots);
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    private function getArtisanProposedSlots(PrivateBookingRequest $privateRequest): array
    {
        return $privateRequest
            ->awaitingEvents
            ->map(function (Evenement $slot) {
                return [
                    'date' => $this->dateFormatter->dateAndTimeLongFormatStartAndEnd($slot->start, $slot->end),
                    'eventId' => $slot->id,
                ];
            })
            ->toArray();
    }

    /**
     * @return array<int, array<string, int|string|null>>
     */
    private function getValidatedSlots(PrivateBookingRequest $privateRequest)
    {
        if ($privateRequest->event === null) {
            return [];
        }

        return [
            [
                'date' => $this->dateFormatter->dateAndTimeLongFormatStartAndEnd($privateRequest->event->start, $privateRequest->event->end),
                'eventId' => $privateRequest->event_id,
            ],
        ];
    }

    /**
     * @return array<int, array<string, string>>
     */
    private function getQuoteLines(PrivateBookingRequest $privateBookingRequest): array
    {
        // We want to display estimated travel expenses even if the quote is not edited yet
        if (
            $privateBookingRequest->getLastQuote() === null
            && $privateBookingRequest->location === PrivateBookingRequestLocation::Customer
            && $privateBookingRequest->isCompany()
        ) {
            $estimatedAmount = Amount::make(
                config(
                    'private-booking.private_client_managed_location.travel_expenses',
                    0,
                    $privateBookingRequest->country_code,
                ),
                $privateBookingRequest->getCurrency(),
            );

            return [[
                'label' => 'Estimated travel expenses',
                'inclVat' => currencyFormat($estimatedAmount),
                'exclVat' => currencyFormat($estimatedAmount),
            ]];
        }

        $quoteLines = new Collection();
        $quoteLines = $quoteLines->merge($privateBookingRequest
            ->getLastQuote()
            ?->getExtraFeeLines() ?? []);
        $quoteLines = $quoteLines->merge($privateBookingRequest
            ->getLastQuote()
            ?->getTravelExpenseLines() ?? []);

        return $quoteLines
            ->map(fn (QuoteLine $quoteLine) => [
                'label' => $quoteLine->label,
                'inclVat' => currencyFormat($quoteLine->getAmountInclVat()),
                'exclVat' => currencyFormat($quoteLine->amount_excl_vat),
            ])
            ->toArray();
    }
}
