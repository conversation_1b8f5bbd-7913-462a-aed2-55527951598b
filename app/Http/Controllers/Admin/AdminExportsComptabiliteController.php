<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Domain\Accounting\Exports\Enums\ExportType;
use App\Enums\Currency;
use App\Helpers\AccountingHelper;
use App\Http\Controllers\AdminController;
use App\Http\Requests\Admin\Invoices\AccountabilityDownloadExportRequest;
use App\Shared\YearMonth;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class AdminExportsComptabiliteController extends AdminController
{
    public function index(): View
    {
        $storage = Storage::disk('exports');

        $currentMonthStart = Carbon::now()->startOfMonth();
        $previousMonthStart = Carbon::now()->subMonth()->startOfMonth();
        $previousMonthEnd = Carbon::now()->subMonth()->endOfMonth();

        return view('admin.exports.index-compta', [
            'currentMonth' => $currentMonthStart,
            'previousMonth' => $previousMonthStart,
            'previousEnd' => $previousMonthEnd,
            'previousExports' => Collection::make(ExportType::cases())
                ->map(function (ExportType $exportType) use ($storage) {
                    $filePath = $exportType->filename(YearMonth::current()->previous());
                    $exists = $storage->fileExists($filePath);

                    return [
                        'name' => $exportType->value,
                        'path' => $filePath,
                        'exists' => $exists,
                        'last_modification_date' => $exists ? $storage->lastModified($filePath) : null,
                    ];
                }),
        ]);
    }

    public function downloadExportVerifPrepaiement(string $currency, int $month, int $year): Response
    {
        return AccountingHelper::downloadStorageFileOrRedirectBack(AccountingHelper::getArtisanPaymentsExportName($month, $year, Currency::from($currency)));
    }

    public function downloadExportCheckErrors(int $month, int $year): Response
    {
        return AccountingHelper::downloadStorageFileOrRedirectBack(AccountingHelper::getInvoiceGenerationExportName($month, $year));
    }

    public function generateExportVerifPrepaiement(int $month, int $year): Response
    {
        Artisan::queue('accounting:artisan-payment-checks', ['month' => $month, 'year' => $year])->allOnQueue('exports');

        Session::flash('flash_message', "Le fichier demandé sera généré d'ici quelques minutes, merci d'attendre.");
        Session::flash('flash_message_success');

        return redirect()->back();
    }

    public function generateExportCheckErrors(int $month, int $year): Response
    {
        Artisan::queue('accounting:invoice-generation-checks', ['month' => $month, 'year' => $year])->allOnQueue('exports');

        Session::flash('flash_message', "Le fichier demandé sera généré d'ici quelques minutes, merci d'attendre.");
        Session::flash('flash_message_success');

        return redirect()->back();
    }

    public function downloadCurrentAccountabilityExport(AccountabilityDownloadExportRequest $request): Response
    {
        return AccountingHelper::downloadStorageFileOrRedirectBack(
            ExportType::from($request->string('export_type')->toString())->filename(YearMonth::current()->previous()),
        );
    }
}
