<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\GiftCards;

use App\Domain\Company\CompanyService;
use App\Domain\Gift\DataTables\GiftCardLotsDataTable;
use App\Domain\Gift\Exports\GiftCardsLotExport;
use App\Domain\Gift\GiftCardService;
use App\Domain\Gift\Models\GiftCardLot;
use App\Domain\Gift\Repositories\GiftCardLotRepository;
use App\Http\Controllers\AdminController;
use App\Http\Requests\Admin\GiftCards\CreateGiftCardLotRequest;
use App\Infrastructure\Auth\AuthFinder;
use App\Services\DateFormatter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Exceptions\NoFilenameGivenException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class AdminGiftCardLotController extends AdminController
{
    public function __construct(private readonly AuthFinder $authFinder)
    {
    }

    public function index(GiftCardLotsDataTable $dataTable): mixed
    {
        $computedData = [];
        $computedData['companies'] = CompanyService::allCompanies();

        return $dataTable->render('admin.gift-card.lot.manage', ['computedData' => $computedData]);
    }

    public function create(
        CreateGiftCardLotRequest $request,
        GiftCardService $giftCardService,
        DateFormatter $dateFormatter,
    ): JsonResponse {
        $admin = $this->authFinder->admin();

        DB::transaction(static fn () => $giftCardService->generateGiftCardLot($request->validated(), $dateFormatter->getCurrentTimezone(), $admin));

        return response()->json([
            'message' => 'La génération est en cours, vous retrouverez prochainement le fichier dans le tableau',
        ]);
    }

    public function delete(Request $request, GiftCardLotRepository $giftCardLotRepository): JsonResponse
    {
        $giftCardLotRepository->deleteById($request->integer('lot_id'));

        return response()->json([
            'message' => 'Le lot a été supprimé avec succés',
        ]);
    }

    /**
     * @throws NoFilenameGivenException
     */
    public function downloadExport(GiftCardLot $lot): Response|BinaryFileResponse
    {
        return (new GiftCardsLotExport($lot->id))->download("Export-gift-cards-lot-{$lot->id}.xlsx");
    }
}
