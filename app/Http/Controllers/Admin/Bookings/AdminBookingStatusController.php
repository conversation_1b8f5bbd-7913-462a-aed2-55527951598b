<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\Bookings;

use App\Domain\Booking\Services\BookingService;
use App\Enums\BookingCancelledOrigin;
use App\Exceptions\Services\BaseException;
use App\Exceptions\UnknownException;
use App\Http\Controllers\AdminController;
use App\Http\Requests\Admin\Bookings\ChangeBookingStatusFormRequest;
use App\Models\Reservation;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminBookingStatusController extends AdminController
{
    /**
     * @throws UnknownException
     */
    public function update(ChangeBookingStatusFormRequest $request, Reservation $booking): RedirectResponse
    {
        $validated = $request->validated();

        try {
            DB::transaction(fn () => BookingService::updateStatus($booking->id, $validated['status'], $validated['change'], BookingCancelledOrigin::Admin));
        } catch (BaseException $baseException) {
            return redirect()->back()
                ->withErrors(['global' => $baseException->getMessage()], $request->getErrorBag())
                ->with('modalId', $validated['modal_id'] ?? null)
                ->with('flash_message', $baseException->getMessage())
                ->with('flash_message_danger', 'flash_message_danger');
        }

        return redirect()->back()
            ->with('flash_message', 'Le changement de statut a été effectué.')
            ->with('flash_message_success', 'flash_message_success');
    }

    /**
     * @throws UnknownException
     */
    public function updateStatusFromBilledToStandby(Request $request, Reservation $booking): RedirectResponse
    {
        try {
            DB::transaction(fn () => BookingService::updateStatusFromBilledToStandby($booking->id));
        } catch (BaseException $baseException) {
            return redirect()->back()
                ->withErrors(['global' => $baseException->getMessage()])
                ->with('modalId', $request->get('modal_id'))
                ->with('flash_message', $baseException->getMessage())
                ->with('flash_message_danger', 'flash_message_danger');
        }

        return redirect()->back()
            ->with('flash_message', 'Le changement de statut a été effectué.')
            ->with('flash_message_success', 'flash_message_success');
    }
}
