<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Domain\Content\CollectionService;
use App\Domain\Content\DataTables\CollectionDataTable;
use App\Domain\Content\Models\Collection;
use App\Domain\Content\Repositories\CollectionRepository;
use App\Exceptions\UnknownException;
use App\Http\Controllers\AdminController;
use App\Http\Requests\Admin\Collections\StoreCollectionFormRequest;
use App\Http\Requests\Admin\Collections\UpdateCollectionFormRequest;
use App\Infrastructure\Search\Query\SearchService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;

class AdminCollectionController extends AdminController
{
    public function __construct(
        protected readonly CollectionService $collectionService,
        protected readonly CollectionRepository $collectionRepository,
        protected readonly SearchService $searchService,
    ) {
    }

    public function index(CollectionDataTable $collectionDataTable): View|JsonResponse
    {
        return $collectionDataTable->render('admin.collection.index');
    }

    public function create(): View
    {
        $availableFilters = $this->searchService->getAvailableFilters();

        return view('admin.collection.create', ['availableFilters' => $availableFilters]);
    }

    public function store(StoreCollectionFormRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        $validated['filters'] = array_merge_recursive([
            'facet' => ['and' => [], 'or' => []],
            'numeric' => ['and' => [], 'or' => []],
        ], $validated['filters']);

        /**
         * @var array<string, mixed> $validated
         */
        $validated = [
            ...$validated['content'],
            ...$validated,
        ];

        try {
            $this->collectionService->store($validated);
        } catch (\Exception $e) {
            throw new UnknownException();
        }

        \Session::flash('flash_message', 'La collection a bien été créée');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.collections.index');
    }

    public function edit(Collection $collection): View
    {
        $availableFilters = $this->searchService->getAvailableFilters();
        $oldContent = old('content', []);
        $oldContentToAppend = [];
        if (!empty($oldContent)) {
            foreach ($oldContent as $locale => $langVal) {
                foreach ($langVal as $props => $propertyVal) {
                    if (!isset($oldContentToAppend[$props])) {
                        $oldContentToAppend[$props] = [];
                    }

                    $oldContentToAppend[$props][$locale] = $propertyVal;
                }
            }
        }

        $localizedAttributes = [
            'name' => [
                ...$collection->getFormLocalizedAttribute('name'),
                ...$oldContentToAppend['name'] ?? [],
            ],
        ];

        return view('admin.collection.edit', [
            'collection' => $collection,
            'localizedAttributes' => $localizedAttributes,
            'availableFilters' => $availableFilters,
        ]);
    }

    public function update(UpdateCollectionFormRequest $request, Collection $collection): RedirectResponse
    {
        $validated = $request->validated();
        $validated['filters'] = array_merge_recursive([
            'facet' => ['and' => [], 'or' => []],
            'numeric' => ['and' => [], 'or' => []],
        ], $validated['filters']);
        /**
         * @var array<string, mixed> $validated
         */
        $validated = [
            ...$validated['content'],
            ...$validated,
        ];

        try {
            $this->collectionService->update($collection, $validated);
        } catch (\Exception $e) {
            throw new UnknownException();
        }

        \Session::flash('flash_message', 'La collection a bien été mise à jour');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.collections.index');
    }

    public function activate(Collection $collection): RedirectResponse
    {
        $this->collectionService->activate($collection);

        \Session::flash('flash_message', 'The collection has been activated');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.collections.index');
    }
}
