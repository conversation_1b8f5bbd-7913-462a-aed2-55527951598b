<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Domain\Content\DataTables\SummarySheetsDataTable;
use App\Domain\Content\Models\SummarySheet;
use App\Domain\Content\Models\SummarySheetTemplate;
use App\Enums\Locale;
use App\Http\Controllers\AdminController;
use App\Infrastructure\Services\SummarySheetService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class AdminSummarySheetController extends AdminController
{
    public function index(SummarySheetsDataTable $dataTable): View|JsonResponse
    {
        return $dataTable->render('admin.summary-sheet.index');
    }

    public function templates(): View
    {
        return view(
            'admin.summary-sheet.templates',
            [
                'templates' => SummarySheetTemplate::orderBy('name')->lazy(),
            ]
        );
    }

    public function createTemplate(Request $request, SummarySheetService $sheetService): Response
    {
        $validated = $request->validate([
            'name' => 'required',
            'locale' => [
                'required',
                Rule::enum(Locale::class),
            ],
        ]);

        if (SummarySheetTemplate::where('locale', $validated['locale'])->first() instanceof SummarySheetTemplate) {
            \Session::flash('flash_message', 'Une template existe déjà dans cette langue.');
            \Session::flash('flash_message_danger');

            return redirect()->back();
        }

        $sheetService->createTemplate($validated['name'], Locale::from($validated['locale']));

        \Session::flash('flash_message', 'La template a été créée avec succès.');
        \Session::flash('flash_message_success');

        return redirect()->back();
    }

    public function publish(SummarySheet $summarySheet, SummarySheetService $sheetService): RedirectResponse
    {
        $sheetService->publish($summarySheet);

        \Session::flash('flash_message', 'Une nouvelle version a été publiée.');
        \Session::flash('flash_message_success');

        return redirect()->back();
    }

    public function download(SummarySheet $summarySheet, SummarySheetService $sheetService): Response
    {
        return $sheetService->download($summarySheet);
    }

    public function delete(SummarySheet $summarySheet, SummarySheetService $sheetService): Response
    {
        if ($sheetService->delete($summarySheet)) {
            \Session::flash('flash_message', 'La fiche récap a bien été supprimée.');
            \Session::flash('flash_message_success');

            return redirect()->back();
        }

        \Session::flash('flash_message', 'Une erreur est survenue lors de la suppression de la fiche récap.');
        \Session::flash('flash_message_danger');

        return redirect()->back();
    }
}
