<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Domain\Content\DataTables\TechniqueDataTable;
use App\Domain\Content\Models\Technique;
use App\Domain\Content\TechniqueService;
use App\Exceptions\UnknownException;
use App\Http\Controllers\AdminController;
use App\Http\Requests\Admin\Techniques\StoreTechniqueRequestForm;
use App\Http\Requests\Admin\Techniques\UpdateTechniqueRequestForm;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class AdminTechniqueController extends AdminController
{
    public function __construct(
        protected readonly TechniqueService $techniqueService
    ) {
    }

    public function index(TechniqueDataTable $dataTable): View|JsonResponse
    {
        return $dataTable->render('admin.technique.index');
    }

    public function store(StoreTechniqueRequestForm $request): RedirectResponse
    {
        $validated = $request->validated();

        try {
            $this->techniqueService->store($validated);
        } catch (\Exception $e) {
            throw new UnknownException();
        }

        \Session::flash('flash_message', 'The technique has been saved');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.techniques.index');
    }

    public function edit(Technique $technique): View
    {
        return view('admin.technique.edit', ['technique' => $technique]);
    }

    public function update(UpdateTechniqueRequestForm $request, Technique $technique): RedirectResponse
    {
        $validated = $request->validated();

        try {
            $this->techniqueService->update($technique, $validated);
        } catch (\Exception $e) {
            throw new UnknownException();
        }

        \Session::flash('flash_message', 'La technique a bien été enregistré');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.techniques.index');
    }

    public function activate(Technique $technique): RedirectResponse
    {
        $this->techniqueService->activate($technique);

        \Session::flash('flash_message', 'The technique has been activated');
        \Session::flash('flash_message_success');

        return redirect()->route('admin.techniques.index');
    }
}
