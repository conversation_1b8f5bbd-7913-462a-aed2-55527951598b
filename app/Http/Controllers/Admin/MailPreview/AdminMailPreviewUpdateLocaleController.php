<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin\MailPreview;

use App\Http\Controllers\AdminController;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class AdminMailPreviewUpdateLocaleController extends AdminController
{
    public function __invoke(Request $request): RedirectResponse
    {
        $request->validate([
            'locale' => Rule::in(config('app.all_locales')),
        ]);

        // To update the local of the email testing, we simply need to change
        // the value stored in the session, it will automatically be
        // retrieved when the mail is generated
        session()->put('email.locale', $request->get('locale'));

        return redirect()->back();
    }
}
