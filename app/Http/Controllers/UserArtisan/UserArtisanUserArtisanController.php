<?php

declare(strict_types=1);

namespace App\Http\Controllers\UserArtisan;

use App\Http\Controllers\Controller;
use App\Infrastructure\Auth\AuthFinder;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class UserArtisanUserArtisanController extends Controller
{
    public function __construct(private readonly AuthFinder $authFinder)
    {
    }

    public function profile(): View
    {
        return view('userartisan.profile');
    }

    public function profilUpdate(Request $request): RedirectResponse
    {
        $userArtisan = $this->authFinder->artisan();

        $validatedData = $request->validate([
            'nom' => 'required|max:255',
            'prenom' => 'required|max:255',
            'password' => 'nullable|min:6',
            'old_password' => [
                'nullable',
                'current_password:artisan',
                'required_with:password',
            ],
            'password_confirmation' => [
                'same:password',
                'required_with:password',
            ],
        ]);

        $userArtisan->prenom = $validatedData['prenom'];
        $userArtisan->nom = $validatedData['nom'];

        if (!empty($validatedData['password'])) {
            $userArtisan->password = \Hash::make($validatedData['password']);
        }

        try {
            $userArtisan->save();
        } catch (\Throwable $exception) {
            \Log::error('An error occurred while trying to update UserArtisan profile', [
                'userartisan_id' => $userArtisan->id,
                'exception' => $exception,
            ]);

            \Session::flash('flash_message', __('exceptions.an_error_occured'));
            \Session::flash('flash_message_danger');
        }

        \Session::flash('flash_message', __('userartisan/controllers/utils.profile.updated'));
        \Session::flash('flash_message_success');

        return redirect()->back();
    }
}
