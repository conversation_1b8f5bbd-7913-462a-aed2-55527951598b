<?php

declare(strict_types=1);

namespace App\Http\Controllers\UserArtisan;

use App\Http\Controllers\Controller;
use App\Services\ArtisanSelected;
use Illuminate\View\View;

class UserArtisanSettingsController extends Controller
{
    public function index(ArtisanSelected $artisanSelected): View
    {
        return view('userartisan.settings.index', ['artisanSelected' => $artisanSelected->get()]);
    }
}
