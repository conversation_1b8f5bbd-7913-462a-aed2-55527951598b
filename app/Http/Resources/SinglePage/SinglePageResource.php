<?php

declare(strict_types=1);

namespace App\Http\Resources\SinglePage;

use App\Domain\Content\Models\SinglePage;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SinglePageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @SuppressWarnings("unused")
     */
    public function toArray(Request $request): array
    {
        /** @var SinglePage $singlePage */
        $singlePage = $this->resource;

        return [
            'id' => $singlePage->id,
            'slug' => $singlePage->getUrl(),
            'title' => $singlePage->title,
            'image' => '/'.$singlePage->hero_image,
            'mode' => $singlePage->mode->value,
        ];
    }
}
