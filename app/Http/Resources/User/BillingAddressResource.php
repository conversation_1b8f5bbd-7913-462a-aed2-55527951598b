<?php

declare(strict_types=1);

namespace App\Http\Resources\User;

use App\Models\Adresse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BillingAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @SuppressWarnings("unused")
     */
    public function toArray(Request $request): array
    {
        /** @var Adresse $address */
        $address = $this->resource;

        return [
            'id' => $address->id,
            'label' => $address->nom,
            'company' => $address->societe,
            'full_name' => $address->destinataire,
            'address' => $address->adresse1,
            'city' => $address->ville,
            'zip_code' => $address->code_postal,
            'country' => $address->pays,
            'default' => $address->default_facturation,
        ];
    }
}
