<?php

declare(strict_types=1);

namespace App\Http\Resources\Reviews;

use App\Enums\Country;
use App\Services\GoogleReviews\CompanyReviews;
use App\Services\GoogleReviews\Review;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class ReviewsGoogleResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function toArray(Request $request): array
    {
        /** @var CompanyReviews $reviews */
        $reviews = $this->resource;

        return [
            'averageRating' => $reviews->averageRating,
            'totalRating' => $reviews->totalRating,
            'country' => Country::France,
            'reviews' => Collection::make($reviews->reviews)->map(fn (Review $review) => [
                'author' => $review->author,
                'rating' => $review->rating,
                'comment' => $review->comment,
            ]),
        ];
    }
}
