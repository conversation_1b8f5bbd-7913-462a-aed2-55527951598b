<?php

declare(strict_types=1);

namespace App\Shared;

use App\Enums\Currency;

class AmountCurrencyMismatchException extends \RuntimeException
{
    public static function fromCurrencies(Currency ...$currencies): self
    {
        $currencies = implode(', ', array_unique(array_map(static fn (Currency $currency) => $currency->value, $currencies)));

        return new self(
            "Impossible to realise arithmetic operation between $currencies currencies"
        );
    }
}
