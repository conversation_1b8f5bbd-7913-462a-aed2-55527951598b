<?php

declare(strict_types=1);

namespace App\Shared\ECommerce;

use App\Enums\DiscountType;
use App\Shared\Amount;
use App\Shared\Percent;

/**
 * @SuppressWarnings(PHPMD.ExcessiveParameterList)
 */
class CartDiscount
{
    public function __construct(
        public readonly string $type,
        public readonly int $id,
        public readonly string $code,
        public readonly Amount $appliedAmount,
        public readonly DiscountType $originalType,
        public readonly int $originalValue,
        public readonly ?int $appliedTickets = null,
        public readonly ?Amount $originalTicketUnitValue = null,
        public readonly ?int $workshopId = null,
        public readonly ?Percent $commissionPercent = null,
    ) {
    }
}
