<?php

declare(strict_types=1);

namespace App\Mail\Guest;

use App\Mail\BaseMailable;
use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class FriendInvitation extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected Reservation $booking)
    {
        parent::__construct();
    }

    /**
     * Build the message.
     */
    public function build(): BaseMailable
    {
        if ($this->booking->getParticipant() === null) {
            throw new \RuntimeException("Unable to get the reservation's participants");
        }

        $referrer = $this->booking->getParticipantFullname();
        $workshop = $this->booking->evenement->atelier->nom;
        $mailTitle = __('emails/participant/friend-invitation.invitation.title', [
            'referrer' => $referrer,
            'workshop' => $workshop,
        ]);

        return $this->markdown('emails.participant.friend-invitation')
            ->with(['event' => $this->booking->evenement,
                'workshop' => $this->booking->evenement->atelier,
                'referrer' => $referrer, ])
            ->subject($this->appendAppName($mailTitle));
    }
}
