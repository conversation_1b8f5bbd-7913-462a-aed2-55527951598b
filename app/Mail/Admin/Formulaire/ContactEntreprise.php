<?php

declare(strict_types=1);

namespace App\Mail\Admin\Formulaire;

use App\Mail\BaseMailable;
use App\Models\Formulaires\ContactEntreprise as ContactEntrepriseModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class ContactEntreprise extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected ContactEntrepriseModel $contactEntreprise)
    {
        parent::__construct();
    }

    /**
     * Build the message.
     */
    public function build(): BaseMailable
    {
        return $this->markdown(
            'emails.admin.formulaire.contact-b2b',
            [
                'nom' => $this->contactEntreprise->nom,
                'email' => $this->contactEntreprise->email,
                'societe_nom' => $this->contactEntreprise->societe_nom,
                'telephone' => $this->contactEntreprise->telephone,
                'message' => $this->contactEntreprise->message,
                'date_envoi' => dateAndTimeLongFormat($this->contactEntreprise->created_at, $this->contactEntreprise->timezone),
            ]
        )->subject('Reçu - [Formulaire de contact Entreprise] - '.$this->contactEntreprise->societe_nom);
    }
}
