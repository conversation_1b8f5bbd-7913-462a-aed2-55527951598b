<?php

declare(strict_types=1);

namespace App\Mail\User\Client\Gift;

use App\Domain\Gift\GiftPdfResolver;
use App\Domain\Gift\Models\GiftCard;
use App\Enums\Country;
use App\Enums\Locale;
use App\Mail\BaseMailable;
use App\Shared\Interfaces\Localized;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

/**
 * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
 */
class GiftGiftCardRecipientMail extends BaseMailable implements ShouldQueue, Localized
{
    use Queueable;
    use SerializesModels;

    public function __construct(
        public GiftCard $giftCard,
        public bool $withAttachment = true,
    ) {
        parent::__construct();
    }

    public function getCountry(): Country
    {
        return getCountry($this->giftCard->getCountry());
    }

    public function getLocale(): Locale
    {
        return getLocale($this->giftCard->getLocale());
    }

    public function build(GiftPdfResolver $pdfResolver): BaseMailable
    {
        $from = $this->giftCard->gift?->from ?? '';

        $subject = !empty($from)
            ? __('emails/guest/gift.gift_card.recipient.title_with_from', ['from' => $from])
            : __('emails/guest/gift.gift_card.recipient.title_without_from');

        $email = $this
            ->markdown('emails.guest.gifts.gift-card-recipient')
            ->with('from', $from)
            ->with('digitalLink', $this->giftCard->gift !== null ? frontUrl(route(localizedRouteName('dgc', $this->locale), [
                'id' => $this->giftCard->gift->uuid,
                'utm_medium' => 'email',
                'utm_source' => 'site',
                'utm_campaign' => 'giftCardBeneficiary',
            ], false), $this->country) : null)
            ->with('giftCardExpiresAt', $this->giftCard->expired_at)
            ->with('giftCardCode', $this->giftCard->code)
            ->with('giftCardType', $this->giftCard->type)
            ->subject($subject);

        if ($this->withAttachment) {
            $this->attachPdf($pdfResolver->getDigitalGiftcardPdf($this->giftCard));
        }

        return $email;
    }
}
