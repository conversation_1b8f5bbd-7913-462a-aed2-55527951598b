<?php

declare(strict_types=1);

namespace App\Mail\User\Client;

use App\Domain\Gift\Models\GiftCard;
use App\Mail\BaseMailable;
use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ReminderBeforeStockTransfer extends BaseMailable
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private readonly int $id,
        private readonly string $className,
    ) {
        parent::__construct();
    }

    /**
     * Build the message.
     */
    public function build(): Mailable
    {
        $content = [];
        if ($this->className === GiftCard::class) {
            $giftCard = GiftCard::with('workshop')->findOrFail($this->id);
            $content = [
                'giftCardId' => $this->id,
                'workshop' => $giftCard?->workshop->nom ?? '',
                'giftCardTicketCount' => $giftCard->ticket,
                'isGiftCard' => true,
                'url' => frontUrl(
                    route(localizedRouteName('utiliser-boncadeau.index', $this->locale), ['code_reduction' => $giftCard->code], absolute: false)
                ),
            ];
        }
        if ($this->className === Reservation::class) {
            $booking = Reservation::with('evenement.atelier')->findOrFail($this->id);
            $content = [
                'giftCardId' => $this->id,
                'workshop' => $booking->evenement->atelier->nom,
                'giftCardTicketCount' => $booking->nb_places,
                'isGiftCard' => false,
                'url' => frontUrl(
                    route(localizedRouteName('evenements.index', $this->locale), absolute: false),
                    $this->country
                ),
            ];
        }

        $mailTitle = __('emails/participant/reminder-before-stock-transfer.title');

        return $this
            ->markdown('emails.participant.reminder-before-stock-transfer')
            ->with($content)
            ->subject($mailTitle);
    }
}
