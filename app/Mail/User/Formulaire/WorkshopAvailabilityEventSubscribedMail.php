<?php

declare(strict_types=1);

namespace App\Mail\User\Formulaire;

use App\Mail\BaseMailable;
use App\Models\WorkshopAvailabilitySubscriber;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class WorkshopAvailabilityEventSubscribedMail extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected WorkshopAvailabilitySubscriber $subscriber)
    {
        parent::__construct();
    }

    /**
     * Build the message.
     */
    public function build(): Mailable
    {
        return $this->markdown('emails.guest.forms-confirmation.workshop-availability-subscribed', [
            'atelier' => $this->subscriber->workshop->getProductName(),
            'url' => $this->subscriber->workshop->getUrl(false, '', $this->locale, $this->country),
            'date_envoi' => dateAndTimeLongFormat(
                $this->subscriber->created_at,
                $this->subscriber->timezone
            ),
            'artisanName' => $this->subscriber->workshop->artisan->getDenomination(),
            'artisanUrl' => $this->subscriber->workshop->artisan->getUrl(),
        ])
            ->subject(__('emails/guest/forms-confirmation.notify_next_events.title', [
                'workshop' => $this->subscriber->workshop->getProductName(),
            ]));
    }
}
