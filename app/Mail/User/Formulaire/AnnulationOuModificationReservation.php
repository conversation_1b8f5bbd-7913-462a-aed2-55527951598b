<?php

declare(strict_types=1);

namespace App\Mail\User\Formulaire;

use App\Mail\BaseMailable;
use App\Models\Formulaires\AnnulationOuModificationReservation as AnnulationOuModificationReservationModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class AnnulationOuModificationReservation extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected AnnulationOuModificationReservationModel $canceledReservation)
    {
        parent::__construct();

        $this->onQueue('high');
    }

    /**
     * Build the message.
     */
    public function build(): BaseMailable
    {
        $user = $this->canceledReservation->user;
        $reservation = $this->canceledReservation->reservation;
        $reservationDetails = $reservation->nb_places.' '.trans_choice('utils.booking.slots', $reservation->nb_places);
        $atelier = $reservation->evenement->atelier->getProductName();

        return $this
            ->markdown('emails.participant.cancel-modify-booking-form-received', [
                'nom' => $user->nom,
                'prenom' => $user->prenom,
                'email' => $user->email,
                'telephone_formulaire' => $this->canceledReservation->telephone_formulaire,
                'message' => $this->canceledReservation->message,
                'atelier' => $atelier,
                'evenement' => $reservation->evenement->present()->openingHoursWithDate(),
                'reservation' => '#'.$reservation->getKey().' : '.$reservationDetails,
                'date_envoi' => dateAndTimeLongFormat($this->canceledReservation->created_at, $this->canceledReservation->timezone),
            ])
            ->subject(__('emails/participant/booking-management.cancel_modify_booking_form.title', ['bookingId' => $reservation->id]));
    }
}
