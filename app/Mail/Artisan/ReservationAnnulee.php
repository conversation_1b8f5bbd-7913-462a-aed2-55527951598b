<?php

declare(strict_types=1);

namespace App\Mail\Artisan;

use App\Domain\Booking\Enums\EventMaintainStatus;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Mail\BaseMailable;
use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/**
 * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
 */
class ReservationAnnulee extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function __construct(
        protected Reservation $booking,
        protected bool $bankTransferCancellation = false,
    ) {
        parent::__construct();
    }

    public function build(): Mailable
    {
        return $this->markdown('emails.artisan.booking-management.reservation-annulee')
            ->with('isPrivateBookingRequest', $this->booking->evenement->privateBookingRequest instanceof PrivateBookingRequest)
            ->with('is_bank_transfer_expiration', $this->bankTransferCancellation)
            ->with('customer', $this->booking->getParticipantFullname())
            ->with('name', $this->booking->evenement->atelier->artisan->getDenominationMail())
            ->with('workshop', $this->booking->evenement->atelier->nom)
            ->with('datetime', $this->booking->evenement->present()->openingHoursWithDate())
            ->with('places', mb_strtolower(displayPlaces(getPlacesPresenter(
                $this->booking->getPlaceNumber(),
                $this->booking->child_number,
                $this->booking->adult_number,
                $this->booking->evenement->atelier->format,
            ))))
            ->with('isCutReached', $this->booking->evenement->isCutReached())
            ->with('cut', $this->booking->evenement->cut)
            ->with('remainingPlaces', mb_strtolower(displayPlaces(getPlacesPresenter(
                $this->booking->evenement->getAvailableSlotsCount(),
                0,
                0,
            ))))
            ->with('totalPlaces', $this->booking->evenement->countParticipants())
            ->with('location', $this->booking->evenement->present()->workshopLocation())
            ->with('url', frontUrl(route(localizedRouteName('artisan.events.show', $this->locale), ['event' => $this->booking->evenement->getKey()], false).($this->booking->evenement->maintain_status === EventMaintainStatus::EVENT_MAINTAINED_TO_BE_CONFIRMED ? '?openModal=true' : ''), $this->country))
            ->subject(
                __(
                    $this->booking->evenement->privateBookingRequest instanceof PrivateBookingRequest
                        ? 'emails/artisan/booking-management.reservation-annulee.title_privat'
                        : 'emails/artisan/booking-management.reservation-annulee.title',
                    [
                        'places' => mb_strtolower(displayPlaces(getPlacesPresenter(
                            $this->booking->getPlaceNumber(),
                            $this->booking->child_number,
                            $this->booking->adult_number,
                            $this->booking->evenement->atelier->format,
                        ))),
                        'date' => dateLongFormat($this->booking->evenement->start),
                    ]
                )
            );
    }
}
