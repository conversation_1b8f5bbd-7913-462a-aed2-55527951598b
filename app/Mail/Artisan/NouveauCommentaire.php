<?php

declare(strict_types=1);

namespace App\Mail\Artisan;

use App\Mail\BaseMailable;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;

class NouveauCommentaire extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(protected Comment $comment)
    {
        parent::__construct();
    }

    /**
     * Build the message.
     */
    public function build(): BaseMailable
    {
        return $this->markdown('emails.artisan.nouveau-commentaire')
            ->with('comment', $this->comment)
            ->subject(__('emails/artisan/others.new_comment.subject', ['workshop' => $this->comment->workshop->nom]));
    }
}
