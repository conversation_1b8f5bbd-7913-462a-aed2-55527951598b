<?php

declare(strict_types=1);

namespace App\Providers;

use App\Http\Middleware\UserArtisan\ImpersonateArtisan;
use App\Infrastructure\Auth\AuthFinder;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\ServiceProvider;
use Illuminate\View\View;

class ImpersonatedArtisan extends ServiceProvider
{
    public function boot(): void
    {
        \View::composer(['components.artisan.layouts.navigation'], static function (View $view): void {
            $userArtisan = AuthFinder::make()->artisan();

            $impersonatedArtisan = null;
            if (Session::get(ImpersonateArtisan::CACHE_KEY) === true) {
                $impersonatedArtisan = $userArtisan;
            }

            $view->with('impersonatedArtisan', $impersonatedArtisan);
        });
    }
}
