<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\DateProposition\DatePropositionStatus;
use App\Models\Formulaires\ContactAutreDateAtelier;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * App\Models\PropositionDate.
 *
 * @property int $id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property int $atelier_id
 * @property int $associable_id
 * @property string $associable_type
 * @property \Carbon\Carbon $start_date
 * @property \Carbon\Carbon $end_date
 * @property \Carbon\Carbon|null $deleted_at
 * @property DatePropositionStatus $status
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Activity> $activities
 * @property Model|\Eloquent $associable
 * @property Atelier $atelier
 *
 * @mixin \Eloquent
 */
class PropositionDate extends Model
{
    use LogsActivity;
    use SoftDeletes;

    protected $table = 'propositionsdates';

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'status' => DatePropositionStatus::class,
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logExcept(['updated_at'])
            ->logOnlyDirty()
            ->dontLogIfAttributesChangedOnly(['updated_at'])
            ->useLogName('propositionDate_log')
            ->dontSubmitEmptyLogs();
    }

    public function getEmail(): string
    {
        return $this->getContactAutreDateAtelierAssociable()->email;
    }

    /**
     * Return the formatted time, using the start date which is the same as the end date.
     */
    public function getPropositionDate(): string
    {
        return dateAndTimeLongFormatStartAndEnd($this->start_date, $this->end_date, $this->atelier->timezone);
    }

    /**
     * Determines if the given event start and end date is included in the proposed date and time.
     */
    public function areDatesCompatibleWithEvent(Evenement $event): bool
    {
        if ($this->atelier_id !== $event->atelier_id) {
            return false;
        }

        // Lets' convert everything into CarbonPeriod instances.
        // In that way we let it handle the logic to know
        // if onw period contains the second onw
        $eventPeriod = $event->start->toPeriod($this->end_date)->setTimezone($event->timezone);
        $proposedDatePeriod = $this->start_date->toPeriod($this->end_date);

        return $proposedDatePeriod
            ->excludeStartDate(false)
            ->excludeEndDate(false)
            ->contains($eventPeriod);
    }

    /* --------------------- */
    /*      RELATIONSHIPS */
    /* --------------------- */

    /**
     * @return BelongsTo<Atelier, PropositionDate>
     */
    public function atelier(): BelongsTo
    {
        return $this->belongsTo(Atelier::class);
    }

    /** @return MorphTo<Model, $this> */
    public function associable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getContactAutreDateAtelierAssociable(): ContactAutreDateAtelier
    {
        if (!$this->associable instanceof ContactAutreDateAtelier) {
            throw new \LogicException('The associable is not a ContactAutreDateAtelier');
        }

        return $this->associable;
    }
}
