<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Lang.
 *
 * @property int $id
 * @property string $lang_nom
 * @property string $nom
 * @property bool $visible_site
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Artisan> $artisans
 *
 * @mixin \Eloquent
 */
class Lang extends Model
{
    public $timestamps = false;

    /** @var array<string, string> */
    protected $casts = [
        'visible_site' => 'bool',
    ];

    public function artisans(): BelongsToMany
    {
        return $this->belongsToMany(Artisan::class, 'lang_artisan', 'lang_id', 'artisan_id');
    }
}
