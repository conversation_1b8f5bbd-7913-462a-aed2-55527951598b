<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Psr\Http\Message\ResponseInterface;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * App\Models\TransactionLePotCommun.
 *
 * @property int $id
 * @property string|null $transaction_LPC
 * @property int|null $type
 * @property int $amount
 * @property int|null $panier_id
 * @property int|null $commande_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int|null $user_id
 * @property string|null $transaction_WCD
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Activity> $activities
 * @property Commande|null $commande
 * @property Panier|null $panier
 * @property User|null $user
 *
 * @mixin \Eloquent
 */
class TransactionLePotCommun extends Model
{
    use LogsActivity;
    use SoftDeletes;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logExcept(['updated_at'])
            ->logOnlyDirty()
            ->dontLogIfAttributesChangedOnly(['updated_at'])
            ->useLogName('transactions_lepotcommun_log')
            ->dontSubmitEmptyLogs();
    }

    /**
     * @var string
     */
    protected $table = 'transactions_lepotcommun';

    public function getTypeTransaction()
    {
        if ($this->type == 0) {
            return 'Paiement';
        }

        if ($this->type == 1) {
            return 'Annulation';
        }
    }

    public function getInformation(): ResponseInterface
    {
        $client = new \GuzzleHttp\Client(['headers' => ['lpc-key' => config('services.lepotcommun.merchant_key')]]);

        return $client->request('GET', config('services.lepotcommun.site').'/api/merchant/getOrder?merchantId='.config('services.lepotcommun.merchant_id').'&lpcTransactionId='.$this->transaction_LPC);
    }

    /* --------------------- */
    /*      RELATIONSHIPS */
    /* --------------------- */

    /** @return BelongsTo<Panier, $this> */
    public function panier(): BelongsTo
    {
        return $this->belongsTo(Panier::class);
    }

    /** @return BelongsTo<User, $this> */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /** @return BelongsTo<Commande, $this> */
    public function commande(): BelongsTo
    {
        return $this->belongsTo(Commande::class);
    }
}
