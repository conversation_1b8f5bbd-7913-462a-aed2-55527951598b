<?php

namespace App\Models\Parrainage;

use App\Abstracts\Reduction;
use App\Domain\ECommerce\Contracts\DiscountInterface;
use App\Models\Panier;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Parrainage\CodeParrainageUserPanier.
 *
 * @property int $id
 * @property string $code_reduction
 * @property int $montant
 * @property int $panier_id
 * @property int $codeparrainageuser_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property CodeParrainageUser $codeparrainageuser
 * @property Panier $panier
 *
 * @mixin \Eloquent
 */
class CodeParrainageUserPanier extends Model implements DiscountInterface
{
    use SoftDeletes;

    /**
     * @var string
     */
    protected $table = 'parrainageuser_paniers';

    /**
     * @var string[]
     */
    protected $guarded = [];

    /** @return BelongsTo<Panier, $this> */
    public function panier(): BelongsTo
    {
        return $this->belongsTo(Panier::class);
    }

    public function codeparrainageuser(): BelongsTo
    {
        return $this->belongsTo(CodeParrainageUser::class);
    }

    public function descriptionReduction()
    {
        return __('utils.sponsorship.desc_personal_code', [
            'code' => $this->code_reduction,
            'value' => $this->montant,
            'type' => $this->codeparrainageuser->currency,
        ]);
    }

    public function getDiscountValue()
    {
        return $this->montant;
    }

    public function getDetailedDiscount(): Reduction
    {
        return $this->codeparrainageuser;
    }
}
