<?php

namespace App\Models\Reductions;

use App\Abstracts\Reduction;
use App\Domain\ECommerce\Contracts\DiscountInterface;
use App\Models\Panier;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Reductions\CadeauBoxPanier.
 *
 * @property int $id
 * @property string $codereduction
 * @property string $box_nom
 * @property int $panier_id
 * @property int|null $cadeaubox_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property CadeauBox|null $cadeaubox
 * @property Panier $panier
 *
 * @mixin \Eloquent
 */
class CadeauBoxPanier extends Model implements DiscountInterface
{
    use SoftDeletes;

    /**
     * @var string
     */
    protected $table = 'cadeauxboxs_paniers';

    /**
     * @var string[]
     */
    protected $guarded = [];

    /** @return BelongsTo<CadeauBox, $this> */
    public function cadeaubox(): BelongsTo
    {
        return $this->belongsTo(CadeauBox::class);
    }

    /** @return BelongsTo<Panier, $this> */
    public function panier(): BelongsTo
    {
        return $this->belongsTo(Panier::class);
    }

    public function descriptionReduction(): string
    {
        return $this->codereduction.' - Box "'.$this->box_nom.'"';
    }

    public function getDiscountValue(): void
    {
        // TODO: Implement getDiscountValue() method.
    }

    public function getDetailedDiscount(): Reduction
    {
        return $this->cadeaubox;
    }
}
