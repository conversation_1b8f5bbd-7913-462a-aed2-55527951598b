<?php

declare(strict_types=1);

namespace App\Models\Casts;

use App\Shared\RelativeAmount;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

/**
 * @implements CastsAttributes<RelativeAmount, mixed>
 *
 * @SuppressWarnings(PHPMD.UnusedFormalParameter) Cannot remove unused cast parameters
 */
class IntegerRelativeAmount extends IntegerAmount implements CastsAttributes
{
    protected string $amountObject = RelativeAmount::class;

    /**
     * @param Model $model
     * @param array<string, mixed> $attributes
     *
     * @return RelativeAmount
     *
     * @throws \InvalidArgumentException
     */
    public function get($model, string $key, mixed $value, array $attributes)
    {
        // @phpstan-ignore-next-line return.type
        return parent::get($model, $key, $value, $attributes);
    }
}
