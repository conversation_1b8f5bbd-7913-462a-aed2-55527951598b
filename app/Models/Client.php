<?php

namespace App\Models;

use App\Interfaces\Facturable;
use App\Shared\Accounting\Billable;
use App\Shared\Accounting\BillingAddress;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;

/**
 * App\Models\Client.
 *
 * @property int $id
 * @property int $associable_id
 * @property string $associable_type
 * @property int $entreprise_id
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property Model|\Eloquent $associable
 * @property Entreprise $entreprise
 *
 * @mixin \Eloquent
 */
class Client extends Model implements Facturable, Billable
{
    use SoftDeletes;

    public function getIdentity()
    {
        switch ($this->associable::class) {
            case Entreprise::class:
                return 'Client '.$this->associable->nom;
            case User::class:
            case Artisan::class:
                return $this->associable->prenom.' '.$this->associable->nom;
            default:
                Log::error('Type client non reconnu');
        }
    }

    public function hasAdresseFacturation(): bool
    {
        if ($this->associable instanceof Entreprise) {
            return false;
        }

        return (bool) $this->getAssociable()?->hasAdresseFacturation();
    }

    public function getAdresseFacturation(): ?Adresse
    {
        if ($this->associable instanceof Entreprise) {
            return null;
        }

        return $this->getAssociable()?->getAdresseFacturation();
    }

    public function getInformationsDestinataire(): string
    {
        return $this->getAssociable()?->getInformationsDestinataire() ?? 'Client # '.$this->id;
    }

    /* --------------------- */
    /*      RELATIONSHIPS */
    /* --------------------- */

    public function associable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getAssociable(): Entreprise|User|Artisan|null
    {
        if (empty($this->associable)) {
            return null;
        }
        if (!$this->associable instanceof Artisan && !$this->associable instanceof User && !$this->associable instanceof Entreprise) {
            throw new \RuntimeException('Invalid associable type');
        }

        return $this->associable;
    }

    /** @return BelongsTo<Entreprise, $this> */
    public function entreprise(): BelongsTo
    {
        return $this->belongsTo(Entreprise::class);
    }

    public function getBillingAddress(): BillingAddress
    {
        if (!$this->associable instanceof Entreprise) {
            throw new \RuntimeException("Client associable on type {$this->associable->getMorphClass()} unhandled");
        }

        return new BillingAddress(
            $this->getMorphClass(),
            $this->id,
            sprintf(
                '%s %s',
                __('invoice.pdf.content.commissions.customer'),
                $this->associable->informations_facturation?->raison_sociale ?: $this->associable->nom,
            ),
        );
    }
}
