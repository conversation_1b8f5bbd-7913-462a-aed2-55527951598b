<?php

declare(strict_types=1);

namespace App\Payment\Types;

use App\Shared\Amount;

readonly class BankTransferResponse
{
    public function __construct(
        public string $paymentIntentId,
        public ?string $bic,
        public ?string $accountHolder,
        public ?string $iban,
        public ?string $accountNumber,
        public ?string $sortCode,
        public string $reference,
        public string $instructionsUrl,
        public Amount $amountReceived,
    ) {
    }
}
