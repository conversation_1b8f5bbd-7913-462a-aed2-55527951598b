<?php

declare(strict_types=1);

namespace App\Payment\Exceptions;

use App\Enums\Country;
use Stripe\Exception\ApiErrorException;

class PaymentAdapterApiException extends \Exception
{
    public static function fromInconsistentUECountry(Country $country): self
    {
        return new self(
            "Inconsistent UE country '{$country->value}' given",
        );
    }

    public static function fromException(ApiErrorException $exception): self
    {
        return new self(
            "Payment adapter API failed with message : {$exception->getMessage()}"
        );
    }
}
