<?php

declare(strict_types=1);

namespace App\Payment\Exceptions;

class RefundException extends \Exception
{
    public static function fromStripeApi(
        string $paymentIntentId,
        string $requestId,
        \Exception $exception
    ): self {
        throw new self(message: "Failed refunding stripe payment intent [$paymentIntentId], got on error for request id [$requestId] : {$exception->getMessage()}", code: $exception->getCode(), previous: $exception);
    }
}
