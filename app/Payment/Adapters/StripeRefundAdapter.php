<?php

declare(strict_types=1);

namespace App\Payment\Adapters;

use App\Enums\StripeAccount;
use App\Payment\Exceptions\RefundException;
use App\Payment\Interfaces\RefundGateway;
use App\Payment\Types\RefundResponse;
use App\Shared\Amount;
use Stripe\Exception\ApiErrorException;

class StripeRefundAdapter implements RefundGateway
{
    public function __construct(
        protected readonly StripeClientResolver $stripeClient,
        protected readonly StripeMetadataManager $metadataManager
    ) {
    }

    /**
     * @throws RefundException
     */
    public function refund(StripeAccount $account, string $paymentIntentId, Amount $amount): RefundResponse
    {
        try {
            $refund = $this->stripeClient->from($account)->refunds->create([
                'payment_intent' => $paymentIntentId,
                'amount' => $amount->value(),
                'reason' => 'requested_by_customer',
                'metadata' => $this->metadataManager->newMetadata(),
            ]);
        } catch (ApiErrorException $exception) {
            throw RefundException::fromStripeApi($paymentIntentId, $exception->getRequestId() ?? 'unknown', $exception);
        }

        return new RefundResponse($refund->id);
    }
}
