<?php

declare(strict_types=1);

namespace App\Abstracts;

use Illuminate\Database\Eloquent\Model;

abstract class Presenter
{
    public function __construct(protected Model $model)
    {
    }

    public function __get(mixed $property): mixed
    {
        if (method_exists($this, $property)) {
            // @phpstan-ignore-next-line method.dynamicName
            return $this->$property();
        }

        $message = '%s does not respond to the %s property or method';

        throw new \Exception(sprintf($message, static::class, $property));
    }
}
