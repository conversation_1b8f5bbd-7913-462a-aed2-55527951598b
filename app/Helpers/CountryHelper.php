<?php

declare(strict_types=1);

namespace App\Helpers;

/**
 * @SuppressWarnings(PHPMD.ExcessiveClassLength)
 */
class CountryHelper
{
    /**
     * @var array<string, array<string, string>>
     */
    private static array $countryData =
        [
            'AC' => [
                'code' => 'AC',
                'html' => '&#x1f1e6;&#x1f1e8;',
                'name' => 'Ascension Island',
                'emoji' => '🇦🇨',
            ],
            'AD' => [
                'code' => 'AD',
                'html' => '&#x1f1e6;&#x1f1e9;',
                'name' => 'Andorra',
                'emoji' => '🇦🇩',
            ],
            'AE' => [
                'code' => 'AE',
                'html' => '&#x1f1e6;&#x1f1ea;',
                'name' => 'United Arab Emirates',
                'emoji' => '🇦🇪',
            ],
            'AF' => [
                'code' => 'AF',
                'html' => '&#x1f1e6;&#x1f1eb;',
                'name' => 'Afghanistan',
                'emoji' => '🇦🇫',
            ],
            'AG' => [
                'code' => 'AG',
                'html' => '&#x1f1e6;&#x1f1ec;',
                'name' => 'Antigua & Barbuda',
                'emoji' => '🇦🇬',
            ],
            'AI' => [
                'code' => 'AI',
                'html' => '&#x1f1e6;&#x1f1ee;',
                'name' => 'Anguilla',
                'emoji' => '🇦🇮',
            ],
            'AL' => [
                'code' => 'AL',
                'html' => '&#x1f1e6;&#x1f1f1;',
                'name' => 'Albania',
                'emoji' => '🇦🇱',
            ],
            'AM' => [
                'code' => 'AM',
                'html' => '&#x1f1e6;&#x1f1f2;',
                'name' => 'Armenia',
                'emoji' => '🇦🇲',
            ],
            'AO' => [
                'code' => 'AO',
                'html' => '&#x1f1e6;&#x1f1f4;',
                'name' => 'Angola',
                'emoji' => '🇦🇴',
            ],
            'AQ' => [
                'code' => 'AQ',
                'html' => '&#x1f1e6;&#x1f1f6;',
                'name' => 'Antarctica',
                'emoji' => '🇦🇶',
            ],
            'AR' => [
                'code' => 'AR',
                'html' => '&#x1f1e6;&#x1f1f7;',
                'name' => 'Argentina',
                'emoji' => '🇦🇷',
            ],
            'AS' => [
                'code' => 'AS',
                'html' => '&#x1f1e6;&#x1f1f8;',
                'name' => 'American Samoa',
                'emoji' => '🇦🇸',
            ],
            'AT' => [
                'code' => 'AT',
                'html' => '&#x1f1e6;&#x1f1f9;',
                'name' => 'Austria',
                'emoji' => '🇦🇹',
            ],
            'AU' => [
                'code' => 'AU',
                'html' => '&#x1f1e6;&#x1f1fa;',
                'name' => 'Australia',
                'emoji' => '🇦🇺',
            ],
            'AW' => [
                'code' => 'AW',
                'html' => '&#x1f1e6;&#x1f1fc;',
                'name' => 'Aruba',
                'emoji' => '🇦🇼',
            ],
            'AX' => [
                'code' => 'AX',
                'html' => '&#x1f1e6;&#x1f1fd;',
                'name' => 'Åland Islands',
                'emoji' => '🇦🇽',
            ],
            'AZ' => [
                'code' => 'AZ',
                'html' => '&#x1f1e6;&#x1f1ff;',
                'name' => 'Azerbaijan',
                'emoji' => '🇦🇿',
            ],
            'BA' => [
                'code' => 'BA',
                'html' => '&#x1f1e7;&#x1f1e6;',
                'name' => 'Bosnia & Herzegovina',
                'emoji' => '🇧🇦',
            ],
            'BB' => [
                'code' => 'BB',
                'html' => '&#x1f1e7;&#x1f1e7;',
                'name' => 'Barbados',
                'emoji' => '🇧🇧',
            ],
            'BD' => [
                'code' => 'BD',
                'html' => '&#x1f1e7;&#x1f1e9;',
                'name' => 'Bangladesh',
                'emoji' => '🇧🇩',
            ],
            'BE' => [
                'code' => 'BE',
                'html' => '&#x1f1e7;&#x1f1ea;',
                'name' => 'Belgium',
                'emoji' => '🇧🇪',
            ],
            'BF' => [
                'code' => 'BF',
                'html' => '&#x1f1e7;&#x1f1eb;',
                'name' => 'Burkina Faso',
                'emoji' => '🇧🇫',
            ],
            'BG' => [
                'code' => 'BG',
                'html' => '&#x1f1e7;&#x1f1ec;',
                'name' => 'Bulgaria',
                'emoji' => '🇧🇬',
            ],
            'BH' => [
                'code' => 'BH',
                'html' => '&#x1f1e7;&#x1f1ed;',
                'name' => 'Bahrain',
                'emoji' => '🇧🇭',
            ],
            'BI' => [
                'code' => 'BI',
                'html' => '&#x1f1e7;&#x1f1ee;',
                'name' => 'Burundi',
                'emoji' => '🇧🇮',
            ],
            'BJ' => [
                'code' => 'BJ',
                'html' => '&#x1f1e7;&#x1f1ef;',
                'name' => 'Benin',
                'emoji' => '🇧🇯',
            ],
            'BL' => [
                'code' => 'BL',
                'html' => '&#x1f1e7;&#x1f1f1;',
                'name' => 'St. Barthélemy',
                'emoji' => '🇧🇱',
            ],
            'BM' => [
                'code' => 'BM',
                'html' => '&#x1f1e7;&#x1f1f2;',
                'name' => 'Bermuda',
                'emoji' => '🇧🇲',
            ],
            'BN' => [
                'code' => 'BN',
                'html' => '&#x1f1e7;&#x1f1f3;',
                'name' => 'Brunei',
                'emoji' => '🇧🇳',
            ],
            'BO' => [
                'code' => 'BO',
                'html' => '&#x1f1e7;&#x1f1f4;',
                'name' => 'Bolivia',
                'emoji' => '🇧🇴',
            ],
            'BQ' => [
                'code' => 'BQ',
                'html' => '&#x1f1e7;&#x1f1f6;',
                'name' => 'Caribbean Netherlands',
                'emoji' => '🇧🇶',
            ],
            'BR' => [
                'code' => 'BR',
                'html' => '&#x1f1e7;&#x1f1f7;',
                'name' => 'Brazil',
                'emoji' => '🇧🇷',
            ],
            'BS' => [
                'code' => 'BS',
                'html' => '&#x1f1e7;&#x1f1f8;',
                'name' => 'Bahamas',
                'emoji' => '🇧🇸',
            ],
            'BT' => [
                'code' => 'BT',
                'html' => '&#x1f1e7;&#x1f1f9;',
                'name' => 'Bhutan',
                'emoji' => '🇧🇹',
            ],
            'BV' => [
                'code' => 'BV',
                'html' => '&#x1f1e7;&#x1f1fb;',
                'name' => 'Bouvet Island',
                'emoji' => '🇧🇻',
            ],
            'BW' => [
                'code' => 'BW',
                'html' => '&#x1f1e7;&#x1f1fc;',
                'name' => 'Botswana',
                'emoji' => '🇧🇼',
            ],
            'BY' => [
                'code' => 'BY',
                'html' => '&#x1f1e7;&#x1f1fe;',
                'name' => 'Belarus',
                'emoji' => '🇧🇾',
            ],
            'BZ' => [
                'code' => 'BZ',
                'html' => '&#x1f1e7;&#x1f1ff;',
                'name' => 'Belize',
                'emoji' => '🇧🇿',
            ],
            'CA' => [
                'code' => 'CA',
                'html' => '&#x1f1e8;&#x1f1e6;',
                'name' => 'Canada',
                'emoji' => '🇨🇦',
            ],
            'CC' => [
                'code' => 'CC',
                'html' => '&#x1f1e8;&#x1f1e8;',
                'name' => 'Cocos (Keeling) Islands',
                'emoji' => '🇨🇨',
            ],
            'CD' => [
                'code' => 'CD',
                'html' => '&#x1f1e8;&#x1f1e9;',
                'name' => 'Congo - Kinshasa',
                'emoji' => '🇨🇩',
            ],
            'CF' => [
                'code' => 'CF',
                'html' => '&#x1f1e8;&#x1f1eb;',
                'name' => 'Central African Republic',
                'emoji' => '🇨🇫',
            ],
            'CG' => [
                'code' => 'CG',
                'html' => '&#x1f1e8;&#x1f1ec;',
                'name' => 'Congo - Brazzaville',
                'emoji' => '🇨🇬',
            ],
            'CH' => [
                'code' => 'CH',
                'html' => '&#x1f1e8;&#x1f1ed;',
                'name' => 'Switzerland',
                'emoji' => '🇨🇭',
            ],
            'CI' => [
                'code' => 'CI',
                'html' => '&#x1f1e8;&#x1f1ee;',
                'name' => 'Côte d’Ivoire',
                'emoji' => '🇨🇮',
            ],
            'CK' => [
                'code' => 'CK',
                'html' => '&#x1f1e8;&#x1f1f0;',
                'name' => 'Cook Islands',
                'emoji' => '🇨🇰',
            ],
            'CL' => [
                'code' => 'CL',
                'html' => '&#x1f1e8;&#x1f1f1;',
                'name' => 'Chile',
                'emoji' => '🇨🇱',
            ],
            'CM' => [
                'code' => 'CM',
                'html' => '&#x1f1e8;&#x1f1f2;',
                'name' => 'Cameroon',
                'emoji' => '🇨🇲',
            ],
            'CN' => [
                'code' => 'CN',
                'html' => '&#x1f1e8;&#x1f1f3;',
                'name' => 'China',
                'emoji' => '🇨🇳',
            ],
            'CO' => [
                'code' => 'CO',
                'html' => '&#x1f1e8;&#x1f1f4;',
                'name' => 'Colombia',
                'emoji' => '🇨🇴',
            ],
            'CP' => [
                'code' => 'CP',
                'html' => '&#x1f1e8;&#x1f1f5;',
                'name' => 'Clipperton Island',
                'emoji' => '🇨🇵',
            ],
            'CR' => [
                'code' => 'CR',
                'html' => '&#x1f1e8;&#x1f1f7;',
                'name' => 'Costa Rica',
                'emoji' => '🇨🇷',
            ],
            'CU' => [
                'code' => 'CU',
                'html' => '&#x1f1e8;&#x1f1fa;',
                'name' => 'Cuba',
                'emoji' => '🇨🇺',
            ],
            'CV' => [
                'code' => 'CV',
                'html' => '&#x1f1e8;&#x1f1fb;',
                'name' => 'Cape Verde',
                'emoji' => '🇨🇻',
            ],
            'CW' => [
                'code' => 'CW',
                'html' => '&#x1f1e8;&#x1f1fc;',
                'name' => 'Curaçao',
                'emoji' => '🇨🇼',
            ],
            'CX' => [
                'code' => 'CX',
                'html' => '&#x1f1e8;&#x1f1fd;',
                'name' => 'Christmas Island',
                'emoji' => '🇨🇽',
            ],
            'CY' => [
                'code' => 'CY',
                'html' => '&#x1f1e8;&#x1f1fe;',
                'name' => 'Cyprus',
                'emoji' => '🇨🇾',
            ],
            'CZ' => [
                'code' => 'CZ',
                'html' => '&#x1f1e8;&#x1f1ff;',
                'name' => 'Czechia',
                'emoji' => '🇨🇿',
            ],
            'DE' => [
                'code' => 'DE',
                'html' => '&#x1f1e9;&#x1f1ea;',
                'name' => 'Germany',
                'emoji' => '🇩🇪',
            ],
            'DG' => [
                'code' => 'DG',
                'html' => '&#x1f1e9;&#x1f1ec;',
                'name' => 'Diego Garcia',
                'emoji' => '🇩🇬',
            ],
            'DJ' => [
                'code' => 'DJ',
                'html' => '&#x1f1e9;&#x1f1ef;',
                'name' => 'Djibouti',
                'emoji' => '🇩🇯',
            ],
            'DK' => [
                'code' => 'DK',
                'html' => '&#x1f1e9;&#x1f1f0;',
                'name' => 'Denmark',
                'emoji' => '🇩🇰',
            ],
            'DM' => [
                'code' => 'DM',
                'html' => '&#x1f1e9;&#x1f1f2;',
                'name' => 'Dominica',
                'emoji' => '🇩🇲',
            ],
            'DO' => [
                'code' => 'DO',
                'html' => '&#x1f1e9;&#x1f1f4;',
                'name' => 'Dominican Republic',
                'emoji' => '🇩🇴',
            ],
            'DZ' => [
                'code' => 'DZ',
                'html' => '&#x1f1e9;&#x1f1ff;',
                'name' => 'Algeria',
                'emoji' => '🇩🇿',
            ],
            'EA' => [
                'code' => 'EA',
                'html' => '&#x1f1ea;&#x1f1e6;',
                'name' => 'Ceuta & Melilla',
                'emoji' => '🇪🇦',
            ],
            'EC' => [
                'code' => 'EC',
                'html' => '&#x1f1ea;&#x1f1e8;',
                'name' => 'Ecuador',
                'emoji' => '🇪🇨',
            ],
            'EE' => [
                'code' => 'EE',
                'html' => '&#x1f1ea;&#x1f1ea;',
                'name' => 'Estonia',
                'emoji' => '🇪🇪',
            ],
            'EG' => [
                'code' => 'EG',
                'html' => '&#x1f1ea;&#x1f1ec;',
                'name' => 'Egypt',
                'emoji' => '🇪🇬',
            ],
            'EH' => [
                'code' => 'EH',
                'html' => '&#x1f1ea;&#x1f1ed;',
                'name' => 'Western Sahara',
                'emoji' => '🇪🇭',
            ],
            'ER' => [
                'code' => 'ER',
                'html' => '&#x1f1ea;&#x1f1f7;',
                'name' => 'Eritrea',
                'emoji' => '🇪🇷',
            ],
            'ES' => [
                'code' => 'ES',
                'html' => '&#x1f1ea;&#x1f1f8;',
                'name' => 'Spain',
                'emoji' => '🇪🇸',
            ],
            'ET' => [
                'code' => 'ET',
                'html' => '&#x1f1ea;&#x1f1f9;',
                'name' => 'Ethiopia',
                'emoji' => '🇪🇹',
            ],
            'EU' => [
                'code' => 'EU',
                'html' => '&#x1f1ea;&#x1f1fa;',
                'name' => 'European Union',
                'emoji' => '🇪🇺',
            ],
            'FI' => [
                'code' => 'FI',
                'html' => '&#x1f1eb;&#x1f1ee;',
                'name' => 'Finland',
                'emoji' => '🇫🇮',
            ],
            'FJ' => [
                'code' => 'FJ',
                'html' => '&#x1f1eb;&#x1f1ef;',
                'name' => 'Fiji',
                'emoji' => '🇫🇯',
            ],
            'FK' => [
                'code' => 'FK',
                'html' => '&#x1f1eb;&#x1f1f0;',
                'name' => 'Falkland Islands',
                'emoji' => '🇫🇰',
            ],
            'FM' => [
                'code' => 'FM',
                'html' => '&#x1f1eb;&#x1f1f2;',
                'name' => 'Micronesia',
                'emoji' => '🇫🇲',
            ],
            'FO' => [
                'code' => 'FO',
                'html' => '&#x1f1eb;&#x1f1f4;',
                'name' => 'Faroe Islands',
                'emoji' => '🇫🇴',
            ],
            'FR' => [
                'code' => 'FR',
                'html' => '&#x1f1eb;&#x1f1f7;',
                'name' => 'France',
                'emoji' => '🇫🇷',
            ],
            'GA' => [
                'code' => 'GA',
                'html' => '&#x1f1ec;&#x1f1e6;',
                'name' => 'Gabon',
                'emoji' => '🇬🇦',
            ],
            'GB' => [
                'code' => 'GB',
                'html' => '&#x1f1ec;&#x1f1e7;',
                'name' => 'United Kingdom',
                'emoji' => '🇬🇧',
            ],
            'GD' => [
                'code' => 'GD',
                'html' => '&#x1f1ec;&#x1f1e9;',
                'name' => 'Grenada',
                'emoji' => '🇬🇩',
            ],
            'GE' => [
                'code' => 'GE',
                'html' => '&#x1f1ec;&#x1f1ea;',
                'name' => 'Georgia',
                'emoji' => '🇬🇪',
            ],
            'GF' => [
                'code' => 'GF',
                'html' => '&#x1f1ec;&#x1f1eb;',
                'name' => 'French Guiana',
                'emoji' => '🇬🇫',
            ],
            'GG' => [
                'code' => 'GG',
                'html' => '&#x1f1ec;&#x1f1ec;',
                'name' => 'Guernsey',
                'emoji' => '🇬🇬',
            ],
            'GH' => [
                'code' => 'GH',
                'html' => '&#x1f1ec;&#x1f1ed;',
                'name' => 'Ghana',
                'emoji' => '🇬🇭',
            ],
            'GI' => [
                'code' => 'GI',
                'html' => '&#x1f1ec;&#x1f1ee;',
                'name' => 'Gibraltar',
                'emoji' => '🇬🇮',
            ],
            'GL' => [
                'code' => 'GL',
                'html' => '&#x1f1ec;&#x1f1f1;',
                'name' => 'Greenland',
                'emoji' => '🇬🇱',
            ],
            'GM' => [
                'code' => 'GM',
                'html' => '&#x1f1ec;&#x1f1f2;',
                'name' => 'Gambia',
                'emoji' => '🇬🇲',
            ],
            'GN' => [
                'code' => 'GN',
                'html' => '&#x1f1ec;&#x1f1f3;',
                'name' => 'Guinea',
                'emoji' => '🇬🇳',
            ],
            'GP' => [
                'code' => 'GP',
                'html' => '&#x1f1ec;&#x1f1f5;',
                'name' => 'Guadeloupe',
                'emoji' => '🇬🇵',
            ],
            'GQ' => [
                'code' => 'GQ',
                'html' => '&#x1f1ec;&#x1f1f6;',
                'name' => 'Equatorial Guinea',
                'emoji' => '🇬🇶',
            ],
            'GR' => [
                'code' => 'GR',
                'html' => '&#x1f1ec;&#x1f1f7;',
                'name' => 'Greece',
                'emoji' => '🇬🇷',
            ],
            'GS' => [
                'code' => 'GS',
                'html' => '&#x1f1ec;&#x1f1f8;',
                'name' => 'South Georgia & South Sandwich Islands',
                'emoji' => '🇬🇸',
            ],
            'GT' => [
                'code' => 'GT',
                'html' => '&#x1f1ec;&#x1f1f9;',
                'name' => 'Guatemala',
                'emoji' => '🇬🇹',
            ],
            'GU' => [
                'code' => 'GU',
                'html' => '&#x1f1ec;&#x1f1fa;',
                'name' => 'Guam',
                'emoji' => '🇬🇺',
            ],
            'GW' => [
                'code' => 'GW',
                'html' => '&#x1f1ec;&#x1f1fc;',
                'name' => 'Guinea-Bissau',
                'emoji' => '🇬🇼',
            ],
            'GY' => [
                'code' => 'GY',
                'html' => '&#x1f1ec;&#x1f1fe;',
                'name' => 'Guyana',
                'emoji' => '🇬🇾',
            ],
            'HK' => [
                'code' => 'HK',
                'html' => '&#x1f1ed;&#x1f1f0;',
                'name' => 'Hong Kong SAR China',
                'emoji' => '🇭🇰',
            ],
            'HM' => [
                'code' => 'HM',
                'html' => '&#x1f1ed;&#x1f1f2;',
                'name' => 'Heard & McDonald Islands',
                'emoji' => '🇭🇲',
            ],
            'HN' => [
                'code' => 'HN',
                'html' => '&#x1f1ed;&#x1f1f3;',
                'name' => 'Honduras',
                'emoji' => '🇭🇳',
            ],
            'HR' => [
                'code' => 'HR',
                'html' => '&#x1f1ed;&#x1f1f7;',
                'name' => 'Croatia',
                'emoji' => '🇭🇷',
            ],
            'HT' => [
                'code' => 'HT',
                'html' => '&#x1f1ed;&#x1f1f9;',
                'name' => 'Haiti',
                'emoji' => '🇭🇹',
            ],
            'HU' => [
                'code' => 'HU',
                'html' => '&#x1f1ed;&#x1f1fa;',
                'name' => 'Hungary',
                'emoji' => '🇭🇺',
            ],
            'IC' => [
                'code' => 'IC',
                'html' => '&#x1f1ee;&#x1f1e8;',
                'name' => 'Canary Islands',
                'emoji' => '🇮🇨',
            ],
            'ID' => [
                'code' => 'ID',
                'html' => '&#x1f1ee;&#x1f1e9;',
                'name' => 'Indonesia',
                'emoji' => '🇮🇩',
            ],
            'IE' => [
                'code' => 'IE',
                'html' => '&#x1f1ee;&#x1f1ea;',
                'name' => 'Ireland',
                'emoji' => '🇮🇪',
            ],
            'IL' => [
                'code' => 'IL',
                'html' => '&#x1f1ee;&#x1f1f1;',
                'name' => 'Israel',
                'emoji' => '🇮🇱',
            ],
            'IM' => [
                'code' => 'IM',
                'html' => '&#x1f1ee;&#x1f1f2;',
                'name' => 'Isle of Man',
                'emoji' => '🇮🇲',
            ],
            'IN' => [
                'code' => 'IN',
                'html' => '&#x1f1ee;&#x1f1f3;',
                'name' => 'India',
                'emoji' => '🇮🇳',
            ],
            'IO' => [
                'code' => 'IO',
                'html' => '&#x1f1ee;&#x1f1f4;',
                'name' => 'British Indian Ocean Territory',
                'emoji' => '🇮🇴',
            ],
            'IQ' => [
                'code' => 'IQ',
                'html' => '&#x1f1ee;&#x1f1f6;',
                'name' => 'Iraq',
                'emoji' => '🇮🇶',
            ],
            'IR' => [
                'code' => 'IR',
                'html' => '&#x1f1ee;&#x1f1f7;',
                'name' => 'Iran',
                'emoji' => '🇮🇷',
            ],
            'IS' => [
                'code' => 'IS',
                'html' => '&#x1f1ee;&#x1f1f8;',
                'name' => 'Iceland',
                'emoji' => '🇮🇸',
            ],
            'IT' => [
                'code' => 'IT',
                'html' => '&#x1f1ee;&#x1f1f9;',
                'name' => 'Italy',
                'emoji' => '🇮🇹',
            ],
            'JE' => [
                'code' => 'JE',
                'html' => '&#x1f1ef;&#x1f1ea;',
                'name' => 'Jersey',
                'emoji' => '🇯🇪',
            ],
            'JM' => [
                'code' => 'JM',
                'html' => '&#x1f1ef;&#x1f1f2;',
                'name' => 'Jamaica',
                'emoji' => '🇯🇲',
            ],
            'JO' => [
                'code' => 'JO',
                'html' => '&#x1f1ef;&#x1f1f4;',
                'name' => 'Jordan',
                'emoji' => '🇯🇴',
            ],
            'JP' => [
                'code' => 'JP',
                'html' => '&#x1f1ef;&#x1f1f5;',
                'name' => 'Japan',
                'emoji' => '🇯🇵',
            ],
            'KE' => [
                'code' => 'KE',
                'html' => '&#x1f1f0;&#x1f1ea;',
                'name' => 'Kenya',
                'emoji' => '🇰🇪',
            ],
            'KG' => [
                'code' => 'KG',
                'html' => '&#x1f1f0;&#x1f1ec;',
                'name' => 'Kyrgyzstan',
                'emoji' => '🇰🇬',
            ],
            'KH' => [
                'code' => 'KH',
                'html' => '&#x1f1f0;&#x1f1ed;',
                'name' => 'Cambodia',
                'emoji' => '🇰🇭',
            ],
            'KI' => [
                'code' => 'KI',
                'html' => '&#x1f1f0;&#x1f1ee;',
                'name' => 'Kiribati',
                'emoji' => '🇰🇮',
            ],
            'KM' => [
                'code' => 'KM',
                'html' => '&#x1f1f0;&#x1f1f2;',
                'name' => 'Comoros',
                'emoji' => '🇰🇲',
            ],
            'KN' => [
                'code' => 'KN',
                'html' => '&#x1f1f0;&#x1f1f3;',
                'name' => 'St. Kitts & Nevis',
                'emoji' => '🇰🇳',
            ],
            'KP' => [
                'code' => 'KP',
                'html' => '&#x1f1f0;&#x1f1f5;',
                'name' => 'North Korea',
                'emoji' => '🇰🇵',
            ],
            'KR' => [
                'code' => 'KR',
                'html' => '&#x1f1f0;&#x1f1f7;',
                'name' => 'South Korea',
                'emoji' => '🇰🇷',
            ],
            'KW' => [
                'code' => 'KW',
                'html' => '&#x1f1f0;&#x1f1fc;',
                'name' => 'Kuwait',
                'emoji' => '🇰🇼',
            ],
            'KY' => [
                'code' => 'KY',
                'html' => '&#x1f1f0;&#x1f1fe;',
                'name' => 'Cayman Islands',
                'emoji' => '🇰🇾',
            ],
            'KZ' => [
                'code' => 'KZ',
                'html' => '&#x1f1f0;&#x1f1ff;',
                'name' => 'Kazakhstan',
                'emoji' => '🇰🇿',
            ],
            'LA' => [
                'code' => 'LA',
                'html' => '&#x1f1f1;&#x1f1e6;',
                'name' => 'Laos',
                'emoji' => '🇱🇦',
            ],
            'LB' => [
                'code' => 'LB',
                'html' => '&#x1f1f1;&#x1f1e7;',
                'name' => 'Lebanon',
                'emoji' => '🇱🇧',
            ],
            'LC' => [
                'code' => 'LC',
                'html' => '&#x1f1f1;&#x1f1e8;',
                'name' => 'St. Lucia',
                'emoji' => '🇱🇨',
            ],
            'LI' => [
                'code' => 'LI',
                'html' => '&#x1f1f1;&#x1f1ee;',
                'name' => 'Liechtenstein',
                'emoji' => '🇱🇮',
            ],
            'LK' => [
                'code' => 'LK',
                'html' => '&#x1f1f1;&#x1f1f0;',
                'name' => 'Sri Lanka',
                'emoji' => '🇱🇰',
            ],
            'LR' => [
                'code' => 'LR',
                'html' => '&#x1f1f1;&#x1f1f7;',
                'name' => 'Liberia',
                'emoji' => '🇱🇷',
            ],
            'LS' => [
                'code' => 'LS',
                'html' => '&#x1f1f1;&#x1f1f8;',
                'name' => 'Lesotho',
                'emoji' => '🇱🇸',
            ],
            'LT' => [
                'code' => 'LT',
                'html' => '&#x1f1f1;&#x1f1f9;',
                'name' => 'Lithuania',
                'emoji' => '🇱🇹',
            ],
            'LU' => [
                'code' => 'LU',
                'html' => '&#x1f1f1;&#x1f1fa;',
                'name' => 'Luxembourg',
                'emoji' => '🇱🇺',
            ],
            'LV' => [
                'code' => 'LV',
                'html' => '&#x1f1f1;&#x1f1fb;',
                'name' => 'Latvia',
                'emoji' => '🇱🇻',
            ],
            'LY' => [
                'code' => 'LY',
                'html' => '&#x1f1f1;&#x1f1fe;',
                'name' => 'Libya',
                'emoji' => '🇱🇾',
            ],
            'MA' => [
                'code' => 'MA',
                'html' => '&#x1f1f2;&#x1f1e6;',
                'name' => 'Morocco',
                'emoji' => '🇲🇦',
            ],
            'MC' => [
                'code' => 'MC',
                'html' => '&#x1f1f2;&#x1f1e8;',
                'name' => 'Monaco',
                'emoji' => '🇲🇨',
            ],
            'MD' => [
                'code' => 'MD',
                'html' => '&#x1f1f2;&#x1f1e9;',
                'name' => 'Moldova',
                'emoji' => '🇲🇩',
            ],
            'ME' => [
                'code' => 'ME',
                'html' => '&#x1f1f2;&#x1f1ea;',
                'name' => 'Montenegro',
                'emoji' => '🇲🇪',
            ],
            'MF' => [
                'code' => 'MF',
                'html' => '&#x1f1f2;&#x1f1eb;',
                'name' => 'St. Martin',
                'emoji' => '🇲🇫',
            ],
            'MG' => [
                'code' => 'MG',
                'html' => '&#x1f1f2;&#x1f1ec;',
                'name' => 'Madagascar',
                'emoji' => '🇲🇬',
            ],
            'MH' => [
                'code' => 'MH',
                'html' => '&#x1f1f2;&#x1f1ed;',
                'name' => 'Marshall Islands',
                'emoji' => '🇲🇭',
            ],
            'MK' => [
                'code' => 'MK',
                'html' => '&#x1f1f2;&#x1f1f0;',
                'name' => 'Macedonia',
                'emoji' => '🇲🇰',
            ],
            'ML' => [
                'code' => 'ML',
                'html' => '&#x1f1f2;&#x1f1f1;',
                'name' => 'Mali',
                'emoji' => '🇲🇱',
            ],
            'MM' => [
                'code' => 'MM',
                'html' => '&#x1f1f2;&#x1f1f2;',
                'name' => 'Myanmar (Burma)',
                'emoji' => '🇲🇲',
            ],
            'MN' => [
                'code' => 'MN',
                'html' => '&#x1f1f2;&#x1f1f3;',
                'name' => 'Mongolia',
                'emoji' => '🇲🇳',
            ],
            'MO' => [
                'code' => 'MO',
                'html' => '&#x1f1f2;&#x1f1f4;',
                'name' => 'Macau SAR China',
                'emoji' => '🇲🇴',
            ],
            'MP' => [
                'code' => 'MP',
                'html' => '&#x1f1f2;&#x1f1f5;',
                'name' => 'Northern Mariana Islands',
                'emoji' => '🇲🇵',
            ],
            'MQ' => [
                'code' => 'MQ',
                'html' => '&#x1f1f2;&#x1f1f6;',
                'name' => 'Martinique',
                'emoji' => '🇲🇶',
            ],
            'MR' => [
                'code' => 'MR',
                'html' => '&#x1f1f2;&#x1f1f7;',
                'name' => 'Mauritania',
                'emoji' => '🇲🇷',
            ],
            'MS' => [
                'code' => 'MS',
                'html' => '&#x1f1f2;&#x1f1f8;',
                'name' => 'Montserrat',
                'emoji' => '🇲🇸',
            ],
            'MT' => [
                'code' => 'MT',
                'html' => '&#x1f1f2;&#x1f1f9;',
                'name' => 'Malta',
                'emoji' => '🇲🇹',
            ],
            'MU' => [
                'code' => 'MU',
                'html' => '&#x1f1f2;&#x1f1fa;',
                'name' => 'Mauritius',
                'emoji' => '🇲🇺',
            ],
            'MV' => [
                'code' => 'MV',
                'html' => '&#x1f1f2;&#x1f1fb;',
                'name' => 'Maldives',
                'emoji' => '🇲🇻',
            ],
            'MW' => [
                'code' => 'MW',
                'html' => '&#x1f1f2;&#x1f1fc;',
                'name' => 'Malawi',
                'emoji' => '🇲🇼',
            ],
            'MX' => [
                'code' => 'MX',
                'html' => '&#x1f1f2;&#x1f1fd;',
                'name' => 'Mexico',
                'emoji' => '🇲🇽',
            ],
            'MY' => [
                'code' => 'MY',
                'html' => '&#x1f1f2;&#x1f1fe;',
                'name' => 'Malaysia',
                'emoji' => '🇲🇾',
            ],
            'MZ' => [
                'code' => 'MZ',
                'html' => '&#x1f1f2;&#x1f1ff;',
                'name' => 'Mozambique',
                'emoji' => '🇲🇿',
            ],
            'NA' => [
                'code' => 'NA',
                'html' => '&#x1f1f3;&#x1f1e6;',
                'name' => 'Namibia',
                'emoji' => '🇳🇦',
            ],
            'NC' => [
                'code' => 'NC',
                'html' => '&#x1f1f3;&#x1f1e8;',
                'name' => 'New Caledonia',
                'emoji' => '🇳🇨',
            ],
            'NE' => [
                'code' => 'NE',
                'html' => '&#x1f1f3;&#x1f1ea;',
                'name' => 'Niger',
                'emoji' => '🇳🇪',
            ],
            'NF' => [
                'code' => 'NF',
                'html' => '&#x1f1f3;&#x1f1eb;',
                'name' => 'Norfolk Island',
                'emoji' => '🇳🇫',
            ],
            'NG' => [
                'code' => 'NG',
                'html' => '&#x1f1f3;&#x1f1ec;',
                'name' => 'Nigeria',
                'emoji' => '🇳🇬',
            ],
            'NI' => [
                'code' => 'NI',
                'html' => '&#x1f1f3;&#x1f1ee;',
                'name' => 'Nicaragua',
                'emoji' => '🇳🇮',
            ],
            'NL' => [
                'code' => 'NL',
                'html' => '&#x1f1f3;&#x1f1f1;',
                'name' => 'Netherlands',
                'emoji' => '🇳🇱',
            ],
            'NO' => [
                'code' => 'NO',
                'html' => '&#x1f1f3;&#x1f1f4;',
                'name' => 'Norway',
                'emoji' => '🇳🇴',
            ],
            'NP' => [
                'code' => 'NP',
                'html' => '&#x1f1f3;&#x1f1f5;',
                'name' => 'Nepal',
                'emoji' => '🇳🇵',
            ],
            'NR' => [
                'code' => 'NR',
                'html' => '&#x1f1f3;&#x1f1f7;',
                'name' => 'Nauru',
                'emoji' => '🇳🇷',
            ],
            'NU' => [
                'code' => 'NU',
                'html' => '&#x1f1f3;&#x1f1fa;',
                'name' => 'Niue',
                'emoji' => '🇳🇺',
            ],
            'NZ' => [
                'code' => 'NZ',
                'html' => '&#x1f1f3;&#x1f1ff;',
                'name' => 'New Zealand',
                'emoji' => '🇳🇿',
            ],
            'OM' => [
                'code' => 'OM',
                'html' => '&#x1f1f4;&#x1f1f2;',
                'name' => 'Oman',
                'emoji' => '🇴🇲',
            ],
            'PA' => [
                'code' => 'PA',
                'html' => '&#x1f1f5;&#x1f1e6;',
                'name' => 'Panama',
                'emoji' => '🇵🇦',
            ],
            'PE' => [
                'code' => 'PE',
                'html' => '&#x1f1f5;&#x1f1ea;',
                'name' => 'Peru',
                'emoji' => '🇵🇪',
            ],
            'PF' => [
                'code' => 'PF',
                'html' => '&#x1f1f5;&#x1f1eb;',
                'name' => 'French Polynesia',
                'emoji' => '🇵🇫',
            ],
            'PG' => [
                'code' => 'PG',
                'html' => '&#x1f1f5;&#x1f1ec;',
                'name' => 'Papua New Guinea',
                'emoji' => '🇵🇬',
            ],
            'PH' => [
                'code' => 'PH',
                'html' => '&#x1f1f5;&#x1f1ed;',
                'name' => 'Philippines',
                'emoji' => '🇵🇭',
            ],
            'PK' => [
                'code' => 'PK',
                'html' => '&#x1f1f5;&#x1f1f0;',
                'name' => 'Pakistan',
                'emoji' => '🇵🇰',
            ],
            'PL' => [
                'code' => 'PL',
                'html' => '&#x1f1f5;&#x1f1f1;',
                'name' => 'Poland',
                'emoji' => '🇵🇱',
            ],
            'PM' => [
                'code' => 'PM',
                'html' => '&#x1f1f5;&#x1f1f2;',
                'name' => 'St. Pierre & Miquelon',
                'emoji' => '🇵🇲',
            ],
            'PN' => [
                'code' => 'PN',
                'html' => '&#x1f1f5;&#x1f1f3;',
                'name' => 'Pitcairn Islands',
                'emoji' => '🇵🇳',
            ],
            'PR' => [
                'code' => 'PR',
                'html' => '&#x1f1f5;&#x1f1f7;',
                'name' => 'Puerto Rico',
                'emoji' => '🇵🇷',
            ],
            'PS' => [
                'code' => 'PS',
                'html' => '&#x1f1f5;&#x1f1f8;',
                'name' => 'Palestinian Territories',
                'emoji' => '🇵🇸',
            ],
            'PT' => [
                'code' => 'PT',
                'html' => '&#x1f1f5;&#x1f1f9;',
                'name' => 'Portugal',
                'emoji' => '🇵🇹',
            ],
            'PW' => [
                'code' => 'PW',
                'html' => '&#x1f1f5;&#x1f1fc;',
                'name' => 'Palau',
                'emoji' => '🇵🇼',
            ],
            'PY' => [
                'code' => 'PY',
                'html' => '&#x1f1f5;&#x1f1fe;',
                'name' => 'Paraguay',
                'emoji' => '🇵🇾',
            ],
            'QA' => [
                'code' => 'QA',
                'html' => 'U+1F1F6 &#x1f1e6;',
                'name' => 'Qatar',
                'emoji' => '🇶🇦',
            ],
            'RE' => [
                'code' => 'RE',
                'html' => 'U+1F1F7&#x1f1ea;',
                'name' => 'Réunion',
                'emoji' => '🇷🇪',
            ],
            'RO' => [
                'code' => 'RO',
                'html' => 'U+1F1F7&#x1f1f4;',
                'name' => 'Romania',
                'emoji' => '🇷🇴',
            ],
            'RS' => [
                'code' => 'RS',
                'html' => 'U+1F1F7&#x1f1f8;',
                'name' => 'Serbia',
                'emoji' => '🇷🇸',
            ],
            'RU' => [
                'code' => 'RU',
                'html' => 'U+1F1F7 &#x1f1fa;',
                'name' => 'Russia',
                'emoji' => '🇷🇺',
            ],
            'RW' => [
                'code' => 'RW',
                'html' => 'U+1F1F7&#x1f1fc;',
                'name' => 'Rwanda',
                'emoji' => '🇷🇼',
            ],
            'SA' => [
                'code' => 'SA',
                'html' => '&#x1f1f8;&#x1f1e6;',
                'name' => 'Saudi Arabia',
                'emoji' => '🇸🇦',
            ],
            'SB' => [
                'code' => 'SB',
                'html' => '&#x1f1f8;&#x1f1e7;',
                'name' => 'Solomon Islands',
                'emoji' => '🇸🇧',
            ],
            'SC' => [
                'code' => 'SC',
                'html' => '&#x1f1f8;&#x1f1e8;',
                'name' => 'Seychelles',
                'emoji' => '🇸🇨',
            ],
            'SD' => [
                'code' => 'SD',
                'html' => '&#x1f1f8;&#x1f1e9;',
                'name' => 'Sudan',
                'emoji' => '🇸🇩',
            ],
            'SE' => [
                'code' => 'SE',
                'html' => '&#x1f1f8;&#x1f1ea;',
                'name' => 'Sweden',
                'emoji' => '🇸🇪',
            ],
            'SG' => [
                'code' => 'SG',
                'html' => '&#x1f1f8;&#x1f1ec;',
                'name' => 'Singapore',
                'emoji' => '🇸🇬',
            ],
            'SH' => [
                'code' => 'SH',
                'html' => '&#x1f1f8;&#x1f1ed;',
                'name' => 'St. Helena',
                'emoji' => '🇸🇭',
            ],
            'SI' => [
                'code' => 'SI',
                'html' => '&#x1f1f8;&#x1f1ee;',
                'name' => 'Slovenia',
                'emoji' => '🇸🇮',
            ],
            'SJ' => [
                'code' => 'SJ',
                'html' => '&#x1f1f8;&#x1f1ef;',
                'name' => 'Svalbard & Jan Mayen',
                'emoji' => '🇸🇯',
            ],
            'SK' => [
                'code' => 'SK',
                'html' => '&#x1f1f8;&#x1f1f0;',
                'name' => 'Slovakia',
                'emoji' => '🇸🇰',
            ],
            'SL' => [
                'code' => 'SL',
                'html' => '&#x1f1f8;&#x1f1f1;',
                'name' => 'Sierra Leone',
                'emoji' => '🇸🇱',
            ],
            'SM' => [
                'code' => 'SM',
                'html' => '&#x1f1f8;&#x1f1f2;',
                'name' => 'San Marino',
                'emoji' => '🇸🇲',
            ],
            'SN' => [
                'code' => 'SN',
                'html' => '&#x1f1f8;&#x1f1f3;',
                'name' => 'Senegal',
                'emoji' => '🇸🇳',
            ],
            'SO' => [
                'code' => 'SO',
                'html' => '&#x1f1f8;&#x1f1f4;',
                'name' => 'Somalia',
                'emoji' => '🇸🇴',
            ],
            'SR' => [
                'code' => 'SR',
                'html' => '&#x1f1f8;&#x1f1f7;',
                'name' => 'Suriname',
                'emoji' => '🇸🇷',
            ],
            'SS' => [
                'code' => 'SS',
                'html' => '&#x1f1f8;&#x1f1f8;',
                'name' => 'South Sudan',
                'emoji' => '🇸🇸',
            ],
            'ST' => [
                'code' => 'ST',
                'html' => '&#x1f1f8;&#x1f1f9;',
                'name' => 'São Tomé & Príncipe',
                'emoji' => '🇸🇹',
            ],
            'SV' => [
                'code' => 'SV',
                'html' => '&#x1f1f8;&#x1f1fb;',
                'name' => 'El Salvador',
                'emoji' => '🇸🇻',
            ],
            'SX' => [
                'code' => 'SX',
                'html' => '&#x1f1f8;&#x1f1fd;',
                'name' => 'Sint Maarten',
                'emoji' => '🇸🇽',
            ],
            'SY' => [
                'code' => 'SY',
                'html' => '&#x1f1f8;&#x1f1fe;',
                'name' => 'Syria',
                'emoji' => '🇸🇾',
            ],
            'SZ' => [
                'code' => 'SZ',
                'html' => '&#x1f1f8;&#x1f1ff;',
                'name' => 'Swaziland',
                'emoji' => '🇸🇿',
            ],
            'TA' => [
                'code' => 'TA',
                'html' => '&#x1f1f9;&#x1f1e6;',
                'name' => 'Tristan da Cunha',
                'emoji' => '🇹🇦',
            ],
            'TC' => [
                'code' => 'TC',
                'html' => '&#x1f1f9;&#x1f1e8;',
                'name' => 'Turks & Caicos Islands',
                'emoji' => '🇹🇨',
            ],
            'TD' => [
                'code' => 'TD',
                'html' => '&#x1f1f9;&#x1f1e9;',
                'name' => 'Chad',
                'emoji' => '🇹🇩',
            ],
            'TF' => [
                'code' => 'TF',
                'html' => '&#x1f1f9;&#x1f1eb;',
                'name' => 'French Southern Territories',
                'emoji' => '🇹🇫',
            ],
            'TG' => [
                'code' => 'TG',
                'html' => '&#x1f1f9;&#x1f1ec;',
                'name' => 'Togo',
                'emoji' => '🇹🇬',
            ],
            'TH' => [
                'code' => 'TH',
                'html' => '&#x1f1f9;&#x1f1ed;',
                'name' => 'Thailand',
                'emoji' => '🇹🇭',
            ],
            'TJ' => [
                'code' => 'TJ',
                'html' => '&#x1f1f9;&#x1f1ef;',
                'name' => 'Tajikistan',
                'emoji' => '🇹🇯',
            ],
            'TK' => [
                'code' => 'TK',
                'html' => '&#x1f1f9;&#x1f1f0;',
                'name' => 'Tokelau',
                'emoji' => '🇹🇰',
            ],
            'TL' => [
                'code' => 'TL',
                'html' => '&#x1f1f9;&#x1f1f1;',
                'name' => 'Timor-Leste',
                'emoji' => '🇹🇱',
            ],
            'TM' => [
                'code' => 'TM',
                'html' => '&#x1f1f9;&#x1f1f2;',
                'name' => 'Turkmenistan',
                'emoji' => '🇹🇲',
            ],
            'TN' => [
                'code' => 'TN',
                'html' => '&#x1f1f9;&#x1f1f3;',
                'name' => 'Tunisia',
                'emoji' => '🇹🇳',
            ],
            'TO' => [
                'code' => 'TO',
                'html' => '&#x1f1f9;&#x1f1f4;',
                'name' => 'Tonga',
                'emoji' => '🇹🇴',
            ],
            'TR' => [
                'code' => 'TR',
                'html' => '&#x1f1f9;&#x1f1f7;',
                'name' => 'Turkey',
                'emoji' => '🇹🇷',
            ],
            'TT' => [
                'code' => 'TT',
                'html' => '&#x1f1f9;&#x1f1f9;',
                'name' => 'Trinidad & Tobago',
                'emoji' => '🇹🇹',
            ],
            'TV' => [
                'code' => 'TV',
                'html' => '&#x1f1f9;&#x1f1fb;',
                'name' => 'Tuvalu',
                'emoji' => '🇹🇻',
            ],
            'TW' => [
                'code' => 'TW',
                'html' => '&#x1f1f9;&#x1f1fc;',
                'name' => 'Taiwan',
                'emoji' => '🇹🇼',
            ],
            'TZ' => [
                'code' => 'TZ',
                'html' => '&#x1f1f9;&#x1f1ff;',
                'name' => 'Tanzania',
                'emoji' => '🇹🇿',
            ],
            'UA' => [
                'code' => 'UA',
                'html' => '&#x1f1fa;&#x1f1e6;',
                'name' => 'Ukraine',
                'emoji' => '🇺🇦',
            ],
            'UG' => [
                'code' => 'UG',
                'html' => '&#x1f1fa;&#x1f1ec;',
                'name' => 'Uganda',
                'emoji' => '🇺🇬',
            ],
            'UM' => [
                'code' => 'UM',
                'html' => '&#x1f1fa;&#x1f1f2;',
                'name' => 'U.S. Outlying Islands',
                'emoji' => '🇺🇲',
            ],
            'UN' => [
                'code' => 'UN',
                'html' => '&#x1f1fa;&#x1f1f3;',
                'name' => 'United Nations',
                'emoji' => '🇺🇳',
            ],
            'US' => [
                'code' => 'US',
                'html' => '&#x1f1fa;&#x1f1f8;',
                'name' => 'United States',
                'emoji' => '🇺🇸',
            ],
            'UY' => [
                'code' => 'UY',
                'html' => '&#x1f1fa;&#x1f1fe;',
                'name' => 'Uruguay',
                'emoji' => '🇺🇾',
            ],
            'UZ' => [
                'code' => 'UZ',
                'html' => '&#x1f1fa;&#x1f1ff;',
                'name' => 'Uzbekistan',
                'emoji' => '🇺🇿',
            ],
            'VA' => [
                'code' => 'VA',
                'html' => '&#x1f1fb;&#x1f1e6;',
                'name' => 'Vatican City',
                'emoji' => '🇻🇦',
            ],
            'VC' => [
                'code' => 'VC',
                'html' => '&#x1f1fb;&#x1f1e8;',
                'name' => 'St. Vincent & Grenadines',
                'emoji' => '🇻🇨',
            ],
            'VE' => [
                'code' => 'VE',
                'html' => '&#x1f1fb;&#x1f1ea;',
                'name' => 'Venezuela',
                'emoji' => '🇻🇪',
            ],
            'VG' => [
                'code' => 'VG',
                'html' => '&#x1f1fb;&#x1f1ec;',
                'name' => 'British Virgin Islands',
                'emoji' => '🇻🇬',
            ],
            'VI' => [
                'code' => 'VI',
                'html' => '&#x1f1fb;&#x1f1ee;',
                'name' => 'U.S. Virgin Islands',
                'emoji' => '🇻🇮',
            ],
            'VN' => [
                'code' => 'VN',
                'html' => '&#x1f1fb;&#x1f1f3;',
                'name' => 'Vietnam',
                'emoji' => '🇻🇳',
            ],
            'VU' => [
                'code' => 'VU',
                'html' => '&#x1f1fb;&#x1f1fa;',
                'name' => 'Vanuatu',
                'emoji' => '🇻🇺',
            ],
            'WF' => [
                'code' => 'WF',
                'html' => '&#x1f1fc;&#x1f1eb;',
                'name' => 'Wallis & Futuna',
                'emoji' => '🇼🇫',
            ],
            'WS' => [
                'code' => 'WS',
                'html' => '&#x1f1fc;&#x1f1f8;',
                'name' => 'Samoa',
                'emoji' => '🇼🇸',
            ],
            'XK' => [
                'code' => 'XK',
                'html' => '&#x1f1fd;&#x1f1f0;',
                'name' => 'Kosovo',
                'emoji' => '🇽🇰',
            ],
            'YE' => [
                'code' => 'YE',
                'html' => '&#x1f1fe;&#x1f1ea;',
                'name' => 'Yemen',
                'emoji' => '🇾🇪',
            ],
            'YT' => [
                'code' => 'YT',
                'html' => '&#x1f1fe;&#x1f1f9;',
                'name' => 'Mayotte',
                'emoji' => '🇾🇹',
            ],
            'ZA' => [
                'code' => 'ZA',
                'html' => '&#x1f1ff;&#x1f1e6;',
                'name' => 'South Africa',
                'emoji' => '🇿🇦',
            ],
            'ZM' => [
                'code' => 'ZM',
                'html' => '&#x1f1ff;&#x1f1f2;',
                'name' => 'Zambia',
                'emoji' => '🇿🇲',
            ],
            'ZW' => [
                'code' => 'ZW',
                'html' => '&#x1f1ff;&#x1f1fc;',
                'name' => 'Zimbabwe',
                'emoji' => '🇿🇼',
            ],
        ];

    public static function getFlagOrCountryCode(string $countryCode): string
    {
        $countryCode = $countryCode === 'en' ? 'GB' : $countryCode;

        return \array_key_exists(mb_strtoupper($countryCode), self::$countryData) ? self::$countryData[mb_strtoupper($countryCode)]['html'] : $countryCode;
    }

    public static function getEmojiFlag(string $countryCode): string
    {
        $countryCode = $countryCode === 'en' ? 'GB' : $countryCode;

        return self::$countryData[mb_strtoupper($countryCode)]['emoji'];
    }
}
