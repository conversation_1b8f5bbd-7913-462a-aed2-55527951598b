<?php

declare(strict_types=1);

namespace App\Helpers;

use App\Models\Artisan;
use App\Models\Atelier;
use App\Models\InformationFacturation;
use App\Models\Lieu;
use Illuminate\Database\Eloquent\Model;

class OnboardingValidation
{
    /**
     * @SuppressWarnings(PHPMD.ElseExpression)
     *
     * @return array<string, string>
     */
    public function customInformationsFacturationsFieldsErrors(?InformationFacturation $model, ?string $index = null): array
    {
        $errors = [];
        if (empty($model?->numero_tva) && $model?->taux_tva > 0) {
            if (\is_string($index)) {
                $errors[$index.'taux_tva]'] = "Le numéro de TVA doit être renseigné si l'artisan y est assujetti.";
            } else {
                $errors['taux_tva'] = "Le numéro de TVA doit être renseigné si l'artisan y est assujetti.";
            }
        }

        return $errors;
    }

    /**
     * @SuppressWarnings(PHPMD.ElseExpression)
     *
     * @return array<string, string>
     */
    public function customAtelierFieldsErrors(?Atelier $model, ?string $index = null): array
    {
        $errors = [];
        if ($model?->nb_pers_min > $model?->nb_pers_max) {
            if (\is_string($index)) {
                $errors[$index.'capacity]'] = "La capacité d'accueil minimale de l'atelier doit être inférieur à la capacité maximum.";
            } else {
                $errors['capacity'] = "La capacité d'accueil minimale de l'atelier doit être inférieur à la capacité maximum.";
            }
        }

        if ($model?->nb_heures == 0 && $model?->nb_minutes == 0) {
            if (\is_string($index)) {
                $errors[$index.'duration]'] = "La durée de l'atelier ne peut être de 0 heures 0 minutes.";
            } else {
                $errors['duration'] = "La durée de l'atelier ne peut être de 0 heures 0 minutes.";
            }
        }

        return $errors;
    }

    /**
     * @SuppressWarnings(PHPMD.ElseExpression)
     *
     * @param string[] $fieldList
     *
     * @return array<int|string, string>
     */
    public function requiredFieldsErrors(array $fieldList, ?Model $model, ?string $index = null): array
    {
        $errors = [];
        foreach ($fieldList as $field) {
            if ($model?->hasAttribute($field) === false) {
                if (\is_string($index)) {
                    $errors[$index.$field.']'] = 'Le champ '.$field.' ne doit pas être vide.';
                } else {
                    $errors[$field] = 'Le champ '.$field.' ne doit pas être vide.';
                }
            }
        }

        return $errors;
    }

    /**
     * @param string[] $adress
     *
     * @return array<string, string>
     */
    public function checkAdress(array $adress, Lieu $lieu): array
    {
        $errors = ['lieu[google_adress]' => ''];
        foreach ($adress as $field) {
            if (!$lieu->hasAttribute($field)) {
                $errors['lieu[google_adress]'] .= ($errors['lieu[google_adress]'] == '') ? $field : ', '.$field;
            }
        }

        if ($errors['lieu[google_adress]'] != '') {
            $errors['lieu[google_adress]'] .= ' ne doit(vent) pas être vide(s).';

            return $errors;
        }

        return [];
    }

    /**
     * @SuppressWarnings(PHPMD.LongVariable)
     * @SuppressWarnings(PHPMD.ElseExpression)
     *
     * @return mixed[]
     */
    public function isArtisanOnboardingComplete(Artisan $artisan): array
    {
        $errors = [];
        $artisanCheck = ['nom', 'prenom', 'email', 'phone', 'cgucgv_accepted', 'metier_id', 'metier_nom', 'craft_id'];
        $lieuCheck = ['lat', 'lng', 'ville_id'];
        $adresseCheck = ['adresse1', 'adresse_ville', 'code_postal', 'pays'];
        $informationsFacturationCheck = ['raison_sociale', 'siren', 'adresse1', 'code_postal', 'ville', 'pays', 'taux_tva'];
        if ($artisan->getCountryCode() === 'gb') {
            $ribCheck = ['holder', 'account_number', 'sort_code'];
        } else {
            $ribCheck = ['holder', 'iban', 'bic'];
        }
        $atelierCheck = ['nom', 'nb_pers_min', 'nb_pers_max', 'nb_heures', 'nb_minutes', 'nb_jours', 'prix', 'craft_id'];
        $rib = $artisan->rib;
        $informationsFacturation = $artisan->informations_facturation;
        $lieu = $artisan->mainLocation();
        $ateliers = $artisan->ateliers;

        $artisanErrors = $this->requiredFieldsErrors($artisanCheck, $artisan, 'artisan[');
        $customArtisanErrors = [];
        $lieuErrors = $this->requiredFieldsErrors($lieuCheck, $lieu, 'lieu[');
        $adresseErrors = $this->checkAdress($adresseCheck, $lieu);
        $informationsFacturationErrors = $this->requiredFieldsErrors($informationsFacturationCheck, $informationsFacturation, 'informations_facturations[');
        $customInformationsFacturationErrors = $this->customInformationsFacturationsFieldsErrors($informationsFacturation, 'informations_facturations[');
        $ribErrors = $this->requiredFieldsErrors($ribCheck, $rib, 'ribs[');
        foreach ($ateliers as $i => $atelier) {
            $atelierErrors = $this->requiredFieldsErrors($atelierCheck, $atelier, 'atelier['.$i.'][');
            $customAteliersErrors = $this->customAtelierFieldsErrors($atelier, 'atelier['.$i.'][');
            $errors = array_merge($errors, $atelierErrors, $customAteliersErrors);
        }

        return array_merge($errors, $artisanErrors, $lieuErrors, $informationsFacturationErrors, $ribErrors, $customArtisanErrors, $customInformationsFacturationErrors, $adresseErrors);
    }
}
