<?php

declare(strict_types=1);

namespace App\Helpers;

class FunnelBreadcrumbsHelper
{
    /**
     * @var bool
     */
    final public const DEFAULT_SHOULD_PAY = true;

    /**
     * @var bool
     */
    final public const DEFAULT_IS_AUTH = false;

    /**
     * @var string
     */
    final public const DEFAULT_STEP_TITLE = 'default step title';

    /**
     * @return array{shouldPay: bool, isAuth: bool, stepTitle: string}
     */
    public static function getDefaultParameters(): array
    {
        return [
            'shouldPay' => self::DEFAULT_SHOULD_PAY,
            'isAuth' => self::DEFAULT_IS_AUTH,
            'stepTitle' => self::DEFAULT_STEP_TITLE,
        ];
    }

    /**
     * @SuppressWarnings(PHPMD.ElseExpression)
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     *
     * @return array<string, mixed>
     */
    public static function getBreadcrumbs(?string $route, bool $shouldPay = true, bool $showPrice = true): array
    {
        $parameters = self::getDefaultParameters();

        $steps = [];
        $position = 1;

        // steps
        $steps['resa'] = ['isCurrent' => false, 'position' => $position++, 'title' => __('front/breadcrumbs.funnel.title.booking')];

        $steps['auth'] = ['isCurrent' => false, 'position' => $position++, 'title' => __('front/breadcrumbs.funnel.title.authentication')];
        if (!$shouldPay) {
            $parameters['shouldPay'] = $shouldPay;
            $steps['paiement'] = ['isCurrent' => false, 'position' => $position++, 'title' => __('front/breadcrumbs.funnel.title.payment_null')];
        } else {
            $steps['facturation'] = ['isCurrent' => false, 'position' => $position++, 'title' => __('front/breadcrumbs.funnel.title.billing')];
            $steps['paiement'] = ['isCurrent' => false, 'position' => $position++, 'title' => __('front/breadcrumbs.funnel.title.payment')];
        }

        // define parameters regarding route
        switch ($route) {
            case localizedRouteName('reservation'):
                if (!$showPrice) {
                    $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.use_gift');
                } else {
                    $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.order');
                }

                $steps['resa']['isCurrent'] = true;
                break;
            case localizedRouteName('facturation'):
                $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.billing');
                $steps['facturation']['isCurrent'] = true;
                break;
            case localizedRouteName('paiement'):
                $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.finalization');
                $steps['paiement']['isCurrent'] = true;
                break;
            case localizedRouteName('gift-card-value.show'):
                $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.gift');
                $steps['resa']['isCurrent'] = true;
                break;
            case localizedRouteName('gift-card-ticket.show'):
                $parameters['stepTitle'] = __('front/breadcrumbs.funnel.step.offered_workshop');
                $steps['resa']['isCurrent'] = true;
                break;
            default:
                $parameters['stepTitle'] = self::DEFAULT_STEP_TITLE;
                break;
        }

        $parameters['steps'] = $steps;

        return $parameters;
    }
}
