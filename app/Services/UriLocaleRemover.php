<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Http\Request;

readonly class UriLocaleRemover
{
    public function fromRequest(Request $request): string
    {
        $pathSegments = $request->segments();
        if ($pathSegments !== [] && $pathSegments[0] === config('app.locale')) {
            array_splice($pathSegments, 0, 1);
        }

        return implode('/', $pathSegments);
    }
}
