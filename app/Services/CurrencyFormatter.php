<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\Currency;
use App\Shared\Amount;

class CurrencyFormatter
{
    /** @SuppressWarnings(PHPMD.BooleanArgumentFlag) */
    public function format(
        int|float|string|Amount|null $number,
        string|Currency|null $currency = 'EUR',
        ?string $locale = null,
        bool $withDecimal = true
    ): string {
        $currency = $this->getCurrency($number, $currency);
        $number = $this->getNumber($number);

        $fmt = new \NumberFormatter($locale ?? app()->getLocale(), \NumberFormatter::CURRENCY);
        if (!$withDecimal) {
            $fmt->setAttribute(\NumberFormatter::MIN_FRACTION_DIGITS, 0);
        }

        $currencyString = $fmt->formatCurrency($number, $currency);

        if ($currencyString === false) {
            return '';
        }

        return $currencyString;
    }

    public function getCurrencySymbol(Currency|string $currency, ?string $locale = null): string
    {
        $currency = $currency instanceof Currency ? $currency->value : $currency;

        $fmt = new \NumberFormatter(($locale ?? app()->getLocale())."@currency=$currency", \NumberFormatter::CURRENCY);

        return $fmt->getSymbol(\NumberFormatter::CURRENCY_SYMBOL);
    }

    private function getCurrency(float|Amount|int|string|null $number, string|Currency|null $currency): mixed
    {
        if ($number instanceof Amount) {
            $currency = $number->currency()->value;
        } elseif ($currency === null) {
            $currency = config('global.default_currency');
        } elseif ($currency instanceof Currency) {
            $currency = $currency->value;
        }

        return $currency;
    }

    private function getNumber(float|Amount|int|string|null $number): int|float
    {
        if ($number instanceof Amount) {
            $number = $number->floatValue();
        } elseif (\is_string($number)) {
            $number = (float) $number;
        } elseif ($number === null) {
            $number = 0;
        }

        return $number;
    }
}
