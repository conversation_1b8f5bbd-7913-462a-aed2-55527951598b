<?php

declare(strict_types=1);

namespace App\Services;

use App\Domain\Gift\Models\GiftCard;
use App\Models\Parrainage\CodeParrainageInvitation;
use App\Models\Parrainage\CodeParrainageUser;
use App\Models\Reductions\CadeauBox;
use App\Models\Reductions\CodeReductionGlobal;
use App\Models\Reductions\CodeReductionUser;

class DatabaseCodeUniquenessChecker
{
    public function isUnique(string $code): bool
    {
        return GiftCard::withTrashed()->where('code', $code)->orWhere('secretid', $code)->doesntExist()
            && CadeauBox::withTrashed()->where('code_reduction', $code)->doesntExist()
            && CodeReductionGlobal::withTrashed()->where('code_reduction', $code)->doesntExist()
            && CodeReductionUser::withTrashed()->where('code_reduction', $code)->doesntExist()
            && CodeParrainageUser::withTrashed()->where('code_reduction', $code)->doesntExist()
            && CodeParrainageInvitation::withTrashed()->where('code_reduction', $code)->doesntExist();
    }
}
