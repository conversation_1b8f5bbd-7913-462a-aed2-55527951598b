<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Artisan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ArtisanSelected
{
    final public const SESSION_KEY = 'usersartisans.artisan.selected';

    protected Request $request;

    public function set(Request $request, Artisan $artisan): self
    {
        Session::put(self::SESSION_KEY, $artisan->getKey());

        $this->request = $request;
        $this->request->attributes->set('artisanSelected', $artisan);

        return $this;
    }

    public function get(): Artisan
    {
        if (!isset($this->request)) {
            throw new \RuntimeException('Request not set.');
        }

        return $this->request->attributes->get('artisanSelected');
    }

    public function getId(): int
    {
        return (int) Session::get(self::SESSION_KEY);
    }

    public function has(): bool
    {
        return Session::has(static::SESSION_KEY);
    }
}
