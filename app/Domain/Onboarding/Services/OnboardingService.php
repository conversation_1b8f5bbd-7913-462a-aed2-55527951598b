<?php

namespace App\Domain\Onboarding\Services;

use App\Domain\Content\ContentService;
use App\Domain\Location\Exceptions\LocationIsCurrentlyUsedException;
use App\Domain\Location\LocationRemover;
use App\Domain\Onboarding\Enums\OnboardingStatus;
use App\Domain\Onboarding\Exceptions\CancelOnboardingForbidden;
use App\Domain\Onboarding\Exceptions\RemoveWorkshopFromOnboarding;
use App\Domain\Onboarding\Models\Onboarding;
use App\Domain\UserArtisan\UserArtisanRemover;
use App\Models\Artisan;
use App\Models\Atelier as Workshop;
use Illuminate\Support\Facades\DB;

class OnboardingService
{
    public static function cancelOnboarding(Artisan $artisan): void
    {
        /** @var UserArtisanRemover $userArtisanRemover */
        $userArtisanRemover = \App::make(UserArtisanRemover::class);

        try {
            if ($artisan->online_at !== null) {
                throw (new CancelOnboardingForbidden())->artisanAlreadyOnline($artisan);
            }

            DB::beginTransaction();

            if (null !== ($onboarding = $artisan->onboarding)) {
                static::removeOnboarding($onboarding);
            }

            $anonymizedEmail = uniqid('', true).'@deleted.com';

            $artisan->rib()->delete();
            $artisan->informations_facturation()->delete();
            $artisan->code_reduction_global()->delete();
            $artisan->langs()->sync([]);

            foreach ($artisan->ateliers as $workshop) {
                // removeContentPage to false because contentPages are already deleted before in removeOnboarding
                static::removeWorkshopFromOnboarding($workshop, false);
            }

            // We don't delete the location if the location is linked to other artisan or workshops not linked to this artisan
            // As we delete workshops linked to the artisan before, here we get only workshops linked to the same location but not the same artisan
            foreach ($artisan->locations as $location) {
                $artisan->locations()->detach($location);
                try {
                    \App::make(LocationRemover::class)->removeLocation($location);
                } catch (LocationIsCurrentlyUsedException) {
                    // do nothing and continue
                } catch (\Throwable $exception) {
                    \Log::error('An error occurred while trying to remove location', [
                        'onboarding' => $onboarding->id,
                        'location' => $location->id,
                        'exception' => $exception,
                    ]);
                }
            }

            $artisan->usersartisans
                ->where('email', $artisan->email)
                ->each(function ($userArtisan) use ($userArtisanRemover, $anonymizedEmail): void {
                    $userArtisanRemover->delete($userArtisan, $anonymizedEmail);
                });

            $artisan->translations()->delete();

            $artisan->email = $anonymizedEmail;
            $artisan->save();
            $artisan->delete();

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            report($exception);
            throw $exception;
        }
    }

    public static function removeOnboarding(Onboarding $onboarding): void
    {
        foreach ($onboarding->contentPages as $contentPage) {
            ContentService::deletePage($contentPage);
        }

        if (null !== $onboarding->contentMedia) {
            ContentService::deleteMedia($onboarding->contentMedia);
        }

        $onboarding->delete();
    }

    public static function removeWorkshopFromOnboarding(Workshop $workshop, bool $removeContentPage = true): void
    {
        if ($workshop->online_at !== null) {
            throw (new RemoveWorkshopFromOnboarding())->workshopAlreadyOnline($workshop);
        }

        if ($workshop->active || $workshop->isVisible()) {
            throw (new RemoveWorkshopFromOnboarding())->workshopIsActiveOrVisible($workshop);
        }

        if ($workshop->reservations->count() > 0) {
            throw (new RemoveWorkshopFromOnboarding())->workshopHasBookings($workshop);
        }

        try {
            DB::beginTransaction();

            if ($removeContentPage && $workshop->contentPage) {
                ContentService::deletePage($workshop->contentPage);
            }

            $workshop->translations()->delete();
            $workshop->evenements()->delete();
            $workshop->comments()->delete();
            $workshop->images()->delete();
            $workshop->techniques()->sync([]);
            $workshop->creations()->sync([]);
            $workshop->tags()->sync([]);
            $workshop->delete();

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            report($exception);
            throw $exception;
        }
    }

    public static function launchOnboarding(Onboarding $onboarding): Onboarding
    {
        $onboarding->save();

        return $onboarding;
    }

    public static function validate(Onboarding $onboarding): Onboarding
    {
        $onboarding->step = config('onboarding.minifunnel_step');
        $onboarding->status = OnboardingStatus::StatusIsOnboard;
        $onboarding->onboarding_reminded_at = null;
        $onboarding->save();

        return $onboarding;
    }
}
