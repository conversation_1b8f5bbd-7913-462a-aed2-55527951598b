<?php

declare(strict_types=1);

namespace App\Domain\Workshop\MediaServices;

use App\Infrastructure\Auth\AuthFinder;
use App\Models\Atelier;

readonly class WorkshopMediaOrderer
{
    public function __construct(private AuthFinder $authFinder)
    {
    }

    /**
     * @param array<int, int|string> $imagesListOrder
     */
    public function updateImagesOrder(array $imagesListOrder, Atelier $workshop): void
    {
        $finalImages = [];
        $logImages = [];
        foreach ($imagesListOrder as $order => $imageId) {
            $oldImageOrder = $workshop->images->where('id', $imageId)->first()?->pivot->order; /* @phpstan-ignore-line property.notFound (pivot not found) */
            $url = $workshop->images->where('id', $imageId)->first()?->getUrl() ?? '';
            $finalImages[$imageId] = ['order' => $order + 1];
            if ($oldImageOrder !== $order + 1) {
                $logImages[] = [
                    'old_order' => $oldImageOrder,
                    'new_order' => $order + 1,
                    'url' => $url,
                ];
            }
        }
        $workshop->images()->sync($finalImages);

        $this->logActivity($workshop, $logImages);
    }

    /**
     * @param array<int, array<string, mixed>> $images
     */
    private function logActivity(Atelier $workshop, array $images): void
    {
        if (empty($images)) {
            return;
        }

        if (!$this->authFinder->isLoggedIn()) {
            return;
        }

        activity('media_management_log')
            ->on($workshop)
            ->by($this->authFinder->tryUser())
            ->withProperties(
                [
                    'images' => $images,
                    'type' => 'update_order',
                ],
            )
            ->log('Change image(s) order');
    }
}
