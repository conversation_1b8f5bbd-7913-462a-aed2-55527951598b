<?php

declare(strict_types=1);

namespace App\Domain\Workshop\Listeners;

use App\Domain\Workshop\Events\Artisan\ArtisanUpdated;
use App\Infrastructure\Auth\AuthFinder;
use Carbon\Carbon;

final class DeactivateArtisan
{
    public function __construct(private readonly AuthFinder $authFinder)
    {
    }

    public function handle(ArtisanUpdated $event): void
    {
        $artisan = $event->artisan;

        if ($artisan->isClean('active')
            || (
                $artisan->isDirty('active')
                && $artisan->active === true
            )
        ) {
            return;
        }

        activity('artisan_log')
            ->performedOn($artisan)
            ->causedBy($this->authFinder->tryAdmin())
            ->withProperties(['deactivate_at' => Carbon::now()])
            ->log('Artisan désactivé.');
    }
}
