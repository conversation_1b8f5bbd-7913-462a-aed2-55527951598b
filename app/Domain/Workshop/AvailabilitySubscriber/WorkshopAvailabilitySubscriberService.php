<?php

declare(strict_types=1);

namespace App\Domain\Workshop\AvailabilitySubscriber;

use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Booking\Enums\WorkshopAvailabilitySubscriberStatus;
use App\Domain\Booking\Enums\WorkshopAvailabilitySubscriberUnsubscribeOrigin;
use App\Mail\Guest\AvailabilitySubscriberSeatsAvailableMail;
use App\Mail\User\Formulaire\WorkshopAvailabilityEventSubscribedMail;
use App\Models\Atelier;
use App\Models\Reservation;
use App\Models\User;
use App\Models\WorkshopAvailabilitySubscriber;
use Carbon\Carbon;
use Illuminate\Contracts\Mail\Mailable;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
readonly class WorkshopAvailabilitySubscriberService
{
    public const SEND_MAX_MAIL = 5;

    public function notifyNewSeatsAvailableForWorkshop(Atelier $workshop): void
    {
        $workshop
            ->workshopAvailabilitySubscribers()
            ->where('status', WorkshopAvailabilitySubscriberStatus::ToProcess)
            ->lazyById(100)
            ->each(fn (WorkshopAvailabilitySubscriber $subscriber) => $this->notifyNewSeatAvailable($subscriber));
    }

    private function notifyNewSeatAvailable(WorkshopAvailabilitySubscriber $subscriber): void
    {
        $this->notifyAndExpireIfTooManyAttempts(
            $subscriber,
            (new AvailabilitySubscriberSeatsAvailableMail($subscriber->workshop, $subscriber->booking, subscriber: $subscriber))->country($subscriber->user?->country_code)
        );
    }

    public function notifyAndExpireIfTooManyAttempts(WorkshopAvailabilitySubscriber $subscriber, Mailable $mailable): WorkshopAvailabilitySubscriber
    {
        Mail::to($subscriber->email)
            ->locale($subscriber->locale)
            ->send($mailable);

        ++$subscriber->sent_mail_count;
        $subscriber->new_seat_last_reminded_at = Carbon::now();
        if ($subscriber->sent_mail_count >= self::SEND_MAX_MAIL) {
            $this->expire($subscriber, WorkshopAvailabilitySubscriberUnsubscribeOrigin::Automatically);
        }
        $subscriber->save();

        return $subscriber;
    }

    public function addFromBooking(Reservation $booking): void
    {
        $participant = $booking->getParticipant();

        if ($participant !== null) {
            $this->subscribe(
                $booking->evenement->atelier,
                $participant->email,
                $booking->est_cadeau,
                $participant,
                $booking->status === BookingStatus::BOOKING_STANDBY
                    ? $booking->id
                    : null
            );
        }
    }

    public function moveToOtherWorkshopFromBookingId(Reservation $originalBooking, Atelier $newWorkshop, WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin): void
    {
        if ($originalBooking->status === BookingStatus::BOOKING_CONFIRMED) {
            $this->processedByBooking($originalBooking, $processEndedOrigin);
        } elseif ($originalBooking->status === BookingStatus::BOOKING_STANDBY) {
            $participant = $originalBooking->getParticipant();
            $this->abandonedByBooking($originalBooking, $processEndedOrigin);
            $this->subscribe(
                $newWorkshop,
                $participant?->email ?? '',
                $originalBooking->est_cadeau,
                $participant,
                $originalBooking->status === BookingStatus::BOOKING_STANDBY
                    ? $originalBooking->id
                    : null
            );
        }
    }

    public function addToListAndNotify(Atelier $workshop, string $email, bool $isGift, ?User $user = null, ?int $bookingToReplaceId = null): WorkshopAvailabilitySubscriber
    {
        $subscriber = $this->subscribe($workshop, $email, $isGift, $user, $bookingToReplaceId);

        try {
            Mail::to($subscriber->email)
                ->locale($subscriber->locale)
                ->send((new WorkshopAvailabilityEventSubscribedMail($subscriber))->country($subscriber->country_code));
        } catch (\Exception $exception) {
            Log::error('Error while sending wait list email', [
                'subscriberId' => $subscriber->getKey(),
                'exception' => $exception,
            ]);
        }

        return $subscriber;
    }

    public function subscribe(Atelier $workshop, string $email, bool $isGift, ?User $user = null, ?int $bookingToReplaceId = null): WorkshopAvailabilitySubscriber
    {
        $subscriber = new WorkshopAvailabilitySubscriber();
        $subscriber->email = $email;
        $subscriber->workshop()->associate($workshop);
        $subscriber->for_gift = $isGift;
        $subscriber->locale = $user->locale ?? \App::getLocale();
        $subscriber->country_code = $user->country_code ?? config('app.country');
        $subscriber->status = WorkshopAvailabilitySubscriberStatus::ToProcess;
        $subscriber->timezone = getTimezone($workshop->timezone);
        $subscriber->booking_id = $bookingToReplaceId;
        $subscriber->user()->associate($user);
        $subscriber->save();

        return $subscriber;
    }

    private function setStatusByBooking(
        WorkshopAvailabilitySubscriberStatus $status,
        Reservation $booking,
        ?WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin = null,
        ?User $user = null
    ): void {
        WorkshopAvailabilitySubscriber::query()
            ->where('status', WorkshopAvailabilitySubscriberStatus::ToProcess)
            ->where('workshop_id', $booking->evenement->atelier_id)
            ->where(function (EloquentBuilder $query) use ($booking, $user): void {
                $query
                    ->where('booking_id', $booking->getKey())
                    ->orWhere('email', $user?->email ?? $booking->getUser()?->email);
            })
            ->eachById(function (WorkshopAvailabilitySubscriber $subscriber) use ($status, $processEndedOrigin): void {
                $this->unsubscribe($subscriber, $status, $processEndedOrigin);
            }, 100);
    }

    public function processedByBooking(
        Reservation $booking,
        ?WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin = null,
        ?User $user = null
    ): void {
        $this->setStatusByBooking(WorkshopAvailabilitySubscriberStatus::Processed, $booking, $processEndedOrigin, $user);
    }

    public function abandonedByBooking(
        Reservation $booking,
        WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin,
        ?User $user = null
    ): void {
        $this->setStatusByBooking(WorkshopAvailabilitySubscriberStatus::Abandoned, $booking, $processEndedOrigin, $user);
    }

    public function abandonedByWorkshop(Atelier $workshop, ?string $email = null, WorkshopAvailabilitySubscriberUnsubscribeOrigin $origin = WorkshopAvailabilitySubscriberUnsubscribeOrigin::Automatically): void
    {
        $workshop
            ->workshopAvailabilitySubscribers()
            ->when($email !== null, static fn (EloquentBuilder $builder) => $builder->where('email', $email))
            ->eachById(function (WorkshopAvailabilitySubscriber $subscriber) use ($origin): void {
                $this->abandon($subscriber, $origin);
            }, 100);
    }

    public function processByWorkshopAndEmail(Atelier $workshop, string $email): void
    {
        $workshop
            ->workshopAvailabilitySubscribers()
            ->where('email', $email)
            ->eachById(function (WorkshopAvailabilitySubscriber $subscriber): void {
                $this->processed($subscriber, WorkshopAvailabilitySubscriberUnsubscribeOrigin::Automatically);
            }, 100);
    }

    public function abandonOutdated(): void
    {
        WorkshopAvailabilitySubscriber::query()
            ->where('status', WorkshopAvailabilitySubscriberStatus::ToProcess)
            ->where('created_at', '<', Carbon::now()->subMonths(6))
            ->lazyById(200)
            ->each(function (WorkshopAvailabilitySubscriber $subscriber): void {
                $this->abandon($subscriber, WorkshopAvailabilitySubscriberUnsubscribeOrigin::Automatically);
            });
    }

    public function abandon(WorkshopAvailabilitySubscriber $subscriber, ?WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin = null): void
    {
        $this->unsubscribe(
            $subscriber,
            WorkshopAvailabilitySubscriberStatus::Abandoned,
            $processEndedOrigin
        );
    }

    public function expire(WorkshopAvailabilitySubscriber $subscriber, WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin): void
    {
        $this->unsubscribe(
            $subscriber,
            WorkshopAvailabilitySubscriberStatus::Expired,
            $processEndedOrigin
        );
    }

    public function processed(WorkshopAvailabilitySubscriber $subscriber, WorkshopAvailabilitySubscriberUnsubscribeOrigin $processEndedOrigin): void
    {
        $this->unsubscribe(
            $subscriber,
            WorkshopAvailabilitySubscriberStatus::Processed,
            $processEndedOrigin
        );
    }

    public function unsubscribe(
        WorkshopAvailabilitySubscriber $subscriber,
        WorkshopAvailabilitySubscriberStatus $status,
        ?WorkshopAvailabilitySubscriberUnsubscribeOrigin $origin = null
    ): void {
        $subscriber->status = $status;
        $subscriber->unsubscribed_origin = $origin;
        $subscriber->save();
    }
}
