<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Orders\ConsistencyChecks\Notifications;

use App\Domain\Accounting\Orders\ConsistencyChecks\ConsistencyError;
use App\Models\Commande;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\SlackMessage;

class OrderConsistencyErrorNotification extends Notification
{
    public function __construct(
        public readonly Commande $order,
        /** @var ConsistencyError[] $errors */
        public readonly array $errors,
    ) {
    }

    /**
     * @return string[]
     */
    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        $route = route('admin.commandes.show', $this->order);
        $invoiceLink = "<{$route}|{$this->order->id}>";

        return (new SlackMessage())
            ->text(implode("\n", [
                ":warning: Order No. {$invoiceLink} for *{$this->order->user_firstname} {$this->order->user_lastname} ({$this->order->user_email})* generated on {$this->order->created_at?->toDateTimeString()} contains the following consistency errors :",
                ...array_map(fn (ConsistencyError $error) => "- {$error->description()}", $this->errors),
            ]));
    }
}
