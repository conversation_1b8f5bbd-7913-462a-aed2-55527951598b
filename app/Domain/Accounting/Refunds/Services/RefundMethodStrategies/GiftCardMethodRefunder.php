<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Refunds\Services\RefundMethodStrategies;

use App\Domain\Accounting\Orders\Enums\PaymentMethodType;
use App\Domain\Accounting\Orders\Models\OrderItem;
use App\Domain\Accounting\Refunds\Enums\RefundType;
use App\Domain\Accounting\Refunds\Services\RefundAllocationCalculator;
use App\Domain\Accounting\Refunds\Types\RefundMethod;
use App\Domain\Accounting\Refunds\Types\RefundOption;
use App\Domain\Accounting\Refunds\Types\RefundPaymentMethod;
use App\Shared\Amount;
use Illuminate\Support\Collection;

final readonly class GiftCardMethodRefunder implements MethodRefunder
{
    public function __construct(
        private RefundAllocationCalculator $allocationCalculator,
    ) {
    }

    public function getRefundOption(OrderItem $orderItem, int $quantity): RefundOption
    {
        $paymentMethodsAllocation = $this->allocationCalculator->getRefundAllocationByPriority(
            Collection::make([
                PaymentMethodType::WorkshopGiftCard,
                PaymentMethodType::AmountGiftCard,
                PaymentMethodType::UserWallet,
                PaymentMethodType::GiftBox,
                PaymentMethodType::Stripe,
                PaymentMethodType::StripeBankTransfer,
            ]),
            $orderItem,
            $quantity,
        );

        $total = $paymentMethodsAllocation->reduce(fn (Amount $total, RefundPaymentMethod $refundPaymentMethod) => $total->add($refundPaymentMethod->amount), Amount::null($orderItem->currency));

        return new RefundOption(
            RefundType::GiftCard,
            $total,
            $quantity,
            Collection::make([
                new RefundMethod(RefundType::GiftCard, $total),
            ]),
            $paymentMethodsAllocation,
        );
    }
}
