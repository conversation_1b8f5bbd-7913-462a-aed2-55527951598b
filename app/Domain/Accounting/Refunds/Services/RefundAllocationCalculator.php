<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Refunds\Services;

use App\Domain\Accounting\Orders\Enums\PaymentMethodType;
use App\Domain\Accounting\Orders\Models\OrderDiscount;
use App\Domain\Accounting\Orders\Models\OrderItem;
use App\Domain\Accounting\Orders\Models\OrderPayment;
use App\Domain\Accounting\Refunds\Types\RefundPaymentMethod;
use App\Models\Commande;
use App\Shared\Amount;
use App\Shared\Percent;
use Illuminate\Support\Collection;

final readonly class RefundAllocationCalculator
{
    /**
     * @param Collection<int, PaymentMethodType> $priorities
     *
     * @return Collection<int, RefundPaymentMethod>
     */
    public function getRefundAllocationByPriority(Collection $priorities, OrderItem $orderItem, int $quantity): Collection
    {
        $totalAmountForItem = $orderItem->total_price;
        $amountForQuantity = $orderItem->unit_price->multiply($quantity);
        $maxRefundableAmount = $orderItem->getMaxRefundableAmount();

        $amountToRefund = Amount::min($totalAmountForItem, $maxRefundableAmount, $amountForQuantity);

        $notRefundedAmount = $totalAmountForItem->substract($amountToRefund);

        if ($priorities->first() === PaymentMethodType::WorkshopGiftCard && $this->canBeRefundedByTicketGiftCardsOnly($orderItem, $quantity)) {
            return $this->getRefundPaymentMethodsToAllocate(Collection::make([$priorities->first()]), $orderItem, $amountToRefund);
        }

        $mandatoryGiftCardsToRefund = $this->getMandatoryGiftCardsToApply($orderItem, $orderItem->quantity - $quantity);
        $amountToRefund = $amountToRefund->substractUntilNull($mandatoryGiftCardsToRefund->reduce(fn (Amount $sum, RefundPaymentMethod $method) => $sum->add($method->realAmount), Amount::null($orderItem->currency)));

        $percentDiscountsToApply = $this->getUnskipablePercentDiscountsAmount($orderItem, $amountToRefund);
        $amountToRefund = $amountToRefund->substract($percentDiscountsToApply);

        $amountDiscountsToApply = $this->getUnskipableAmountDiscountsAmount($orderItem);
        if ($amountDiscountsToApply->isGreatherThan($notRefundedAmount)) {
            $amountToRefund = $amountToRefund->substractUntilNull($amountDiscountsToApply->substract($notRefundedAmount));
        }

        return $this->getRefundPaymentMethodsToAllocate($priorities, $orderItem, $amountToRefund, $mandatoryGiftCardsToRefund);
    }

    private function canBeRefundedByTicketGiftCardsOnly(OrderItem $orderItem, int $quantity): bool
    {
        return $orderItem->order->discounts
            ->filter(fn (OrderDiscount $orderDiscount) => $orderDiscount->isWorkshopGiftCard())
            ->filter(fn (OrderDiscount $orderDiscount) => $orderDiscount->isApplicableTo($orderItem))
            ->sum(fn (OrderDiscount $orderDiscount) => $orderDiscount->getRemainingTicketsToAllocate()) >= $quantity;
    }

    /**
     * Here are defined the mandatory ticket gift cards to use for refund
     * Some payments/discounts cannot be allocated or fully allocated on other order items,
     * and we have to allocate them in the current refund.
     *
     * @return Collection<int, RefundPaymentMethod>
     */
    private function getMandatoryGiftCardsToApply(OrderItem $orderItem, int $quantityToIgnore): Collection
    {
        $applicablesGiftCardsDiscounts = $orderItem->order->discounts
            ->filter(fn (OrderDiscount $orderDiscount) => $orderDiscount->isWorkshopDiscount() && $orderDiscount->isApplicableTo($orderItem) && $orderDiscount->hasRemainToAllocate())
            ->keyBy('id');
        $applicablesAllocations = $applicablesGiftCardsDiscounts->mapWithKeys(fn (OrderDiscount $orderDiscount) => [
            $orderDiscount->id => $orderDiscount->getRemainingTicketsToAllocate(),
        ]);

        $orderItem->order->items
            ->filter(fn (OrderItem $item) => !$item->isInvoiced())
            ->map(function (OrderItem $item) use ($orderItem, $applicablesGiftCardsDiscounts, &$applicablesAllocations, $quantityToIgnore) {
                $allocatableTickets = $item->is($orderItem) ?
                    $quantityToIgnore :
                    $item->getInvoicableQuantity();

                foreach ($applicablesGiftCardsDiscounts as $applicableGiftCard) {
                    if (!$applicableGiftCard->isApplicableTo($item) || $allocatableTickets === 0) {
                        continue;
                    }

                    $ticketsToAllocate = (int) min($allocatableTickets, $applicableGiftCard->maxRefundableTicketsUntil($item->getInvoicableAmount()), $applicablesAllocations[$applicableGiftCard->id]);
                    $applicablesAllocations[$applicableGiftCard->id] -= $ticketsToAllocate;
                    $allocatableTickets -= $ticketsToAllocate;
                }

                return $item;
            });

        return Collection::make($applicablesAllocations)
            ->filter()
            ->map(function (int $tickets, int $giftCardId) use ($applicablesGiftCardsDiscounts) {
                $giftCardDiscount = $applicablesGiftCardsDiscounts->get($giftCardId);

                if (!$giftCardDiscount instanceof OrderDiscount || $giftCardDiscount->original_ticket_unit_value === null) {
                    throw new \RuntimeException("Error while finding ticket gift cards #{$giftCardId}");
                }

                return RefundPaymentMethod::make(
                    $giftCardDiscount,
                    $giftCardDiscount->original_ticket_unit_value->multiply($tickets),
                    $tickets,
                    $giftCardDiscount->applied_amount->divide((int) $giftCardDiscount->applied_tickets)->multiply($tickets),
                );
            })
            ->values();
    }

    private function getUnskipablePercentDiscountsAmount(OrderItem $orderItem, Amount $refundableAmount): Amount
    {
        $remainingRefundableAmount = $refundableAmount;
        $percentDiscounts = $orderItem->order->discounts->filter(fn (OrderDiscount $orderDiscount) => !$orderDiscount->isPaymentMethod() && $orderDiscount->isPercentDiscount());

        if ($percentDiscounts->isEmpty()) {
            return Amount::null($orderItem->currency);
        }

        return $refundableAmount->substract(
            $percentDiscounts
                ->filter(fn (OrderDiscount $orderDiscount) => $orderDiscount->isApplicableTo($orderItem))
                ->sortByDesc(fn (OrderDiscount $orderDiscount) => $orderDiscount->original_value)
                ->reduce(function (Amount $amount, OrderDiscount $orderDiscount) {
                    return $amount->substract($amount->multiply(Percent::make($orderDiscount->original_value ?? 0)->rate()));
                }, $remainingRefundableAmount)
        );
    }

    private function getUnskipableAmountDiscountsAmount(OrderItem $orderItem): Amount
    {
        $amountDiscounts = $orderItem->order->discounts->filter(fn (OrderDiscount $orderDiscount) => !$orderDiscount->isPaymentMethod() && $orderDiscount->isAmountDiscount());

        if ($amountDiscounts->isEmpty()) {
            return Amount::null($orderItem->currency);
        }

        $otherOrderItems = $orderItem->order->items->filter(fn (OrderItem $item) => !$item->is($orderItem) && !$item->isInvoiced());

        $remainingAmountToAllocate = Amount::sum(...$amountDiscounts->map(fn (OrderDiscount $orderDiscount) => $orderDiscount->getRemainingAmountToAllocate()));

        return $orderItem->order->discounts
            ->filter(fn (OrderDiscount $orderDiscount) => !$orderDiscount->isPaymentMethod() && $orderDiscount->isAmountDiscount())
            ->reduce(function (Amount $refunded, OrderDiscount $orderDiscount) use ($otherOrderItems) {
                $remainingAmountToAllocate = $orderDiscount->getRemainingAmountToAllocate();

                foreach ($otherOrderItems as $otherOrderItem) {
                    if ($orderDiscount->isApplicableTo($otherOrderItem)) {
                        $toAllocate = Amount::min($remainingAmountToAllocate, $otherOrderItem->getInvoicableAmount());

                        $remainingAmountToAllocate = $remainingAmountToAllocate->substract($toAllocate);
                        $refunded = $refunded->substract($toAllocate);
                    }
                }

                return $refunded;
            }, $remainingAmountToAllocate);
    }

    /**
     * @param Collection<int, PaymentMethodType> $priorities
     * @param Collection<int, RefundPaymentMethod> $mandatoryGiftCardsToRefund
     *
     * @return Collection<int, RefundPaymentMethod>
     */
    private function getRefundPaymentMethodsToAllocate(Collection $priorities, OrderItem $orderItem, Amount $amountToRefund, Collection $mandatoryGiftCardsToRefund = new Collection()): mixed
    {
        return $priorities
            ->flatMap(fn (PaymentMethodType $paymentMethodType) => $this->getOrderPaymentMethodsByType($paymentMethodType, $orderItem->order))
            ->filter(fn (OrderDiscount|OrderPayment $orderPayment) => $orderPayment->isApplicableTo($orderItem))
            ->map(function (OrderDiscount|OrderPayment $orderPayment) use (&$amountToRefund, $mandatoryGiftCardsToRefund) {
                $amount = $orderPayment->maxRefundableAmountUntil($amountToRefund);

                if ($amount->isNull()) {
                    return null;
                }

                $tickets = $orderPayment->maxRefundableTicketsUntil($amountToRefund);
                $refundedAmount = $orderPayment->realRefundableAmountUntil($amountToRefund);

                $relatedMandatoryGiftCard = $mandatoryGiftCardsToRefund->first(fn (RefundPaymentMethod $refundPaymentMethod) => $refundPaymentMethod->isSameType($orderPayment));
                if ($relatedMandatoryGiftCard !== null) {
                    $tickets = max(0, $tickets - $relatedMandatoryGiftCard->tickets);
                    $refundedAmount = $tickets === 0 ? Amount::null($orderPayment->currency) : $refundedAmount->substract($relatedMandatoryGiftCard->amount);
                    $amount = $tickets === 0 ? Amount::null($orderPayment->currency) : $amount->substract($relatedMandatoryGiftCard->realAmount);
                }

                $amountToRefund = $amountToRefund->substract($amount);

                return RefundPaymentMethod::make(
                    $orderPayment,
                    $refundedAmount,
                    $tickets,
                    $amount,
                );
            })
            ->merge($mandatoryGiftCardsToRefund)
            ->groupBy(fn (?RefundPaymentMethod $paymentMethod) => "{$paymentMethod?->type}|{$paymentMethod?->id}")
            ->map(fn (Collection $refundPaymentMethods) => $this->mergeRefundPaymentMethods($refundPaymentMethods))
            ->filter()
            ->values();
    }

    /**
     * @return Collection<int, OrderDiscount|OrderPayment>
     */
    private function getOrderPaymentMethodsByType(PaymentMethodType $paymentMethodType, Commande $order): Collection
    {
        return Collection::empty()
            ->merge($order->discounts->filter(fn (OrderDiscount $orderDiscount) => $orderDiscount->isPaymentMethod() && PaymentMethodType::fromPayment($orderDiscount) === $paymentMethodType))
            ->merge($order->payments->filter(fn (OrderPayment $orderPayment) => PaymentMethodType::fromPayment($orderPayment) === $paymentMethodType));
    }

    /**
     * @param Collection<int, RefundPaymentMethod|null> $refundPaymentMethods
     */
    private function mergeRefundPaymentMethods(Collection $refundPaymentMethods): ?RefundPaymentMethod
    {
        if ($refundPaymentMethods->count() === 1 || $refundPaymentMethods->first() === null) {
            return $refundPaymentMethods->first();
        }

        return RefundPaymentMethod::make(
            $refundPaymentMethods->first(),
            Amount::sum(...$refundPaymentMethods->filter()->pluck('amount')),
            $refundPaymentMethods->sum('tickets'),
            Amount::sum(...$refundPaymentMethods->filter()->pluck('realAmount')),
        );
    }
}
