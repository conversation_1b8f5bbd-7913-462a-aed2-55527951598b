<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Refunds\Exceptions;

enum OrderRefundError: string
{
    case AlreadyOccurred = 'refund_already_occurred';
    case QuantityTooHigh = 'refund_quantity_too_high';
    case TooManyItems = 'refund_booking_too_many_items';
    case OrderStatusNotValid = 'refund_order_status_not_valid';
    case IncompatiblePaymentMethods = 'refund_incompatible_payment_methods';
    case QuantityToRefundTooHigh = 'too_much_to_be_refunded';
    case OrderOriginNatureEtDecouvertes = 'order_origin_nature_et_decouvertes';
    case OrderPaymentOngoing = 'order_payment_ongoing';

    public function getMessage(): string
    {
        return match ($this) {
            self::AlreadyOccurred => 'Refund already occured',
            self::QuantityTooHigh => 'Refund quantity too high',
            self::TooManyItems => 'Too many items',
            self::OrderStatusNotValid => 'Order status is invalid',
            self::IncompatiblePaymentMethods => 'Incompatible payment methods',
            self::QuantityToRefundTooHigh => 'Order quantity mismatch',
            self::OrderOriginNatureEtDecouvertes => 'Booking origin is Nature & Découvertes',
            self::OrderPaymentOngoing => 'Order has ongoing payments',
        };
    }
}
