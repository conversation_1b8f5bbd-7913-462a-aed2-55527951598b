<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Refunds\Exceptions;

class AdditionalProductRefundNotPossible extends \RuntimeException
{
    public AdditionalProductRefundError $state;

    public static function fromState(AdditionalProductRefundError $code): self
    {
        $exception = new self("Unable to refund additional product with error code : $code->value");
        $exception->state = $code;

        return $exception;
    }
}
