<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Services\ArtisanChecks\Rules;

use App\Domain\Accounting\Invoices\Repositories\ArtisanRepository;
use App\Domain\Accounting\Invoices\Services\ArtisanChecks\ArtisanErrors;

/**
 * @SuppressWarnings(PHPMD.LongClassName)
 */
final readonly class ArtisanBookingsFromPreviousMonthMustBeCommissionned implements ArtisanInvoicesChecker
{
    public function __construct(
        private ArtisanRepository $artisanRepository,
    ) {
    }

    public function handle(ArtisanErrors $artisanErrors, \Closure $next): void
    {
        $results = $this->artisanRepository->getHavingBookingsButNoCommissionInvoiceForMonth(
            $artisanErrors->month->previous(),
            $artisanErrors->artisanId,
        );

        $results->each(function (\stdClass $result) use ($artisanErrors): void {
            $artisanErrors->addErrorForArtisanId($result->id, [
                "Artisan bookings No {$result->booking_ids} does not appear in any commission invoice for month {$artisanErrors->month->previous()}",
            ]);
        });

        $next($artisanErrors);
    }
}
