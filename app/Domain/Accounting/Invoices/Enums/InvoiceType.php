<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Enums;

enum InvoiceType: int
{
    case Commission = 1;
    case Booking = 2;
    case Prestation = 3;
    case Additional = 4;
    case DirectInvitation = 6;
    case DirectArtisan = 7;
    case ArtisanPrestation = 8;
    case DirectPrestation = 9;
    case DirectGiftCard = 10;

    public function prefix(): string
    {
        return match ($this) {
            self::Commission => 'FA',
            self::Booking,
            self::DirectInvitation,
            self::DirectArtisan,
            self::ArtisanPrestation => 'WCD',
            self::Prestation,
            self::Additional,
            self::DirectPrestation => 'FD',
            self::DirectGiftCard => 'FK',
        };
    }

    public function slug(): string
    {
        return match ($this) {
            self::Commission => 'facture_mensuelle_commission',
            self::Booking => 'facture_prestation_reservation',
            self::Prestation => 'facture_directe_prestation',
            self::Additional => 'facture_directe_produits_comple',
            self::DirectInvitation => 'facture_directe_invitation',
            self::DirectArtisan => 'facture_directe_artisan_wcd',
            self::ArtisanPrestation => 'facture_prestation_achat_client_entreprise',
            self::DirectPrestation => 'facture_directe_prestation_achat_client_entreprise',
            self::DirectGiftCard => 'facture_directe_cartes_cadeau',
        };
    }

    public function storageFolder(): string
    {
        return match ($this) {
            self::Commission => 'commissions',
            self::Booking => 'prestation-artisan',
            self::Prestation => 'prestation-wecandoo',
            self::Additional => 'produits-complementaires',
            self::DirectInvitation => 'directe-artisan-invitation',
            self::DirectArtisan => 'directe-artisan-wecandoo',
            default => 'autres',
        };
    }

    public function getLabel(): string
    {
        return (string) __('enums.invoice.type.'.$this->slug());
    }
}
