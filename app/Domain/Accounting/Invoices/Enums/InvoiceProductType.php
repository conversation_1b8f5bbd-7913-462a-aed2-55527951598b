<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Enums;

use App\Domain\Accounting\Orders\Enums\ProductType;
use App\Domain\PrivateBooking\Enums\QuoteLineType;

enum InvoiceProductType: int
{
    case Booking = 1;
    case AmountGiftCard = 2;
    case WorkshopGiftCard = 3;
    case PhysicalCard = 4;
    case Delivery = 5;
    case TravelExpenses = 6;
    case Custom = 7;

    public static function fromProductType(ProductType $productType): self
    {
        return match ($productType) {
            ProductType::Booking => self::Booking,
            ProductType::AmountGiftCard => self::AmountGiftCard,
            ProductType::WorkshopGiftCard => self::WorkshopGiftCard,
            ProductType::PhysicalCard => self::PhysicalCard,
            ProductType::Delivery => self::Delivery,
        };
    }

    public static function fromQuoteLineType(QuoteLineType $quoteLineType): self
    {
        return match ($quoteLineType) {
            QuoteLineType::Booking => self::Booking,
            QuoteLineType::TravelExpenses => self::TravelExpenses,
            QuoteLineType::Custom => self::Custom,
        };
    }
}
