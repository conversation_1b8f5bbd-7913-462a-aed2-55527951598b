<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\DataTables;

use App\Domain\Accounting\CommissionInvoices\Models\CommissionInvoice;
use App\Domain\Accounting\Invoices\Models\Invoice;
use App\Domain\Accounting\Invoices\Models\InvoiceLineDiscount;
use App\Domain\Accounting\Invoices\Models\InvoiceLinePaymentMethod;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Models\Commande;
use App\Services\CurrencyFormatter;
use App\Services\DateFormatter;
use App\Shared\Amount;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\QueryDataTable;
use Yajra\DataTables\Services\DataTable;

class InvoiceDataTable extends DataTable
{
    /**
     * @param Builder<CommissionInvoice> $query
     */
    public function dataTable(Builder $query, DateFormatter $dateFormatter, CurrencyFormatter $currencyFormatter): QueryDataTable
    {
        /** @var QueryDataTable $datatables */
        $datatables = datatables($query);

        return $datatables
            ->addColumn('id', static fn (Invoice $invoice) => $invoice->id)
            ->addColumn('order', static function (Invoice $invoice): string {
                return match ($invoice->associable_type) {
                    (new Commande())->getMorphClass() => sprintf('<a href="%s">Order #%s</a>', route('admin.commandes.show', $invoice->associable_id), $invoice->associable_id),
                    (new PrivateBookingRequest())->getMorphClass() => sprintf('Private Booking #%1$s', $invoice->associable_id),
                    default => '',
                };
            })
            ->addColumn('number', static fn (Invoice $invoice) => $invoice->isCancelled() ?
                "<s>{$invoice->number}</s>" :
                $invoice->number)
            ->addColumn('emitter_name', static fn (Invoice $invoice) => $invoice->emitter_name)
            ->addColumn('recipient_name', static fn (Invoice $invoice) => $invoice->recipient_name)
            ->addColumn('issue_date', static fn (Invoice $invoice) => $dateFormatter->dateShortFormat($invoice->issue_date))
            ->addColumn('final_price', static fn (Invoice $invoice) => $currencyFormatter->format($invoice->final_price))
            ->addColumn('discounts', static fn (Invoice $invoice) => $invoice->discountLines
                ->map(static fn (InvoiceLineDiscount $invoiceLineDiscount) => sprintf(
                    '%s&nbsp;-&nbsp;<small>%s</small>',
                    $currencyFormatter->format($invoiceLineDiscount->amount),
                    $invoiceLineDiscount->relatedDiscount?->getAttribute('discount_type'),
                ))
                ->join('<br />'))
            ->addColumn('payments', static fn (Invoice $invoice) => $invoice->paymentMethods
                ->groupBy(static fn (InvoiceLinePaymentMethod $paymentMethod) => $paymentMethod->type->value)
                ->map(static fn (Collection $invoicePaymentMethods, string $type) => sprintf(
                    '%s&nbsp;-&nbsp;<small>%s</small>',
                    $currencyFormatter->format(Amount::sum(...$invoicePaymentMethods->pluck('amount'))),
                    str_replace('_', ' ', $type),
                ))
                ->join('<br />'))
            ->addColumn('download_link', static function (Invoice $invoice) {
                return '<a href="'.route('admin.invoices.download', $invoice).'" target="_blank" class="btn btn-warning"><i class="fa fa-download"></i></a>';
            })
            ->addColumn('status', static fn (Invoice $invoice) => $invoice->status->value)
            ->addColumn('created_at', static fn (Invoice $invoice) => $invoice->created_at !== null ? $dateFormatter->dateAndTimeShortFormat($invoice->created_at) : null)

            ->rawColumns(['order', 'number', 'discounts', 'payments', 'download_link'])

            ->filterColumn('id', static fn (Builder $query, string $keyword) => $query
                ->where(fn (Builder $builder) => $builder
                    ->orWhere('id', $keyword)
                    ->orWhere('uuid', $keyword)))
            ->filterColumn('order', static fn (Builder $query, string $keyword) => $query
                ->where('order_id', $keyword))
            ->filterColumn('number', static fn (Builder $query, string $keyword) => $query
                ->where('number', $keyword))
            ->filterColumn('emitter_name', static fn (Builder $query, string $keyword) => $query
                ->where('emitter_name', 'like', "%{$keyword}%"))
            ->filterColumn('recipient_name', static fn (Builder $query, string $keyword) => $query
                ->where('recipient_name', 'like', "%{$keyword}%"))

            ->orderColumn('id', 'id $1')
            ->orderColumn('number', 'number $1')
            ->orderColumn('issue_date', 'issue_date $1')
            ->orderColumn('final_price', 'final_price $1');
    }

    /**
     * @return Builder<Invoice>
     */
    public function query(): Builder
    {
        return Invoice::query()
            ->with('discountLines.relatedDiscount', 'paymentMethods');
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('invoices-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Brtip')
            ->pageLength(50)
            ->lengthMenu([10, 25, 50, 100, 500])
            ->orderBy(0, 'desc')
            ->fixedHeader(false)
            ->initComplete('function(){setupSearchColumns(this);$(\'.search-header input[type="text"]\').css({\'width\':\'100%\',\'display\':\'inline-block\'});}')
            ->orderCellsTop(true)
            ->buttons(
                Button::make(['pageLength']),
            );
    }

    /** @return Column[] */
    protected function getColumns(): array
    {
        return [
            Column::make('id')->searchable()->orderable()->title('#'),
            Column::make('order')->searchable()->orderable(false)->title('Commande'),
            Column::make('number')->searchable()->orderable()->title('Numéro facture'),
            Column::make('emitter_name')->searchable()->orderable(false)->title('Émetteur'),
            Column::make('recipient_name')->searchable()->orderable(false)->title('Destinataire'),
            Column::make('issue_date')->searchable(false)->orderable()->title('Date émission'),
            Column::make('final_price')->searchable(false)->orderable()->title('Montant TTC'),
            Column::make('discounts')->searchable(false)->orderable(false)->title('Promotions'),
            Column::make('payments')->searchable(false)->orderable(false)->title('Paiements'),
            Column::make('download_link')->searchable(false)->orderable(false)->title('Télécharger'),
            Column::make('status')->searchable(false)->orderable(false)->title('Statut'),
            Column::make('created_at')->searchable(false)->orderable(false)->title('Création'),
        ];
    }
}
