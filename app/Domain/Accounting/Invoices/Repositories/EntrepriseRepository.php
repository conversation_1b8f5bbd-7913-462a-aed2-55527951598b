<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Repositories;

use App\Models\Entreprise;

final readonly class EntrepriseRepository
{
    public function __construct(
        private Entreprise $model,
    ) {
    }

    public function getWecandooFranceEntreprise(): Entreprise
    {
        return $this->model->newQuery()
            ->where('trigram', 'WCD')
            ->firstOrFail();
    }

    public function getWecandooGreatBritainEntreprise(): Entreprise
    {
        return $this->model->newQuery()
            ->where('trigram', 'WCL')
            ->firstOrFail();
    }
}
