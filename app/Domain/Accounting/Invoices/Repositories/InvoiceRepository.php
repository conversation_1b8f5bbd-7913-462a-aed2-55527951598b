<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Repositories;

use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use App\Domain\Accounting\Invoices\Models\Invoice;
use App\Models\Atelier;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Carbon\Exceptions\EndLessPeriodException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final readonly class InvoiceRepository
{
    public function __construct(
        private Invoice $model,
    ) {
    }

    /**
     * @return Collection<int, Invoice>
     */
    public function getWorkshopInvoices(Atelier $workshop, CarbonImmutable $startDate, CarbonImmutable $endDate): Collection
    {
        return $this->model->newQuery()
            ->whereHas('lines', fn (Builder $query) => $query
                ->scopes(['relatedToWorkshopBooking' => $workshop])
                ->whereBetween('event_end', [$startDate, $endDate]))
            ->where('status', InvoiceStatus::Issued)
            ->get();
    }

    /**
     * @return array<array{month: CarbonInterface, invoicesCount: int}>
     *
     * @throws EndLessPeriodException
     */
    public function getInvoiceCountByMonth(Atelier $workshop): array
    {
        return $this->model->newQuery()
            ->select([
                DB::raw("DATE_FORMAT(`issue_date`,'%Y-%m') as month"),
                DB::raw('COUNT(*) AS invoicesCount'),
            ])
            ->whereHas('lines', fn (Builder $query) => $query
                ->scopes(['relatedToWorkshopBooking' => $workshop]))
            ->where('status', InvoiceStatus::Issued)
            ->groupBy(DB::raw("DATE_FORMAT(`issue_date`,'%Y-%m')"))
            ->toBase()
            ->get()
            ->keyBy('month')
            ->sortKeysDesc()
            ->map(fn (\stdClass $object) => [
                'month' => CarbonImmutable::createStrict(...array_map(fn (string $dateElement) => (int) $dateElement, explode('-', $object->month))),
                'invoicesCount' => $object->invoicesCount,
            ])
            ->toArray();
    }

    /** @return Builder<Invoice> */
    public function getInvoicesForWorkshopBuilder(Atelier $workshop): Builder
    {
        return Invoice::query()
            ->with('lines')
            ->where('emitter_type', $workshop->artisan->getMorphClass())
            ->where('emitter_id', $workshop->artisan->getKey())
            ->whereHas('lines', fn ($builder) => $builder->where('workshop_id', $workshop->getKey()))
            ->orderByDesc('issue_date')
            ->orderByDesc('id');
    }
}
