<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Invoices\Pdf;

use App\Domain\Accounting\Invoices\Models\Invoice;
use App\Domain\Accounting\Invoices\Transformers\InvoiceTransformer;
use App\Infrastructure\Pdf\DomPdfPrintable;
use App\Infrastructure\Pdf\Printable;
use Illuminate\Contracts\View\View;

class InvoicePdf implements Printable, DomPdfPrintable
{
    public function __construct(
        private readonly Invoice $invoice,
    ) {
    }

    public function getView(): View
    {
        $transfomer = \App::make(InvoiceTransformer::class);

        return view('pdf.invoices.invoice', [
            'invoice' => $transfomer->transform($this->invoice),
        ]);
    }

    public function getLocale(): ?string
    {
        return $this->invoice->getRelatedOrderUser()?->preferredLocale();
    }

    public function getFileName(): string
    {
        return sprintf('%s_%s.pdf', __('invoice.invoice'), $this->invoice->number);
    }

    public function getDirectory(): string
    {
        return 'invoice';
    }
}
