<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Exports\ExportsA;

use App\Domain\Accounting\Exports\AccountingExport;
use App\Domain\Gift\Enums\GiftCardCreationReason;
use App\Domain\Gift\Models\GiftCard;
use App\Shared\DateRange;
use Illuminate\Support\LazyCollection;

readonly class A5TransformationOrRefundCodesExport extends AccountingExport
{
    protected function header(DateRange $dateRange): array
    {
        return [
            'GC_ID',
            'Code',
            'Created_at',
            'Statut',
            'Amount',
            'Devise',
        ];
    }

    protected function rows(DateRange $dateRange): \Generator
    {
        /** @var LazyCollection<int, GiftCard> $giftCards */
        $giftCards = GiftCard::withTrashed()
            ->whereIn('creation_reason', [
                GiftCardCreationReason::BookingTransformation,
                GiftCardCreationReason::WalletTransformation,
                GiftCardCreationReason::TicketIntoValueTransformation,
                GiftCardCreationReason::BoxTransformation,
                GiftCardCreationReason::Refund,
            ])
            ->whereBetween('created_at', [$dateRange->start()->toDateTimeString(), $dateRange->end()->toDateTimeString()])
            ->lazyById();

        foreach ($giftCards as $giftCard) {
            yield [
                $giftCard->id,
                $giftCard->code,
                $giftCard->created_at?->format('d/m/Y'),
                $giftCard->status,
                $giftCard->value,
                $giftCard->currency,
            ];
        }
    }
}
