<?php

declare(strict_types=1);

namespace App\Domain\Accounting\Exports\ExportsC;

use App\Domain\Accounting\CommissionInvoices\Models\CommissionInvoice;
use App\Domain\Accounting\Exports\AccountingExport;
use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use App\Domain\Accounting\Orders\Models\OrderItemTicket;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Gift\Enums\GiftCardStatus;
use App\Domain\Gift\Enums\GiftCardType;
use App\Domain\Gift\Models\GiftCard;
use App\Enums\Currency;
use App\Enums\Order\OrderStatus;
use App\Models\Evenement;
use App\Models\Reservation;
use App\Shared\DateRange;
use Carbon\CarbonImmutable;
use Illuminate\Database\Query\JoinClause;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
readonly class C3CommissionProvisionsExport extends AccountingExport
{
    protected function header(DateRange $dateRange): array
    {
        return [
            '<PERSON><PERSON>',
            'Année',
            'Montant',
            'GCs Value',
            'GCs Ticket',
            'Reservations commandées',
            'Reservations hors commande',
            'Commission moyenne M-1',
            'Devise',
        ];
    }

    protected function rows(DateRange $dateRange): \Generator
    {
        foreach (Currency::cases() as $currency) {
            $averageCommissionPercent = $this->getAverageCommissionPercentageFromPreviousMonthAndCurrency($dateRange->end(), $currency);

            $valueGiftCards = $this->getRemainingAmountOfActiveValueGiftCards($currency, $dateRange->end());
            $ticketGiftCards = $this->getRemainingAmountOfActiveTicketGiftCards($currency, $dateRange->end());
            $orderedBookings = $this->getRemainingAmountOfConfirmedAndOrderedBookings($currency, $dateRange->end());
            $notOrderedBookings = $this->getRemainingAmountOfConfirmedAndNotOrderedBookings($currency, $dateRange->end());

            yield [
                $dateRange->end()->month,
                $dateRange->end()->year,
                round(($valueGiftCards + $ticketGiftCards + $orderedBookings + $notOrderedBookings) * $averageCommissionPercent, 2),
                round($valueGiftCards, 2),
                round($ticketGiftCards, 2),
                round($orderedBookings, 2),
                round($notOrderedBookings, 2),
                round($averageCommissionPercent * 100, 4),
                $currency->value,
            ];
        }
    }

    private function getAverageCommissionPercentageFromPreviousMonthAndCurrency(CarbonImmutable $endDate, Currency $currency): float
    {
        return (float) CommissionInvoice::query()
            ->selectRaw('sum(total_including_vat) / sum(total_sales) as avg')
            ->where('status', InvoiceStatus::Issued)
            ->where('currency', $currency)
            ->whereBetween('issue_date', [$endDate->startOfMonth()->toDateString(), $endDate->endOfMonth()->toDateString()])
            ->value('avg');
    }

    private function getRemainingAmountOfActiveValueGiftCards(Currency $currency, CarbonImmutable $lastCreationDate): float
    {
        return (float) GiftCard::query()
            ->where('status', GiftCardStatus::ACTIVE)
            ->where('type', GiftCardType::VALUE)
            ->where('currency', $currency)
            ->where(fn ($builder) => $builder
                ->orWhereNotNull('order_id')
                ->orWhereNotNull('sold_at'))
            ->where('created_at', '<=', $lastCreationDate->toDateTimeString())
            ->sum('remaining_value');
    }

    private function getRemainingAmountOfActiveTicketGiftCards(Currency $currency, CarbonImmutable $lastCreationDate): float
    {
        return (float) GiftCard::query()
            ->selectRaw('SUM(value / ticket * remaining_ticket) as sum')
            ->where('status', GiftCardStatus::ACTIVE)
            ->where('type', GiftCardType::TICKET)
            ->where('currency', $currency)
            ->where(fn ($builder) => $builder
                ->orWhereNotNull('order_id')
                ->orWhereNotNull('sold_at'))
            ->where('created_at', '<=', $lastCreationDate->toDateTimeString())
            ->value('sum');
    }

    /** @test */
    private function getRemainingAmountOfConfirmedAndOrderedBookings(Currency $currency, CarbonImmutable $lastCreationDate): float
    {
        return (float) Reservation::query()
            ->selectRaw(
                'SUM(order_items.final_price / 100 / order_items.quantity * reservations.nb_places) as sum'
            )
            ->join('order_item_tickets', 'order_item_tickets.reservation_id', '=', 'reservations.id')
            ->join('order_items', fn (JoinClause $join) => $join
                ->on('order_items.item_id', '=', 'order_item_tickets.id')
                ->where('order_items.item_type', '=', (new OrderItemTicket())->getMorphClass()))
            ->join('commandes', 'commandes.id', '=', 'order_items.order_id')
            ->whereIn('commandes.status', OrderStatus::successful())
            ->whereIn('reservations.status', [BookingStatus::BOOKING_CONFIRMED, BookingStatus::BOOKING_STANDBY])
            ->where('reservations.created_at', '<=', $lastCreationDate->toDateTimeString())
            ->where('commandes.currency', $currency)
            ->value('sum');
    }

    /** @test */
    private function getRemainingAmountOfConfirmedAndNotOrderedBookings(Currency $currency, CarbonImmutable $lastCreationDate): float
    {
        return (float) Reservation::query()
            ->whereNull('commande_id')
            ->whereIn('status', [BookingStatus::BOOKING_CONFIRMED, BookingStatus::BOOKING_STANDBY])
            ->whereIn('origin', Evenement::VALID_BOOKING_FOR_BILLING_ORIGINS)
            ->where('created_at', '<=', $lastCreationDate->toDateTimeString())
            ->where('currency', $currency)
            ->sum('prix');
    }
}
