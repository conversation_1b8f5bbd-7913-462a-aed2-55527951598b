<?php

declare(strict_types=1);

namespace App\Domain\ECommerce\Listeners;

use App\Domain\ECommerce\CartService;
use App\Domain\ECommerce\Events\CartInitiated;
use App\Models\Reductions\CodeReductionGlobal;
use Carbon\Carbon;

class AutoApplyDiscount
{
    protected bool $isAutoDiscountEnabled = false;
    protected string $code;

    public function __construct(
        private readonly CartService $cartService
    ) {
        $this->isAutoDiscountEnabled = config('discount.auto_discount.enabled');
        $this->code = config('discount.auto_discount.code');
    }

    public function handle(CartInitiated $event): void
    {
        $event->cart->loadMissing([
            'items.relatedProduct',
        ]);

        if (!$this->isAutoDiscountEnabled || $event->cart->hasDiscount()) {
            return;
        }

        $discount = CodeReductionGlobal::where('code_reduction', $this->code)->first();
        if (
            $discount instanceof CodeReductionGlobal
            && $discount->start <= Carbon::now()
            && $discount->end >= Carbon::now()
        ) {
            $this->cartService::applyDiscountOnCart($event->cart, $discount);
        }
    }
}
