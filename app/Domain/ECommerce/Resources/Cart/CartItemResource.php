<?php

declare(strict_types=1);

namespace App\Domain\ECommerce\Resources\Cart;

use App\Domain\ECommerce\Enums\Product;
use App\Domain\ECommerce\Models\CartItem;
use App\Domain\Gift\Models\GiftCard;
use App\Models\Atelier;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function toArray(Request $request): array
    {
        /** @var CartItem $cartItem */
        $cartItem = $this->resource;

        return [
            'id' => $cartItem->id,
            'type' => $cartItem->type,
            'basePrice' => $cartItem->base_price,
            'quantity' => $cartItem->quantity,
            'price' => $cartItem->amount,
            'priceDiscounted' => $cartItem->discounted_amount,
            'currency' => $cartItem->currency,
            'productId' => $cartItem->product_id,
            'offeredAt' => $cartItem->offered_at
                ?->format('Y-m-d'),
            'sendViaEmail' => $cartItem->should_send_email_to_beneficiary === true,
            'giftMessage' => $cartItem->gift_message,
            'giftTheme' => $cartItem->gift_theme,
            'giftFrom' => $cartItem->gift_from,
            'giftTo' => $cartItem->gift_to,
            'giftRecipientEmail' => $cartItem->gift_recipient_email,
            'workshopId' => $this->getWorkshopId($cartItem),
            'address' => $this->getAddress($cartItem),
            'physicalCardItem' => $this->getPhysicalCardItem($cartItem),
            'giftReceiver' => $this->getGiftReceiver($cartItem),
            'giftGiver' => $this->getGiftGiver($cartItem),
            'booking' => $this->when($cartItem->relatedProduct instanceof Reservation, fn () => CartItemBookingResource::make($cartItem->relatedProduct)),
        ];
    }

    private function getWorkshopId(CartItem $cartItem): ?int
    {
        $relatedProduct = $cartItem->relatedProduct;

        if ($relatedProduct instanceof Atelier) {
            return $relatedProduct->id;
        }

        if ($relatedProduct instanceof Reservation) {
            return $relatedProduct->evenement->atelier_id;
        }

        if ($relatedProduct instanceof GiftCard) {
            return $relatedProduct->workshop_id;
        }

        return null;
    }

    /**
     * @return array<string, string|null>|null
     */
    private function getAddress(CartItem $cartItem): ?array
    {
        $address = $cartItem->address;

        if ($address === null) {
            return null;
        }

        return [
            'fullName' => $address->destinataire,
            'address' => $address->adresse1,
            'zipCode' => $address->code_postal,
            'city' => $address->ville,
            'country' => $address->pays,
            'phoneNumber' => $address->telephone_contact,
            'extraInformations' => $address->informations_complementaires,
        ];
    }

    private function getPhysicalCardItem(CartItem $cartItem): self
    {
        return self::make($cartItem->subItems->where('product_id', Product::PHYSICAL_CARD)->first());
    }

    /**
     * @return array<string, string|null>|null
     */
    private function getGiftReceiver(CartItem $cartItem): ?array
    {
        if ($cartItem->gift_to === null && $cartItem->gift_recipient_email === null) {
            return null;
        }

        return [
            'name' => $cartItem->gift_to,
            'email' => $cartItem->gift_recipient_email,
        ];
    }

    /**
     * @return array<string, string|null>|null
     */
    private function getGiftGiver(CartItem $cartItem): ?array
    {
        if ($cartItem->gift_from === null) {
            return null;
        }

        return [
            'name' => $cartItem->gift_from,
            'email' => null,
        ];
    }
}
