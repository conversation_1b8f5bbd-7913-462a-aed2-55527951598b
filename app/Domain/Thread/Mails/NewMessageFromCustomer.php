<?php

declare(strict_types=1);

namespace App\Domain\Thread\Mails;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\Thread\Models\ThreadMessage;
use App\Domain\Thread\Models\ThreadMessageAttachment;
use App\Mail\BaseMailable;
use App\Models\Artisan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

final class NewMessageFromCustomer extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly ThreadMessage $message,
        private readonly Artisan $artisan,
    ) {
        parent::__construct();
    }

    public function build(): Mailable
    {
        /** @var PrivateBookingRequest $privateBookingRequest */
        $privateBookingRequest = $this->message->thread->associable;

        return $this->markdown('emails.artisan.thread.new-message-from-customer')
            ->subject(
                __(
                    'emails/artisan/threads.new_message.subject',
                    [
                        'firstname' => $this->message->sender->getUser()->prenom,
                    ]
                )
            )
            ->with('name', $this->artisan->prenom)
            ->with('firstname', $this->message->sender->getUser()->prenom)
            ->with('lastname', $this->message->sender->getUser()->nom)
            ->with(
                'company',
                empty($privateBookingRequest->company_name) ? '' : '('.$privateBookingRequest->company_name.')'
            )
            ->with('message', $this->message->body)
            ->with('attachments', $this->message->attachments->map(fn (ThreadMessageAttachment $attachment) => $attachment->original_filename)->toArray())
            ->with(
                'url',
                route(
                    'artisan.privatization.index',
                    [
                        'open' => 'thread',
                        'id' => $this->message->thread->getKey(),
                    ]
                )
            );
    }
}
