<?php

declare(strict_types=1);

namespace App\Domain\Thread\Services;

use App\Domain\Thread\Models\ThreadMessageAttachment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final readonly class ThreadAttachment
{
    public function removeAttachment(ThreadMessageAttachment $attachment): void
    {
        if ($attachment->trashed()) {
            return;
        }

        $attachment->delete();

        $storage = Storage::disk(config('thread.disk'));

        if ($storage->exists($attachment->filename)) {
            $storage->delete($attachment->filename);
        }
    }

    public function downloadAttachment(ThreadMessageAttachment $attachment): StreamedResponse
    {
        if ($attachment->trashed()) {
            throw new HttpException(419, 'The attachment has expired.');
        }

        $storage = Storage::disk(config('thread.disk'));

        if (!$storage->exists($attachment->filename)) {
            Log::warning(
                'Unable to download attachment.',
                [
                    'attachment' => $attachment->getKey(),
                    'filename' => $attachment->filename,
                ],
            );

            throw new NotFoundHttpException();
        }

        return $storage->download($attachment->filename, $attachment->original_filename);
    }
}
