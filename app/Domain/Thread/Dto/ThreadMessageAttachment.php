<?php

declare(strict_types=1);

namespace App\Domain\Thread\Dto;

use App\Domain\Thread\Enums\ThreadAttachmentType;
use App\Domain\Thread\Models\ThreadMessageAttachment as Model;
use Livewire\Wireable;

final readonly class ThreadMessageAttachment implements Wireable
{
    private function __construct(
        public int $id,
        public int $associableId,
        public ThreadAttachmentType $type,
        public string $originalFilename,
        public bool $isTrashed,
    ) {
    }

    public static function fromModel(Model $attachment): self
    {
        return new self(
            $attachment->getKey(),
            $attachment->message->thread->associable_id,
            $attachment->type,
            $attachment->original_filename,
            $attachment->trashed(),
        );
    }

    /** @return array{id: int, associableId: int, type: ThreadAttachmentType, originalFilename: string, isTrashed: bool} */
    public function toLivewire(): array
    {
        return [
            'id' => $this->id,
            'associableId' => $this->associableId,
            'type' => $this->type,
            'originalFilename' => $this->originalFilename,
            'isTrashed' => $this->isTrashed,
        ];
    }

    /** @param array{id: int, associableId: int, type: ThreadAttachmentType, originalFilename: string, isTrashed: bool} $value */
    public static function fromLivewire($value): self
    {
        return new self(
            $value['id'],
            $value['associableId'],
            $value['type'],
            $value['originalFilename'],
            $value['isTrashed'],
        );
    }
}
