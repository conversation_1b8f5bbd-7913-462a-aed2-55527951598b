<?php

declare(strict_types=1);

namespace App\Domain\Thread\Resources;

use App\Domain\Thread\Dto\ThreadMessageSender;
use App\Domain\Thread\Models\ThreadParticipant;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ThreadMessageSenderResource extends JsonResource
{
    public function __construct(?ThreadMessageSender $resource, private readonly ?ThreadParticipant $loggedParticipant)
    {
        parent::__construct($resource);
    }

    /**
     * @return array<string, mixed>
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function toArray(Request $request): array
    {
        $threadMessageSender = $this->getThreadMessageSender();
        if ($threadMessageSender === null) {
            return [];
        }

        return [
            'id' => $threadMessageSender->id,
            'name' => $threadMessageSender->name,
            'avatar' => $threadMessageSender->avatar,
            'isMe' => $threadMessageSender->id === $this->loggedParticipant?->participant_id,
        ];
    }

    private function getThreadMessageSender(): ?ThreadMessageSender
    {
        /** @var ?ThreadMessageSender $threadMessageSender */
        $threadMessageSender = $this->resource;

        return $threadMessageSender;
    }
}
