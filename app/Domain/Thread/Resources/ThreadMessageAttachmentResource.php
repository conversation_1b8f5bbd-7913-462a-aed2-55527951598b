<?php

declare(strict_types=1);

namespace App\Domain\Thread\Resources;

use App\Domain\Thread\Dto\ThreadMessageAttachment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ThreadMessageAttachmentResource extends JsonResource
{
    public function __construct(ThreadMessageAttachment $resource)
    {
        parent::__construct($resource);
    }

    /**
     * @return array<string, mixed>
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function toArray(Request $request): array
    {
        $threadMessageAttachment = $this->getThreadMessageAttachment();

        $downloadUrl = null;
        if ($threadMessageAttachment->isTrashed === false) {
            $downloadUrl = route(
                'front.api.private-bookings.threads.attachments.download',
                [
                    'privateBooking' => $threadMessageAttachment->associableId,
                    'attachment' => $threadMessageAttachment->id,
                ]
            );
        }

        return [
            'type' => $threadMessageAttachment->type->value,
            'originalFilename' => $threadMessageAttachment->originalFilename,
            'isTrashed' => $threadMessageAttachment->isTrashed,
            'downloadUrl' => $downloadUrl,
        ];
    }

    private function getThreadMessageAttachment(): ThreadMessageAttachment
    {
        /** @var ThreadMessageAttachment $threadMessageAttachment */
        $threadMessageAttachment = $this->resource;

        return $threadMessageAttachment;
    }
}
