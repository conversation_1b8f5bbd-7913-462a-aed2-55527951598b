<?php

declare(strict_types=1);

namespace App\Domain\Content;

use App\Domain\Content\Models\Collection;
use App\Domain\Content\Repositories\CollectionRepository;
use App\Domain\Content\Repositories\PageRepository;
use App\Domain\Content\Repositories\TagRepository;
use App\Exceptions\ValidationException;
use App\Models\Tag;
use Carbon\Carbon;

class ReplaceTagByCollectionService
{
    public function __construct(
        protected readonly TagRepository $tagRepository,
        protected readonly CollectionRepository $collectionRepository,
        protected readonly CollectionService $collectionService,
        protected readonly PageRepository $pageRepository
    ) {
    }

    public function replace(Tag $tag, Collection $collection): Collection
    {
        $singlePage = $tag->singlePage;
        if ($singlePage !== null) {
            $singlePage->subject()->associate($collection);
        }
        $collection->name = $tag->name;
        $collection->identifier = $tag->slug;
        $collection->is_active = $tag->is_active;

        $collection->singlePage?->sections()->delete();
        $collection->singlePage()->delete();
        if ($singlePage !== null) {
            $singlePage->save();
        }

        $tag->deleted_at = Carbon::now();
        $tag->slug = "{$tag->slug}-old";
        $tag->save();
        $collection->save();

        return $collection;
    }

    /**
     * @throws ValidationException
     */
    public function validate(Tag $tag, Collection $collection): void
    {
        $errors = [];

        $result = $this->getTagErrors($tag);
        if (!empty($result)) {
            $errors['tag'] = $result;
        }

        $result = $this->getCollectionErrors($collection);
        if (!empty($result)) {
            $errors['collection'] = $result;
        }

        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }
    }

    /**
     * @return array<string>
     */
    private function getTagErrors(Tag $tag): array
    {
        $errors = [];

        if (!$tag->is_active) {
            $errors[] = "Le tag n'est pas actif.";
        }

        if ($tag->singlePage === null) {
            $errors[] = "Le tag n'a pas de singlePage";
        }

        return $errors;
    }

    /**
     * @return array<string>
     */
    private function getCollectionErrors(Collection $collection): array
    {
        $errors = [];

        if ($collection->is_active) {
            $errors[] = 'La collection est déjà active.';
        }

        if ($collection->singlePage !== null) {
            $pageIdentifier = $collection->singlePage->slug;
            $errors[] = "La collection a déjà une single page liée ($pageIdentifier)";
        }

        if ($collection->is_auto) {
            $errors[] = 'La collection a été créée automatiquement (généralement pour les pages villes/savoir-faire)';
        }

        return $errors;
    }
}
