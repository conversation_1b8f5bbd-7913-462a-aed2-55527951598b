<?php

namespace App\Domain\Content\Notifications;

use App\Domain\Content\Enums\PageTypes;
use App\Domain\Content\Models\ContentPage;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\SlackMessage;

class ToTranslateStatus extends Notification
{
    use Queueable;

    protected ?array $availableLanguages;

    public function __construct(
        protected ContentPage $page,
        protected string $pageName,
        protected string $pageUrl,
        protected string $city,
        ?array $availableLanguages,
    ) {
        if (\is_array($availableLanguages)) {
            natcasesort($availableLanguages);
        }

        $this->availableLanguages = $availableLanguages;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<string>
     */
    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        $slackMessage = new SlackMessage();

        if ($this->page->type === PageTypes::PAGE_TYPES_ARTISAN) {
            return $slackMessage->text('The artisan page <'.$this->pageUrl.'|'.$this->pageName.'> - *'.$this->city.'* - is ready to be translated.');
        }

        if ($this->page->type === PageTypes::PAGE_TYPES_WORKSHOP) {
            $availableLanguages = '';
            if (\is_array($this->availableLanguages)) {
                $availableLanguages = ' animated in *'.implode(', ', $this->availableLanguages).'*';
            }

            return $slackMessage->text('The workshop page <'.$this->pageUrl.'|'.$this->pageName.'> - *'.$this->city.'* -'.$availableLanguages.' is ready to be translated.');
        }

        \Log::error(
            'Invalid page type for ToTranslateStatus slack notification.',
            [
                'page' => $this->page->toArray(),
            ],
        );

        throw new \InvalidArgumentException('Invalid page type for ToTranslateStatus slack notification.');
    }

    /**
     * Available options: @see \SlackChat::message.
     *
     * @return array<string, mixed>
     */
    public function getSlackApiOptions(): array
    {
        return [
            'unfurl_links' => false,
            'unfurl_media' => false,
        ];
    }
}
