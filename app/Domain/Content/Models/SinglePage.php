<?php

namespace App\Domain\Content\Models;

use App\Domain\Content\Abstracts\CategorizationModel;
use App\Domain\Content\Enums\SinglePageMode;
use App\Models\Tag;
use App\Models\Ville;
use App\Traits\HasTranslation;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * App\Domain\Content\Models\SinglePage.
 *
 * @property int $id
 * @property string $slug
 * @property string $subject_type
 * @property int $subject_id
 * @property int $craft_id
 * @property int $city_id
 * @property SinglePageMode $mode
 * @property string $title
 * @property string $description
 * @property string $meta_title
 * @property string $meta_description
 * @property string|null $seo_title
 * @property string|null $seo_content
 * @property string $hero_image
 * @property string $badge_image
 * @property string $country_code
 * @property bool $is_condensed
 * @property \Carbon\Carbon|null $publish_at
 * @property \Carbon\Carbon|null $expires_at
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property Craft|null $craft
 * @property Ville|null $city
 * @property EloquentCollection<int, \App\Domain\Content\Models\SinglePageSection> $sections
 * @property \Illuminate\Database\Eloquent\Collection<int, static> $customLinkedPages
 * @property \Illuminate\Database\Eloquent\Collection<int, static> $pagesLinkedTo
 * @property \App\Models\Translations\SinglePageTranslation|null $translation
 * @property \Illuminate\Database\Eloquent\Collection<int, \App\Models\Translations\SinglePageTranslation> $translations
 * @property ?CategorizationModel $subject
 * @property array<int|string, mixed> $form_localized
 *
 * @mixin \Eloquent
 */
class SinglePage extends Model implements TranslatableContract
{
    use HasTranslation;

    protected $guarded = [];

    protected $casts = [
        'mode' => SinglePageMode::class,
        'is_condensed' => 'boolean',
        'publish_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    protected array $translatedAttributes = [
        'title',
        'description',
        'meta_title',
        'meta_description',
        'seo_title',
        'seo_content',
    ];

    /* Relationships */
    public function sections(): HasMany
    {
        return $this->hasMany(SinglePageSection::class, 'single_page_id', 'id')->orderBy('order');
    }

    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /** @return BelongsTo<Craft, $this> */
    public function craft(): BelongsTo
    {
        return $this->belongsTo(Craft::class);
    }

    /** @return BelongsTo<Ville, $this> */
    public function city(): BelongsTo
    {
        return $this->belongsTo(Ville::class, 'city_id');
    }

    public function getSubject(): ?CategorizationModel
    {
        $validTypes = [
            Craft::class,
            Ville::class,
            Technique::class,
            Creation::class,
            Collection::class,
            Tag::class,
        ];

        if (empty($this->subject)) {
            return null;
        }

        foreach ($validTypes as $type) {
            if (is_a($this->subject, $type)) {
                return $this->subject;
            }
        }

        throw new \RuntimeException('Invalid subject');
    }

    public function getTechnique(): ?Technique
    {
        if ($this->subject instanceof Technique) {
            return $this->subject;
        }

        return null;
    }

    public function getSubjectOrFail(): CategorizationModel
    {
        $subject = $this->getSubject();
        if ($subject === null) {
            throw new \RuntimeException('No subject found');
        }

        return $subject;
    }

    public function getUrl(): string
    {
        return "/ateliers/$this->slug";
    }

    /**
     * returns linked pages to this page.
     *
     * @return BelongsToMany<SinglePage, $this>
     */
    public function customLinkedPages(): BelongsToMany
    {
        return $this
            ->belongsToMany(self::class, 'single_page_linked_pages', 'single_page_id', 'linked_id')
            ->orderByPivot('order');
    }

    /**
     * returns pages that this page is linked to (reverse of customLinkedPages).
     *
     * @return BelongsToMany<SinglePage, $this>
     */
    public function pagesLinkedTo(): BelongsToMany
    {
        return $this->belongsToMany(self::class, 'single_page_linked_pages', 'linked_id', 'single_page_id');
    }
}
