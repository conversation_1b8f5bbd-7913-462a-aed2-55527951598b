<?php

declare(strict_types=1);

namespace App\Domain\Content;

use App\Domain\Content\Models\Collection;
use App\Domain\Content\Repositories\CollectionRepository;
use App\Infrastructure\Image\PublicImageStorage;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Http\File;

class CollectionService
{
    public function __construct(
        protected readonly CollectionRepository $collectionRepository,
        private readonly PublicImageStorage $publicImageStorage,
    ) {
    }

    /**
     * @param array<string, mixed> $attributes
     *
     * @throws \Exception
     */
    public function store(array $attributes): Collection
    {
        if (empty($attributes['thumbnail_image'])) {
            $attributes['thumbnail_image'] = new File(Collection::getDefaultThumbnailPath());
        }

        $attributes['thumbnail_image'] = $this->publicImageStorage
            ->put(
                Collection::DEFAULT_FOLDER,
                $attributes['thumbnail_image'],
            );

        if ($attributes['thumbnail_image'] === false) {
            $exception = new \RuntimeException("Couldn't persist thumbnail_image to filestorage");
            report($exception);
            throw $exception;
        }

        try {
            $collection = $this->collectionRepository->store($attributes);
        } catch (\Exception $e) {
            if (\is_string($attributes['thumbnail_image'])) {
                $this->publicImageStorage->delete($attributes['thumbnail_image']);
            }
            throw $e;
        }

        return $collection;
    }

    /**
     * @param array<string, mixed> $attributes
     *
     * @throws \Exception
     */
    public function update(Collection $collection, array $attributes): void
    {
        $previousThumbnailImage = $collection->thumbnail_image;

        if (!empty($attributes['thumbnail_image'])) {
            $attributes['thumbnail_image'] = $this->publicImageStorage
                ->put(
                    Collection::DEFAULT_FOLDER,
                    $attributes['thumbnail_image'],
                );

            if ($attributes['thumbnail_image'] === false) {
                $exception = new \RuntimeException("Couldn't persist thumbnail_image to filestorage");
                report($exception);
                throw $exception;
            }
        }

        try {
            $this->collectionRepository->update($collection, $attributes);
        } catch (\Exception $e) {
            $this->publicImageStorage->delete($attributes['thumbnail_image']);

            throw $e;
        }

        $this->publicImageStorage->delete($previousThumbnailImage);
    }

    /**
     * @param array<string|int, string|int> $ids
     * @param array<(int|string), mixed> $relations
     *
     * @return EloquentCollection<int, Collection>
     */
    public function get(array $ids, array $relations): EloquentCollection
    {
        return Collection::active()
            ->whereIn('id', $ids)
            ->with($relations)
            ->get();
    }

    public function activate(Collection $collection): void
    {
        $this->collectionRepository->update($collection, [
            'is_active' => true,
        ]);
    }
}
