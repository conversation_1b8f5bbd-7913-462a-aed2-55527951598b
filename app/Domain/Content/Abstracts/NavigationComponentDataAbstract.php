<?php

declare(strict_types=1);

namespace App\Domain\Content\Abstracts;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\Arr;
use Illuminate\Validation\Factory as ValidatorFactory;

abstract class NavigationComponentDataAbstract
{
    abstract protected function getValidator(?string $country): Validator;

    /**
     * @param array<string, mixed> $data
     */
    public function __construct(
        protected array $data,
        protected readonly ValidatorFactory $validatorFactory
    ) {
    }

    /**
     * @return array<string, mixed>
     */
    public function validate(?string $country): array
    {
        return $this->getValidator($country)->validate();
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * @return array<string, mixed>
     */
    public function toArrayWithAssetsAbsoluteUrl(): array
    {
        return $this->toArray();
    }

    /**
     * @return array<string, mixed>
     */
    public static function getDefaultData(): array
    {
        return [];
    }

    /**
     * @return array<string>
     */
    public static function getDefaultImages(): array
    {
        return [];
    }

    public function applyData(self $dataToApply): void
    {
        $this->checkIsCurrentClass($dataToApply);

        $newDataArrayDot = Arr::dot($dataToApply->toArray());
        foreach ($newDataArrayDot as $key => $value) {
            $this->set($key, $value);
        }
    }

    protected function checkIsCurrentClass(self $class): void
    {
        $validType = static::class;
        if (!is_a($class, $validType, true)) {
            throw new \InvalidArgumentException("Given parameter is not of type $validType");
        }
    }

    public function get(string $dotNotation, mixed $default = null): mixed
    {
        return Arr::get($this->data, $dotNotation, $default);
    }

    public function set(string $dotNotation, mixed $value): void
    {
        Arr::set($this->data, $dotNotation, $value);
    }
}
