<?php

declare(strict_types=1);

namespace App\Domain\Content\DataTables;

use App\Domain\Content\Models\SummarySheet;
use App\Domain\Content\Repositories\SummarySheetRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Ya<PERSON>ra\DataTables\DataTableAbstract;
use Ya<PERSON>ra\DataTables\DataTables;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

final class SummarySheetsDataTable extends DataTable
{
    private const PUBLISHED = 'published';
    private const CREATION = 'creation';

    public function __construct(private readonly DataTables $dataTable)
    {
        parent::__construct();
    }

    /**
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     *
     * @param Builder<SummarySheet> $query
     */
    public function dataTable(Builder $query): DataTableAbstract
    {
        /** @var EloquentDataTable $dataTable */
        $dataTable = $this->dataTable::of($query);

        return $dataTable
            ->filterColumn('workshop', static function (Builder $query, string $keyword): void {
                if (is_numeric($keyword)) {
                    $query->where('w.id', '=', $keyword);

                    return;
                }

                $query->whereRaw('w.nom like ?', [sprintf('%%%s%%', $keyword)]);
            })
            ->filterColumn('artisan', static function (Builder $query, string $keyword): void {
                if (is_numeric($keyword)) {
                    $query->where('a.id', '=', $keyword);

                    return;
                }

                $query->whereRaw('CONCAT(a.prenom, " ", a.nom, " ", IFNULL(a.nom_alternatif, "")) like ?', [sprintf('%%%s%%', mb_trim($keyword))]);
            })
            ->filterColumn('email', static function (Builder $query, string $keyword): void {
                $query->whereRaw('a.email like ?', [sprintf('%%%s%%', mb_trim($keyword))]);
            })
            ->filterColumn('status', function (Builder $query, string $value): void {
                if (preg_match("/$value/", self::PUBLISHED) === 1) {
                    $query->whereNotNull('docs_revision_id');
                } elseif (preg_match("/$value/", self::CREATION) === 1) {
                    $query->whereNull('docs_revision_id');
                }
            })
            ->orderColumn('created_at', static function (Builder $query, string $order): void {
                $query->orderBy('summary_sheets.created_at', $order);
            })
            ->editColumn('status', function (SummarySheet $summarySheet) {
                return \is_string($summarySheet->docs_revision_id) ? 'Publié' : 'Création';
            })
            ->addColumn('workshop', function (SummarySheet $summarySheet): string {
                if (empty($summarySheet->atelier)) {
                    return 'Workshop deleted';
                }
                $route = route('admin.ateliers.edit', $summarySheet->atelier_id);
                $display = "#{$summarySheet->atelier_id} - {$summarySheet->atelier->nom}";

                return '<a href="'.$route.'" target="_blank">'.$display.'</a>';
            })
            ->addColumn('artisan', static function (SummarySheet $summarySheet) {
                if ($summarySheet->atelier?->artisan === null) {
                    return "artisan's workshop deleted";
                }
                $route = route('admin.artisans.edit', $summarySheet->atelier->artisan_id);
                $display = "#{$summarySheet->atelier->artisan_id} - {$summarySheet->atelier->artisan->getNomEtDenomination()}";

                return '<a href="'.$route.'" target="_blank">'.$display.'</a>';
            })
            ->addColumn('email', static function (SummarySheet $summarySheet) {
                return $summarySheet->atelier?->artisan->email;
            })
            ->addColumn('created_at', static function (SummarySheet $summarySheet) {
                if (!$summarySheet->created_at instanceof Carbon) {
                    return '';
                }

                return dateAndTimeLongFormat($summarySheet->created_at);
            })
            ->addColumn('template', static function (SummarySheet $summarySheet) {
                return $summarySheet->template?->name;
            })
            ->addColumn('actions', static function (SummarySheet $summarySheet) {
                $html = <<<HTML
                    <a href="{$summarySheet->getGoogleDocsEditUrl()}" target="_blank">
                        <i class="fa fa-edit" aria-hidden="true"></i>
                    </a>
                    HTML;

                if (\is_string($summarySheet->docs_revision_id)) {
                    $downloadRoute = route('admin.summary-sheet.download', $summarySheet);
                    $deleteRoute = route('admin.summary-sheet.delete', $summarySheet);

                    $html .= <<<HTML
                        <a href="{$downloadRoute}" target="_blank">
                            <i class="fa fa-download" aria-hidden="true"></i>
                        </a>

                        <a class="text-danger" href="#" onclick="javascript:showDeleteSummarySheetModal('{$deleteRoute}')">
                            <i class="fa fa-trash-alt" aria-hidden="true"></i>
                        </a>
                        HTML;
                }

                return $html;
            })
            ->rawColumns(['workshop', 'artisan', 'actions']);
    }

    /**
     * Get query source of dataTable.
     *
     * @return Builder<SummarySheet>
     */
    public function query(SummarySheetRepository $repository): Builder
    {
        return $repository->dataTablesQuery();
    }

    /** Optional method if you want to use html builder. */
    public function html(): \Yajra\DataTables\Html\Builder
    {
        return $this->builder()
            ->setTableId('summary-sheets-table')
            ->columns($this->getColumns())
            ->minifiedAjax('', null, [], ['timeout' => 3000])
            ->dom('Brtip')
            ->orderBy(3)
            ->initComplete('function(){setupSearchColumns(this);}')
            ->orderCellsTop(true)
            ->buttons(
                Button::make(['pageLength']),
            );
    }

    /**
     * @return array<Column>
     */
    protected function getColumns(): array
    {
        return [
            Column::make('id')
                ->title('#')
                ->orderable(false)
                ->searchable(false),
            Column::make('workshop')
                ->title('Atelier')
                ->orderable(false)
                ->searchable(),
            Column::make('artisan')
                ->title('Artisan')
                ->orderable(false)
                ->searchable(),
            Column::make('email')
                ->title('Email')
                ->orderable(false)
                ->searchable(),
            Column::make('created_at')
                ->title('Date')
                ->orderable()
                ->searchable(false),
            Column::make('template')
                ->title('Template')
                ->orderable(false)
                ->searchable(false),
            Column::make('status')
                ->title('Statut')
                ->addClass('select-filter')
                ->width('8%')
                ->selectOptions([
                    self::PUBLISHED => 'Publié',
                    self::CREATION => 'Création',
                ]),
            Column::make('actions')
                ->searchable(false)
                ->orderable(false)
                ->title('Actions'),
        ];
    }
}
