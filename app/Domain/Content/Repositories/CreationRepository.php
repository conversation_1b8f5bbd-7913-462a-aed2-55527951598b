<?php

namespace App\Domain\Content\Repositories;

use App\Domain\Content\Models\Creation;
use App\Infrastructure\Search\Query\SearchService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class CreationRepository
{
    public function __construct(
        private SearchService $searchService,
    ) {
    }

    public function fetch(?string $search = null, int $hitsPerPage = 20, int $page = 1): LengthAwarePaginator
    {
        if ($page < 1) {
            $page = 1;
        }
        $country = config('app.country');

        $query = Creation::query()
            ->active()
            ->select('creations.*')
            /*
             * The relation is checked manually (instead of using whereHas) because the usage of the keyword exists in
             * SQL seems way more time-consuming than checking the result of the join (~10sec vs 170ms)
             */
            ->leftJoin('single_pages', function (JoinClause $join): void {
                $join->on('single_pages.subject_id', '=', 'creations.id')
                    ->where('single_pages.subject_type', '=', (new Creation())->getMorphClass());
            })
            ->leftJoin('workshop_creation as wc', 'wc.creation_id', '=', 'creations.id')
            ->leftJoin('ateliers as at', function ($join): void {
                $join->on('at.id', '=', 'wc.workshop_id')
                    ->whereNull('at.deleted_at')
                    ->where('at.active', true)
                    ->whereNull('at.invisible_until');
            })
            ->leftJoin('lieux as lx', 'lx.id', '=', 'at.lieu_id')
            ->leftJoin('villes as v', 'v.id', '=', 'lx.ville_id')
            ->where('v.country_code', $country)
            ->groupBy('creations.id');

        if ($search) {
            $query->where('creations.name', 'LIKE', "%{$search}%");
        }

        /*
         * Must be done that way to get the count of items after group by.
         * By default, when using ->count() laravel will return the count of items before group by is applied
         */
        $count = DB::table(DB::raw("({$query->toSql()}) as sub"))
            ->mergeBindings($query->getQuery())
            ->count();

        if ($count > 0) {
            /** @var Collection<int, Creation> $results */
            $results = $query
                ->with(['singlePage'])
                ->offset(($page - 1) * $hitsPerPage)
                ->limit($hitsPerPage)
                ->get();
            /*
             * Must be done that way instead of using ->withCount()
             * SQL will compute the count for each item before applying the limit & the offset
             * Doing it this way takes much less time.
             */
            $workshopsCountPerCreationId = DB::table('workshop_creation')
                ->leftJoin('ateliers as at', 'at.id', '=', 'workshop_creation.workshop_id')
                ->leftJoin('lieux as lx', 'lx.id', '=', 'at.lieu_id')
                ->leftJoin('villes as v', 'v.id', '=', 'lx.ville_id')
                ->selectRaw('workshop_creation.creation_id as creation_id, count(workshop_creation.workshop_id) as workshops_count')
                ->whereIn('workshop_creation.creation_id', $results->pluck('id'))
                ->where('v.country_code', $country)
                ->groupBy('workshop_creation.creation_id')
                ->get();
            $results = $results->map(function (Creation $item) use ($workshopsCountPerCreationId): array {
                return [
                    'identifier' => $item->identifier,
                    'name' => $item->name,
                    'algolia_identifier' => $this->searchService->getSearchEngineIdentifierForCategory($item),
                    'thumbnail_image' => $item->getThumbnailImageUrl(),
                    'redirect_url' => $item->singlePage?->getUrl(),
                    'workshops_count' => $workshopsCountPerCreationId
                            ->where('creation_id', $item->id)
                            ->first()?->workshops_count ?? 0,
                ];
            });
        }

        return new LengthAwarePaginator($results ?? collect(), $count, $hitsPerPage, $page);
    }

    /**
     * @param array<string, mixed> $attributes
     */
    public function store(array $attributes): Creation
    {
        try {
            return Creation::create($attributes);
        } catch (\Exception $e) {
            report($e);

            throw $e;
        }
    }

    /**
     * @param array<string, mixed> $attributes
     */
    public function update(Creation $creation, array $attributes): void
    {
        if (!$creation->update($attributes)) {
            $exception = new \RuntimeException("Couldn't update Creation [{$creation->getKey()}]");
            report($exception);
            throw $exception;
        }
    }
}
