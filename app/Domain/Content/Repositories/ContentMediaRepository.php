<?php

namespace App\Domain\Content\Repositories;

use Illuminate\Support\Facades\DB;

class ContentMediaRepository
{
    public function dataTablesQuery(): \Illuminate\Database\Query\Builder
    {
        $first = DB::table('content_media as cm')
            ->leftJoin('admins as ad', 'ad.id', '=', 'cm.creator_id')
            ->leftJoin('content_pages as cp', 'cp.content_media_id', '=', 'cm.id')
            ->left<PERSON>oin('onboardings as o', 'o.content_media_id', '=', 'cm.id')
            ->leftJoin('artisans as oa', 'oa.id', '=', 'o.artisan_id')
            ->leftJoin('artisans as a', 'a.id', '=', 'cp.type_id')
            ->left<PERSON>oin('ateliers as w', 'w.id', '=', 'cp.type_id')
            ->whereNull('cm.deleted_at')
            ->whereNull('cp.type')
            ->whereRaw('o.status <> "is_onboard"')
            ->select(['cm.id as mediaId', 'cm.source as mediaSource', 'cm.status as mediaStatus', 'cm.notes as mediaNotes'])
            ->addSelect(['ad.id as creatorId', 'ad.prenom as creatorFirstname', 'ad.nom as creatorLastname'])
            ->addSelect(['cm.priority as mediaPriority'])
            ->selectRaw('IFNULL(cp.type, "onboarding") as pageType')
            ->addSelect(['a.prenom as artisanFirstname', 'a.nom as artisanLastname', 'a.id as artisanId'])
            ->addSelect(['w.nom as workshopName', 'w.id as workshopId'])
            ->addSelect(['oa.id as onboardedId', 'oa.prenom as onboardedFirstname', 'oa.nom as onboardedLastname', 'oa.nom_alternatif as onboardedAltName']);

        $union = DB::table('content_media as cm')
            ->leftJoin('admins as ad', 'ad.id', '=', 'cm.creator_id')
            ->leftJoin('content_pages as cp', 'cp.content_media_id', '=', 'cm.id')
            ->leftJoin('onboarding_pages as op', 'op.content_page_id', '=', 'cp.id')
            ->leftJoin('onboardings as o', 'o.id', '=', 'op.onboarding_id')
            ->leftJoin('artisans as oa', 'oa.id', '=', 'o.artisan_id')
            ->leftJoin('artisans as a', 'a.id', '=', 'cp.type_id')
            ->leftJoin('ateliers as w', 'w.id', '=', 'cp.type_id')
            ->whereNull('cm.deleted_at')
            ->whereNotNull('cp.type')
            ->select(['cm.id as mediaId', 'cm.source as mediaSource', 'cm.status as mediaStatus', 'cm.notes as mediaNotes'])
            ->addSelect(['ad.id as creatorId', 'ad.prenom as creatorFirstname', 'ad.nom as creatorLastname'])
            ->addSelect(['cm.priority as mediaPriority'])
            ->selectRaw('IFNULL(cp.type, "onboarding") as pageType')
            ->addSelect(['a.prenom as artisanFirstname', 'a.nom as artisanLastname', 'a.id as artisanId'])
            ->addSelect(['w.nom as workshopName', 'w.id as workshopId'])
            ->addSelect(['oa.id as onboardedId', 'oa.prenom as onboardedFirstname', 'oa.nom as onboardedLastname', 'oa.nom_alternatif as onboardedAltName'])
            ->union($first);

        return DB::table(DB::raw(sprintf('(%s) as x', $union->toSql())))
            ->select(['x.mediaId', 'x.mediaSource', 'x.mediaStatus', 'x.mediaNotes'])
            ->addSelect(['x.creatorId', 'x.creatorFirstname', 'x.creatorLastname'])
            ->addSelect(['x.mediaPriority', 'x.pageType'])
            ->addSelect(['x.artisanFirstname', 'x.artisanLastname', 'x.artisanId'])
            ->addSelect(['x.workshopName', 'x.workshopId'])
            ->addSelect(['x.onboardedId', 'x.onboardedFirstname', 'x.onboardedLastname', 'x.onboardedAltName']);
    }
}
