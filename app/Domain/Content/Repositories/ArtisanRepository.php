<?php

namespace App\Domain\Content\Repositories;

use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use App\Domain\Accounting\Orders\Enums\PaymentMethodType;
use App\Domain\Accounting\Orders\Enums\ProductType;
use App\Domain\Booking\Enums\BookingOrigin;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Workshop\Enums\BookingOptionType;
use App\Enums\Currency;
use App\Enums\Event\EventType;
use App\Enums\Order\OrderStatus;
use App\Infrastructure\Crm\Hubspot\HubspotModelTransformer;
use App\Models\Artisan;
use App\Models\Reservation;
use App\Models\Tag;
use App\Shared\Amount;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Database\Query\Builder as BuilderContracts;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\LazyCollection;

class ArtisanRepository
{
    public function getArtisansAsFacets(array $ids)
    {
        return Artisan::whereIn('artisans.id', $ids)
            ->with([
                'photo_profil',
                'locations',
            ])
            ->get()
            ->map(function (Artisan $artisan) {
                return [
                    'id' => $artisan->id,
                    'permalien' => $artisan->permalien,
                    'photo_profil' => $artisan->photo_profil->getUrl(),
                    'nom' => $artisan->nom,
                    'prenom' => $artisan->prenom,
                    'city' => $artisan->mainLocation()->city,
                    'job' => $artisan->metier_nom,
                ];
            });
    }

    /**
     * @fixme Any evolution of the business model, in particular $artisan->active, will also have an impact on this code
     *
     * @return ?object{id: int, prenom: string, nom: string, nom_alternatif: string, active: int}
     */
    public static function findFromSponsorCode(int $artisanId, string $sponsorCode): ?object
    {
        return \DB::table('artisans AS a')
            ->select(['cpi.id', 'a.prenom', 'a.nom', 'a.nom_alternatif', 'a.active'])
            ->whereNull('a.deleted_at')
            ->where('a.id', '<>', $artisanId)
            ->join('codesparrainageinvitation AS cpi', function (JoinClause $builder) use ($sponsorCode): void {
                $builder->on('a.id', '=', 'cpi.user_id');
                $builder->where('cpi.user_type', '=', (new Artisan())->getMorphClass());
                $builder->where('cpi.code_reduction', '=', $sponsorCode);
            })
            ->first();
    }

    public function getNotSynchronizedArtisans(): LazyCollection
    {
        return Artisan::with(HubspotModelTransformer::getRequiredArtisanRelationship())
            ->whereNull('synchronized_at')
            ->orWhereColumn('synchronized_at', '<', 'updated_at')
            ->lazyById(500);
    }

    /**
     * @return LazyCollection<int, Artisan>
     */
    public function getArtisansNeedingStatisticsUpdate(): LazyCollection
    {
        return Artisan::with(HubspotModelTransformer::getRequiredArtisanRelationship())
            ->lazyById(500);
    }

    public function updateSynchronizationDateByIds(array $artisansIds, CarbonImmutable $date): void
    {
        Artisan::withoutTimestamps(fn () => Artisan::whereIn('id', $artisansIds)->update(['synchronized_at' => $date]));
    }

    public function getTurnoverWithoutCommission(int $artisanId): float
    {
        return $this->getTurnoverRawQuery($artisanId);
    }

    public function getYearToDateTurnoverWithoutCommission(int $artisanId): float
    {
        return $this->getTurnoverRawQuery($artisanId, true);
    }

    /**
     * return an array with columns month, attendees, turnover for th artisan.
     *
     * @return array<int, object{year: int, month: int, turnover: int, attendees: int, currency: string}>
     */
    public function getParticipantsCountAndTurnOverByMonth(Artisan $artisan): array
    {
        $to = new CarbonImmutable('last day of last month');
        $from = $to->subYear();

        return DB::select(
            DB::raw(
                <<<SQL
                    select
                        commission_invoices.year,
                        commission_invoices.month,
                        commission_invoices.currency,
                        CAST(SUM(
                            CAST(commission_invoices.total_sales AS SIGNED) -
                            CAST(commission_invoices.total_including_vat AS SIGNED)
                        ) AS SIGNED) AS turnover,
                        CAST((
                         select sum(commission_invoice_workshops.participants_count)
                         from commission_invoice_workshops
                         where commission_invoices.id = commission_invoice_workshops.commission_invoice_id
                        ) AS SIGNED) AS attendees
                    from commission_invoices
                    where commission_invoices.status = 'issued'
                      and commission_invoices.issue_date between '{$from->toDateString()}' and '{$to->toDateString()}'
                      and commission_invoices.recipient_id = '{$artisan->getKey()}'
                    group by commission_invoices.year, commission_invoices.month
                    SQL,
            )->getValue(DB::connection()->getQueryGrammar()),
        );
    }

    private function getTurnoverRawQuery(int $artisanId, bool $ytd = false): float
    {
        $turnover = DB::table('commission_invoices')
            ->selectRaw('GREATEST(0, SUM(total_sales) - SUM(total_including_vat)) AS total, currency')
            ->where('recipient_id', '=', $artisanId)
            ->where('status', '=', InvoiceStatus::Issued->value)
            ->groupBy('currency');

        if ($ytd) {
            $turnover->where('issue_date', '>=', Carbon::now()->startOfYear());
        }
        $turnover = $turnover->first();

        if ($turnover === null) {
            return 0;
        }

        return Amount::make($turnover->total, Currency::from($turnover->currency))->floatValue();
    }

    public function getPayoutStatsForWorkshops(Artisan $artisan, array $workshopIds, CarbonInterface $dateFrom, CarbonInterface $dateTo): array
    {
        return DB::table('commission_invoices')
            ->join('commission_invoice_workshops', 'commission_invoices.id', '=', 'commission_invoice_workshops.commission_invoice_id')
            ->selectRaw('
            commission_invoice_workshops.workshop_id,
            commission_invoices.year,
            commission_invoices.month,
            commission_invoices.currency,
            SUM(commission_invoice_workshops.sales_amount - commission_invoice_workshops.total_amount) as payout
        ')
            ->where('commission_invoices.status', InvoiceStatus::Issued)
            ->whereBetween('commission_invoices.issue_date', [$dateFrom->toDateString(), $dateTo->toDateString()])
            ->where('commission_invoices.recipient_id', $artisan->getKey())
            ->whereIn('commission_invoice_workshops.workshop_id', $workshopIds)
            ->groupBy('commission_invoice_workshops.workshop_id', 'commission_invoices.year', 'commission_invoices.month', 'commission_invoices.currency')
            ->orderBy('commission_invoice_workshops.workshop_id')
            ->get()
            ->toArray();
    }

    public function getFuturePayoutStatsForWorkshops(array $workshopIds, CarbonInterface $from): array
    {
        return DB::table('reservations')
            ->join('evenements', 'reservations.evenement_id', '=', 'evenements.id')
            ->join('ateliers', 'evenements.atelier_id', '=', 'ateliers.id')
            ->selectRaw('ateliers.id as workshop_id, YEAR(evenements.start) as year, MONTH(evenements.start) as month, ateliers.currency, SUM((reservations.prix * ((100 - reservations.commission_percent) / 100)) * 100) as payout')
            ->whereIn('evenements.atelier_id', $workshopIds)
            ->whereIn('reservations.status', [BookingStatus::BOOKING_CONFIRMED, BookingStatus::BOOKING_BILLED])
            ->where('reservations.origin', '<>', BookingOrigin::Artisan)
            ->whereNull('reservations.deleted_at')
            ->where('evenements.start', '>', $from)
            ->where('evenements.start', '<=', $from->copy()->endOfYear()->endOfDay())
            ->groupBy('workshop_id', 'year', 'month', 'ateliers.currency')
            ->orderBy('workshop_id')
            ->get()
            ->toArray();
    }

    /**
     * after 2024, new invoice organisation
     * join tables invoices -> invoice_lines -> invoice_line_payment_method filter on [type = workshop_gift_card]
     * before 2024 old invoice organisation
     * join tables invoices -> invoice_lines filter on [code not null + type = App\Models\Reservation + product_type = 3].
     */
    public function getGiftCardsStatsForWorkshops(Artisan $artisan, array $workshopIds, CarbonInterface $from, CarbonInterface $to): array
    {
        $emitter = (new Artisan())->getMorphClass();
        $results = [];

        // Handle the period before 2024 if applicable
        if ($from->year < 2024) {
            $before2024End = $to->year < 2024 ? $to : CarbonImmutable::createFromDate(2023, 12, 31);
            $results = array_merge(
                $results,
                $this->getGiftCardsStatsQuery($artisan->getKey(), $from, $before2024End, $emitter, $workshopIds, true)->get()->toArray(),
            );
        }

        // Handle the period from 2024 onwards if applicable
        if ($to->year >= 2024) {
            $from2024Start = $from->year < 2024 ? CarbonImmutable::createFromDate(2024, 1, 1) : $from;
            $results = array_merge(
                $results,
                $this->getGiftCardsStatsQuery($artisan->getKey(), $from2024Start, $to, $emitter, $workshopIds, false)->get()->toArray(),
            );
        }

        return $results;
    }

    private function getGiftCardsStatsQuery(
        int $artisanId,
        CarbonInterface $from,
        CarbonInterface $to,
        string $emitter,
        array $workshopIds,
        bool $isBefore2024,
    ): BuilderContracts {
        $query = DB::table('invoice_lines')
            ->select(
                'invoice_lines.workshop_id',
                DB::raw('YEAR(invoices.issue_date) as year'),
                DB::raw('MONTH(invoices.issue_date) as month'),
                'invoice_lines.currency',
                DB::raw('CAST(SUM('.($isBefore2024 ? 'invoice_lines.quantity' : 'COALESCE(invoice_line_payment_methods.tickets, 0)').') AS UNSIGNED) as total'),
            )
            ->join('invoices', 'invoice_lines.invoice_id', '=', 'invoices.id')
            ->whereBetween('invoices.issue_date', [$from->toDateString(), $to->toDateString()])
            ->where('invoices.emitter_id', $artisanId)
            ->where('invoices.emitter_type', $emitter)
            ->whereIn('invoice_lines.workshop_id', $workshopIds)
            ->groupBy('invoice_lines.workshop_id', 'year', 'month', 'invoice_lines.currency')
            ->orderBy('invoice_lines.workshop_id');

        if ($isBefore2024) {
            $relatedType = (new Reservation())->getMorphClass();
            $query->whereNotNull('invoice_lines.code')
                ->where('invoice_lines.related_type', $relatedType)
                ->where('invoice_lines.product_type', ProductType::WorkshopGiftCard->value);
        } else {
            $query->join('invoice_line_payment_methods', 'invoice_line_payment_methods.invoice_line_id', '=', 'invoice_lines.id')
                ->where('invoice_line_payment_methods.type', PaymentMethodType::WorkshopGiftCard);
        }

        return $query;
    }

    public function getFutureGiftCardStatsForWorkshops(
        array $workshopIds,
        CarbonInterface $fromDate,
    ): array {
        return DB::table('order_item_tickets')
            ->select(
                'order_item_tickets.workshop_id as workshop_id',
                DB::raw('YEAR(evenements.start) as year'),
                DB::raw('MONTH(evenements.start) as month'),
                'order_discounts.currency as currency',
                DB::raw('CAST(SUM(order_item_tickets.adult_tickets + order_item_tickets.child_tickets) AS UNSIGNED ) as total'),
            )
            ->join('order_items', function ($join): void {
                $join->on('order_items.item_id', '=', 'order_item_tickets.id')
                    ->where('order_items.item_type', '=', 'order_item_ticket');
            })
            ->join('commandes', 'order_items.order_id', '=', 'commandes.id')
            ->join('order_discounts', function ($join): void {
                $join->on('order_discounts.order_id', '=', 'commandes.id')
                    ->on('order_item_tickets.workshop_id', '=', 'order_discounts.workshop_id');
            })
            ->join('reservations', 'reservations.id', '=', 'order_item_tickets.reservation_id')
            ->join('evenements', 'reservations.evenement_id', '=', 'evenements.id')
            ->whereIn('evenements.atelier_id', $workshopIds)
            ->where('reservations.status', 'confirmed')
            ->where('reservations.origin', '<>', 'artisan')
            ->whereNull('reservations.deleted_at')
            ->where('evenements.start', '>', $fromDate->toDateString())
            ->where('commandes.status', OrderStatus::Paid->value)
            ->groupBy('order_item_tickets.workshop_id', 'year', 'month', 'order_discounts.currency')
            ->orderBy('order_item_tickets.workshop_id')
            ->get()
            ->toArray();
    }

    public function getArtisanSelectorBuilder(): BuilderContracts
    {
        return DB::table('artisans')
            ->leftJoin('artisan_location AS al', function (JoinClause $join): void {
                $join
                    ->on('al.artisan_id', '=', 'artisans.id')
                    ->where('al.is_main', '=', true);
            })
            ->join('lieux', 'al.location_id', '=', 'lieux.id')
            ->join('villes', 'lieux.ville_id', '=', 'villes.id')
            ->join('crafts', 'crafts.id', '=', 'artisans.craft_id')
            ->select(
                'artisans.id AS id',
                'artisans.prenom AS first_name',
                'artisans.nom AS last_name',
                'artisans.email AS email',
                'artisans.nom_alternatif AS alternative_name',
                'artisans.country_code',
                'crafts.name AS craft',
                'villes.ville_nom AS city',
            )
            ->whereNull('artisans.deleted_at')
            ->orderBy('artisans.id');
    }

    public function getPrivateBookingPayoutForArtisan(Artisan $artisan): int
    {
        return (int) DB::table('commission_invoice_workshop_bookings')
            ->selectRaw('SUM(commission_invoice_workshop_bookings.total_price - commission_invoice_workshop_bookings.commission_amount) as payout')
            ->join('commission_invoice_workshops', 'commission_invoice_workshops.id', '=', 'commission_invoice_workshop_bookings.commission_invoice_workshop_id')
            ->join('commission_invoices', 'commission_invoices.id', '=', 'commission_invoice_workshops.commission_invoice_id')
            ->join('reservations', 'reservations.id', '=', 'commission_invoice_workshop_bookings.booking_id')
            ->join('evenements', 'evenements.id', '=', 'reservations.evenement_id')
            ->where('commission_invoices.status', '=', InvoiceStatus::Issued)
            ->where('commission_invoices.recipient_id', '=', $artisan->getKey())
            ->whereIn('evenements.type', EventType::getPrivateTypes())
            ->value('payout');
    }

    public function getFuturPrivateBookingPayoutForArtisan(Artisan $artisan): int
    {
        $start = Carbon::now()->startOfMonth();

        return (int) DB::table('reservations')
            ->selectRaw('SUM((reservations.prix * ((100-reservations.commission_percent)/100))*100) as payout')
            ->join('evenements', 'evenements.id', '=', 'reservations.evenement_id')
            ->join('ateliers', 'ateliers.id', '=', 'evenements.atelier_id')
            ->where('ateliers.artisan_id', '=', $artisan->getKey())
            ->where('reservations.status', '=', BookingStatus::BOOKING_CONFIRMED)
            ->where('reservations.origin', '<>', BookingOrigin::Artisan)
            ->whereIn('evenements.type', EventType::getPrivateTypes())
            ->where('evenements.start', '>', $start)
            ->value('payout');
    }

    public function getArtisansWithoutFuturEventsBuilder(): Builder
    {
        return $this->getArtisansBuilderForFuturEvents()
            ->whereNull('artisans.add_events_reminded_at')
            ->whereDoesntHave('ateliers.evenements', static fn ($builder) => $builder
                ->where('start', '>', Carbon::now())
                ->whereNull('cancelled_at'));
    }

    public function getArtisansWithFuturEventsBuilder(): Builder
    {
        return $this->getArtisansBuilderForFuturEvents()
            ->whereNotNull('artisans.add_events_reminded_at')
            ->whereHas('ateliers.evenements', static fn ($builder) => $builder
                ->where('start', '>', Carbon::now())
                ->whereNull('cancelled_at'));
    }

    private function getArtisansBuilderForFuturEvents(): Builder
    {
        return Artisan::query()
            ->select('artisans.*')
            ->join('ateliers', 'ateliers.artisan_id', '=', 'artisans.id')
            ->leftJoin(
                'atelier_tag AS at',
                static fn (JoinClause $builder) => $builder
                    ->on('ateliers.id', '=', 'at.atelier_id')
                    ->where('at.tag_id', '=', Tag::GROUP_ONLY),
            )
            ->leftJoin(
                'booking_options as bo',
                static fn (JoinClause $builder) => $builder
                    ->on('ateliers.id', '=', 'bo.workshop_id')
                    ->where('bo.type', '=', BookingOptionType::PrivateClientManagedLocation)
                    ->where('bo.is_active', '=', true),
            )
            // We don't want group only (managed by tag for now)
            ->whereNull('at.tag_id')
            // We don't want workshop available for privatization on client location (previously on the go)
            ->whereNull('bo.id')
            ->where('artisans.active', '=', true)
            ->where(static fn ($builder) => $builder
                ->orWhereNull('ateliers.date_fin_message_absence')
                ->orWhere('ateliers.date_fin_message_absence', '<', Carbon::now()))
            ->orderBy('artisans.id')
            ->groupBy('artisans.id');
    }
}
