<?php

declare(strict_types=1);

namespace App\Domain\Content\Repositories;

use App\Domain\Content\Models\Collection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as LaravelCollection;

class CollectionRepository
{
    /**
     * @param array<string> $relations
     */
    public function findByIdentifier(string $identifier, array $relations = []): Collection
    {
        return Collection::where('identifier', $identifier)->with($relations)->firstOrFail();
    }

    /**
     * @param array<int|string> $ids
     *
     * @return LaravelCollection<int, Collection>
     */
    public function getByIds(array $ids): LaravelCollection
    {
        return Collection::findMany($ids);
    }

    /**
     * @return LaravelCollection<int, Collection>
     */
    public function getActive(): LaravelCollection
    {
        return Collection::active()->with(['translations'])->get();
    }

    /**
     * @return LengthAwarePaginator<Collection>
     */
    public function fetch(?string $search = null, int $hitsPerPage = 20, int $page = 1): LengthAwarePaginator
    {
        /** @var Builder<Collection> $query */
        $query = Collection::query()
            ->active()
            ->with(['singlePage']);

        if (!empty($search)) {
            $query->where('name', 'LIKE', "%{$search}%");
        }

        return $query->paginate(
            perPage: $hitsPerPage,
            page: $page
        );
    }

    /**
     * @return Builder<Collection>
     */
    public function dataTablesQuery(): Builder
    {
        return Collection::query()->with(['translations']);
    }

    /**
     * @param array<string, mixed> $attributes
     *
     * @throws \Exception
     */
    public function store(array $attributes): Collection
    {
        try {
            return Collection::create($attributes);
        } catch (\Exception $e) {
            report($e);

            throw $e;
        }
    }

    /**
     * @param array<string, mixed> $attributes
     */
    public function update(Collection $collection, array $attributes): void
    {
        if (!$collection->update($attributes)) {
            $exception = new \RuntimeException("Couldn't update Collection [{$collection->getKey()}]");
            report($exception);
            throw $exception;
        }
    }
}
