<?php

namespace App\Domain\Content\Repositories;

use App\Infrastructure\Search\Query\SearchService;
use App\Models\Ville;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class VilleRepository
{
    public function __construct(
        protected SearchService $searchService
    ) {
    }

    public function fetch(?string $search = null, int $hitsPerPage = 20, int $page = 1): LengthAwarePaginator
    {
        if ($page < 1) {
            $page = 1;
        }
        $query = Ville::query()
            ->active()
            ->select('villes.*')
            ->leftJoin('lieux as lx', 'lx.ville_id', '=', 'villes.id')
            ->leftJoin('ateliers as at', function ($join): void {
                $join->on('at.lieu_id', '=', 'lx.id')
                    ->whereNull('at.deleted_at')
                    ->where('at.active', true)
                    ->whereNull('at.invisible_until');
            })
            ->where('villes.country_code', config('app.country'))
            ->whereNotNull('at.id')
            ->whereHas('singlePage')
            ->groupBy('villes.id');

        if ($search) {
            $query->where('villes.ville_nom', 'LIKE', "%{$search}%");
        }

        /*
         * Must be done that way to get the count of items after group by.
         * By default, when using ->count() laravel will return the count of items before group by is applied
         */
        $count = DB::table(DB::raw("({$query->toSql()}) as sub"))
            ->mergeBindings($query->getQuery())
            ->count();

        if ($count > 0) {
            /** @var Collection<int, Ville> $results */
            $results = $query
                ->with(['singlePage'])
                ->offset(($page - 1) * $hitsPerPage)
                ->limit($hitsPerPage)
                ->get();
            /*
             * Must be done that way instead of using ->withCount()
             * SQL will compute the count for each item before applying the limit & the offset
             * Doing it this way takes much less time.
             */
            $workshopsCountPerVilleId = DB::table('villes')
                ->selectRaw('villes.id as ville_id, count(at.id) as workshops_count')
                ->leftJoin('lieux as lx', 'lx.ville_id', '=', 'villes.id')
                ->leftJoin('ateliers as at', function ($join): void {
                    $join->on('at.lieu_id', '=', 'lx.id')
                        ->whereNull('at.deleted_at')
                        ->where('at.active', true)
                        ->whereNull('at.invisible_until');
                })
                ->whereIn('villes.id', $results->pluck('id'))
                ->groupBy('villes.id')
                ->get();
            $results = $results->map(function (Ville $city) use ($workshopsCountPerVilleId): array {
                return [
                    'permalien' => $city->permalien,
                    'ville_nom' => $city->ville_nom,
                    'algolia_identifier' => $this->searchService->getSearchEngineIdentifierForCategory($city),
                    'thumbnail_image' => $city->getThumbnailImageUrl(),
                    'redirect_url' => $city->singlePage?->getUrl(),
                    'workshops_count' => $workshopsCountPerVilleId
                            ->where('ville_id', $city->id)
                            ->first()?->workshops_count ?? 0,
                ];
            });
        }

        return new LengthAwarePaginator($results ?? collect(), $count, $hitsPerPage, $page);
    }
}
