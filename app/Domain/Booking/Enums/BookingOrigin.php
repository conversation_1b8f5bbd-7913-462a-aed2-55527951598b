<?php

declare(strict_types=1);

namespace App\Domain\Booking\Enums;

enum BookingOrigin: string
{
    case Artisan = 'artisan';

    case Wecandoo = 'wecandoo';

    case Customer = 'customer';

    case Partner = 'partner';

    case Widget = 'widget';

    case NatureEtDecouvertes = 'nature_et_decouvertes';

    public function getLabel(): ?string
    {
        return __(sprintf('enums.booking.origins.%s', $this->value));
    }

    public function getAlternateLabel(): ?string
    {
        return match ($this) {
            self::Artisan => __('enums.booking.origins_alternate.direct'),
            self::Customer => __('enums.booking.origins_alternate.website'),
            self::Widget => __('enums.booking.origins_alternate.widget'),
            self::NatureEtDecouvertes => __(sprintf('enums.booking.origins.%s', $this->value)),
            default => __('enums.booking.origins_alternate.other'),
        };
    }
}
