<?php

declare(strict_types=1);

namespace App\Domain\Booking\Enums;

use App\Enums\ABaseEnum;

class BookingStatus extends ABaseEnum
{
    /**
     * @var string
     */
    final public const BOOKING_CONFIRMED = 'confirmed';

    /**
     * @var string
     */
    final public const BOOKING_PENDING = 'pending';

    /**
     * @var string
     */
    final public const BOOKING_CANCELLED = 'cancelled';

    /**
     * @var string
     */
    final public const BOOKING_STANDBY = 'standby';

    /**
     * @var string
     */
    final public const BOOKING_BILLED = 'billed';

    /**
     * @var string
     */
    final public const BOOKING_AWAITING_PAYMENT = 'awaiting_payment';

    public static function getLabel(string $value, ?string $locale = null): ?string
    {
        return __(sprintf('enums.booking.status.%s', $value), locale: $locale);
    }

    /**
     * @return array<string>
     */
    public static function notBilledBooking(): array
    {
        return [
            self::BOOKING_CONFIRMED,
            self::BOOKING_PENDING,
            self::BOOKING_STANDBY,
            self::BOOKING_AWAITING_PAYMENT,
            self::BOOKING_CANCELLED,
        ];
    }

    /**
     * Bookings that are waiting for an user action and can expire.
     *
     * @return array<string>
     */
    public static function onGoing(): array
    {
        return [
            self::BOOKING_PENDING,
            self::BOOKING_AWAITING_PAYMENT,
        ];
    }

    /**
     * @return array<string>
     */
    public static function cancelable(): array
    {
        return [
            self::BOOKING_CONFIRMED,
            self::BOOKING_PENDING,
            self::BOOKING_STANDBY,
            self::BOOKING_AWAITING_PAYMENT,
        ];
    }
}
