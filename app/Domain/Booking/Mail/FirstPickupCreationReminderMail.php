<?php

declare(strict_types=1);

namespace App\Domain\Booking\Mail;

use App\Domain\Booking\Models\PickupCreationReminder;
use App\Mail\BaseMailable;
use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class FirstPickupCreationReminderMail extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function __construct(protected readonly PickupCreationReminder $pickupCreationReminder, protected readonly Reservation $booking)
    {
        parent::__construct();
    }

    public function build(): Mailable
    {
        return $this->markdown('emails.participant.first-pickup-creation-reminder')
            ->with('workshopName', $this->booking->evenement->atelier->nom)
            ->with('artisanName', $this->booking->evenement->atelier->artisan->prenom)
            ->with('pickupReception', $this->pickupCreationReminder->pickup_reception)
            ->with('dueDate', dateLongFormat($this->pickupCreationReminder->pickup_due_date))
            ->with('pickupInfo', $this->pickupCreationReminder->pickup_info)
            ->with('location', $this->booking->evenement->getLieu()?->getFullAddress())
            ->subject(__('emails/participant/booking-management.pickup_creation_reminder.first_reminder.title'));
    }
}
