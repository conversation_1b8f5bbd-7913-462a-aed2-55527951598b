<?php

declare(strict_types=1);

namespace App\Domain\Booking\Notifications;

use App\Models\Evenement;
use App\Models\Reservation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\SlackMessage;

/** @SuppressWarnings(PHPMD) */
class StandbyBookingError extends Notification
{
    use Queueable;

    public function __construct(
        protected readonly Evenement $event,
        protected readonly Reservation $booking,
        protected readonly string $reason,
        protected readonly string $origin,
    ) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<string>
     */
    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage())
            ->text(sprintf(
                ":alert::alert::alert: Unable to update booking status :alert::alert::alert:\n*Event ID:* %s\n*Booking ID:* %s\n\n*Reason: %s*\n*Origin:* %s",
                $this->event->getKey(),
                $this->booking->getKey(),
                $this->reason,
                $this->origin,
            ));
    }
}
