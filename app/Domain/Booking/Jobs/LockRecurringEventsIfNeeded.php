<?php

declare(strict_types=1);

namespace App\Domain\Booking\Jobs;

use App\Domain\Booking\Events\RecurringEventsCreated;
use App\Models\EvenementRecurrent;
use Illuminate\Contracts\Queue\ShouldQueue;

final readonly class LockRecurringEventsIfNeeded implements ShouldQueue
{
    public function handle(RecurringEventsCreated $recurringEventsCreated): void
    {
        $events = EvenementRecurrent::findOrFail($recurringEventsCreated->id)->evenements()->get();

        foreach ($events as $event) {
            $event->lockIfNeeded();
        }
    }
}
