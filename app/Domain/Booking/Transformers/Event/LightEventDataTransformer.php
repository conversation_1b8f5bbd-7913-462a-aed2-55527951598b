<?php

declare(strict_types=1);

namespace App\Domain\Booking\Transformers\Event;

use App\Domain\Booking\QueryResults\LightEvent;

class LightEventDataTransformer
{
    /**
     * @return array<string, mixed>
     */
    public static function transform(LightEvent $data): array
    {
        return [
            'id' => $data->id,
            'atelier_id' => $data->atelierId,
            'start' => $data->start
                ->setTimezone($data->timezone)
                ->format(\DateTimeInterface::RFC3339),
            'end' => $data->end
                ->setTimezone($data->timezone)
                ->format(\DateTimeInterface::RFC3339),
            'timezone' => $data->timezone,
        ];
    }
}
