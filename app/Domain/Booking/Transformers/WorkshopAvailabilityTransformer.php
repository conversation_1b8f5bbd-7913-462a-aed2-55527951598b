<?php

declare(strict_types=1);

namespace App\Domain\Booking\Transformers;

use App\Domain\Booking\QueryResults\WorkshopAvailability;
use App\Domain\Booking\Transformers\Event\LightEventDataTransformer;

class WorkshopAvailabilityTransformer
{
    /** @return array<string, mixed> */
    public static function transform(WorkshopAvailability $workshopAvailability): array
    {
        $latestEvent = $workshopAvailability->latestEvent;
        $nextBookableEvent = $workshopAvailability->nextBookableEvent;

        return [
            'latest_event' => $latestEvent !== null ? LightEventDataTransformer::transform($latestEvent) : null,
            'next_bookable_event' => $nextBookableEvent !== null ? LightEventDataTransformer::transform($nextBookableEvent) : null,
            'away_message' => $workshopAvailability->awayMessage,
            'away_until' => $workshopAvailability
                ->awayUntil
                ?->setTimezone($workshopAvailability->timezone)
                ->format(\DateTimeInterface::RFC3339),
            'timezone' => $workshopAvailability->timezone,
        ];
    }
}
