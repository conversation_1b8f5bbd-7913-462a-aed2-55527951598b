<?php

declare(strict_types=1);

namespace App\Domain\Booking\Events;

use App\Models\Admin;
use App\Models\Evenement;
use App\Models\User;
use App\Models\UserArtisan;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

final class EventCancelled
{
    use Dispatchable;
    use SerializesModels;

    public function __construct(
        public readonly Evenement $event,
        public readonly string $origin,
        public readonly Admin|User|UserArtisan $causer,
        public readonly ?string $additionalContext,
    ) {
    }
}
