<?php

declare(strict_types=1);

namespace App\Domain\Booking\Validators;

use App\Domain\Booking\Exceptions\Event\StackedSameWorkshopException;
use App\Domain\Booking\Repositories\EventRepository;
use App\Models\Atelier;
use Carbon\CarbonInterface;

final readonly class EventValidator
{
    public function __construct(private EventRepository $eventRepository)
    {
    }

    /**
     * @param array<string, int> $data
     */
    public function validateEventCutAndCapacity(array $data): void
    {
        if ($data['capacity'] < 1) {
            \Log::error('Trying to create an event with capacity lower than 1', [$data]);
            throw new \InvalidArgumentException('Event capacity lower than 1');
        }

        if ($data['cut'] < 1) {
            \Log::error('Trying to create an event with cut lower than 1', [$data]);
            throw new \InvalidArgumentException('Event cut lower than 1');
        }

        if ($data['capacity'] < $data['cut']) {
            \Log::error('Trying to create an event with capacity lower than cut', [$data]);
            throw new \InvalidArgumentException('Event capacity lower than cut');
        }
    }

    /**
     * An event can be created if:
     *     - There is no other event for the same workshop at the same start date.
     *     - There is no overlapping event for the same workshop,
     *       except when the newly created event starts at the same time as an event from a cumulable workshop.
     *
     * @throws StackedSameWorkshopException
     */
    public function validateNoConflictingEvent(Atelier $workshop, CarbonInterface $start, CarbonInterface $end): void
    {
        // If an event already exists at the same start for the same workshop, there's a conflict
        if (\count($this->eventRepository->findByStartAndWorkshop($start, $workshop)) > 0) {
            throw new StackedSameWorkshopException($workshop->getKey(), $workshop->nom, $start, $end);
        }

        // If the workshop does not already have an event in the desired time slot, the event can be created
        if (!$this->eventRepository->hasEventsForWorkshop($workshop->getKey(), $start, $end)) {
            return;
        }

        // The event could still be created if there is another event at the same start time and its workshop is
        // cumulable with the workshop of the event we try to create
        $existingEventsAtSameStart = $this->eventRepository->getEventsForArtisanFromStartDate($workshop->artisan, $start);

        if ($existingEventsAtSameStart === null) {
            // Trying to create an event that overlaps with an existing event for the same workshop.
            throw new StackedSameWorkshopException($workshop->getKey(), $workshop->nom, $start, $end);
        }

        foreach ($existingEventsAtSameStart as $event) {
            if ($workshop->isAtelierCumulable($event->atelier)) {
                return;
            }
        }

        // Cannot create the event because there's already an event for the same workshop
        // and this new event is non-cumulative with other events starting at the same time
        throw new StackedSameWorkshopException($workshop->getKey(), $workshop->nom, $start, $end);
    }
}
