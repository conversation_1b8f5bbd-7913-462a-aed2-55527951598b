<?php

declare(strict_types=1);

namespace App\Domain\Booking\Services;

use App\Domain\Booking\Enums\UnavailabilityOrigin;
use App\Domain\Booking\Exceptions\Unavailability\AlreadyUnavailable;
use App\Domain\Booking\Exceptions\Unavailability\EventAlreadyBooked;
use App\Domain\Booking\Exceptions\Unavailability\EventWithoutBooking;
use App\Domain\Booking\Models\Unavailability;
use App\Domain\Booking\Repositories\EventRepository;
use App\Domain\Booking\Repositories\UnavailabilityRepository;
use App\Domain\Content\Repositories\WorkshopRepository;
use App\Models\Artisan;
use App\Models\Evenement;
use Carbon\Carbon;

class UnavailabilityCreator
{
    public function __construct(
        protected UnavailabilityRepository $unavailabilityRepository,
        protected EventRepository $eventRepository,
        protected EventRemover $eventRemover,
        protected WorkshopRepository $workshopRepository,
    ) {
    }

    /**
     * @param int[] $workshopIds
     */
    public function createUnavailabilityForArtisan(
        int $artisanId,
        array $workshopIds,
        Carbon $startAt,
        Carbon $endAt,
        string $message,
        UnavailabilityOrigin $origin
    ): void {
        try {
            $this->checkUnavailabilityBeCreated($workshopIds, $startAt, $endAt);
        } catch (EventWithoutBooking) {
            $this->eventRemover->deleteBetweenDatesForWorkshops($workshopIds, $startAt, $endAt, $origin->getLabel());
        }

        $artisan = Artisan::findOrFail($artisanId);

        $unavailability = new Unavailability([
            'start_at' => $startAt,
            'end_at' => $endAt,
            'creation_origin' => $origin,
            'message' => $message,
            'artisan_id' => $artisanId,
            'timezone' => $artisan->timezone,
        ]);
        $unavailability->save();

        $unavailability->workshops()->attach($workshopIds);
    }

    /**
     * @param int[] $workshopIds
     *
     * @throws AlreadyUnavailable
     * @throws EventAlreadyBooked
     * @throws EventWithoutBooking
     */
    public function checkUnavailabilityBeCreated(array $workshopIds, Carbon $startAt, Carbon $endAt): void
    {
        if ($this->unavailabilityRepository->isWorkshopsUnavailableForPeriod($workshopIds, $startAt, $endAt)) {
            throw new AlreadyUnavailable();
        }

        $eventsBetweenDates = $this->eventRepository->getEventsForWorkshopsBetweenDates(
            $workshopIds,
            $startAt,
            $endAt
        );

        $eventsBetweenDates
            ->each(function (Evenement $event): void {
                if ($event->countParticipantsAndPotentialParticipants() > 0) {
                    throw new EventAlreadyBooked();
                }
            });

        if ($eventsBetweenDates->count() > 0) {
            throw new EventWithoutBooking();
        }
    }
}
