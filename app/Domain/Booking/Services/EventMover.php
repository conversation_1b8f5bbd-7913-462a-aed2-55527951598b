<?php

declare(strict_types=1);

namespace App\Domain\Booking\Services;

use App\Domain\Booking\Exceptions\Event\EventCantBeDeletedException;
use App\Domain\Booking\Exceptions\Event\EventCantBeMovedException;
use App\Domain\Booking\Repositories\UnavailabilityRepository;
use App\Infrastructure\Auth\AuthFinder;
use App\Models\Evenement;
use Carbon\Carbon;

class EventMover
{
    public function __construct(
        private readonly UnavailabilityRepository $unavailabilityRepository,
        private readonly AuthFinder $authFinder,
    ) {
    }

    /**
     * @throws EventCantBeMovedException|EventCantBeDeletedException
     */
    public function move(Evenement $event, Carbon $newStart): void
    {
        if (!empty($event->checkDeplacement()['errors'])) {
            throw new EventCantBeMovedException(__('calendar.layout.workshop_move_forbidden'));
        }

        $diff = $event->start->diffInMinutes($newStart, false);
        $newEnd = $event->end->clone()->addMinutes($diff);

        if ($this->unavailabilityRepository->isWorkshopsUnavailableForPeriod([$event->atelier->id], $newStart, $newEnd)) {
            throw new EventCantBeMovedException(__('userartisan/unavailability.exception.artisan_unavailable'));
        }

        $event->start = $newStart;
        $event->end = $newEnd;

        // We want to log when an artisan moved an event with participants
        if ($event->countParticipants() > 0 && $this->authFinder->isArtisanLoggedIn()) {
            $event->event_moved_by_artisan_at = Carbon::now();
        }

        $event->save();
    }
}
