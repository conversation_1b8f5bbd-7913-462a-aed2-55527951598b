<?php

declare(strict_types=1);

namespace App\Domain\Booking\QueryResults;

use Carbon\CarbonImmutable;

class Event
{
    /**
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        public readonly ?int $id,
        public readonly int $atelierId,
        public readonly CarbonImmutable $start,
        public readonly CarbonImmutable $end,
        public readonly int $capacity,
        public readonly int $cut,
        public readonly ?int $takenSeats,
        public readonly string $details,
        public readonly string $timezone,
        public readonly string $type,
        public readonly ?CarbonImmutable $cancelledAt,
        public readonly ?Lieu $place,
        public readonly ?string $miraklId = null,
        public readonly ?int $miraklLastSentCapacity = null,
        public readonly ?CarbonImmutable $miraklLastSentStart = null,
    ) {
    }

    /**
     * @param array<string, mixed> $data
     */
    public static function make(array $data): self
    {
        $takenSeats = null;
        $id = null;
        $lieuData = null;

        self::validateData($data);

        if (isset($data['id']) && \is_int($data['id'])) {
            $id = $data['id'];
        }

        if (isset($data['taken_seats']) && \is_int($data['taken_seats'])) {
            $takenSeats = $data['taken_seats'];
        }

        if (!empty($data['place_id'])) {
            $lieuData = new Lieu(
                $data['place_id'],
                $data['place_city'] ?? '',
                $data['place_district'] ?? '',
                $data['place_address'] ?? ''
            );
        }

        return new self(
            $id,
            $data['atelier_id'],
            CarbonImmutable::parse($data['start']),
            CarbonImmutable::parse($data['end']),
            $data['capacity'],
            $data['cut'],
            $takenSeats,
            $data['details'],
            $data['timezone'],
            $data['type'],
            $data['cancelled_at'],
            $lieuData
        );
    }

    /**
     * @param array<string, mixed> $data
     */
    private static function validateData(array $data): void
    {
        $strings = [
            'start',
            'end',
            'details',
            'type',
            'timezone',
        ];
        $int = [
            'capacity',
            'cut',
            'atelier_id',
        ];

        $exception = new \RuntimeException('Cannot retrieve event values');

        foreach ($strings as $string) {
            if (!\is_string($data[$string])) {
                throw $exception;
            }
        }

        foreach ($int as $integer) {
            if (!\is_int($data[$integer])) {
                throw $exception;
            }
        }

        if (!(null === $data['cancelled_at'] || $data['cancelled_at'] instanceof CarbonImmutable)) {
            throw $exception;
        }
    }

    public function availableSeats(): int
    {
        return max(0, $this->capacity - (int) $this->takenSeats);
    }
}
