<?php

declare(strict_types=1);

namespace App\Domain\DateRequest\Listeners;

use App\Domain\Booking\Events\EventCreated;
use App\Domain\DateRequest\DateRequestService;
use App\Enums\DateProposition\DatePropositionStatus;
use App\Models\PropositionDate;

class ApproveProposedDates
{
    public function __construct(public DateRequestService $dateRequestService)
    {
    }

    /**
     * Handle the event.
     */
    public function handle(EventCreated $event): void
    {
        PropositionDate::query()
            ->where('status', DatePropositionStatus::Pending)
            ->where('atelier_id', $event->event->atelier_id)
            ->lazy(100)
            ->each(function (PropositionDate $propositionDate) use ($event): void {
                if ($propositionDate->areDatesCompatibleWithEvent($event->event)) {
                    $this->dateRequestService->approveAndNotify($propositionDate, $event->event);
                }
            });
    }
}
