<?php

declare(strict_types=1);

namespace App\Domain\Marketplace\Services\MiraklApi\NoneServices;

use App\Domain\Marketplace\Services\MiraklApi\Types\Workshop;
use App\Domain\Marketplace\Services\MiraklApi\Types\WorkshopCreationResponse;
use App\Domain\Marketplace\Services\MiraklApi\WorkshopGateway;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
 */
readonly class NoneMiraklWorkshopService implements WorkshopGateway
{
    public function create(Workshop $workshop, Collection $events): WorkshopCreationResponse
    {
        return new WorkshopCreationResponse(
            Str::random(20),
            Collection::empty(),
        );
    }

    public function update(string $workshopId, Workshop $workshop): void
    {
        // Nothing
    }

    public function deactivate(string $workshopId, CarbonImmutable $date, Collection $events): Collection
    {
        return Collection::empty();
    }

    public function reactivate(string $workshopId, Collection $events): Collection
    {
        return Collection::empty();
    }

    public function updateStockByWorkshopId(string $workshopid, Collection $events): Collection
    {
        return Collection::empty();
    }
}
