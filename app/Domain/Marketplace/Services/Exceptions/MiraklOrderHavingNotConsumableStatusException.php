<?php

declare(strict_types=1);

namespace App\Domain\Marketplace\Services\Exceptions;

use App\Domain\Marketplace\Services\MiraklApi\OrderGateway;
use App\Models\Commande;

/**
 * @SuppressWarnings(PHPMD)
 */
class MiraklOrderHavingNotConsumableStatusException extends \Exception
{
    public string $state = '';

    private function __construct(string $message = '', int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public static function forOrderFromMiraklOrderStatus(Commande $order, string $state): self
    {
        $orderPendingState = OrderGateway::ORDER_PENDING_STATE;

        $self = new self(
            "Failed to consume order #{$order->id} : Mirakl order state is '{$state}', '{$orderPendingState}' expected'",
        );

        $self->state = $state;

        return $self;
    }
}
