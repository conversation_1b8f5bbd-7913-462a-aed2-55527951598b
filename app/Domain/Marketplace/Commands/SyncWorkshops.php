<?php

declare(strict_types=1);

namespace App\Domain\Marketplace\Commands;

use App\Console\Command;
use App\Domain\Marketplace\Jobs\DeleteOldLocation;
use App\Domain\Marketplace\Jobs\UpdateLocation;
use App\Domain\Marketplace\Jobs\UpdateWorkshops;
use App\Models\Lieu;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class SyncWorkshops extends Command
{
    protected $signature = 'mirakl:sync-workshops-to-n-d';
    protected $description = 'Trigger workshop update jobs for all recently updated ND Mirakl workshops';

    /**
     * @throws \InvalidArgumentException
     */
    public function handle(): int
    {
        // Mirakl do not have update endpoint on locations, so we have to create a new location and delete the old one
        // on each WCD update. Given locations can be shared between several workshops, we have to store
        // the old Mirakl location id until all related workshops are updated, and delete it after
        $this->dispatchOldUnusedMiraklLocationIdsRemoval();

        $this->dispatchUpdatedLocationsCreation();

        $this->dispatchWorkshopUpdates();

        return self::SUCCESS;
    }

    private function dispatchOldUnusedMiraklLocationIdsRemoval(): void
    {
        Lieu::query()
            ->selectRaw('distinct(mirakl_old_id) as mirakl_old_id')
            ->join('mirakl_locations', 'lieux.id', '=', 'mirakl_locations.location_id')
            ->whereNotNull('mirakl_locations.mirakl_old_id')
            ->whereNotExists(static fn (Builder $builder) => $builder
                ->select('*')
                ->from('ateliers')
                ->join('mirakl_workshops', 'ateliers.id', '=', 'mirakl_workshops.workshop_id')
                ->whereRaw('mirakl_workshops.mirakl_location_id = mirakl_locations.mirakl_old_id'))
            ->toBase()
            ->get()
            ->each(static fn (\stdClass $object) => dispatch(new DeleteOldLocation($object->mirakl_old_id)));
    }

    private function dispatchUpdatedLocationsCreation(): void
    {
        Lieu::query()
            ->select('lieux.id')
            ->join('mirakl_locations', 'lieux.id', '=', 'mirakl_locations.location_id')
            ->whereNotNull('mirakl_locations.mirakl_id')
            ->where('lieux.updated_at', '>', DB::raw('`mirakl_locations`.`last_synced_at`'))
            ->toBase()
            ->get()
            ->each(static fn (\stdClass $object) => dispatch(new UpdateLocation($object->id)));
    }

    private function dispatchWorkshopUpdates(): void
    {
        dispatch(new UpdateWorkshops());
    }
}
