<?php

declare(strict_types=1);

namespace App\Domain\Marketplace\Notifications;

use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\SlackMessage;

class MiraklOrderRefusedNotification extends Notification
{
    public function __construct(
        public readonly string $miraklOrderId,
        public readonly string $message,
    ) {
    }

    /**
     * @return string[]
     */
    public function via(): array
    {
        return ['slack'];
    }

    public function toSlack(): SlackMessage
    {
        return (new SlackMessage())
            ->text(implode("\n", [
                ":herb: N&D Mirakl No. {$this->miraklOrderId} order has been refused for reason : {$this->message}",
            ]));
    }
}
