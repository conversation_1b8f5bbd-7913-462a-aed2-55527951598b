<?php

declare(strict_types=1);

namespace App\Domain\Marketplace\Models;

use App\Models\Commande;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Domain\Marketplace\Models\MiraklOrder.
 *
 * @property int $id
 * @property int $order_id
 * @property string $mirakl_id
 * @property Carbon|null $consumed_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Commande $order
 *
 * @mixin \Eloquent
 */
class MiraklOrder extends Model
{
    public $casts = [
        'consumed_at' => 'datetime',
    ];

    /** @return BelongsTo<Commande, $this> */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Commande::class, 'order_id');
    }
}
