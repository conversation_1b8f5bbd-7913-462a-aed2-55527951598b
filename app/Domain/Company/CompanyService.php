<?php

declare(strict_types=1);

namespace App\Domain\Company;

use App\Models\Entreprise;
use Illuminate\Database\Eloquent\Collection;

class CompanyService
{
    public static function findCompany(string $search): ?Entreprise
    {
        try {
            return Entreprise::whereUuid($search)
                ->orWhere('id', $search)
                ->orWhere('trigram', $search)
                ->firstOrFail();
        } catch (\Throwable) {
            return null;
        }
    }

    /** @return Collection<int, Entreprise> */
    public static function allCompanies(): Collection
    {
        return Entreprise::all();
    }

    /** @return Collection<int, Entreprise> */
    public static function allExistingCompanies(): Collection
    {
        return Entreprise::withoutTrashed()->orderBy('nom')->get();
    }
}
