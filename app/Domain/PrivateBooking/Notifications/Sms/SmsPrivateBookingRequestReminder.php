<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Notifications\Sms;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\VonageMessage;
use Illuminate\Notifications\Notification;

class SmsPrivateBookingRequestReminder extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(protected PrivateBookingRequest $request)
    {
    }

    /**
     * @return string[]
     */
    public function via(): array
    {
        return ['vonage'];
    }

    public function toVonage(): VonageMessage
    {
        $url = frontUrl(route(
            localizedRouteName('artisan.privatization.index', $this->locale),
            [
                'open' => 'private-booking-request',
                'id' => $this->request->id,
            ],
            false
        ));

        return (new VonageMessage())
            ->content(trans_choice(
                'sms/artisan.group_booking_request_reminder.content',
                $this->request->participants_number,
                [
                    'participants' => $this->request->present()->participantsNumberWithoutPendingChanges(),
                    'url' => shortenUrl($url),
                ],
                $this->locale
            ))
            ->from('Wecandoo');
    }
}
