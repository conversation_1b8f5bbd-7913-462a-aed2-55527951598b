<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Notifications\Emails\Change\Artisan;

use App\Domain\PrivateBooking\Enums\CauserType;
use App\Domain\PrivateBooking\Models\PrivateBookingRequestChange;
use App\Domain\PrivateBooking\Services\PrivateBookingEventClientBooker;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPaymentManager;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPriceResolver;
use App\Domain\PrivateBooking\Services\PrivateBookingService;
use App\Mail\BaseMailable;
use App\Models\Evenement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;

/**
 * @SuppressWarnings(PHPMD.ElseExpression)
 */
class PrivateBookingRequestChangeExpiredMail extends BaseMailable implements ShouldQueue
{
    use Queueable;

    public function __construct(protected int $changeId, protected CauserType $causerType)
    {
        parent::__construct();
    }

    public function build(
        PrivateBookingRequestPriceResolver $priceResolver,
        PrivateBookingRequestPaymentManager $paymentManager,
        PrivateBookingEventClientBooker $eventBooker,
    ): Mailable {
        /** @var PrivateBookingRequestChange $change */
        $change = PrivateBookingRequestChange::with([
            'privateBookingRequest' => [
                'event',
                'workshop',
            ],
        ])->findOrFail($this->changeId);

        $participantsDiff = $change->getParticipantsDiff();

        $chosenEvent = $change->privateBookingRequest->event ?? $change->privateBookingRequest->getEventSelectedByCustomerForQuote();
        $privatizationDate = $chosenEvent instanceof Evenement
            ? dateAndTimeLongFormatStartAndEnd($chosenEvent->start, $chosenEvent->end, $chosenEvent->timezone)
            : null;

        if ($this->causerType === CauserType::Artisan) {
            $subject = $chosenEvent instanceof Evenement
                ? __('emails/artisan/private-booking.change.request-change-expired.causer_artisan.title', ['date' => dateAndTimeLongFormat($chosenEvent->start, $chosenEvent->timezone)])
                : __('emails/artisan/private-booking.change.request-change-expired.causer_artisan.title_without_chosen_event');
        } else {
            $subject = $chosenEvent instanceof Evenement
                ? trans_choice('emails/artisan/private-booking.change.request-change-expired.causer_customer.title', $change->getTotalParticipants(), ['date' => dateAndTimeLongFormat($chosenEvent->start, $chosenEvent->timezone), 'participants' => $change->privateBookingRequest->participants_number])
                : trans_choice('emails/artisan/private-booking.change.request-change-expired.causer_customer.title_without_chosen_event', $change->getTotalParticipants(), ['participants' => $change->privateBookingRequest->participants_number]);
        }

        $formattedSlots = PrivateBookingService::getFormattedProposedSlots($change->privateBookingRequest);

        $oldAmount = $priceResolver->getPrivateBookingRequestPrice($change->privateBookingRequest);
        $newAmount = $priceResolver->getChangeRequestPrice($change);
        $amountAlreadyPaid = $change->privateBookingRequest->isCompany()
            ? $paymentManager->getAmountAlreadyPaid($change->privateBookingRequest)
            : $eventBooker->getAmountAlreadyPaid($change->privateBookingRequest);
        $amountToPay = $newAmount->substract($amountAlreadyPaid);

        return $this->markdown('emails.artisan.private-booking.change.request-change-expired')
            ->with('artisan', $change->privateBookingRequest->workshop->artisan->getDenominationMail())
            ->with('firstName', $change->privateBookingRequest->first_name)
            ->with('lastName', $change->privateBookingRequest->last_name)
            ->with('requestId', $change->privateBookingRequest->getKey())
            ->with('workshopName', $change->privateBookingRequest->workshop->nom)
            ->with('oldAmount', currencyFormat($oldAmount))
            ->with('oldParticipants', $change->privateBookingRequest->participants_number)
            ->with('amountToPay', currencyFormat($amountToPay))
            ->with('participantsDiff', $participantsDiff)
            ->with('privatizationDate', $privatizationDate)
            ->with('isCompany', $change->privateBookingRequest->is_company)
            ->with('company', $change->privateBookingRequest->company_name)
            ->with('formattedSlots', $formattedSlots)
            ->with('location', $change->privateBookingRequest->location)
            ->with('address', $change->privateBookingRequest->getEventLocation())
            ->with('causerIsArtisan', $this->causerType === CauserType::Artisan)
            ->subject($subject);
    }
}
