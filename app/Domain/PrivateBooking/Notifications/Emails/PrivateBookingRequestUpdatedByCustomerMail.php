<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Notifications\Emails;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Services\PrivateBookingRequestPriceResolver;
use App\Domain\PrivateBooking\Services\PrivateBookingService;
use App\Mail\BaseMailable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/** @SuppressWarnings(PHPMD.LongClassName) */
class PrivateBookingRequestUpdatedByCustomerMail extends BaseMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function __construct(protected readonly PrivateBookingRequest $request)
    {
        parent::__construct();
    }

    public function build(
        PrivateBookingRequestPriceResolver $priceResolver,
    ): Mailable {
        $subject = $this->request->is_company
            ? __('emails/artisan/private-booking.request-updated-by-customer.subject_with_quote', ['company' => $this->request->company_name ?? ''])
            : __('emails/artisan/private-booking.request-updated-by-customer.subject', ['firstName' => $this->request->first_name, 'lastName' => $this->request->last_name]);

        $formattedSlots = PrivateBookingService::getFormattedProposedSlots($this->request);

        return $this->markdown('emails.artisan.private-booking.request-updated-by-customer')
            ->with('requestId', $this->request->getKey())
            ->with('workshopName', $this->request->workshop->nom)
            ->with('price', currencyFormat($priceResolver->getCurrentPrice($this->request)))
            ->with('participantsNumber', $this->request->present()->participantsNumberWithoutPendingChanges())
            ->with('artisan', $this->request->workshop->artisan->getDenominationMail())
            ->with('isCompany', $this->request->is_company)
            ->with('company', $this->request->company_name)
            ->with('expirationDate', dateAndTimeShortFormat($this->request->expires_at, $this->request->timezone))
            ->with('formattedSlots', $formattedSlots)
            ->with('firstName', $this->request->first_name)
            ->with('lastName', $this->request->last_name)
            ->with('location', $this->request->location)
            ->with('address', $this->request->present()->address())
            ->subject($subject);
    }
}
