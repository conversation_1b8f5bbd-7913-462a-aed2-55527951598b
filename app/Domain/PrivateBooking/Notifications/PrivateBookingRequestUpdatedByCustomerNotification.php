<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Notifications;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Notifications\Emails\PrivateBookingRequestUpdatedByCustomerMail;
use App\Models\Artisan;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\VonageMessage;
use Illuminate\Notifications\Notification;

/**
 * @SuppressWarnings(PHPMD.LongClassName)
 */
class PrivateBookingRequestUpdatedByCustomerNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(protected PrivateBookingRequest $request)
    {
    }

    /**
     * @return string[]
     */
    public function via(): array
    {
        return ['vonage', 'mail'];
    }

    public function toMail(Artisan $notifiable): PrivateBookingRequestUpdatedByCustomerMail
    {
        if (empty($notifiable->email)) {
            throw new \InvalidArgumentException();
        }

        $recipients = $notifiable->getEmailsForNotifications()->toArray();

        return (new PrivateBookingRequestUpdatedByCustomerMail($this->request))
            ->to($recipients)
            ->country($notifiable->getCountryCode())
            ->locale($notifiable->preferredLocale());
    }

    public function toVonage(): VonageMessage
    {
        $url = frontUrl(route(
            localizedRouteName('artisan.privatization.index', $this->locale),
            [
                'open' => 'private-booking-request',
                'id' => $this->request->id,
            ],
            false
        ));

        $content = $this->request->is_company
            ? 'sms/artisan.group_booking_request_updated_by_customer.content_with_quote'
            : 'sms/artisan.group_booking_request_updated_by_customer.content';

        return (new VonageMessage())
            ->content(trans_choice(
                $content,
                $this->request->participants_number,
                [
                    'url' => shortenUrl($url),
                ],
                $this->locale
            ))
            ->from('Wecandoo');
    }
}
