<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Requests;

use App\Domain\PrivateBooking\Rules\PrivateBookingRequestMaxRule;
use App\Models\Atelier;
use App\Rules\ArtisanAvailableForWorkshopAndDateRule;
use App\Rules\ValidEmail;
use App\Rules\Workshop\WorkshopMinAdultRule;
use App\Rules\Workshop\WorkshopMinChildRule;
use App\Rules\WorkshopGuardAgainstDisabledLocationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;

/**
 * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
 */
class PrivateBookingRequestCreateRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        // Let's replace the "date" attribute from date to datetime, this will make the validation much easier.
        $this->merge([
            'dates' => $this
                ->collect('dates')
                ->map(function (array $dates) {
                    return array_merge($dates, [
                        'date' => Carbon::parse($dates['date'] ?? null)
                            ->setTimeFrom(Carbon::parse($dates['start'] ?? null))
                            ->toDateTimeString('minute'),
                    ]);
                })
                ->toArray(),
            'comparison_date' => Carbon::now()->addDays($this->boolean('isCompany')
                ? config()->integer('private-booking.days_before_first_date.for_companies_last_minute')
                : config()->integer('private-booking.days_before_first_date.other_than_companies')),
        ]);
    }

    /**
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        /** @var Atelier $workshop */
        $workshop = $this->route('workshop');

        $adultNumber = $workshop->isAllowedForAdult() ? $this->integer('adultNumber') : 0;
        $childNumber = $workshop->isAllowedForChild() ? $this->integer('childNumber') : 0;
        $totalParticipants = $adultNumber + $childNumber;

        $isCompany = $this->boolean('isCompany');

        $rules = [
            'adultNumber' => [
                Rule::requiredIf($workshop->isAllowedForAdult()),
                'numeric',
                new WorkshopMinAdultRule($workshop),
                new PrivateBookingRequestMaxRule($workshop, $this->isOnTheGo(), $totalParticipants),
            ],
            'childNumber' => [
                Rule::requiredIf($workshop->isAllowedForChild()),
                'numeric',
                new WorkshopMinChildRule($workshop),
                new PrivateBookingRequestMaxRule($workshop, $this->isOnTheGo(), $totalParticipants),
            ],
            'firstName' => [
                'required',
                'max:191',
            ],
            'lastName' => [
                'required',
                'max:191',
            ],
            'companyName' => [
                Rule::requiredIf($isCompany),
                Rule::excludeIf($isCompany === false),
                'max:191',
            ],
            'email' => [
                'required',
                new ValidEmail(),
            ],
            'phone' => [
                'required',
                'phone:AUTO',
            ],
            'message' => [
                Rule::requiredIf($this->isOnTheGo()),
                'nullable',
                'string',
            ],
            'isCompany' => [
                'sometimes',
                'bool',
            ],
            'isOnTheGo' => [
                'required',
                'bool',
                new WorkshopGuardAgainstDisabledLocationRule($workshop),
            ],
            'address1' => [
                Rule::requiredIf($this->isOnTheGo()),
                Rule::prohibitedIf($this->isLocationAtArtisan()),
                'nullable',
                'max:191',
            ],
            'address2' => [
                Rule::prohibitedIf($this->isLocationAtArtisan()),
                'nullable',
                'max:191',
            ],
            'zipCode' => [
                Rule::requiredIf($this->isOnTheGo()),
                Rule::prohibitedIf($this->isLocationAtArtisan()),
                'nullable',
                'max:10',
            ],
            'city' => [
                Rule::requiredIf($this->isOnTheGo()),
                Rule::prohibitedIf($this->isLocationAtArtisan()),
                'nullable',
                'max:100',
            ],
            'country' => [
                Rule::requiredIf($this->isOnTheGo()),
                Rule::prohibitedIf($this->isLocationAtArtisan()),
                'nullable',
                'max:100',
            ],
            'dates' => [
                'required',
                'array',
                'min:1',
                'max:10',
            ],
            'dates.*.date' => [
                'bail',
                'required',
                'date',
                'after_or_equal:'.$this->input('comparison_date'),
                'before:'.Carbon::now()->addMonths(18),
                new ArtisanAvailableForWorkshopAndDateRule($workshop),
            ],
            'dates.*.start' => [
                'required',
                'date_format:H:i',
                'before:required.*.end',
            ],
        ];

        // We will now add the end time rule for each proposed dates as the
        // time between the start and the end must at least as long as
        // the duration of the workshop the request is done one
        foreach ($this->collect('dates') as $key => $proposedSlot) {
            $date = Carbon::parse($proposedSlot['date'] ?? null);
            $start = Carbon::parse($proposedSlot['start'] ?? null);

            $date
                ->setTime($start->hour, $start->minute)
                ->addHours($workshop->nb_heures)
                ->addMinutes($workshop->nb_minutes);

            $rules["dates.$key.end"] = 'required|date_format:H:i|after_or_equal:'.$date->format('H:i');
        }

        return $rules;
    }

    /**
     * @return array<string, string>
     */
    public function messages(): array
    {
        $comparisonDate = $this->input('comparison_date');
        if (!$comparisonDate instanceof Carbon) {
            throw new \InvalidArgumentException('Unable to find comparison date.');
        }

        return [
            'firstName.required' => __('front/group-booking/request.rules.firstName.required'),
            'lastName.required' => __('front/group-booking/request.rules.lastName.required'),
            'address1.required' => __('front/group-booking/request.rules.address1.required'),
            'address1.required_if' => __('front/group-booking/request.rules.address1.required'),
            'zipCode.required' => __('front/group-booking/request.rules.zipCode.required'),
            'zipCode.required_if' => __('front/group-booking/request.rules.zipCode.required'),
            'city.required' => __('front/group-booking/request.rules.city.required'),
            'city.required_if' => __('front/group-booking/request.rules.city.required'),
            'country.required' => __('front/group-booking/request.rules.country.required'),
            'country.required_if' => __('front/group-booking/request.rules.country.required'),
            'companyName.required' => __('front/group-booking/request.rules.companyName.required'),
            'companyName.required_if' => __('front/group-booking/request.rules.companyName.required'),
            'phone.required' => __('front/group-booking/request.rules.phone.required'),
            'message.required_if' => __('front/group-booking/request.rules.message.required'),
            'message.required' => __('front/group-booking/request.rules.message.required'),
            'email.required' => __('front/group-booking/request.rules.email.required'),
            'dates.*.date.required' => __('front/group-booking/request.rules.proposed-slots-date-required'),
            'dates.*.date.after_or_equal' => __('front/group-booking/request.rules.proposed-slots-date-after', ['date' => dateAndTimeLongFormat($comparisonDate)]),
            'dates.*.end.after_or_equal' => __('front/group-booking/request.rules.proposed-slots-end-after'),
        ];
    }

    /**
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'firstName' => __('validation.attributes.first_name'),
            'lastName' => __('validation.attributes.last_name'),
            'dates' => __('front/group-booking/request.attributes.proposed-slots'),
            'dates.*.date' => __('validation.attributes.date'),
            'dates.*.start' => __('validation.attributes.start_time'),
            'dates.*.end' => __('validation.attributes.end_time'),
        ];
    }

    // TODO: change the method name when the front parameter was changed
    private function isLocationAtArtisan(): bool
    {
        return $this->isOnTheGo() === false;
    }

    // TODO: change the method name when the front parameter was changed
    private function isOnTheGo(): bool
    {
        // TODO: change the request parameter in the front request
        return $this->boolean('isOnTheGo');
    }
}
