<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Services;

use App\Domain\PrivateBooking\Exceptions\BillingAddressException;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Models\Quote;
use App\Models\Adresse;

class BillingAddressManager
{
    public function setBillingAddress(PrivateBookingRequest $privateBookingRequest, Adresse $billingAddress): void
    {
        $quote = $privateBookingRequest->getLastQuote();

        if (!$quote instanceof Quote) {
            throw BillingAddressException::createQuoteNotFoundError();
        }

        $this->guardAgainstInvalidData($quote, $privateBookingRequest, $billingAddress);

        $quote->recipient_name = $billingAddress->destinataire ?? '';
        $quote->recipient_company_name = $billingAddress->societe;
        $quote->recipient_address_1 = $billingAddress->adresse1;
        $quote->recipient_address_2 = $billingAddress->adresse2;
        $quote->recipient_city = $billingAddress->ville;
        $quote->recipient_country = $billingAddress->pays ?? '';
        $quote->recipient_zip_code = $billingAddress->code_postal;
        $quote->save();
    }

    private function guardAgainstInvalidData(Quote $quote, PrivateBookingRequest $privateBookingRequest, Adresse $billingAddress): void
    {
        if ($privateBookingRequest->is_company === false) {
            throw BillingAddressException::createNoBillingForPublicBookingError();
        }

        if ($privateBookingRequest->user_id !== null && ($privateBookingRequest->user_id !== $billingAddress->user_id)) {
            throw BillingAddressException::createUserMismatchError();
        }

        if ($quote->isExpired()) {
            throw BillingAddressException::createExpiredQuoteError();
        }

        if ($quote->hasBillingAddress()) {
            throw BillingAddressException::createAddressAlreadySetError();
        }
    }
}
