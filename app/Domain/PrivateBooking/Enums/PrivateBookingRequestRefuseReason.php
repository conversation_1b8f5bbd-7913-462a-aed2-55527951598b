<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Enums;

enum PrivateBookingRequestRefuseReason: string
{
    /** @deprecated The auto refusal feature was deleted, but we need to keep this case for the DB */
    case Auto = 'auto';
    case Availability = 'availability';
    case Price = 'price';
    case Participant = 'participant';
    case Other = 'other';

    public function getLabel(): ?string
    {
        return __(sprintf('enums.private-booking-request.refuse_reasons.%s', $this->value));
    }
}
