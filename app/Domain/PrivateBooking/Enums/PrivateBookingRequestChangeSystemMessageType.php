<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Enums;

/** @SuppressWarnings(PHPMD.LongClassName) */
enum PrivateBookingRequestChangeSystemMessageType: string
{
    case New = 'new';
    case Refused = 'refused';
    case Accepted = 'accepted';
    case ExpiredArtisan = 'expired_artisan';
    case ExpiredCustomer = 'expired_customer';
    case Processed = 'processed';
    case AcceptedProcessed = 'accepted_processed';
}
