<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Enums;

enum PrivateBookingRequestManagementStep: string
{
    case FIRST_STEP = 'first_step';
    case CHOOSE_PROPOSED_SLOT_STEP = 'choose_proposed_slot_step';
    case CHOOSE_PROPOSED_SLOT_MESSAGE_STEP = 'choose_proposed_slot_message_step';
    case CHOOSE_PROPOSED_SLOT_QUOTE_STEP = 'choose_proposed_slot_quote_step';
    case OTHER_PROPOSAL_STEP = 'other_proposal_step';
    case OTHER_PROPOSAL_MESSAGE_STEP = 'other_proposal_message_step';
    case OTHER_PROPOSAL_QUOTE_STEP = 'other_proposal_quote_step';
    case REFUSE_STEP = 'refuse_step';
    case REFUSE_MESSAGE_STEP = 'refuse_message_step';

    /**
     * @return array<self>
     */
    public static function getSecondSteps(): array
    {
        return [
            self::CHOOSE_PROPOSED_SLOT_STEP,
            self::OTHER_PROPOSAL_STEP,
            self::REFUSE_STEP,
        ];
    }

    /**
     * @return array<self>
     */
    public static function getLastSteps(): array
    {
        return [
            self::CHOOSE_PROPOSED_SLOT_MESSAGE_STEP,
            self::OTHER_PROPOSAL_MESSAGE_STEP,
            self::REFUSE_MESSAGE_STEP,
        ];
    }

    public static function isValidSecondStep(string $step): bool
    {
        foreach (self::getSecondSteps() as $validStep) {
            if ($validStep->value === $step) {
                return true;
            }
        }

        return false;
    }

    public static function isLastStep(string $step): bool
    {
        foreach (self::getLastSteps() as $lastStep) {
            if ($lastStep->value === $step) {
                return true;
            }
        }

        return false;
    }

    public static function getGtmTag(string $step): string
    {
        if (!self::isLastStep($step)) {
            return '';
        }

        return 'gtm-'.str_replace('_', '-', $step);
    }
}
