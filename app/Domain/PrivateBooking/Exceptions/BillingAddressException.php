<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Exceptions;

class BillingAddressException extends \LogicException
{
    public const ERROR_NO_BILLING_FOR_PUBLIC_BOOKING = 'billing_is_not_company';
    public const ERROR_USER_MISMATCH = 'billing_user_mismatch';
    public const ERROR_QUOTE_NOT_FOUND = 'billing_quote_not_found';
    public const ERROR_EXPIRED_QUOTE = 'billing_expired_quote';
    public const ERROR_ADDRESS_ALREADY_SET = 'billing_address_already_set';

    public function __construct(
        private string $errorCode,
        ?string $message = null,
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct(
            $message ?? __('api/private-booking.failure_unable_to_set_this_billing_address'),
            $code,
            $previous
        );
    }

    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    public static function createNoBillingForPublicBookingError(): self
    {
        return new self(self::ERROR_NO_BILLING_FOR_PUBLIC_BOOKING);
    }

    public static function createUserMismatchError(): self
    {
        return new self(self::ERROR_USER_MISMATCH);
    }

    public static function createQuoteNotFoundError(): self
    {
        return new self(self::ERROR_QUOTE_NOT_FOUND);
    }

    public static function createExpiredQuoteError(): self
    {
        return new self(self::ERROR_EXPIRED_QUOTE);
    }

    public static function createAddressAlreadySetError(): self
    {
        return new self(self::ERROR_ADDRESS_ALREADY_SET);
    }
}
