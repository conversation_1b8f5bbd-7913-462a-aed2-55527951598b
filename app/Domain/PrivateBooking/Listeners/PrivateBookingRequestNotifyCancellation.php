<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Listeners;

use App\Domain\PrivateBooking\Events\PrivateBookingRequestCancelled;
use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\PrivateBooking\Notifications\Emails\PrivateBookingRequestCancelledMail;

/** @SuppressWarnings(PHPMD) */
readonly class PrivateBookingRequestNotifyCancellation
{
    public function handle(PrivateBookingRequestCancelled $event): void
    {
        /** @var PrivateBookingRequest $privateBookingRequest */
        $privateBookingRequest = PrivateBookingRequest::query()
            ->with([
                'user',
                'workshop.artisan.locations.ville',
            ])
            ->findOrFail($event->privateBookingRequestId);

        $privateBookingRequest->user?->sendEmailNotification(new PrivateBookingRequestCancelledMail($privateBookingRequest, PrivateBookingRequestCancelledMail::RECEIVER_TYPE_CUSTOMER));
        $privateBookingRequest
            ->workshop
            ->artisan
            ->sendEmailNotification(new PrivateBookingRequestCancelledMail($privateBookingRequest, PrivateBookingRequestCancelledMail::RECEIVER_TYPE_ARTISAN));
    }
}
