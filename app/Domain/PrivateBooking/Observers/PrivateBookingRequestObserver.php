<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Observers;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use Carbon\Carbon;

class PrivateBookingRequestObserver
{
    public function saving(PrivateBookingRequest $privateBookingRequest): void
    {
        if ($privateBookingRequest->isDirty('status')) {
            $privateBookingRequest->status_updated_at = Carbon::now();
        }
    }
}
