<?php

declare(strict_types=1);

namespace App\Domain\PrivateBooking\Jobs;

use App\Domain\PrivateBooking\Services\PrivateBookingService;
use Carbon\Carbon;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * @SuppressWarnings(PHPMD.LongVariable)
 */
class RemindArtisanOfPrivateBookingRequest implements ShouldQueue
{
    use Batchable;
    use InteractsWithQueue;
    use Queueable;

    public function handle(PrivateBookingService $privateBookingService): void
    {
        $this->sendSecondReminders($privateBookingService);

        $this->sendFirstReminders($privateBookingService);
    }

    private function sendFirstReminders(PrivateBookingService $privateBookingService): void
    {
        $requestsWithQuoteFirstReminder = $privateBookingService->getRequestsAwaitingArtisanAction(
            true,
            Carbon::now()->addHours((int) config('private-booking.reminder.3_days_before')),
            1
        );
        $requestsWithoutQuoteFirstReminder = $privateBookingService->getRequestsAwaitingArtisanAction(
            false,
            Carbon::now()->addHours((int) config('private-booking.reminder.2_days_before')),
            1
        );

        $requestsFirstReminder = $requestsWithQuoteFirstReminder->merge($requestsWithoutQuoteFirstReminder);
        foreach ($requestsFirstReminder as $request) {
            $privateBookingService->remindArtisan($request, 1);
        }
    }

    private function sendSecondReminders(PrivateBookingService $privateBookingService): void
    {
        $requestsWithQuoteSecondReminder = $privateBookingService->getRequestsAwaitingArtisanAction(
            true,
            Carbon::now()->addHours((int) config('private-booking.reminder.1_day_before')),
            2
        );
        $requestsWithoutQuoteSecondReminder = $privateBookingService->getRequestsAwaitingArtisanAction(
            false,
            Carbon::now()->addHours((int) config('private-booking.reminder.1_day_before')),
            2
        );

        $requestsSecondReminder = $requestsWithQuoteSecondReminder->merge($requestsWithoutQuoteSecondReminder);
        foreach ($requestsSecondReminder as $request) {
            $privateBookingService->remindArtisan($request, 2);
        }
    }
}
