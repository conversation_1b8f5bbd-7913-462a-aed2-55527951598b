<?php

declare(strict_types=1);

namespace App\Domain\Gift\DataTables;

use App\Domain\Gift\Models\GiftCardLot;
use App\Domain\Gift\Repositories\GiftCardLotRepository;
use App\Services\CurrencyFormatter;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Yajra\DataTables\DataTableAbstract;
use Yajra\DataTables\Html\Builder;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\QueryDataTable;
use Yajra\DataTables\Services\DataTable;

class GiftCardLotsDataTable extends DataTable
{
    /**
     * @param EloquentBuilder<GiftCardLot> $query
     */
    public function dataTable(EloquentBuilder $query, CurrencyFormatter $currencyFormatter): DataTableAbstract
    {
        /** @var QueryDataTable $datatables */
        $datatables = datatables($query);

        return $datatables
            ->addColumn('company', static fn (GiftCardLot $giftCardLot) => $giftCardLot->company->nom)
            ->editColumn('validity_end', static fn (GiftCardLot $giftCardLot): string => $giftCardLot->validity_end->toDateString())
            ->editColumn('amount', static fn (GiftCardLot $giftCardLot): string => $currencyFormatter->format($giftCardLot->amount))
            ->editColumn('tickets', static fn (GiftCardLot $giftCardLot) => $giftCardLot->isTicketLot() ? $giftCardLot->tickets : '-')
            ->editColumn('prepaid', static fn (GiftCardLot $giftCardLot): string => $giftCardLot->prepaid ? 'Prépayé' : 'Dépot-vente')
            ->editColumn('paid_price', fn (GiftCardLot $giftCardLot): string => $giftCardLot->paid_price !== null ? $currencyFormatter->format($giftCardLot->paid_price) : '-')
            ->editColumn('status', fn (GiftCardLot $giftCardLot): string => $giftCardLot->status)
            ->filterColumn('company', static fn (EloquentBuilder $query, string $keyword) => $query
                ->whereHas('company', fn (EloquentBuilder $query) => $query
                    ->where('nom', 'like', "%{$keyword}%")))
            ->filterColumn('description', static fn (EloquentBuilder $query, string $keyword) => $query
                ->where('description', 'like', "%{$keyword}%"))
            ->addColumn('actions', function (GiftCardLot $giftCardLot): string {
                if ($giftCardLot->isPending()) {
                    return '<span><i class="fas fa-cog fa-spin"></i> <small>En cours de génération</small></span>';
                }

                $buttons = '<a href="'.route('admin.gift-card.lot.download-lot', $giftCardLot).'" class="btn btn-link" data-toggle="tooltip" data-placement="top" '.
                    'title="Télécharger">'.
                    '<i class="fas fa-file-excel fa-2x text-success"></i></a>';

                return $buttons.('<button class="btn btn-link" data-toggle="tooltip" data-placement="top" '.
                        'title="Supprimer le lot" onclick="rowDelete(&quot;'.$giftCardLot->id.'&quot;)">'.
                        '<i class="fas fa-trash tex-danger"></i></button>');
            })

            ->rawColumns(['actions']);
    }

    /**
     * @return EloquentBuilder<GiftCardLot>
     */
    public function query(GiftCardLotRepository $repository): EloquentBuilder
    {
        return $repository->dataTablesQuery()
            ->with('company');
    }

    public function html(): Builder
    {
        return $this->builder()
            ->setTableId('gift-card-lots-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Brtip')
            ->orderBy(0)
            ->fixedHeader(false)
            ->initComplete('function(){setupSearchColumns(this);}')
            ->orderCellsTop(true)
            ->buttons(
                Button::make(['excel', 'pageLength']),
            );
    }

    /**
     * @return Column[]
     */
    protected function getColumns(): array
    {
        return [
            Column::make('id')->name('id')->title('#ID')->searchable(),
            Column::make('company')->title('Entreprise associée')->searchable()->orderable(false),
            Column::make('prepaid')->title('Type de paiement')
                ->addClass('select-filter')
                ->multiple(false)
                ->selectOptions([0 => 'Dépot-vente', 1 => 'Prépayé']),
            Column::make('validity_end')->title('Date de péremption')->searchable(),
            Column::make('quantity')->title('Nb de codes')->searchable(false)->orderable(),
            Column::make('amount')->title('Montant Total')->searchable(false)->orderable(),
            Column::make('tickets')->title('Nb de places total')->searchable(false)->orderable(),
            Column::make('paid_price')->title('Prix payé')->searchable(false)->orderable(),
            Column::make('status')->title('Status')->orderable(false),
            Column::make('description')->title('Description')->orderable(false),
            Column::make('actions')->searchable(false)->orderable(false),
        ];
    }

    protected function filename(): string
    {
        return 'GiftCardLots_'.Carbon::now()->format('YmdHis');
    }
}
