<?php

declare(strict_types=1);

namespace App\Domain\Gift\Pdf;

use App\Domain\Gift\Models\Gift;
use App\Infrastructure\Pdf\Printable;
use App\Models\Image;
use App\Models\Reservation;
use Illuminate\Contracts\View\View;

class DigitalGiftCardBookingPdf implements Printable
{
    public function __construct(
        private readonly Gift $gift,
    ) {
        if (!$this->gift->subject instanceof Reservation) {
            throw new \InvalidArgumentException(sprintf('This PDF only support Gift with %s subjects, %s given.', Reservation::class, $this->gift->subject::class));
        }
    }

    public function getView(): View
    {
        $booking = $this->gift->booking();
        $workshop = $booking->getWorkshop();

        if ($workshop === null) {
            throw new \LogicException(sprintf('Workshop not found for booking #%s.', $booking->getKey()));
        }

        return view('pdf.digital-gift-card.booking', [
            'gift' => $this->gift,
            'booking' => $booking,
            'workshop' => $workshop,
            'images' => $workshop->images->map(
                fn (Image $img): string => $img->getUrl(['width' => 300, 'height' => null])
            ),
        ]);
    }

    public function getLocale(): ?string
    {
        return $this->gift->getLocale();
    }

    public function getFileName(): string
    {
        return __('pdf/digital-gift-card.common.filename', ['id' => $this->gift->getKey()], $this->getLocale());
    }

    public function getDirectory(): string
    {
        return 'digital-giftcard';
    }
}
