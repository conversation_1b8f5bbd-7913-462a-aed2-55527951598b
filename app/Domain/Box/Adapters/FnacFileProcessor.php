<?php

declare(strict_types=1);

namespace App\Domain\Box\Adapters;

use App\Domain\Box\Abstracts\FileProcessorAbstract;
use Illuminate\Support\Facades\Storage;

class FnacFileProcessor extends FileProcessorAbstract
{
    public function __construct()
    {
        $this->directory = '/oneprepaid/fnac/act/';
        $this->archivePath = config('box-domain.archive_directory');
        $this->delimiter = '|';
        $this->fileExtension = '.psv';
        $this->header = [
            'date',
            'reseller',
            'ean',
            'action',
            'activationCode',
            'shopId',
            'shopLabel',
            'additionalInfo',
        ];
    }

    /**
     * @param array<string, mixed>|null $option
     *
     * @return string[]
     */
    public function getAllFiles(?array $option = null): array
    {
        $files = Storage::disk('sftp')->allFiles($this->directory);
        $filesArray = [];

        foreach ($files as $file) {
            if (!$this->isAlreadyProcessed($file) && $this->isActivationFile($file)) {
                $filesArray[] = basename($file);
            }
        }

        return $filesArray;
    }

    protected function isActivationFile(string $file): bool
    {
        return str_contains($file, 'ACTIV');
    }
}
