<?php

declare(strict_types=1);

namespace App\Domain\Box\BoxSync;

use Illuminate\Database\Eloquent\Builder;

class PoterieBoxSync extends BoxSyncAbstract
{
    public function getWorkshopQueryBuilder(): Builder
    {
        return $this->getActiveWorkshopsQuery()
            ->where('craft_id', 4)
            ->whereBetween('prix', [38, 50]);
    }

    public function getBoxInternalPrice(): float
    {
        return 50;
    }
}
