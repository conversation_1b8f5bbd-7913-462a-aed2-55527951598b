<?php

declare(strict_types=1);

namespace App\Domain\Box\BoxSync;

use Illuminate\Database\Eloquent\Builder;

class LesAteliersPoterieCeramiqueBoxSync extends BoxSyncAbstract
{
    public function getWorkshopQueryBuilder(): Builder
    {
        return $this->getActiveWorkshopsQuery(true)
            ->where('craft_id', 4) // poterie et céramique
            ->whereBetween('prix', [45, 59]);
    }

    public function getBoxInternalPrice(): float
    {
        return 59;
    }
}
