<?php

declare(strict_types=1);

namespace App\Domain\Box\BoxSync;

use Illuminate\Database\Eloquent\Builder;

class CosmetiqueBoxSync extends BoxSyncAbstract
{
    public function getWorkshopQueryBuilder(): Builder
    {
        return $this->getActiveWorkshopsQuery()
            ->where('craft_id', 17)
            ->whereBetween('prix', [38, 50]);
    }

    public function getBoxInternalPrice(): float
    {
        return 50;
    }
}
