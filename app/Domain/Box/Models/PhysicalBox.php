<?php

declare(strict_types=1);

namespace App\Domain\Box\Models;

use App\Domain\Box\Repositories\CadeauBoxRepository;
use App\Models\Reductions\CadeauBox;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Domain\Box\Models\PhysicalBox.
 *
 * @property string $activation_code
 * @property int $giftbox_id
 * @property string|null $store
 * @property Carbon|null $sale_date
 * @property string|null $status_info
 * @property string|null $tracking_num
 * @property string|null $tracking_url
 * @property int|null $production_lot_id
 * @property int|null $delivery_lot_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon|null $physical_validity
 * @property CadeauBox $box
 * @property BoxLot|null $boxLot
 *
 * @mixin \Eloquent
 */
class PhysicalBox extends Model
{
    use SoftDeletes;

    protected $casts = [
        'sale_date' => 'datetime',
        'physical_validity' => 'datetime',
    ];

    protected $primaryKey = 'activation_code';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $guarded = [];

    protected static function booting(): void
    {
        static::creating(function ($physicalBox): void {
            $physicalBox->physical_validity = Carbon::now()->addMonths(config('box-domain.default_physical_box_validity'));
            if ($physicalBox->activation_code === null) {
                $physicalBox->activation_code = CadeauBoxRepository::generateUniqueActivationCode();
            }
        });
        parent::booting();
    }

    /** @return BelongsTo<BoxLot, $this> */
    public function boxLot(): BelongsTo
    {
        return $this->belongsTo(BoxLot::class, 'production_lot_id');
    }

    /** @return BelongsTo<CadeauBox, $this> */
    public function box(): BelongsTo
    {
        return $this->belongsTo(CadeauBox::class, 'giftbox_id');
    }
}
