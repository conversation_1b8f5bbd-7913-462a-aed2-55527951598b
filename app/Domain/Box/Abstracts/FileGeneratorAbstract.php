<?php

declare(strict_types=1);

namespace App\Domain\Box\Abstracts;

use Illuminate\Support\Facades\Storage;

/**
 * @phpstan-type BoxDispatched = array<object{ean: string, activationCode: string, physicalValidity: string}> */
abstract class FileGeneratorAbstract
{
    protected string $eol = "\r\n";

    protected string $directory;

    protected string $filename;

    protected string $filenameSeparator = '_';

    protected string $delimiter = ';';

    protected string $fileExtension = '.txt';

    abstract protected function getHeader(string $ean): string;

    abstract protected function getFooter(): string;

    /** @param  array<string, mixed> $data */
    abstract protected function getLine(array $data): string;

    abstract public function getFileName(): string;

    /** @param  BoxDispatched $data */
    abstract public function generate(array $data): string;

    public function pushFile(string $filename, string $contents): void
    {
        Storage::disk('sftp')->put($this->directory.$filename, $contents, 'public');
    }
}
