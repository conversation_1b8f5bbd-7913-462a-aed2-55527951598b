<?php

declare(strict_types=1);

namespace App\Domain\Box\Abstracts;

use App\Domain\Box\Exceptions\BadFormattedContentException;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\Storage;
use League\Csv\Exception;
use League\Csv\InvalidArgument;
use League\Csv\Reader;
use League\Flysystem\UnableToCheckFileExistence;

/**
 * @SuppressWarnings(PHPMD)
 */
abstract class FileProcessorAbstract
{
    protected ?string $directory;

    protected ?int $headerOffset = null;

    protected string $delimiter = ';';

    protected string $fileExtension = '.txt';

    /**
     * @var array<string>
     */
    protected array $header = [];

    protected ?string $archivePath;

    /**
     * @var string f default to indicate row that comes from forced sales
     */
    protected string $forceFlag = 'F';

    protected ?string $resellerTrigram = null;

    /**
     * @param array<string, mixed>|null $option
     *
     * @return string[]
     */
    abstract public function getAllFiles(?array $option = null);

    /**
     * @return \Iterator<array<mixed>>
     *
     * @throws InvalidArgument
     * @throws BadFormattedContentException
     * @throws FileNotFoundException
     * @throws Exception
     */
    public function processFile(string $filePath, bool $fetchHeaders = false): iterable
    {
        $csv = Reader::createFromString($this->getFileContent($filePath));
        if (null !== $this->headerOffset) {
            $csv->setHeaderOffset($this->headerOffset);
        }
        $csv->setDelimiter($this->delimiter);

        return $csv->getRecords($fetchHeaders ? $csv->getHeader() : $this->header);
    }

    protected function getFileContent(string $filePath): string
    {
        $fileContent = Storage::disk('sftp')->get($this->directory.'/'.$filePath);
        if (empty($fileContent)) {
            \Log::error("Unable to find any content in $filePath");

            return '';
        }

        return $fileContent;
    }

    public function archiveFiles(): void
    {
        $files = Storage::disk('sftp')->allFiles($this->directory);
        foreach ($files as $file) {
            $this->archiveFile($file);
        }
    }

    public function archiveFile(string $file): void
    {
        if ($file === basename($file)) {
            $file = $this->directory.$file;
        }

        if ($this->isValid($this->archivePath.'/'.$file)) {
            // TODO: find correct sftp rights to use move instead of copy
            Storage::disk('sftp')->copy($file, $this->archivePath.'/'.$file);
        }
    }

    protected function isValid(string $file): bool
    {
        if (str_starts_with(basename($file), '.')) {
            return false;
        }

        return !Storage::disk('sftp')->exists($file);
    }

    protected function isAlreadyProcessed(string $file): bool
    {
        try {
            return Storage::disk('sftp')->exists($this->archivePath.'/'.$file);
        } catch (UnableToCheckFileExistence) {
            // If something happens, waiting 5 seconds then retrying. If a there is a second error, it will be thrown
            sleep(5);

            return Storage::disk('sftp')->exists($this->archivePath.'/'.$file);
        }
    }

    /**
     * @param array<mixed, mixed> $record contains all data of a sale (activationCode, store, date, etc...)
     * @param string $file the file containing the record
     */
    public function isForced(array $record, string $file): bool
    {
        return $this->forceFlag === ($record['action'] ?? '');
    }

    public function getReseller(): ?string
    {
        return $this->resellerTrigram;
    }
}
