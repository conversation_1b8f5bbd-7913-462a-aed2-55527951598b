<?php

declare(strict_types=1);

namespace App\Domain\User\Repositories;

use App\Infrastructure\Crm\GlobalTrackerService;
use App\Models\Atelier;
use App\Models\User;
use App\Models\UserFavoriteWorkshop;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Pagination\LengthAwarePaginator;

class UserFavoriteRepository
{
    public function __construct()
    {
    }

    protected function getTable(): string
    {
        /**
         * @var UserFavoriteWorkshop $modelInstance
         */
        $modelInstance = UserFavoriteWorkshop::newModelInstance();

        return $modelInstance->getTable();
    }

    public function addToFavoritesForUserId(User $user, Atelier $workshop): void
    {
        try {
            $user->favorites()->syncWithoutDetaching($workshop);
        } catch (UniqueConstraintViolationException) {
            // This can happen if two calls are made at the exact same time
            return;
        }
        GlobalTrackerService::addWorkshopToFavorites($user, $workshop);
    }

    public function removeFromUserFavorites(User $user, Atelier $workshop): void
    {
        $user->favorites()->detach($workshop);
        GlobalTrackerService::trackUser($user);
    }

    /**
     * @return LengthAwarePaginator<\stdClass>
     */
    public function getPaginatedFavoritesIdForUserId(int|string $userId, int $perPage = 20, int $page = 0): LengthAwarePaginator
    {
        /**
         * @var LengthAwarePaginator<\stdClass> $paginator
         */
        $paginator = \DB::table($this->getTable())
            ->where('user_id', $userId)
            ->select('atelier_id as id')
            ->paginate(
                perPage: $perPage,
                page: $page
            );

        $paginator->through(fn ($item) => $item->id);

        return $paginator;
    }
}
