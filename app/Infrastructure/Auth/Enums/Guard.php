<?php

declare(strict_types=1);

namespace App\Infrastructure\Auth\Enums;

/** @SuppressWarnings(PHPMD.BooleanArgumentFlag) */
enum Guard: string
{
    case Admin = 'admin';
    case Artisan = 'artisan';
    case Customer = 'customer';
    case Widget = 'widget';
    case Api = 'api';

    public function getLoginUrl(bool $absolute = false): string
    {
        return match ($this) {
            self::Admin => route('admin.login', absolute: $absolute),
            self::Artisan => route(localizedRouteName('artisan.login'), absolute: $absolute),
            default => route(
                localizedRouteName('login.show'),
                parameters: [
                    'redirect' => request()->getPathInfo(),
                ],
                absolute: $absolute
            ),
        };
    }

    public static function getDefaultLoginUrl(bool $absolute = false): string
    {
        return self::Customer->getLoginUrl($absolute);
    }

    public function getHomeUrl(bool $absolute = false): string
    {
        return match ($this) {
            self::Admin => route('admin.home', absolute: $absolute),
            self::Artisan => route(localizedRouteName('artisan.home'), absolute: $absolute),
            default => route(localizedRouteName('home'), absolute: $absolute),
        };
    }

    public static function getDefaultHomeUrl(bool $absolute = false): string
    {
        return self::Customer->getLoginUrl($absolute);
    }
}
