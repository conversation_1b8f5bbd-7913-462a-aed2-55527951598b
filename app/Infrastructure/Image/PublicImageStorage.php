<?php

declare(strict_types=1);

namespace App\Infrastructure\Image;

use Illuminate\Container\Attributes\Storage;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Http\File;
use Illuminate\Http\UploadedFile;
use Psr\Http\Message\StreamInterface;

readonly class PublicImageStorage
{
    public function __construct(
        #[Storage('images')]
        protected Filesystem $filesystem,
    ) {
    }

    /**
     * @param array<string, ?string> $options
     */
    public function getUrl(string $imagePath, array $options = ['width' => null, 'height' => null]): string
    {
        $config = [];
        if (!empty($options['width'])) {
            $config['width'] = $options['width'];
        }

        if (!empty($options['height'])) {
            $config['height'] = $options['height'];
        }

        // Fit = cover by default

        return $this->filesystem->url($imagePath).(empty($config) ? '' : '?'.http_build_query($config));
    }

    public function get(string $imagePath): ?string
    {
        return $this->filesystem->get($imagePath);
    }

    /** @return ($content is string ? bool : ($content is StreamInterface ? bool : string|false)) */
    public function put(string $imagePath, File|UploadedFile|StreamInterface|string $content): bool|string
    {
        return $this->filesystem->put($imagePath, $content);
    }

    public function delete(string $imagePath): bool
    {
        return $this->filesystem->delete($imagePath);
    }
}
