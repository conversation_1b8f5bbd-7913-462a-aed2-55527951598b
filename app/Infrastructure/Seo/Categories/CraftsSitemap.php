<?php

declare(strict_types=1);

namespace App\Infrastructure\Seo\Categories;

use App\Domain\Content\Models\Craft;
use App\Domain\Seo\Sitemap;
use App\Enums\Country;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CraftsSitemap implements Sitemap
{
    public function getSlug(): string
    {
        return 'crafts';
    }

    public function getContent(int $page, int $perPage, Country $country): LengthAwarePaginator
    {
        // The country parameter is ignored as crafts are not country based
        return Craft::query()
            ->has('singlePage')
            ->with([
                'translations',
                'singlePage',
            ])
            ->scopes([
                'active',
            ])
            ->paginate($perPage, page: $page)
            ->through(function (Craft $craft) {
                return [
                    'name' => $craft->name,
                    'slug' => $craft->singlePage?->getUrl(),
                ];
            });
    }
}
