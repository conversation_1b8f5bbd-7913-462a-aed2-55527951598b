<?php

declare(strict_types=1);

namespace App\Infrastructure\Seo\Categories;

use App\Domain\Seo\Sitemap;
use App\Enums\Country;
use App\Models\Ville;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CitiesSitemap implements Sitemap
{
    public function getSlug(): string
    {
        return 'cities';
    }

    public function getContent(int $page, int $perPage, Country $country): LengthAwarePaginator
    {
        return Ville::query()
            ->with('translations')
            ->scopes([
                'active',
            ])
            ->where('country_code', $country)
            ->paginate($perPage, page: $page)
            ->through(function (Ville $city) {
                return [
                    'name' => $city->ville_nom,
                    'slug' => $city->singlePage?->getUrl(),
                ];
            });
    }
}
