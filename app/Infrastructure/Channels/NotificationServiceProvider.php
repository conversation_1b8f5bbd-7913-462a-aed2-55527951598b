<?php

declare(strict_types=1);

namespace App\Infrastructure\Channels;

use Illuminate\Foundation\Application;
use Illuminate\Notifications\ChannelManager;
use Illuminate\Notifications\Channels\VonageSmsChannel;
use Illuminate\Notifications\SlackNotificationRouterChannel;
use Illuminate\Notifications\Vonage;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;
use Vonage\Client;

final class NotificationServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(Client::class, function (Application $app) {
            return Vonage::make($app['config']['vonage'])->client();
        });

        $this->app->bind(VonageSmsChannel::class, function (Application $app) {
            return new VonageSmsChannel($app->make(Client::class), $app['config']['vonage.sms_from']);
        });

        $this->app->bind(SlackNotificationRouterChannel::class, fn (Application $app) => new SlackNotificationRouterChannel($app));

        Notification::resolved(function (ChannelManager $service): void {
            $service->extend('vonage', function ($app) {
                // If no Vonage API key is available, let's send an email instead !
                if (empty($app['config']['vonage.api_key'])) {
                    return $app->make(FakeSmsChannel::class);
                }

                return $app->make(VonageSmsChannel::class);
            });

            $service->extend('slack', function ($app) {
                // If no Slack API key is available, let's send an email instead !
                if (empty($app['config']['services.slack.notifications.bot_user_oauth_token'])) {
                    return $app->make(FakeSlackChannel::class);
                }

                return $app->make(SlackNotificationRouterChannel::class);
            });
        });
    }

    public function boot(): void
    {
        // Not calling parent::boot(), as we definitely don't want to try to publish the config EACH TIME a console command runs!
    }
}
