<?php

declare(strict_types=1);

namespace App\Infrastructure\Interfaces;

use Google\Service\Docs\Document;
use Google\Service\Drive\DriveFile;
use Google\Service\Drive\FileList;

/*
 * This interface is mainly used to fake App\Infrastructure\Services\GoogleDocs service
 * But this interface should return DTO instead of GoogleDocs document
 */
interface DocumentManagerInterface
{
    public function getDocumentById(string $id): Document;

    public function listFiles(): FileList;

    public function createDocument(string $title): Document;

    public function duplicateFile(string $originId, string $newTitle): DriveFile;

    /** @param array<string, string> $strings */
    public function replacePlaceholdersInDocument(string $id, array $strings): void;

    public function allowPublicEdit(string $id): void;

    public function allowEditByDomain(string $id, string $domain): void;
}
