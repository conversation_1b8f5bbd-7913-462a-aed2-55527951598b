<?php

declare(strict_types=1);

namespace App\Infrastructure\EventBanner;

use App\Infrastructure\EventBanner\Repositories\EventBannerRepository;
use App\Models\UserArtisan;
use Illuminate\Support\Facades\App;

class BannerLoader
{
    /** @return Banner[] */
    public function getBanners(UserArtisan $userArtisan): array
    {
        $locale = App::getLocale();
        $eventBanners = (new EventBannerRepository())->loadBanners($userArtisan);
        $artisan = $userArtisan->artisans->first();

        $banners = [];

        foreach ($eventBanners as $eventBanner) {
            if (!$eventBanner->shouldDisplay($artisan)) {
                continue;
            }

            $banner = new Banner(
                $eventBanner->name,
                $eventBanner->icon,
                $eventBanner->type,
                $eventBanner->getTitle($locale),
                $eventBanner->getText($locale),
                $eventBanner->getCta($locale),
            );

            $banners[] = $banner;
        }

        return $banners;
    }
}
