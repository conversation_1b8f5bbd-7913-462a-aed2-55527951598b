<?php

declare(strict_types=1);

namespace App\Infrastructure\Crm\Hubspot\Jobs;

use App\Domain\Content\Repositories\ArtisanRepository;
use App\Infrastructure\Crm\Customerio\Exceptions\DisabledServiceException;
use App\Infrastructure\Crm\Hubspot\Services\ArtisanObjectManager;
use App\Infrastructure\Crm\Hubspot\Traits\HubspotDispatchable;
use Carbon\CarbonImmutable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SynchronizeArtisans implements ShouldQueue
{
    use HubspotDispatchable;
    use InteractsWithQueue;
    use Queueable;

    public function __construct()
    {
        $this->onQueue('crm');
    }

    /**
     * Execute the job.
     *
     * @throws DisabledServiceException
     */
    public function handle(ArtisanRepository $artisanRepository, ArtisanObjectManager $hubspotArtisanObjectManager): void
    {
        $notSynchronizedArtisans = $artisanRepository->getNotSynchronizedArtisans();
        \Log::info('Synchronising updated artisans', ['count' => $notSynchronizedArtisans->count()]);

        foreach ($notSynchronizedArtisans->chunk(100) as $artisans) {
            try {
                $hubspotArtisanObjectManager->updateArtisanBatch($artisans, updateContactAssociation: true);
            } catch (\Throwable $e) {
                report($e);
            }

            $artisanRepository->updateSynchronizationDateByIds($artisans->pluck('id')->all(), CarbonImmutable::now());
        }
    }
}
