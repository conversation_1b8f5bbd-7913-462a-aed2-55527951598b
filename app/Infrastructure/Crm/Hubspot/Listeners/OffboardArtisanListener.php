<?php

declare(strict_types=1);

namespace App\Infrastructure\Crm\Hubspot\Listeners;

use App\Infrastructure\Crm\Hubspot\DTO\OffboardingArtisanInformation;
use App\Infrastructure\Crm\Hubspot\Events\OffboardArtisan;
use App\Infrastructure\Crm\Hubspot\Services\OffboardingArtisanService;
use App\Models\Artisan;
use Illuminate\Support\Facades\Log;

final readonly class OffboardArtisanListener
{
    public function __construct(
        private OffboardingArtisanService $artisanOffboardingService,
    ) {
    }

    public function handle(OffboardArtisan $event): void
    {
        $information = OffboardingArtisanInformation::fromEvent($event);
        $artisan = Artisan::query()->findOrFail($event->artisanId);

        if ($artisan->hubspot_custom_object_id === null) {
            $exception = new \InvalidArgumentException('Artisan does not have a hubspot custom object id');
            Log::error(
                $exception->getMessage(),
                [
                    ...$information->toHubspotProperties(),
                    'artisanId' => $event->artisanId,
                    'exception' => $exception,
                ]
            );

            report($exception);

            return;
        }

        $this->artisanOffboardingService->offboard($artisan, $information);
    }
}
