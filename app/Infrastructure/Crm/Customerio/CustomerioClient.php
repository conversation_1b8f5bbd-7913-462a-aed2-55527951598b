<?php

declare(strict_types=1);

namespace App\Infrastructure\Crm\Customerio;

use App\Infrastructure\Crm\Customerio\Exceptions\CustomerIoException;
use Symfony\Component\HttpClient\Exception\ServerException;
use Symfony\Component\HttpClient\Exception\TransportException;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

/**
 * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
 */
class CustomerioClient
{
    public const APP_API_V1_URI = 'https://api-eu.customer.io/v1/';
    public const TRACK_API_V2_URI = 'https://track-eu.customer.io/api/v2/';

    private HttpClientInterface $httpClient;

    protected string $apiKey;

    protected string $siteId;

    protected string $appKey;

    /**
     * Client constructor.
     */
    public function __construct(string $apiKey, string $siteId, string $appKey)
    {
        $this->httpClient = HttpClient::create();
        $this->apiKey = $apiKey;
        $this->siteId = $siteId;
        $this->appKey = $appKey;
    }

    /**
     * Sends POST request to Customer.io API.
     *
     * @param array<string, mixed> $payload
     *
     * @throws TransportExceptionInterface
     * @throws \JsonException
     * @throws CustomerIoException
     */
    public function post(string $endpoint, array $payload, bool $trackApi = true): ResponseInterface
    {
        return $this->request('POST', $endpoint, $payload, $trackApi);
    }

    /**
     * Sends PUT request to Customer.io API.
     *
     * @param array<string, mixed> $json
     *
     * @throws TransportExceptionInterface
     * @throws \JsonException
     * @throws CustomerIoException
     */
    public function put(string $endpoint, array $json, bool $trackApi = true): ResponseInterface
    {
        return $this->request('PUT', $endpoint, $json, $trackApi);
    }

    /**
     * Sends GET request to Customer.io API.
     *
     * @throws TransportExceptionInterface
     * @throws \JsonException
     * @throws CustomerIoException
     */
    public function get(string $endpoint, bool $trackApi = true): ResponseInterface
    {
        return $this->request('GET', $endpoint, [], $trackApi);
    }

    /**
     * @param array<string, mixed> $json
     *
     * @throws TransportExceptionInterface
     * @throws CustomerIoException
     */
    protected function request(string $method, string $path, array $json, bool $trackApi = true): ResponseInterface
    {
        $apiEndpoint = $trackApi ? self::TRACK_API_V2_URI : self::APP_API_V1_URI;

        $options = $this->getDefaultParams($trackApi);
        $url = $apiEndpoint.$path;

        if (!empty($json)) {
            $options['json'] = $json;
        }

        try {
            $response = $this->httpClient->request($method, $url, $options);
        } catch (ServerException|TransportException $e) {
            // If something happens, waiting 5 seconds then retrying. If a there is a second error, it will be thrown
            sleep(5);

            $response = $this->httpClient->request($method, $url, $options);
        }

        if ($response->getStatusCode() === 400) {
            throw new CustomerIoException('The request was malformed or invalid', $response->getStatusCode());
        }

        if ($response->getStatusCode() === 401) {
            throw new CustomerIoException('Unauthorized', $response->getStatusCode());
        }

        return $response;
    }

    /**
     * @return array<string, array<string, string>>
     */
    protected function getDefaultParams(bool $trackApi = true): array
    {
        $authorization = $trackApi ? 'Basic '.base64_encode($this->siteId.':'.$this->apiKey) : 'Bearer '.$this->appKey;

        return [
            'headers' => [
                'Authorization' => $authorization,
                'Accept' => 'application/json',
            ],
        ];
    }
}
