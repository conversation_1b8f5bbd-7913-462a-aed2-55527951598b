<?php

declare(strict_types=1);

namespace App\Infrastructure\Crm\Customerio;

use App\Domain\ECommerce\Models\CartItem;
use App\Domain\Gift\Enums\GiftCardType;
use App\Models\Atelier;
use App\Models\Panier;
use App\Models\Reservation;
use Illuminate\Support\Collection;

class CustomerioCartTransformer
{
    /**
     * @return array<string, mixed>
     */
    public static function formatForApi(Panier $cart): array
    {
        $locale = $cart->user?->preferredLocale();

        $formattedCartItems = self::getFormattedCartItems($cart, $locale);

        return [
            'with_booking' => $formattedCartItems->filter(fn ($item) => $item['type'] === 'booking')->isNotEmpty(),
            'with_ticket' => $formattedCartItems->filter(fn ($item) => $item['type'] === GiftCardType::TICKET)->isNotEmpty(),
            'with_value' => $formattedCartItems->filter(fn ($item) => $item['type'] === GiftCardType::VALUE)->isNotEmpty(),
            'with_paper' => $formattedCartItems->filter(fn ($item) => $item['type'] === 'paper')->isNotEmpty(),
            'restore_cart_link' => internationalUrl(route(localizedRouteName('front.panier.abandoned', $locale), absolute: false)),
            'items' => $formattedCartItems->toArray(),
        ];
    }

    /**
     * @return Collection<int, mixed>
     */
    private static function getFormattedCartItems(Panier $cart, ?string $locale = null): Collection
    {
        return $cart
            ->items
            ->map(function (CartItem $item) use ($locale) {
                $relatedProduct = $item->getRelatedProduct();

                $formattedItem = match (true) {
                    // @phpstan-ignore-next-line argument.type
                    $item->isTicketGiftCardItem() => self::formatForGiftCardTicket($item, $relatedProduct, $locale),
                    $item->isAmountGiftCardItem() => self::formatForGiftCardValue($item, $locale),
                    $item->isBookingItem() && $relatedProduct instanceof Reservation => self::formatForReservation($item, $relatedProduct, $locale),
                    default => null,
                };

                // In case the item has sub items (aka, physical cards),
                // we'll also format them and add it to the list
                $formattedSubItems = $item->subItems
                    ->map(function (CartItem $subItem) use ($item) {
                        return $subItem->isPhysicalCard()
                            ? self::formatForGiftCardPhysical($subItem, $item)
                            : null;
                    })
                    ->filter()
                    ->toArray();

                return [$formattedItem, ...$formattedSubItems];
            })
            ->flatten(1)
            ->filter(); // Remove empty items
    }

    /**
     * @return array<string, mixed>
     */
    private static function formatForGiftCardTicket(CartItem $item, Atelier $workshop, ?string $locale = null): array
    {
        return [
            'type' => GiftCardType::TICKET,
            'name' => $workshop->getProductName(),
            'image' => $workshop->getImageUrl(),
            'link' => internationalUrl(route(localizedRouteName('atelier.display', $locale), $workshop->permalien, absolute: false)),
            'city' => $workshop->lieu?->adresse_ville,
            'workshop_id' => $workshop->getKey(),
            'price' => $item->discounted_amount,
            'currency' => $item->currency,
            'recipient_name' => $item->gift_to,
        ];
    }

    /**
     * @return array<string, mixed>
     */
    private static function formatForGiftCardValue(CartItem $item, ?string $locale = null): array
    {
        return [
            'type' => GiftCardType::VALUE,
            'name' => 'Carte cadeau digital',
            'image' => 'https://userimg-assets-eu.customeriomail.com/images/client-env-119961/1696426179866_carte-cadeau-600-450_01HBXE2GWHTJ44BB2WZ5WGFW3E.png',
            'link' => internationalUrl(route(localizedRouteName('gift-card-value.show', $locale), $item->amount, absolute: false)),
            'city' => null,
            'workshop_id' => null,
            'price' => $item->discounted_amount,
            'currency' => $item->currency,
            'recipient_name' => $item->gift_to,
        ];
    }

    /**
     * @return array<string, mixed>
     */
    private static function formatForGiftCardPhysical(CartItem $item, CartItem $parentItem): array
    {
        $relatedProduct = $parentItem->getRelatedProduct();

        return match (true) {
            // @phpstan-ignore-next-line argument.type
            $parentItem->isTicketGiftCardItem() => array_merge(self::formatForGiftCardTicket($parentItem, $relatedProduct), [
                'type' => 'paper',
                'price' => $item->discounted_amount,
            ]),
            $parentItem->isAmountGiftCardItem() => array_merge(self::formatForGiftCardValue($parentItem), [
                'type' => 'paper',
                'name' => 'Carte cadeau papier',
                'price' => $item->discounted_amount,
            ]),
            default => [],
        };
    }

    /**
     * @return array<string, mixed>
     */
    private static function formatForReservation(CartItem $item, Reservation $relatedProduct, ?string $locale = null): array
    {
        $workshop = $relatedProduct->evenement->atelier;

        return [
            'type' => 'booking',
            'name' => $workshop->getProductName(),
            'image' => $workshop->getImageUrl(),
            'link' => internationalUrl(route(localizedRouteName('atelier.display', $locale), $workshop->permalien, absolute: false)),
            'city' => $workshop->lieu?->adresse_ville,
            'workshop_id' => $workshop->getKey(),
            'price' => $item->discounted_amount,
        ];
    }
}
