<?php

declare(strict_types=1);

namespace App\Infrastructure\Crm\Customerio\Handlers;

use App\Infrastructure\Crm\Customerio\CustomerioModelTransformer;
use App\Models\User;
use Illuminate\Support\Collection;

abstract class CustomerioHandler implements CustomerioInterface
{
    public function transformUserBatchData(Collection $users): array
    {
        $usersData = [];
        foreach ($users as $user) {
            $usersData[] = $this->prepareIdentifyRequestBody(
                CustomerioModelTransformer::transformUserDataWithWishlist($user),
                $user
            );
        }

        return $usersData;
    }

    public function prepareIdentifyRequestBody(array $attributes, string|User $emailOrUser): array
    {
        $identifiers = $emailOrUser instanceof User
            ? ['id' => (string) $emailOrUser->id]
            : ['email' => $emailOrUser];

        return [
            'type' => self::PERSON_TYPE,
            'identifiers' => $identifiers,
            'action' => self::IDENTIFY_ACTION,
            'attributes' => $attributes,
        ];
    }

    public function prepareEventRequestBody(string $eventName, array $attributes, User $user, ?int $timestamp = null): array
    {
        return [
            'type' => self::PERSON_TYPE,
            'identifiers' => [
                'id' => ''.$user->id,
            ],
            'action' => self::EVENT_ACTION,
            'attributes' => $attributes,
            'name' => $eventName,
            'timestamp' => $timestamp,
        ];
    }
}
