<?php

declare(strict_types=1);

namespace App\Infrastructure\NotificationSettings;

use App\Infrastructure\NotificationSettings\Models\ArtisanNotificationSetting;
use App\Infrastructure\NotificationSettings\Repositories\NotificationRepository;
use App\Models\Artisan;
use Illuminate\Support\Collection;

/**
 * @SuppressWarnings(PHPMD)
 */
class NotificationSettings
{
    public function __construct(readonly NotificationRepository $notificationRepository)
    {
    }

    /**
     * @return array<string, array<string, mixed>>
     */
    public function getSettingsForArtisan(Artisan $artisan): array
    {
        $groupNotifications = $this->notificationRepository->getGroups();
        $artisan->refresh();
        $artisanSettings = $artisan->notificationSettings;
        $settings = [];

        foreach ($groupNotifications as $group) {
            $settings[$group->key] = [
                'label' => __('userartisan/view/settings/pages.'.$group->label),
                'canBeFullyDisabled' => $this->notificationRepository->groupCanBeFullyDisabled($group->key),
            ];
            foreach ($this->notificationRepository->getNotificationsByGroup($group->key) as $notification) {
                $notification->label = __('userartisan/view/settings/pages.'.$notification->label);
                $settings[$group->key]['notifications'][] = $this->getStatusForNotification($notification, $artisanSettings);
            }
        }

        return $settings;
    }

    /**
     * @param Collection<int, ArtisanNotificationSetting> $artisanSettings
     */
    private function getStatusForNotification(Notification $notification, Collection $artisanSettings): Notification
    {
        foreach ($notification->types as $type => $value) {
            $notification->setTypeIsActive($type, $this->isActive($type, $artisanSettings, $notification->class));
        }

        return $notification;
    }

    /**
     * @param Collection<int, ArtisanNotificationSetting> $artisanSettings
     */
    public function isActive(string $type, Collection $artisanSettings, ?string $notification = null): bool
    {
        $settings = $artisanSettings;
        if (!empty($notification)) {
            $settings = $artisanSettings->where('notification', $notification);
        }

        return $settings->contains(function ($val) use ($type) {
            return $val->type === $type;
        });
    }

    /**
     * @param array<string, bool> $types
     */
    public function updateNotificationSettingsForArtisan(string $notification, array $types, Artisan $artisan): void
    {
        foreach ($types as $type => $isActive) {
            if ($isActive) {
                ArtisanNotificationSetting::updateOrCreate([
                    'notification' => $notification,
                    'type' => $type,
                    'artisan_id' => $artisan->id,
                ]);
            } else {
                ArtisanNotificationSetting::where([
                    'notification' => $notification,
                    'type' => $type,
                    'artisan_id' => $artisan->id,
                ])
                    ->delete();
            }
        }
    }

    public function setupDefaultSettingsForArtisan(Artisan $artisan): void
    {
        $groupNotifications = $this->notificationRepository->getGroups();
        foreach ($groupNotifications as $group) {
            foreach ($this->notificationRepository->getNotificationsByGroup($group->key) as $notification) {
                $this->updateNotificationSettingsForArtisan($notification->class, $notification->types, $artisan);
            }
        }
    }
}
