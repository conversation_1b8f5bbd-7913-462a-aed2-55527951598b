<?php

declare(strict_types=1);

namespace App\Infrastructure\FeatureFlagging;

use App\Enums\Country;
use Illuminate\Support\ServiceProvider;
use <PERSON>vel\Pennant\Events\UnexpectedNullScopeEncountered;
use Laravel\Pennant\Events\UnknownFeatureResolved;
use Laravel\Pennant\Feature;

/**
 * @SuppressWarnings(PHPMD.UnusedFormalParameter) Parameter $country is used as a scope by the package
 */
class FeatureFlaggingProvider extends ServiceProvider
{
    public function boot(): void
    {
        Feature::useMorphMap();

        foreach (config()->array('pennant.features.by_country') as $feature) {
            Feature::define($feature, static function (Country $country) {
                // No logic here, it's handled in the back office. This function
                // is called only when nothing exists in the database,
                // which will act as a default value.
                return false;
            });
        }

        \Event::listen(static function (UnknownFeatureResolved $event): void {
            \Log::warning("Resolving unknown feature [$event->feature].");
        });

        \Event::listen(static function (UnexpectedNullScopeEncountered $event): void {
            \Log::warning("Unhandled null scope given to feature [$event->feature].");
        });
    }
}
