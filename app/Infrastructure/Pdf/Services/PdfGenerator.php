<?php

declare(strict_types=1);

namespace App\Infrastructure\Pdf\Services;

use App\Infrastructure\Pdf\Helpers\PdfFileResponse;
use App\Infrastructure\Pdf\HtmlPdfRenderer;
use App\Infrastructure\Pdf\Printable;
use App\Infrastructure\Pdf\Renderer\DomPdfRenderer;
use App\Infrastructure\Pdf\Renderer\WeasyprintPdfRenderer;
use Illuminate\Container\Attributes\Storage;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Http\Response;
use Symfony\Component\Process\Exception\ProcessFailedException;

/**
 * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
 */
readonly class PdfGenerator
{
    private Filesystem $filesystem;

    /**
     * Order by priority:  first Renderer to support the PDF will be used.
     *
     * @var HtmlPdfRenderer[]
     */
    private array $renderers;

    public function __construct(
        #[Storage('pdfs')]
        Filesystem $filesystem,
        WeasyprintPdfRenderer $weasyprint<PERSON>enderer,
        DomPdfRenderer $domPdfRenderer,
    ) {
        $this->filesystem = $filesystem;
        $this->renderers = [
            $domPdfRenderer,
            $weasyprintRenderer,
        ];
    }

    /**
     * Ensure a PDF is rendered and stored on the file system, and return its content.
     *
     * @param bool $force Set to true to force rendering even if file already exists
     */
    public function generate(Printable $pdf, bool $force = false): string
    {
        $path = $this->getPath($pdf);

        if ($force || !$this->filesystem->exists($path)) {
            $content = $this->renderPrintable($pdf);

            $this->filesystem->put($path, $content);

            return $content;
        }

        /** @var string */
        $content = $this->filesystem->get($path);

        return $content;
    }

    /**
     * Removes a PDF from storage if a corresponding file already exists.
     */
    public function delete(Printable $pdf): void
    {
        $this->filesystem->delete($this->getPath($pdf));
    }

    /**
     * Stream the given PDF to the user.
     */
    public function stream(Printable $pdf, int $status = 200, bool $force = false): Response
    {
        return $this->serve($pdf, $status, $force, stream: true);
    }

    /**
     * Download the given PDF to the user device.
     */
    public function download(Printable $pdf, int $status = 200, bool $force = false): Response
    {
        return $this->serve($pdf, $status, $force, stream: false);
    }

    /**
     * Steam or download the givent PDF using a PdfFileResponse.
     */
    private function serve(Printable $pdf, int $status = 200, bool $force = false, bool $stream = false): Response
    {
        try {
            return new PdfFileResponse(
                $pdf->getFileName(),
                $this->generate($pdf, $force),
                stream: $stream,
                status: $status,
            );
        } catch (ProcessFailedException $exception) {
            report($exception);

            return abort(500, 'Could not generate PDF file');
        }
    }

    /**
     * Get filesystem path to the given PDF.
     */
    private function getPath(Printable $pdf): string
    {
        return implode('/', array_filter([
            $pdf->getDirectory(),
            $pdf->getFileName(),
        ]));
    }

    /**
     * Find the right renderer for the given Printable and use it to render the PDF content.
     */
    private function renderPrintable(Printable $pdf): string
    {
        foreach ($this->renderers as $renderer) {
            if ($renderer->supports($pdf)) {
                return $renderer->renderPrintable($pdf);
            }
        }

        throw new \Exception(sprintf('Could not find a renderer supporting %s', $pdf::class));
    }
}
