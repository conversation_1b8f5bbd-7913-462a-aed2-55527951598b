<?php

declare(strict_types=1);

namespace App\Infrastructure\Pdf\Renderer;

use App\Infrastructure\Pdf\DomPdfPrintable;
use App\Infrastructure\Pdf\Printable;
use Barryvdh\DomPDF\PDF as DomPDF;

class DomPdfRenderer extends PdfRenderer
{
    public function supports(Printable $pdf): bool
    {
        return $pdf instanceof DomPdfPrintable;
    }

    protected function render(string $html): string
    {
        /*
         * Sadly DomPDF doesn't support rendering multiple times with the same instance.
         * So we get a new instance for each render instead of injecting the service.
         * See: https://github.com/dompdf/dompdf/issues/3194
         */
        return \App::make(DomPDF::class)->loadHTML($html)->output();
    }
}
