<?php

declare(strict_types=1);

namespace App\Infrastructure\Search;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

interface SearchableRepositoryInterface
{
    /**
     * Marks all workshops as requiring a search sync.
     *
     * @return int the number of update rows
     */
    public function markAllAsOutOfSync(): int;

    /**
     * Marks all given items as in sync.
     *
     * @param int[] $ids
     *
     * @return int the number of updated rows
     */
    public function markAsSynchronizedByIds(array $ids): int;

    /**
     * Marks all related models (children, ...) items as in sync.
     *
     * @param int[] $ids
     *
     * @return int the number of updated rows
     */
    public function markRelatedModelsAsOutOfSync(array $ids): int;

    /**
     * @return class-string<SearchableInterface> the related models' class name
     */
    public function getModelClassName(): string;

    /**
     * Returns a query allowing to select all Searchable items to sync.
     *
     * @return Builder<Model>
     */
    public function findByOutOfSyncStateQb(): Builder;

    /**
     * This method allows to prefetch some values by batch, in order to avoid doing a single request per item when indexing.
     *
     * @param Collection<int, Model> $items
     */
    public function processSearchableChunk(Collection $items): void;
}
