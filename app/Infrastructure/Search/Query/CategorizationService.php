<?php

namespace App\Infrastructure\Search\Query;

use Algolia\AlgoliaSearch\SearchIndex;
use App\Domain\Content\Abstracts\CategorizationModel;
use App\Domain\Content\Models\Craft;
use App\Domain\Content\Models\Creation;
use App\Domain\Content\Models\Page;
use App\Domain\Content\Models\SinglePage;
use App\Domain\Content\Models\Technique;
use App\Domain\Content\Repositories\ArtisanRepository;
use App\Enums\Categorization\FacetType;
use App\Infrastructure\Search\Client\AlgoliaClient;
use App\Models\Artisan;
use App\Models\Atelier;
use App\Models\Tag;
use App\Models\Ville;
use Illuminate\Cache\Repository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

final class CategorizationService
{
    public function __construct(
        readonly private AlgoliaClient $client,
        readonly private ArtisanRepository $artisanRepository,
        readonly private Repository $cache,
    ) {
    }

    private SearchIndex $index;

    private function getIndex(): SearchIndex
    {
        return $this->index ?? $this->index = $this->client->getIndexByLocale(Atelier::class, \App::getLocale());
    }

    public function getHydratedFacet(FacetType $type, array $query = [], ?Ville $city = null, ?Craft $craft = null, int $count = 20): array
    {
        return $this->hydrateAlgoliaFacet(
            $this->fetchFacet($type->value, $query, $count),
            $type->model(),
            $city,
            $craft,
        );
    }

    public function fetchFacet(string $facetName, array $query = [], int $count = 20): array
    {
        try {
            return $this->getIndex()
                ->searchForFacetValues($facetName, '', [
                    'maxFacetHits' => $count,
                    'facetingAfterDistinct' => true,
                    ...$query,
                ]) ?? [];
        } catch (\Exception $e) {
            report($e);

            return [];
        }
    }

    protected function hydrateAlgoliaFacet(array $facet, string $model, ?Ville $city = null, ?Craft $craft = null): array
    {
        return match ($model) {
            Artisan::class => $this->hydrateArtisan($facet),
            Craft::class, Ville::class, Technique::class, Creation::class, Tag::class => $this->hydrateCategory($facet, $model, $city, $craft),
            default => throw new \Exception("Unknown type [$model]"),
        };
    }

    protected function hydrateArtisan(array $algoliaResult): array
    {
        if (!isset($algoliaResult['facetHits'])) {
            return $algoliaResult;
        }

        $dbItems = $this->artisanRepository->getArtisansAsFacets(array_column($algoliaResult['facetHits'], 'value'));
        if (\is_array($algoliaResult['facetHits']) && \count($algoliaResult['facetHits']) > 0) {
            foreach ($algoliaResult['facetHits'] as $key => $facet) {
                $foundItem = $dbItems->where('id', $facet['value'])->first();
                if ($foundItem) {
                    $algoliaResult['facetHits'][$key] = [
                        ...$facet,
                        ...$foundItem,
                    ];
                } else {
                    $algoliaResult['facetHits'][$key] = null;
                }
            }
        }

        return $algoliaResult;
    }

    protected function hydrateCategory(array $algoliaResult, string $model, ?Ville $city = null, ?Craft $craft = null): array
    {
        if (!isset($algoliaResult['facetHits'])) {
            return $algoliaResult;
        }

        $newHits = [];
        $ids = array_map(static fn ($item) => explode('||', $item['value'])[0], $algoliaResult['facetHits']);
        /** @var Collection $dbModels */
        $dbModels = $model::with(['singlePage'])->whereIn('id', $ids)->get();

        foreach ($algoliaResult['facetHits'] as $facetHit) {
            $explodedFacet = explode('||', $facetHit['value']);
            /** @var ?CategorizationModel $model */
            $model = $dbModels->find($explodedFacet[0]);

            if ($model === null) {
                continue;
            }

            $newHits[] = [
                ...$facetHit,
                'thumbnail_image' => $model->getThumbnailImageUrl(),
                'slug' => $model->{$model::$slugAttributeName},
                'redirectUrl' => $this->getRedirectUrl($model, $city, $craft),
                'label' => $explodedFacet[1] ?? '',
                'description' => $model->getDescription(),
            ];
        }

        return [
            ...$algoliaResult,
            'facetHits' => $newHits,
        ];
    }

    protected function getRedirectUrl(CategorizationModel $model, ?Ville $city = null, ?Craft $craft = null): string
    {
        // This is supposed to be temporary, it's a hack to find the single page match a
        // format [City x Craft]. There are some variants and exceptions so there are
        // some or conditions, we use the cache to limite the number of queries
        if ($model instanceof Ville && $craft !== null) {
            $city = $model;
        } elseif ($model instanceof Craft && $city !== null) {
            $craft = $model;
        }

        if ($city !== null && $craft !== null) {
            $slug = $this->cache->remember("facet.craft.{$city->getSlug()}.{$craft->getSlug()}", now()->addWeek(), function () use ($city, $craft) {
                return $this->getCityCraftSinglePage($city, $craft)?->getUrl();
            });

            if ($slug !== null) {
                return $slug;
            }
        }

        return $model->singlePage?->slug
            ?? route('ateliers.ateliers', [], false);
    }

    public function getCityCraftSinglePage(Ville $city, Craft $craft): ?SinglePage
    {
        $citySlug = $city->getSlug();
        $craftSlug = $craft->getSlug();

        return SinglePage::query()
            ->where('slug', "$citySlug/$craftSlug")
            ->orWhere('slug', "$citySlug-$craftSlug")
            ->when($craftSlug === 'agriculture', function (Builder $query) use ($citySlug, $craftSlug): void {
                $query
                    ->orWhere('slug', "$citySlug-$craftSlug-urbaine")
                    ->orWhere('slug', "$citySlug/$craftSlug-urbaine");
            })
            ->when($craftSlug === 'manger', function (Builder $query) use ($citySlug): void {
                $query->orWhere('slug', "$citySlug/le-gourmand");
            })
            ->first();
    }
}
