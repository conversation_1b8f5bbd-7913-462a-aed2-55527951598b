<?php

declare(strict_types=1);

namespace App\Infrastructure\Search\Jobs;

use App\Infrastructure\Database\Listeners\QueriesListener;
use App\Infrastructure\Search\AppSearchIndexer;
use App\Infrastructure\Search\SearchableInterface;
use App\Infrastructure\Search\SearchableRepositoryInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Cache\Lock;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Symfony\Component\Console\Helper\ProgressBar;

final class SearchableIndexerJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;

    public const DISPATCH_LOCK_PREFIX = ':dispatched';
    public const DISPATCH_DELAY = 10;
    private const QUEUED_MAX_DURATION = 20 * 60;  // This value must be less than the 'search' queue timeout
    private const SYNC_MAX_DURATION = 2 * 60 * 60;

    private ?ProgressBar $progressBar = null;
    private int $maxDuration;
    private float $startTime;

    private ?Lock $openedLock = null;
    private ?QueriesListener $queriesListener = null;

    /**
     * @param class-string<SearchableInterface> $searchableClass
     *
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag) It's okay to have a bool flag here
     */
    public function __construct(private readonly string $searchableClass, private readonly bool $skipRemainingDispatch = false)
    {
        $this->onQueue('search');
    }

    public function __invoke(AppSearchIndexer $indexer, QueriesListener $queriesListener): void
    {
        $this->maxDuration = $this->job?->getQueue() === 'sync' ? self::SYNC_MAX_DURATION : self::QUEUED_MAX_DURATION;
        $this->queriesListener = $queriesListener;

        if (!\in_array($this->searchableClass, AppSearchIndexer::SEARCHABLE_CLASSES, true)) {
            throw new \InvalidArgumentException('The searchable class is not handled');
        }

        $lock = $indexer->getLockForSearchable($this->searchableClass, $this->maxDuration + 1);
        if (!(bool) $lock->get()) {
            \Log::warning('Another indexer job is already running, aborting.');

            return;
        }

        $this->openedLock = $lock;

        try {
            $this->runBatchIndex($indexer);
        } finally {
            // Canceling the lock preventing dealing with the same models
            $this->openedLock?->release();
            // Canceling the lock preventing dispatching a new job
            $indexer->getLockForSearchable($this->searchableClass, 1, self::DISPATCH_LOCK_PREFIX)->forceRelease();
        }

        if ($this->skipRemainingDispatch) {
            return;
        }

        // Let's dispatch another job if any other searchable item should be indexed: maybe the timeout has been reached, or maybe they just have been updated
        foreach (AppSearchIndexer::SEARCHABLE_CLASSES as $class) {
            $remainingCount = $indexer->getRepository($class)->findByOutOfSyncStateQb()->count();
            if (0 === $remainingCount) {
                continue;
            }

            \Log::info('Some searchable items are still out of sync, dispatching another job', ['class' => $class, 'remaining' => $remainingCount]);
            $indexer->dispatchIndexerJob($class);
        }
    }

    public function getOpenedLock(): ?Lock
    {
        return $this->openedLock;
    }

    public function setProgressBar(?ProgressBar $bar): void
    {
        $this->progressBar = $bar;
    }

    private function runBatchIndex(AppSearchIndexer $indexer): void
    {
        $this->startTime = microtime(true);
        $repository = $indexer->getRepository($this->searchableClass);
        $query = $repository->findByOutOfSyncStateQb();
        $countAtStart = $query->count();

        \Log::info('Starting the searchable indexer job', ['count_to_process' => $countAtStart, 'max_duration' => $this->maxDuration]);

        // The bar can continue after the initial max, if some items have been marked as out of sync after the start
        $this->progressBar?->start($countAtStart);

        // As the consumer marks items as "in sync" by batch, the pagination will always be false and middle items will be skipped
        while (($loopCount = $query->count()) > 0) {
            \Log::debug('Starting the searchable indexer loop', ['count_to_process' => $loopCount, 'duration_from_start' => microtime(true) - $this->startTime]);

            try {
                $indexer->syncWithSearchIndex($this->searchableClass, $this->getItemsByQuery($repository));
            } catch (OutOfTimeIndexerException) {
                \Log::info('Stopping the loop, as the max duration has been reached');
                break;
            }
        }

        \Log::debug('Searchable indexer job finished', ['remaining' => $query->count(), 'duration' => microtime(true) - $this->startTime]);
    }

    /**
     * @return \Generator<SearchableInterface>
     */
    private function getItemsByQuery(SearchableRepositoryInterface $repository): \Generator
    {
        $query = $repository->findByOutOfSyncStateQb();

        $chunkSize = 1000; // Maximum WHERE IN size
        $page = 1;
        while (true) {
            $items = $query->forPage($page++, $chunkSize)->get();

            // pre-fetching some values in order to avoid running requests for each item
            $repository->processSearchableChunk($items);
            $this->queriesListener?->startWatcher('chunk');

            foreach ($items as $item) {
                if (!$item instanceof SearchableInterface) {
                    throw new \RuntimeException('The item must be searchable');
                }

                yield $item;
                $this->progressBar?->advance();

                // Max duration
                if (microtime(true) - $this->startTime >= $this->maxDuration) {
                    throw new OutOfTimeIndexerException();
                }
            }

            // Let's report an error if any SQL query is executed during the loop
            // ALL QUERIES MUST BE BATCHED BY CHUNK
            $this->queriesListener?->stopWatcherAndReportQueryInProduction('chunk', ['type' => 'searchable_indexer_job', 'model' => $repository->getModelClassName()], 0);

            // No more page for this loop. Caution: some items have been skipped, as the fist items are marked as "in sync" by batch
            if ($items->count() < $chunkSize) {
                break;
            }
        }
    }
}
