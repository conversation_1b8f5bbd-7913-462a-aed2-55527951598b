<?php

declare(strict_types=1);

namespace App\Enums;

use Stripe\StripeObject;

enum Currency: string
{
    case EURO = 'EUR';
    case GBP = 'GBP';

    public static function fromStripeObject(StripeObject $object): self
    {
        if (!isset($object['currency'])) {
            throw new \InvalidArgumentException('StripeCurrency object ['.$object::class.'] has no property currency.');
        }

        return self::from(mb_strtoupper($object['currency']));
    }
}
