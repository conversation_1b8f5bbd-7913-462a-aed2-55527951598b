<?php

declare(strict_types=1);

namespace App\Enums;

enum Mood: string
{
    case Smile = 'smile';
    case Meh = 'meh';
    case Frown = 'frown';

    public static function getDefault(): static
    {
        return static::Smile;
    }

    /**
     * @return array<mixed, mixed>
     */
    public static function toArray(): array
    {
        $result = [];

        foreach (static::cases() as $mood) {
            $result[$mood->name] = $mood->name;
        }

        return $result;
    }

    public static function fromInt(string|int|null $mood): static
    {
        if (\is_string($mood)) {
            $mood = (int) $mood;
        }

        if ($mood === null) {
            $mood = 1;
        }

        return match (true) {
            $mood < 0 => static::Frown,
            $mood > 0 => static::Smile,
            default => static::Meh,
        };
    }

    public static function fromName(string $name): self
    {
        $enum = self::tryFromName($name);

        if ($enum === null) {
            throw new \ValueError("$name is not a part of ".self::class);
        }

        return $enum;
    }

    public static function tryFromName(string $name): ?self
    {
        $reflection = new \ReflectionEnum(self::class);

        try {
            /** @var self $enum */
            $enum = $reflection->getCase($name)->getValue();

            return $enum;
        } catch (\ReflectionException) {
            return null;
        }
    }

    public function getInt(): int
    {
        return match ($this) {
            static::Smile => 1,
            static::Meh => 0,
            static::Frown => -1,
        };
    }

    /**
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     *
     * @deprecated use blade component instead: <x-admin.mood-smiley :mood="..." />
     */
    public function getSmiley(bool $light = true): string
    {
        return view(
            'admin.reservation.mood-smiley',
            [
                'mood' => $this->value,
                'light' => $light,
                'moodExplanation' => $this->getExplanation(),
            ],
        )
            ->render();
    }

    public function getExplanation(): string
    {
        return match ($this) {
            static::Smile => 'toutes les réservations ont un good mood',
            static::Meh => 'au moins une réservation a un mood moyen',
            static::Frown => 'au moins une réservation a un bad mood',
        };
    }
}
