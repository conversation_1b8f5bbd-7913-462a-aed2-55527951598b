<?php

declare(strict_types=1);

namespace App\Enums\Payment;

enum PaymentMethod: string
{
    case Card = 'card';
    case CustomerBalance = 'customer_balance';
    case Ideal = 'ideal';
    case Bancontact = 'bancontact';
    case Klarna = 'klarna';

    public function slug(): string
    {
        return match ($this) {
            self::Card => 'stripe_card',
            self::Ideal => 'stripe_ideal',
            self::Bancontact => 'stripe_bancontact',
            self::CustomerBalance => 'stripe_bank_transfer',
            self::Klarna => 'stripe_klarna',
        };
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Card => 'Carte bancaire',
            self::CustomerBalance => 'Virement bancaire',
            self::Ideal => 'Ideal',
            self::Bancontact => 'Bancontact',
            self::Klarna => 'Klarna',
        };
    }
}
