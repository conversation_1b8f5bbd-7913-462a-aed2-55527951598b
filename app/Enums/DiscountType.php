<?php

declare(strict_types=1);

namespace App\Enums;

enum DiscountType: string
{
    case Percent = 'percent';
    case Amount = 'amount';
    case Workshop = 'workshop';

    public static function fromMixedName(string $value): self
    {
        return match ($value) {
            'pourcentage' => self::Percent,
            'ticket' => self::Workshop,
            'montant',
            'value' => self::Amount,
            default => self::from($value),
        };
    }
}
