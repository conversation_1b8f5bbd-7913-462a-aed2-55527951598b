<?php

declare(strict_types=1);

namespace App\Exports;

use App\Domain\Booking\Enums\BookingStatus;
use App\Models\Evenement;
use App\Models\Reservation;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

/**
 * @implements WithMapping<Reservation>
 */
class ParticipantsExport implements FromQuery, WithMapping, WithHeadings, WithStrictNullComparison
{
    use Exportable;

    public function __construct(protected Evenement $event)
    {
    }

    /** @return EloquentBuilder<Reservation>|Relation<Reservation> */
    public function query(): Builder|EloquentBuilder|Relation
    {
        return $this->event->reservations()->whereIn('status', [BookingStatus::BOOKING_CONFIRMED, BookingStatus::BOOKING_BILLED]);
    }

    /** @return string[] */
    public function headings(): array
    {
        return [
            __('exports.artisan.attendees.id_booking'),
            __('exports.artisan.attendees.workshop_name'),
            __('exports.artisan.attendees.lastname'),
            __('exports.artisan.attendees.firstname'),
            __('exports.artisan.attendees.phone'),
            __('exports.artisan.attendees.email'),
            __('exports.artisan.attendees.available_slots'),
            __('exports.artisan.attendees.booking_date'),
            __('exports.artisan.attendees.event_date'),
            __('exports.artisan.attendees.booking_status'),
        ];
    }

    /**
     * @param Reservation $row
     *
     * @return mixed[]
     */
    public function map($row): array
    {
        return [
            $row->id,
            $this->event->atelier->nom,
            $row->getParticipantLastname(),
            $row->getParticipantFirstname(),
            $row->getParticipantPhone(),
            $row->getParticipantEmail(),
            $row->nb_places,
            dateShortFormat($row->created_at),
            dateShortFormat($this->event->start),
            BookingStatus::getLabel($row->status),
        ];
    }
}
