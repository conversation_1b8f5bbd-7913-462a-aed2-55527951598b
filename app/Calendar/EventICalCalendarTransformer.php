<?php

declare(strict_types=1);

namespace App\Calendar;

use App\Models\Evenement as Event;
use Illuminate\Support\Collection;

class EventICalCalendarTransformer
{
    /**
     * Transforms the given array.
     *
     * @return array{name: string, start: mixed, end: mixed, address: mixed, address_name: mixed, lat: mixed, lng: mixed, description: string, participants: array<int, array{email: string, name: string}>}
     */
    public function transform(Event $event): array
    {
        $array = [
            'participants' => [],
        ];
        $eventName = $event->atelier->short_title.' - '.$event->countParticipants().' participant(s)';
        $lieu = $event->getLieu();
        $description = '';

        foreach ($event->confirmedReservations as $reservation) {
            $slotsCount = trans_choice('utils.booking.slots_with_count', $reservation->nb_places, ['count' => $reservation->nb_places]);
            $userName = $reservation->getParticipantFullname();
            $phoneNumber = $reservation->displayTelephone();
            $emailAddress = $reservation->getParticipantEmail();
            $description .= $slotsCount.' - '.$userName.' - '.$phoneNumber;

            if (\is_string($emailAddress)) {
                $description .= ' - '.$emailAddress;

                $array['participants'][] = [
                    'email' => $emailAddress,
                    'name' => $userName,
                ];
            }

            $description .= "\n";
        }

        $array['name'] = $eventName;
        $array['start'] = $event->start;
        $array['end'] = $event->end;
        $array['address'] = $lieu?->getFullAddress();
        $array['address_name'] = $lieu?->lieu_nom;
        $array['lat'] = $lieu?->lat;
        $array['lng'] = $lieu?->lng;
        $array['description'] = $description;

        return $array;
    }

    /**
     * @param Collection<int, Event> $events
     *
     * @return array<int, array{name: string, start: mixed, end: mixed, address: mixed, address_name: mixed, lat: mixed, lng: mixed, description: string, participants: array<int, array{email: string, name: string}>}>
     */
    public function transformCollection(Collection $events): array
    {
        $transformedEvents = [];
        foreach ($events as $event) {
            $transformedEvents[] = $this->transform($event);
        }

        return $transformedEvents;
    }
}
