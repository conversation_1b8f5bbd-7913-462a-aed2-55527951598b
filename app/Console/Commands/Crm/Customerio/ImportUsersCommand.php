<?php

declare(strict_types=1);

namespace App\Console\Commands\Crm\Customerio;

use App\Console\Command;
use App\Enums\Locale;
use App\Enums\Order\OrderStatus;
use App\Infrastructure\Crm\Customerio\CustomerioModelTransformer;
use App\Infrastructure\Crm\Customerio\Handlers\CustomerioInterface;
use App\Infrastructure\Crm\GlobalTrackerService;
use App\Models\Commande;
use App\Models\Reservation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

/**
 * @SuppressWarnings(PHPMD)
 */
final class ImportUsersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customerio:import-users
                                {name : The name of the import, you may reuse a name}
                                {--email=* : The user to import by email}
                                {--fileName= : The name of the file to import users from}
                                {--disk=private : The name of the disk the file is retrieved from}
                                {--withEvents : Import events linked to users}';

    /**
     * The console command description.
     *
     * @var string|null
     */
    protected $description = 'Import user(s) in customer.io';

    /**
     * Execute the console command.
     */
    public function handle(CustomerioInterface $customerIo): int
    {
        $importName = $this->argument('name');
        /** @var array<int, string> $emails */
        $emails = $this->option('email');
        $fileName = $this->option('fileName');
        $withEvents = $this->option('withEvents');

        if (!empty($emails)) {
            $this->components->info('Importing emails from given list');
            $this->processBatch($customerIo, $importName, $withEvents, $emails);
        } elseif ($fileName !== null) {
            $this->handleImportFromFile($customerIo, $importName, $withEvents, $fileName);
        } else {
            $this->components->error('Please provide either a list of email or a file to import');
        }

        $this->components->info('Successfully finished importing users');

        return self::SUCCESS;
    }

    private function addCustomerEvents(
        CustomerioInterface $customerIo,
        User $user,
    ): void {
        // Import user orders
        $user
            ->commandes
            ->where('status', OrderStatus::Paid)
            ->each(function (Commande $order): void {
                GlobalTrackerService::trackOrder($order);
            });

        // Import user bookings
        $user->reservations->each(function (Reservation $booking) use ($customerIo, $user): void {
            if ($booking->estValide() && $booking->evenement->start < Carbon::today()->startOfDay()) {
                // We set the customer.io timestamp to the same timestamp that if the event had been triggered by the WorkshopParticipants job (daily at 5h30)
                $timestamp = $booking->evenement->start->copy()->addDay()->setTime(5, 30)->getTimestamp();

                $customerIo->trackCustomerEvent(
                    $user,
                    CustomerioInterface::ATTENDED_A_WORKSHOP,
                    CustomerioModelTransformer::transformEventParticipationData(
                        $booking->evenement,
                        $booking,
                        Locale::from($user->preferredLocale())
                    ),
                    $timestamp
                );
            }
        });
    }

    /**
     * @param array<int, string> $emails
     */
    private function processBatch(
        CustomerioInterface $customerIo,
        string $importName,
        bool $withEvents,
        array $emails
    ): void {
        User::query()
            ->with([
                'codeparrainageuser',
                'codeparrainageinvitation',
                'adresses',
            ])
            ->whereIn('email', $emails)
            ->eachById(function (User $user) use ($customerIo, $importName, $withEvents): void {
                try {
                    $customerIo->addOrUpdateCustomer($user, [
                        'import_name' => $importName,
                    ]);
                    if ($withEvents) {
                        $this->addCustomerEvents($customerIo, $user);
                    }

                    $this->info("User [$user->email] successfully imported");
                } catch (\Exception $exception) {
                    $this->error("An error occurred for [$user->email]: ".$exception->getMessage());

                    \Log::error('Error occurred while importing user to customer io', [
                        'email' => $user->email,
                        'exception' => $exception,
                    ]);
                }
            }, 200);
    }

    private function handleImportFromFile(CustomerioInterface $customerIo, string $importName, bool $withEvents, string $fileName): void
    {
        $disk = Storage::disk($this->option('disk'));

        if (!$disk->exists($fileName)) {
            $this->components->error('The file does not exist.');

            return;
        }

        $handle = $disk->readStream($fileName);
        if ($handle === null) {
            $this->components->error('Failed to open file stream.');

            return;
        }

        $batchSize = 500;
        $emails = [];
        $lineCount = 0;

        while (($line = fgets($handle)) !== false) {
            // We expect that each line is an email
            $emails[] = mb_trim($line);
            ++$lineCount;

            if ($lineCount % $batchSize === 0) {
                $this->processBatch($customerIo, $importName, $withEvents, $emails);
                $emails = []; // Reset batch
            }
        }

        // Process the last batch if it has records and didn't reach the batch size limit
        if (!empty($emails)) {
            $this->processBatch($customerIo, $importName, $withEvents, $emails);
        }

        fclose($handle);

        $this->info("Imported $lineCount users from file.");
    }
}
