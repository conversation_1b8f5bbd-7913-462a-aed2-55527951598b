<?php

declare(strict_types=1);

namespace App\Console\Commands\Crm\Customerio;

use App\Console\Command;
use App\Infrastructure\Crm\Customerio\CustomerioModelTransformer;
use App\Infrastructure\Crm\Customerio\Exceptions\CustomerIoException;
use App\Infrastructure\Crm\Customerio\Handlers\CustomerioInterface;
use App\Models\Artisan;
use Illuminate\Database\Eloquent\Collection;

final class ImportArtisans extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'customerio:import-artisans {collectionName : Collection name in customer.io}';

    /**
     * The console command description.
     */
    protected $description = 'Import artisans in customer.io';

    public function __construct(private readonly CustomerioInterface $customerIo)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->customerIo->isApiDriver() === false) {
            $this->info('C.io is not enabled');

            return self::FAILURE;
        }

        $collectionName = $this->argument('collectionName');

        $collectionIdToUpdate = null;

        $allCollections = $this->customerIo->allCollections();
        foreach ($allCollections as $collection) {
            if (!isset($collection['name'], $collection['id'])) {
                continue;
            }

            if ($collection['name'] === $collectionName) {
                $collectionIdToUpdate = $collection['id'];
                break;
            }
        }

        $artisans = Artisan::where('active', '=', true)
            ->get();

        try {
            $this->addOrUpdateArtisanCollection($collectionName, $artisans, $collectionIdToUpdate);
        } catch (CustomerIoException $e) {
            $this->error('Une erreur s\'est produite : "'.$e->getMessage().'"');

            return self::FAILURE;
        }

        $this->info(\count($artisans).' artisan(s) importé(s)');

        return self::SUCCESS;
    }

    /**
     * @param Collection<int, Artisan> $artisans
     *
     * @throws CustomerIoException
     */
    private function addOrUpdateArtisanCollection(string $collectionName, Collection $artisans, ?int $collectionIdToUpdate): void
    {
        $data = [];
        foreach ($artisans as $artisan) {
            $data[] = CustomerioModelTransformer::transformArtisanData($artisan);
        }

        if (null !== $collectionIdToUpdate) {
            $this->customerIo->updateCollection($collectionIdToUpdate, $data);

            return;
        }

        $this->customerIo->addCollection($collectionName, $data);
    }
}
