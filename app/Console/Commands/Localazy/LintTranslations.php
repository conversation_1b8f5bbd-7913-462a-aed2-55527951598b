<?php

declare(strict_types=1);

namespace App\Console\Commands\Localazy;

use App\Console\Command;
use App\Console\Commands\Localazy\Helper\LocalazyHelper;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;
use Symfony\Component\Process\Process;

final class LintTranslations extends Command
{
    protected $signature = 'translations:lint
                            {--source-path= : the file or folder to lint from}
                            {--show-diff : display the diff when linting}
                            {--show-stats : display the number of existing keys}
                            {--show-files : display the files names}
                            {--fix : fix files instead of displaying errors}';

    protected $description = 'Lint and optionally fix all translation files of a wanted directory.';

    /**
     * @SuppressWarnings(PHPMD)
     */
    public function handle(): int
    {
        $sourcePath = mb_rtrim((string) $this->option('source-path'), '/');
        $fix = $this->option('fix');
        $showDiff = $this->option('show-diff');
        $showStats = $this->option('show-stats');
        $showFiles = $this->option('show-files');

        if (is_dir($sourcePath)) {
            $files = (new Finder())->files()->in($sourcePath)->sortByName();
        } elseif (file_exists($sourcePath)) {
            $files = [new SplFileInfo((string) realpath($sourcePath), '', basename($sourcePath))];
        } else {
            throw new \InvalidArgumentException("Unable to find the source path: $sourcePath");
        }

        if (\count($files) === 0) {
            $this->output->warning('Unable to find any file to convert');

            return self::INVALID;
        }

        $errors = 0;
        $filesCount = \count($files);
        $allTranslations = [];

        foreach ($files as $file) {
            if ($showFiles) {
                $this->output->writeln("Parsing {$file->getRelativePathname()}");
            }

            try {
                $rawSource = file_get_contents($file->getRealPath());
                $source = LocalazyHelper::sortArrayByKeys(LocalazyHelper::getFileContent($file));
            } catch (\Throwable $e) {
                $this->output->error("Unable to parse the file {$file->getRelativePathname()}: {$e->getMessage()}");

                return self::FAILURE;
            }

            if ($showStats) {
                $allTranslations[$file->getRelativePathname()] = LocalazyHelper::flattenArray($source);
            }

            $formattedSource = LocalazyHelper::generateContent($source, $file->getExtension());

            if ($rawSource === $formattedSource) {
                continue;
            }

            $this->output->writeln("<comment>Invalid file: {$file->getRelativePathname()}</comment>");
            ++$errors;

            if ($showDiff) {
                $tmpFile = tmpfile();
                if (!\is_resource($tmpFile)) {
                    throw new \Exception('Unable to create temp file.');
                }
                $tmpFileName = stream_get_meta_data($tmpFile)['uri'];

                fwrite($tmpFile, $formattedSource);

                $process = new Process(['diff', $file->getRealPath(), $tmpFileName]); // Using linux diff tool, as it's easier than writing it in php
                $process->run();

                if (mb_strlen($process->getErrorOutput()) > 0) {
                    $this->output->error($process->getErrorOutput());
                }

                if (mb_strlen($process->getOutput()) > 0) {
                    $this->output->writeln($process->getOutput());
                }

                fclose($tmpFile);
            }

            if ($fix) {
                try {
                    file_put_contents($file->getRealPath(), $formattedSource);
                } catch (\Throwable $e) {
                    $this->output->error("Error when writing the new file content: {$e->getMessage()}");
                    continue;
                }
            }
        }

        if ($showStats) {
            $keysCount = \count($allTranslations, \COUNT_RECURSIVE) - \count($allTranslations);
            $duplicatedKeys = $keysCount - \count(array_unique(array_merge(...array_values(array_map('array_keys', $allTranslations)))));
            $duplicatedTranslations = $keysCount - \count(array_unique(array_merge(...array_values(array_map('array_values', $allTranslations)))));
            $this->output->writeln("<info>\nStatistics\n==========\nFiles: $filesCount\nKeys: $keysCount\nDuplicated keys: $duplicatedKeys\nDuplicated translations: $duplicatedTranslations</info>");
        }

        if ($errors === 0) {
            $this->output->success("All $filesCount files are okay :)");

            return self::SUCCESS;
        }

        if ($fix) {
            $this->output->success("$errors / $filesCount files have been linted with success.");

            return self::SUCCESS;
        }

        $this->output->error("$errors / $filesCount files are not valid");

        return self::FAILURE;
    }
}
