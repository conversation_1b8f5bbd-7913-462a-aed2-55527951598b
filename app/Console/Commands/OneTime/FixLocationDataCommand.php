<?php

declare(strict_types=1);

namespace App\Console\Commands\OneTime;

use App\Console\Command;
use App\Models\Atelier;
use App\Models\Evenement;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\DB;

use function Laravel\Prompts\alert;
use function Laravel\Prompts\confirm;
use function Laravel\Prompts\progress;
use function Laravel\Prompts\spin;
use function Laravel\Prompts\text;

class FixLocationDataCommand extends Command
{
    protected $signature = 'data:fix-location';

    public function handle(): int
    {
        $workshopId = text(
            label: 'enter workshop Id',
            required: true,
        );

        /** @var Atelier $workshop */
        $workshop = Atelier::findOrFail($workshopId);

        $confirmed = confirm(
            label: 'do you want to update this workshop: '.$workshop->nom,
            default: false,
            yes: 'Yes',
            no: 'No',
        );

        if (!$confirmed) {
            return self::FAILURE;
        }

        $inputDate = text(
            label: 'date from which the location will change?',
            placeholder: '2025-02-19',
            validate: 'required|date_format:Y-m-d',
        );

        $deadline = CarbonImmutable::parse($inputDate);

        $confirmed = confirm(
            label: 'All events before this date will be updated with current workshop location: '.$workshop->lieu_id.'; continue? ',
            default: false,
            yes: 'Yes',
            no: 'No',
        );
        if (!$confirmed) {
            return self::FAILURE;
        }

        $events = Evenement::query()
            ->where('atelier_id', $workshop->id)
            ->where('start', '<', $deadline)
            ->whereNull('lieu_id')
            ->whereNull('cancelled_at')
            ->get();

        DB::beginTransaction();
        if ($events->count() > 0) {
            $updatedEvents = progress(
                label: 'Updating events location',
                steps: $events,
                callback: fn ($event) => $this->updateEvent($event, $workshop)
            );
            info('Updated '.\count($updatedEvents).' event(s)');
        }

        $response = spin(
            callback: fn () => $this->updateWorkshopLocation($workshop),
            message: 'Updating workshop location...'
        );

        if (!$response) {
            DB::rollBack();
            alert("Can't found artisan main location, all changes reverted, check the date");

            return self::FAILURE;
        }

        DB::commit();

        return self::SUCCESS;
    }

    private function updateEvent(Evenement $event, Atelier $workshop): void
    {
        $event->lieu_id = $workshop->lieu_id;
        $event->save();
    }

    private function updateWorkshopLocation(Atelier $workshop): bool
    {
        $artisanLocationId = $workshop->artisan->mainLocation()->getKey();
        if (empty($artisanLocationId)) {
            return false;
        }
        $workshop->lieu_id = $artisanLocationId;
        $workshop->save();

        return true;
    }
}
