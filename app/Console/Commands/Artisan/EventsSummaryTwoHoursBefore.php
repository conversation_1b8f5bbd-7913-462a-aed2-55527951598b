<?php

declare(strict_types=1);

namespace App\Console\Commands\Artisan;

use App\Console\Command;
use App\Console\Interfaces\CanBeIgnored;
use App\Models\Evenement;
use App\Notifications\Artisan\EventReminderTodayNotification;
use Carbon\Carbon;

class EventsSummaryTwoHoursBefore extends Command implements CanBeIgnored
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:two-hours-before';

    /**
     * The console command description.
     *
     * @var ?string
     */
    protected $description = 'Send participant summary two hours before events starts.';

    public function handle(): int
    {
        $start = Carbon::now()->addHour();
        $end = Carbon::now()->addHours(2);

        $events = Evenement::query()
            ->where('start', '>', $start)
            ->where('start', '<=', $end)
            ->whereNull('cancelled_at')
            ->with(['atelier.artisan'])
            ->lazy(100);

        $count = 0;

        foreach ($events as $event) {
            /** @var Evenement $event */
            if ($event->countValidBookingsNotArtisan() > 0) {
                ++$count;
                try {
                    $event->atelier->artisan->notify(new EventReminderTodayNotification($event));
                } catch (\Throwable $exception) {
                    --$count;
                    \Log::error('An error occurred while trying to send notification', [
                        'notifiable' => $event->atelier->artisan_id,
                        'notification' => EventReminderTodayNotification::class,
                        'exception' => $exception,
                    ]);
                }
            }
        }

        $this->components->info("Sent $count notifications");

        return self::SUCCESS;
    }
}
