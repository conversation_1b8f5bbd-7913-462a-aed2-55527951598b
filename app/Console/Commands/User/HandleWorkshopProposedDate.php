<?php

declare(strict_types=1);

namespace App\Console\Commands\User;

use App\Console\Command;
use App\Enums\DateProposition\DatePropositionStatus;
use App\Enums\Forms\FormStatus;
use App\Mail\User\Autre\WorkshopProposedDateDeniedMail;
use App\Models\Formulaires\ContactAutreDateAtelier;
use App\Models\PropositionDate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class HandleWorkshopProposedDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workshop:handle-proposed-dates';

    /**
     * The console command description.
     *
     * @var string|null
     */
    protected $description = 'Expire proposed dates where the date has passed and notify users for denied proposed dates';

    public function handle(): int
    {
        $expiredCount = 0;
        $deniedRequests = 0;

        // Expire all dates that have passed their date
        PropositionDate::query()
            ->where('status', DatePropositionStatus::Pending)
            ->whereDate('start_date', '<', Carbon::now())
            ->eachById(function (PropositionDate $proposedDate) use (&$expiredCount): void {
                $proposedDate->status = DatePropositionStatus::Expired;
                $proposedDate->save();
                ++$expiredCount;

                activity('propositiondate_log')
                    ->performedOn($proposedDate)
                    ->log('Péremption de la proposition de date - Action automatique');
            });

        ContactAutreDateAtelier::query()
            ->where('status', FormStatus::ToProcess->value)
            ->eachById(function (ContactAutreDateAtelier $contactAutreDateAtelier) use (&$deniedRequests): void {
                if ($contactAutreDateAtelier->hasApprovedDates()) {
                    $contactAutreDateAtelier->status = FormStatus::Processed;
                    $contactAutreDateAtelier->save();

                    return;
                }
                if (!$contactAutreDateAtelier->hasPendingDates()) {
                    $contactAutreDateAtelier->status = FormStatus::Processed;
                    $contactAutreDateAtelier->save();
                    Mail::to($contactAutreDateAtelier->email)->send(
                        new WorkshopProposedDateDeniedMail($contactAutreDateAtelier)
                    );
                    ++$deniedRequests;
                }
            });

        $this->components->info("[$expiredCount] proposed dates have been expired and [$deniedRequests] requests have been denied");

        return self::SUCCESS;
    }
}
