<?php

declare(strict_types=1);

namespace App\Console\Commands\Workshop;

use App\Console\Command;
use App\Models\Tag;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateWorkshopTags extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'workshop-tags:update';

    /**
     * The console command description.
     *
     * @var ?string
     */
    protected $description = 'Update listes des ateliers associés à certains tags';

    private ?Builder $workshopsSource = null;

    private int $workshopsImpacted = 0;

    /**
     * @SuppressWarnings(PHPMD)
     */
    public function handle(): int
    {
        $toUpdate = [
            'nouveau' => [
                'query' => DB::table('ateliers')
                    ->where('online_at', '!=', null)
                    ->where('online_at', '>', Carbon::now()->subMonth()),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'en-ligne' => [
                'query' => DB::table('ateliers')->where('is_online', true),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'polyvalent' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'noel' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'cadeau' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'experience' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'made-in-france' => [
                'query' => $this->getWorkshopsSource()
                    ->where('lieu_id', '<>', 809) // Atelier Wecandoo PARIS
                ,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'personnalise' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'elle' => [
                'query' => $this->getWorkshopsSource(),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'maman' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [20, 21, 22, 24, 25, 26, 27, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118],
            ],
            'fete-des-meres' => [
                'query' => $this->getWorkshopsSource()
                    ->where('lieu_id', '<>', 809) // Atelier Wecandoo PARIS
                ,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'papa' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where('craft_id', '!=', 2),
                'workshops' => [1, 504, 517, 602, 1025, 1026, 2466, 2485, 1486, 1551, 1552, 3217, 3218, 66, 157, 304, 425, 977, 1463, 1796, 2230, 2960, 168, 312, 432, 718, 804, 819, 975, 1058, 2353, 121, 1009, 3294, 1171, 1169, 2233, 2906, 1170, 48, 1743, 1744, 1745, 2124, 2125, 2134, 97, 79, 1582, 1737, 1921, 2029, 2451, 546, 2535, 381, 525, 192, 267, 406, 760, 1180, 1307, 1516, 1580, 1651, 1705, 1745, 1780, 1922, 2134, 2137, 2164, 2272, 2672, 238, 404, 780, 821, 1306, 1324, 1327, 1510, 2155, 1499, 1743, 1958, 2028, 2526, 670, 1306, 2534, 987, 547, 1126, 623, 511, 1844, 2219, 2273, 1497, 2156, 2554, 1779, 1520],
                'crafts' => [20, 21, 22, 24, 25, 30, 31, 32, 33, 36, 38, 39, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 57, 58, 59, 60, 63, 65, 66, 67, 68, 69, 70, 72, 73, 74, 75, 76, 77, 78, 79, 84, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 115, 116],
            ],
            'lui' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [48, 1743, 381, 525, 3326, 546, 3153, 4, 79, 265, 283, 405, 526, 781, 821, 1006, 1270, 1398, 1432, 1510, 1581, 1582, 1647, 1704, 1781, 1886, 1921, 2029, 2030, 2165, 2407, 2451, 2968, 3035, 3284, 3285, 3372, 3443, 3671, 3691, 3793, 3940, 3944, 4318, 511, 1844, 2219, 623, 2969, 1083, 2991, 3457, 628, 670, 3328, 1779, 1499, 987, 1960, 3957, 1497, 3956, 547, 1126, 2554, 3152, 3619, 3620, 4228, 4274, 2534, 2156, 100, 1, 66, 157, 304, 425, 977, 1463, 1796, 2230, 2792, 2960, 3798, 1169, 504, 517, 602, 1025, 1026, 2485, 3427, 3428, 3769, 3832, 4131, 575, 2235, 2615, 459, 550, 1374, 2422, 3900, 4313, 3294, 2233, 3748, 3970, 2574, 2865, 1009, 3450, 2238, 1486, 1551, 1552, 3218, 3219, 3220, 3221, 3365, 455, 539, 604, 643, 1192, 1243, 1375, 1715, 3449, 3556, 2295, 3196, 3016, 312, 1058, 3451, 168, 432, 804, 819, 3690],
                'crafts' => [20, 21, 24, 29, 30, 31, 32, 33, 35, 36, 38, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 39, 56, 57, 59, 60, 65, 66, 67, 72, 73, 74, 75, 76, 77, 78, 79, 96, 97, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 116, 117, 118, 85, 86, 87, 88, 89, 90],
            ],
            'copain' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where('prix', '>=', 50),
                'workshops' => [],
                'crafts' => [25, 30, 33, 36, 39, 41, 44, 46, 47, 49, 50, 51, 52, 66],
            ],
            'copine' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where('prix', '>=', 50),
                'workshops' => [],
                'crafts' => [20, 21, 26, 27, 28, 34, 35, 36, 37, 38, 40, 41, 42, 43, 54, 55, 46, 57, 65],
            ],
            'parents' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where(function ($query): void {
                    $query->where('ateliers.nom', 'LIKE', '%famille%')
                    ->orWhere('ateliers.nom', 'LIKE', '%parent%')
                    ->orWhere('ateliers.sous_titre', 'LIKE', '%famille%')
                    ->orWhere('ateliers.sous_titre', 'LIKE', '%parent%')
                    ->orWhere('ateliers.deroulement_atelier', 'LIKE', '%famille%')
                    ->orWhere('ateliers.deroulement_atelier', 'LIKE', '%parent%');
                }),
                'workshops' => [],
                'crafts' => [15, 20, 21, 22, 24, 29, 32, 41, 44, 46, 47, 48, 49, 50, 51, 69],
            ],
            'famille' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where(function ($query): void {
                    $query->where('ateliers.nom', 'LIKE', '%famille%')
                        ->orWhere('ateliers.nom', 'LIKE', '%enfant%')
                        ->orWhere('ateliers.sous_titre', 'LIKE', '%famille%')
                        ->orWhere('ateliers.sous_titre', 'LIKE', '%enfant%')
                        ->orWhere('ateliers.deroulement_atelier', 'LIKE', '%famille%')
                        ->orWhere('ateliers.deroulement_atelier', 'LIKE', '%enfant%');
                }),
                'workshops' => [],
                'crafts' => [15, 20, 21, 22, 24, 32, 34, 47, 48, 49, 69],
            ],
            'original' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [16, 30, 32, 33, 36, 37, 43, 48, 50, 51, 52, 58, 60, 61, 62, 63, 64, 66, 67],
            ],
            'fait-main' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [20, 21, 25, 26, 27, 28, 29, 30, 32, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 49, 51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 69],
            ],
            'diy' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [26, 27, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 57, 69],
            ],
            'gourmand' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [24, 44, 45, 46, 47, 48, 49, 50, 51],
            ],
            'bricolage' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [25, 31, 32, 52, 53, 66, 67],
            ],
            'createur' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [1, 13, 14, 16, 4],
            ],
            'style' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [6, 3, 17],
            ],
            'epicurien' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [18],
            ],
            'kid' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [15],
            ],
            'fete-des-peres' => [
                'query' => null,
                'craftsQuery' => $this->getWorkshopsSource()->where('craft_id', '!=', 2),
                'workshops' => [1, 48, 100, 246, 381, 425, 511, 623, 1170, 1229, 1284, 1306, 1499, 1743, 1744, 1745, 1844, 2028, 2032, 2124, 2125, 2134, 2233, 2534, 2571, 3294, 3748, 4208, 4348, 4399, 4547, 4831, 4983, 5059, 5078, 5464, 5469, 5568, 5697],
                'crafts' => [22, 24, 30, 31, 32, 33, 36, 38, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 57, 59, 60, 65, 66, 67, 68, 70, 73, 74, 75, 76, 77, 78, 79, 85, 86, 87, 88, 89, 90, 97, 98, 99, 100, 101, 102, 103],
            ],
            'rentree-nature' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [70, 68],
            ],
            'enfants-et-famille' => [
                'query' => $this->getWorkshopsSource()->whereIn('format_id', [3, 4, 5]),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'exterieur' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [462, 580, 74, 1568, 1650, 1649, 1072, 1565, 1567, 901, 37, 1203, 1761, 1754, 1810, 442, 1816, 1483, 1729, 1730, 2016, 2034, 2033, 2062, 2035, 2108, 2109, 2414, 2441, 2497, 2498, 936, 1202, 1484, 2853, 2820, 2821, 2892, 2915, 2944, 3053, 3050, 3051, 2940, 2941, 3012, 2885, 2884, 3254, 3255, 3256, 3257, 3258, 3259, 2942, 3350, 3098, 3484, 3487, 3116, 3468, 2998, 3099, 3100, 3101, 3627, 3634, 3665, 3666, 3349, 3615, 3343, 3723, 3722, 3724, 3776, 3775, 3802, 3235, 3234, 3979, 3946, 3675, 3676, 3381, 3382, 3992, 3993, 3976, 4141, 4176, 4177, 4178, 4179, 3922, 3923, 3924, 4043, 4377, 4249, 4250, 4251, 4681, 4142, 4143, 3986, 3987, 5045, 5091, 5165, 5158, 5159, 4852, 4853, 4854, 5424, 5217, 5218, 5467, 5118, 5495, 5119, 5120, 5537, 5862, 5937, 5934, 6035, 6045, 5821, 5822, 5823, 6195, 6197, 6093, 6094, 6138, 5670, 5671, 5763, 6306, 6233, 6234, 6431, 6432, 6307, 6238, 6239, 6503, 6504, 6508, 5851, 6449, 6450, 5933, 6181, 6545, 6584, 6583, 5932, 6643, 6401, 5644, 5645, 5646, 2031, 2067, 2014, 2177, 2027, 1931, 1639, 1964, 2176, 2276, 2062, 2108, 1892, 2274, 2410, 2450, 2494, 1852, 2448, 1485, 2481, 2482, 2908, 2914, 2915, 2935, 2936, 2986, 3008, 3039, 2857, 2909, 3052, 2847, 2961, 2962, 3116, 3115, 3244, 3245, 3145, 3170, 3061, 3097, 3089, 3090, 3091, 3146, 3112, 3175, 3370, 3098, 3293, 3127, 3139, 3126, 3117, 3102, 3243, 3241, 3240, 3604, 3603, 3602, 3732, 3701, 3248, 3249, 3760, 3673, 3758, 3759, 3235, 3745, 3746, 3075, 3946, 3947, 3948, 4071, 4003, 4218, 4029, 4030, 4166, 4167, 4123, 4124, 4055, 4068, 4069, 4070, 4056, 4057, 4371, 4511, 3942, 3943, 4579, 4144, 4656, 4540, 4243, 4735, 4875, 4874, 4878, 4624, 4625, 4321, 4322, 4801, 4881, 5027, 4734, 4847, 5033, 5034, 5029, 5148, 5008, 3852, 5067, 5537, 5398, 5449, 5450, 5451, 2592, 5658, 5704, 5396, 5782, 5937, 6035, 5821, 5822, 5823, 5935, 5936, 5813, 5763, 5739, 5741, 6346, 5311, 6240, 6238, 6239, 6379, 5899, 6445, 6446, 5851, 6449, 6484, 5644, 5645, 5646],
                'crafts' => [],
            ],
            'terroir' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [2031, 2067, 2014, 2177, 2027, 1931, 1639, 1964, 2176, 2276, 2062, 2108, 1892, 2274, 2410, 2450, 2494, 1852, 2448, 1485, 2481, 2482, 2908, 2914, 2915, 2935, 2936, 2986, 3008, 3039, 2857, 2909, 3052, 2847, 2961, 2962, 3116, 3115, 3244, 3245, 3145, 3170, 3061, 3097, 3089, 3090, 3091, 3146, 3112, 3175, 3370, 3098, 3293, 3127, 3139, 3126, 3117, 3102, 3243, 3241, 3240, 3604, 3603, 3602, 3732, 3701, 3248, 3249, 3760, 3673, 3758, 3759, 3235, 3745, 3746, 3075, 3946, 3947, 3948, 4071, 4003, 4218, 4029, 4030, 4166, 4167, 4123, 4124, 4055, 4068, 4069, 4070, 4056, 4057, 4371, 4511, 3942, 3943, 4579, 4144, 4656, 4540, 4243, 4735, 4875, 4874, 4878, 4624, 4625, 4321, 4322, 4801, 4881, 5027, 4734, 4847, 5033, 5034, 5029, 5148, 5008, 3852, 5067, 5537, 5398, 5449, 5450, 5451, 2592, 5658, 5704, 5396, 5782, 5937, 6035, 5821, 5822, 5823, 5935, 5936, 5813, 5763, 5739, 5741, 6346, 5311, 6240, 6238, 6239, 6379, 5899, 6445, 6446, 5851, 6449, 6484, 5644, 5645, 5646],
                'crafts' => [],
            ],
            'tourisme-savoir-faire' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [462, 580, 74, 1568, 1650, 1649, 1072, 1565, 1567, 901, 37, 1203, 1761, 1754, 1810, 442, 1816, 1483, 1729, 1730, 2016, 2034, 2033, 2062, 2035, 2108, 2109, 2414, 2441, 2497, 2498, 936, 1202, 1484, 2853, 2820, 2821, 2892, 2915, 2944, 3053, 3050, 3051, 2940, 2941, 3012, 2885, 2884, 3254, 3255, 3256, 3257, 3258, 3259, 2942, 3350, 3098, 3484, 3487, 3116, 3468, 2998, 3099, 3100, 3101, 3627, 3634, 3665, 3666, 3349, 3615, 3343, 3723, 3722, 3724, 3776, 3775, 3802, 3235, 3234, 3979, 3946, 3675, 3676, 3381, 3382, 3992, 3993, 3976, 4141, 4176, 4177, 4178, 4179, 3922, 3923, 3924, 4043, 4377, 4249, 4250, 4251, 4681, 4142, 4143, 3986, 3987, 5045, 5091, 5165, 5158, 5159, 4852, 4853, 4854, 5424, 5217, 5218, 5467, 5118, 5495, 5119, 5120, 5537, 5862, 5937, 5934, 6035, 6045, 5821, 5822, 5823, 6195, 6197, 6093, 6094, 6138, 5670, 5671, 5763, 6306, 6233, 6234, 6431, 6432, 6307, 6238, 6239, 6503, 6504, 6508, 5851, 6449, 6450, 5933, 6181, 6545, 6584, 6583, 5932, 6643, 6401, 5644, 5645, 5646, 2031, 2067, 2014, 2177, 2027, 1931, 1639, 1964, 2176, 2276, 1892, 2274, 2410, 2450, 2494, 1852, 2448, 1485, 2481, 2482, 2908, 2914, 2935, 2936, 2986, 3008, 3039, 2857, 2909, 3052, 2847, 2961, 2962, 3115, 3244, 3245, 3145, 3170, 3061, 3097, 3089, 3090, 3091, 3146, 3112, 3175, 3370, 3293, 3127, 3139, 3126, 3117, 3102, 3243, 3241, 3240, 3604, 3603, 3602, 3732, 3701, 3248, 3249, 3760, 3673, 3758, 3759, 3745, 3746, 3075, 3947, 3948, 4071, 4003, 4218, 4029, 4030, 4166, 4167, 4123, 4124, 4055, 4068, 4069, 4070, 4056, 4057, 4371, 4511, 3942, 3943, 4579, 4144, 4656, 4540, 4243, 4735, 4875, 4874, 4878, 4624, 4625, 4321, 4322, 4801, 4881, 5027, 4734, 4847, 5033, 5034, 5029, 5148, 5008, 3852, 5067, 5398, 5449, 5450, 5451, 2592, 5658, 5704, 5396, 5782, 5935, 5936, 5813, 5739, 5741, 6346, 5311, 6240, 6379, 5899, 6445, 6446, 6484, 4649, 375, 3500, 366, 1091, 1118, 6100, 5121, 3741, 5633, 5756, 5632, 4720, 3850, 3658, 1616, 1838, 1226, 3471, 4931, 1399, 4307, 1703, 1452, 4306, 946, 1451, 4896, 6186, 6169, 6168, 4739, 4740, 4593, 4749, 1043, 5275, 5276, 5239, 5134, 4148, 4002, 3173, 3095, 2978, 3094, 3096, 3422, 3515, 4897, 3140, 3473, 3474, 4770, 1024, 955, 981, 1135, 732, 4370, 2484, 4242, 1904, 5738, 5427, 6327, 5294, 5425, 5740, 5301, 5032, 5300, 5293, 2251, 5423, 1300, 4427, 4711, 4428, 1482, 5076, 892],
                'crafts' => [],
            ],
            'specialite-regionale' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [2177, 4649, 375, 3500, 366, 1091, 2014, 1118, 6100, 5121, 3741, 5633, 5756, 5632, 4720, 4511, 3850, 3658, 1616, 1483, 5008, 5029, 1838, 1226, 3471, 3370, 4931, 3075, 1399, 3604, 3240, 4307, 1703, 1452, 3603, 4306, 946, 3602, 1451, 4896, 6186, 6169, 6168, 4739, 4740, 4593, 4749, 1043, 5275, 5276, 5239, 5134, 4371, 4029, 4030, 4148, 4002, 4003, 3173, 3089, 3095, 3097, 2978, 3091, 3175, 3094, 3112, 3096, 3422, 5067, 3515, 4897, 2935, 3948, 3701, 3140, 3126, 3473, 3127, 3474, 2035, 4770, 1024, 955, 981, 1135, 732, 4370, 3993, 3992, 2484, 4242, 4243, 1904, 5034, 5738, 5427, 6327, 5294, 5033, 5425, 5740, 5301, 5032, 5300, 5293, 2251, 5423, 6379, 1300, 4427, 4711, 4428, 1482, 5076, 892],
                'crafts' => [],
            ],
            'pas-cher' => [
                'query' => $this->getWorkshopsSource()->where('prix', '<=', 50),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'a-deux' => [
                'query' => $this->getWorkshopsSource()
                    ->where('nb_pers_max', '=', 2),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'saint-valentin' => [
                'query' => $this->getWorkshopsSource()
                    ->where('craft_id', '!=', 15)
                    ->where('nb_pers_max', '>', 1)
                    ->leftJoin('atelier_tag', function ($join): void {
                        $join->on('ateliers.id', '=', 'atelier_tag.atelier_id')
                            ->where('atelier_tag.tag_id', '=', 23);
                    })
                    ->whereNull('atelier_tag.atelier_id'),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'ado' => [
                'query' => $this->getWorkshopsSource()
                    ->whereNotNull('age_min')
                    ->where('age_min', '<', 17)
                    ->where('lieu_id', '<>', 809) // Atelier Wecandoo PARIS
                ,
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
            'detente' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [8, 9, 14, 56, 103, 108, 110, 111, 113, 144, 145, 147, 147, 150, 160, 175, 178, 179, 182, 186, 219, 245, 262, 268, 269, 270, 271, 272, 274, 277, 279, 281, 299, 305, 311, 321, 322, 323, 349, 363, 374, 375, 380, 397, 402, 417, 427, 430, 435, 436, 445, 454, 455, 456, 459, 460, 461, 466, 468, 482, 483, 490, 491, 496, 503, 517, 527, 534, 539, 540, 548, 549, 550, 558, 560, 561, 565, 575, 579, 582, 583, 584, 585, 586, 589, 592, 595, 596, 601, 603, 604, 604, 604, 605, 607, 612, 613, 619, 620, 621, 634, 640, 641, 643, 644, 646, 646, 658, 660, 662, 667, 694, 700, 701, 701, 702, 705, 706, 707, 710, 710, 712, 712, 713, 715, 715, 716, 723, 726, 727, 729, 730, 739, 740, 741, 743, 771, 776, 792, 793, 793, 793, 793, 794, 816, 817, 818, 829, 830, 830, 862, 864, 868, 883, 884, 885, 890, 892, 895, 896, 899, 900, 902, 907, 912, 913, 917, 918, 919, 924, 934, 938, 939, 952, 953, 969, 970, 976, 982, 990, 990, 992, 998, 1000, 1009, 1023, 1027, 1028, 1035, 1053, 1054, 1060, 1076, 1087, 1091, 1092, 1101, 1105, 1106, 1107, 1112, 1136, 1137, 1138, 1140, 1142, 1143, 1170, 1171, 1183, 1189, 1191, 1192, 1198, 1205, 1206, 1207, 1209, 1210, 1211, 1212, 1212, 1231, 1242, 1243, 1244, 1246, 1247, 1248, 1256, 1268, 1269, 1269, 1277, 1277, 1286, 1288, 1289, 1290, 1291, 1294, 1295, 1296, 1298, 1300, 1308, 1309, 1310, 1313, 1314, 1314, 1315, 1316, 1317, 1320, 1320, 1331, 1333, 1334, 1335, 1338, 1339, 1340, 1343, 1352, 1353, 1354, 1354, 1355, 1355, 1357, 1371, 1374, 1375, 1378, 1379, 1380, 1383, 1384, 1385, 1385, 1386, 1412, 1413, 1416, 1417, 1420, 1423, 1423, 1424, 1424, 1425, 1426, 1427, 1427, 1428, 1428, 1431, 1433, 1434, 1441, 1443, 1469, 1473, 1474, 1479, 1486, 1495, 1496, 1498, 1501, 1502, 1528, 1533, 1534, 1551, 1552, 1558, 1578, 1578, 1583, 1585, 1593, 1594, 1597, 1598, 1599, 1600, 1609, 1612, 1613, 1619, 1620, 1643, 1656, 1662, 1663, 1665, 1667, 1676, 1677, 1688, 1689, 1691, 1692, 1696, 1703, 1707, 1710, 1711, 1712, 1717, 1723, 1738, 1739, 1740, 1751, 1761, 1771, 1772, 1772, 1773, 1783, 1783, 1785, 1786, 1787, 1788, 1791, 1798, 1804, 1815, 1831, 1832, 1834, 1836, 1840, 1841, 1853, 1854, 1855, 1865, 1866, 1867, 1873, 1874, 1875, 1879, 1880, 1881, 1883, 1884, 1885, 1885, 1896, 1896, 1897, 1901, 1903, 1907, 1913, 1914, 1915, 1924, 1928, 1929, 1930, 1933, 1933, 1934, 1938, 1938, 1942, 1959, 1969, 1970, 1977, 1982, 1986, 1987, 1988, 1989, 1990, 1995, 1996, 2031, 2040, 2041, 2042, 2050, 2064, 2065, 2066, 2079, 2082, 2083, 2089, 2091, 2092, 2093, 2096, 2097, 2098, 2100, 2102, 2112, 2117, 2118, 2118, 2135, 2148, 2150, 2152, 2157, 2163, 2167, 2168, 2169, 2171, 2186, 2187, 2200, 2201, 2202, 2207, 2208, 2233, 2234, 2235, 2236, 2238, 2239, 2240, 2242, 2243, 2243, 2245, 2248, 2250, 2265, 2270, 2277, 2279, 2280, 2281, 2282, 2284, 2284, 2285, 2285, 2287, 2288, 2289, 2294, 2296, 2297, 2313, 2332, 2333, 2334, 2335, 2336, 2337, 2338, 2341, 2342, 2343, 2345, 2347, 2348, 2364, 2365, 2367, 2369, 2371, 2372, 2373, 2374, 2375, 2388, 2392, 2393, 2394, 2395, 2396, 2397, 2399, 2400, 2401, 2402, 2410, 2411, 2430, 2431, 2431, 2435, 2436, 2437, 2438, 2439, 2440, 2453, 2454, 2456, 2457, 2458, 2462, 2463, 2476, 2477, 2483, 2488, 2500, 2501, 2502, 2506, 2507, 2508, 2509, 2510, 2514, 2517, 2520, 2521, 2522, 2523, 2524, 2525, 2527, 2538, 2540, 2544, 2545, 2546, 2555, 2556, 2558, 2565, 2567, 2574, 2575, 2575, 2576, 2578, 2584, 2585, 2586, 2590, 2591, 2596, 2597, 2611, 2613, 2613, 2614, 2615, 2616, 2630, 2630, 2631, 2656, 2661, 2662, 2663, 2673, 2674, 2675, 2682, 2683, 2684, 2690, 2694, 2695, 2696, 2698, 2708, 2720, 2724, 2724, 2732, 2735, 2736, 2737, 2738, 2739, 2743, 2744, 2745, 2746, 2749, 2750, 2751, 2753, 2754, 2763, 2771, 2790, 2798, 2799, 2800, 2809, 2812, 2813, 2814, 2814, 2824, 2825, 2826, 2835, 2840, 2842, 2842, 2855, 2856, 2873, 2874, 2875, 2880, 2885, 2889, 2890, 2904, 2905, 2910, 2911, 2912, 2915, 2918, 2919, 2920, 2921, 2922, 2922, 2926, 2927, 2929, 2931, 2933, 2934, 2937, 2938, 2939, 2946, 2953, 2954, 2955, 2956, 2974, 2984, 2985, 2987, 2988, 2989, 3002, 3003, 3004, 3005, 3006, 3010, 3015, 3016, 3037, 3038, 3065, 3069, 3070, 3070, 3084, 3095, 3097, 3098, 3107, 3110, 3119, 3132, 3138, 3141, 3146, 3148, 3149, 3150, 3151, 3155, 3156, 3157, 3161, 3163, 3169, 3181, 3191, 3192, 3193, 3196, 3197, 3200, 3201, 3205, 3206, 3207, 3210, 3211, 3212, 3213, 3217, 3219, 3220, 3221, 3224, 3225, 3242, 3252, 3253, 3264, 3265, 3266, 3267, 3273, 3275, 3280, 3289, 3290, 3291, 3294, 3295, 3296, 3296, 3306, 3311, 3312, 3316, 3317, 3323, 3325, 3347, 3348, 3357, 3365, 3366, 3368, 3375, 3378, 3379, 3380, 3383, 3384, 3389, 3394, 3395, 3396, 3418, 3419, 3420, 3440, 3441, 3454, 3461, 3463, 3464, 3470, 3470, 3471, 3477, 3478, 3480, 3481, 3482, 3491, 3492, 3492, 3493, 3494, 3495, 3496, 3497, 3498, 3499, 3500, 3503, 3504, 3505, 3529, 3530, 3531, 3547, 3548, 3549, 3550, 3551, 3553, 3554, 3556, 3557, 3558, 3559, 3563, 3565, 3575, 3576, 3577, 3578, 3579, 3582, 3583, 3592, 3605, 3606, 3612, 3624, 3625, 3626, 3628, 3629, 3650, 3651, 3653, 3654, 3655, 3656, 3662, 3663, 3667, 3668, 3668, 3695, 3707, 3708, 3710, 3711, 3712, 3713, 3714, 3715, 3716, 3717, 3726, 3730, 3735, 3738, 3739, 3740, 3742, 3743, 3744, 3748, 3750, 3771, 3778, 3787, 3795, 3796, 3797, 3801, 3813, 3814, 3821, 3822, 3823, 3828, 3829, 3830, 3831, 3833, 3840, 3840, 3845, 3856, 3858, 3860, 3861, 3862, 3863, 3864, 3869, 3878, 3887, 3888, 3900, 3902, 3903, 3904, 3909, 3916, 3918, 3919, 3920, 3930, 3934, 3939, 3949, 3959, 3960, 3966, 3967, 3968, 3970, 3971, 3972, 3973, 3974, 3981, 3983, 3984, 3985, 3988, 3994, 3995, 3996, 3997, 3998, 3999, 4000, 4001, 4008, 4009, 4022, 4024, 4025, 4028, 4042, 4044, 4058, 4059, 4067, 4070, 4075, 4076, 4081, 4082, 4090, 4093, 4094, 4095, 4095, 4105, 4106, 4109, 4117, 4118, 4119, 4124, 4125, 4125, 4126, 4126, 4134, 4135, 4136, 4137, 4138, 4144, 4148, 4161, 4162, 4162, 4163, 4164, 4168, 4169, 4169, 4170, 4171, 4172, 4175, 4184, 4185, 4186, 4188, 4189, 4190, 4195, 4195, 4195, 4197, 4200, 4201, 4202, 4215, 4217, 4219, 4229, 4233, 4234, 4234, 4235, 4244, 4245, 4258, 4259, 4260, 4267, 4268, 4270, 4272, 4282, 4294, 4299, 4300, 4301, 4302, 4309, 4310, 4311, 4313, 4314, 4315, 4323, 4323, 4324, 4337, 4338, 4345, 4346, 4347, 4348, 4352, 4353, 4361, 4364, 4367, 4368, 4369, 4372, 4373, 4385, 4394, 4395, 4396, 4397, 4398, 4400, 4406, 4407, 4412, 4417, 4423, 4429, 4432, 4433, 4435, 4436, 4437, 4438, 4455, 4457, 4463, 4472, 4474, 4475, 4476, 4477, 4483, 4484, 4485, 4490, 4504, 4507, 4519, 4522, 4523, 4536, 4537, 4538, 4540, 4542, 4543, 4544, 4546, 4547, 4548, 4553, 4554, 4555, 4556, 4561, 4564, 4568, 4569, 4570, 4576, 4578, 4587, 4589, 4590, 4591, 4592, 4594, 4595, 4596, 4597, 4598, 4601, 4603, 4604, 4605, 4606, 4616, 4626, 4628, 4629, 4632, 4635, 4637, 4641, 4643, 4644, 4645, 4652, 4653, 4654, 4658, 4664, 4665, 4667, 4668, 4669, 4670, 4674, 4676, 4677, 4678, 4690, 4691, 4692, 4694, 4695, 4696, 4699, 4703, 4704, 4705, 4708, 4709, 4710, 4712, 4712, 4726, 4753, 4754, 4755, 4756, 4758, 4759, 4764, 4765, 4766, 4778, 4783, 4784, 4785, 4786, 4794, 4796, 4798, 4800, 4801, 4805, 4806, 4808, 4810, 4828, 4830, 4840, 4842, 4847, 4848, 4849, 4850, 4859, 4860, 4861, 4863, 4864, 4865, 4866, 4869, 4870, 4873, 4874, 4875, 4877, 4879, 4880, 4883, 4884, 4885, 4887, 4888, 4889, 4898, 4899, 4900, 4901, 4901, 4902, 4911, 4912, 4913, 4914, 4922, 4942, 4952, 4953, 4954, 4955, 4956, 4959, 4963, 4966, 4976, 4977, 4981, 4989, 4995, 5000, 5001, 5002, 5003, 5013, 5014, 5015, 5019, 5020, 5023, 5024, 5029, 5038, 5039, 5041, 5042, 5049, 5055, 5075, 5083, 5085, 5086, 5089, 5099, 5103, 5105, 5109, 5116, 5117, 5125, 5128, 5133, 5135, 5137, 5138, 5139, 5140, 5149, 5150, 5153, 5157, 5159, 5162, 5164, 5166, 5173, 5174, 5175, 5176, 5177, 5178, 5186, 5187, 5188, 5191, 5192, 5193, 5197, 5220, 5221, 5225, 5226, 5230, 5234, 5235, 5236, 5237, 5240, 5241, 5242, 5243, 5247, 5250, 5251, 5255, 5257, 5263, 5277, 5278, 5278, 5280, 5281, 5282, 5283, 5284, 5287, 5288, 5289, 5290, 5291, 5309, 5310, 5317, 5321, 5322, 5323, 5324, 5325, 5327, 5328, 5330, 5340, 5341, 5342, 5343, 5344, 5345, 5346, 5355, 5356, 5363, 5364, 5372, 5376, 5385, 5386, 5392, 5397, 5398, 5401, 5403, 5403, 5407, 5408, 5410, 5411, 5415, 5416, 5419, 5420, 5421, 5422, 5428, 5429, 5431, 5433, 5434, 5435, 5436, 5437, 5439, 5440, 5441, 5442, 5443, 5445, 5446, 5453, 5458, 5459, 5465, 5466, 5468, 5470, 5471, 5485, 5486, 5488, 5489, 5490, 5496, 5497, 5497, 5499, 5504, 5505, 5506, 5507, 5509, 5510, 5511, 5518, 5530, 5531, 5532, 5533, 5534, 5536, 5543, 5547, 5548, 5548, 5551, 5551, 5552, 5553, 5556, 5577, 5589, 5590, 5590, 5591, 5591, 5597, 5605, 5609, 5610, 5614, 5631, 5638, 5639, 5639, 5640, 5643, 5657, 5658, 5660, 5664, 5665, 5666, 5672, 5673, 5674, 5675, 5676, 5678, 5679, 5680, 5686, 5687, 5688, 5693, 5694, 5695, 5696, 5697, 5698, 5699, 5700, 5701, 5703, 5705, 5708, 5709, 5710, 5711, 5712, 5713, 5714, 5715, 5716, 5717, 5719, 5728, 5729, 5737, 5748, 5753, 5755, 5756, 5762, 5775, 5780, 5781, 5782, 5789, 5790, 5791, 5792, 5793, 5794, 5795, 5796, 5797, 5803, 5808, 5816, 5827, 5828, 5830, 5831, 5832, 5833, 5834, 5835, 5836, 5837, 5846, 5847, 5848, 5851, 5855, 5856, 5857, 5861, 5865, 5867, 5868, 5869, 5870, 5871, 5872, 5874, 5876, 5877, 5878, 5879, 5880, 5882, 5883, 5889, 5892, 5893, 5896, 5897, 5898, 5900, 5910, 5911, 5912, 5918, 5919, 5920, 5921, 5922, 5923, 5924, 5925, 5926, 5927, 5928, 5929, 5930, 5931, 5932, 5933, 5935, 5936, 5938, 5939, 5940, 5942, 5947, 5948, 5949, 5954, 5955, 5963, 5964, 5965, 5966, 5970, 5971, 5972, 5976, 5977, 5986, 5987, 5988, 5989, 5990, 5998, 6006, 6007, 6008, 6009, 6012, 6021, 6023, 6024, 6028, 6029, 6030, 6031, 6032, 6036, 6038, 6039, 6040, 6043, 6044, 6050, 6051, 6054, 6056, 6057, 6061, 6063, 6064, 6065, 6066, 6066, 6071, 6072, 6075, 6076, 6077, 6078, 6079, 6080, 6082, 6083, 6084, 6085, 6090, 6096, 6099, 6100, 6109, 6110, 6111, 6114, 6118, 6119, 6120, 6125, 6126, 6131, 6132, 6132, 6133, 6134, 6149, 6150, 6151, 6152, 6152, 6154, 6155, 6156, 6158, 6159, 6160, 6161, 6162, 6163, 6164, 6165, 6166, 6167, 6167, 6170, 6170, 6174, 6175, 6180, 6183, 6187, 6190, 6203, 6204, 6205, 6206, 6207, 6209, 6214, 6217, 6218, 6224, 6227, 6230, 6235, 6236, 6237, 6238, 6239, 6240, 6242, 6243, 6244, 6259, 6260, 6266, 6267, 6268, 6278, 6279, 6287, 6288, 6289, 6292, 6293, 6294, 6295, 6301, 6303, 6321, 6322, 6322, 6323, 6328, 6329, 6341, 6343, 6346, 6351, 6352, 6357, 6359, 6360, 6361, 6362, 6363, 6364, 6365, 6367, 6373, 6374, 6375, 6377, 6378, 6390, 6402, 6404, 6405, 6406, 6407, 6407, 6409, 6428, 6429, 6430, 6433, 6434, 6435, 6436, 6439, 6440, 6441, 6444, 6445, 6448, 6456, 6459, 6461, 6462, 6463, 6469, 6470, 6477, 6478, 6479, 6482, 6485, 6486, 6487, 6488, 6489, 6491, 6501, 6502, 6506, 6507, 6508, 6511, 6512, 6513, 6513, 6514, 6518, 6519, 6521, 6521, 6532, 6533, 6534, 6539, 6541, 6542, 6543, 6545, 6549, 6550, 6554, 6555, 6558, 6559, 6560, 6563, 6564, 6565, 6570, 6571, 6572, 6575, 6580, 6581, 6582, 6584, 6589, 6590, 6590, 6603, 6612, 6613, 6614, 6631, 6632, 6633, 6649, 6656, 6657, 6665, 6666, 6670, 6671, 6672, 6677, 6678, 6679, 6680, 6683, 6684, 6690, 6691, 6692, 6693, 6694, 6699, 6704, 6705, 6706, 6707, 6708, 6709, 6709, 6710, 6711, 6715, 6721, 6722, 6725, 6731, 6732, 6742, 6743, 6744, 6745, 6768, 6769, 6770, 6772, 6776, 6778, 6783, 6784, 6785, 6787, 6789, 6790, 6792, 6797, 6798, 6798, 6800, 6808, 6809, 6810, 6814, 6816, 6817, 6818, 6819, 6820, 6821, 6827, 6831, 6832, 6833, 6834, 6835, 6841, 6842, 6845, 6855, 6858, 6859, 6860, 6861, 6863, 6864, 6865, 6866, 6867, 6869, 6871, 6872, 6872, 6874, 6881, 6885, 6886, 6894, 6895, 6896, 6897, 6898, 6900, 6901, 6903, 6915, 6921, 6922, 6923, 6924, 6925, 6926, 6933, 6934, 6943, 6946, 6947, 6950, 6950, 6951, 6951, 6952, 6953, 6963, 6964, 6965, 6965, 6966, 6974, 6975, 6976, 6981, 6986, 6993, 6994, 6998, 7004, 7012, 7016, 7017, 7018, 7020, 7020, 7027, 7028, 7029, 7030, 7031, 7034, 7035, 7036, 7037, 7037, 7038, 7039, 7040, 7041, 7041, 7042, 7044, 7046, 7048, 7049, 7050, 7051, 7052, 7053, 7054, 7055, 7055, 7060, 7063, 7064, 7065, 7067, 7068, 7070, 7081, 7082, 7089, 7091, 7092, 7098, 7117, 7117, 7118, 7118, 7121, 7129, 7130, 7131, 7134, 7135, 7138, 7138, 7148, 7167, 7168, 7175, 7210, 7234, 7235, 7248, 7250, 7252, 7252, 7255, 7256, 7261, 7262, 7272, 7281, 7296, 7318],
                'crafts' => [],
            ],
            'festif' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [216, 227, 986, 1279, 1280, 1347, 1421, 1550, 1603, 1656, 1819, 1951, 1971, 2025, 2106, 2832, 2833, 2947, 2948, 2949, 3108, 3109, 3367, 3431, 3560, 3593, 3594, 3835, 3853, 3854, 3867, 4037, 4086, 4196, 4275, 4305, 4317, 4723, 4724, 4725, 4788, 5004, 5005, 5007, 5053, 5076, 5167, 5538, 5550, 5992, 6331, 6967, 6968, 7026, 6108, 7230, 6297, 6298, 6641, 5934, 234, 581, 833, 926, 927, 1078, 1451, 1452, 5297, 5389, 5414, 5447, 5951, 5952, 5953, 106, 562, 714, 866, 867, 1400, 1401, 1402, 1852, 2398, 2424, 2914, 2936, 3286, 3373, 4029, 4030, 4068, 4389, 4656, 4721, 4722, 4734, 4735, 4881, 4960, 4982, 4999, 5008, 5010, 5073, 5136, 5279, 5805, 5838, 6396, 6397, 6104, 2494, 4071, 3673, 394, 661, 744, 1068, 1208, 1252, 1606, 1616, 2022, 2023, 2634, 2635, 2636, 2689, 3727, 4047, 4048, 4073, 4085, 4192, 4193, 4281, 4990, 4994, 5238, 5265, 5361, 5368, 5866, 6046, 6761, 6822, 7031, 7032, 7033, 5826, 5840, 4871, 6004, 6246, 6005, 6245, 1043, 3136, 3392, 4143, 4191, 5134, 5239, 5350, 3298, 290, 663, 955, 1024, 1332, 1419, 1422, 1919, 2026, 2251, 2415, 2703, 2994, 3074, 3106, 3263, 3432, 3433, 3514, 3515, 3516, 3851, 3913, 4124, 4370, 4427, 4428, 4470, 4526, 4528, 4646, 4647, 4648, 4688, 4770, 4792, 4897, 5056, 5161, 5306, 1659, 6794, 5799, 6198, 6610, 4728, 7215, 2995, 6438, 6037, 6326, 1892, 2016, 2274, 2276, 2986, 3140, 3173, 3175, 3241, 3371, 3474, 3761, 4242, 4243, 4307, 4448, 4649, 4821, 5032, 5033, 5034, 5275, 5276, 5294, 5423, 4748, 3039, 6484, 1603, 1904, 2251, 2415, 2703, 2994, 3074, 3075, 3106, 3432, 3433, 3473, 3515, 3516, 3921, 4306, 6717, 5798, 6438, 99, 581, 2246, 2418, 2484, 2714, 3852, 4286, 4638, 5295, 5296, 5297, 5390, 5391, 5413, 7132, 1451, 5952, 5953, 4454, 4459, 4460, 2655, 2996, 3434, 3479, 3564, 3850, 3912, 4469, 4793, 4812, 6437, 7215, 3011, 1659, 2731, 1658, 1273, 1348, 2692, 3075, 4748, 7094, 7095, 7096, 4003, 3091, 981, 1273, 1348, 1399, 1904, 3075, 3140, 3173, 3175, 3240, 3371, 3473, 3474, 3728, 3761, 3921, 4002, 4242, 4306, 4307, 4650, 4651, 5034, 5293, 4748, 2015, 3031, 5740, 5738, 4003, 6379, 562, 1402, 2013],
                'crafts' => [],
            ],
            'wahou' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [74, 442, 462, 580, 901, 936, 1568, 1761, 2108, 2247, 2853, 2885, 3115, 3234, 3235, 3254, 3256, 3258, 3259, 3381, 3382, 3487, 3665, 3722, 3776, 4043, 4055, 4143, 4852, 4853, 4854, 5495, 5937, 6035, 1754, 4141, 3627, 2884, 6045, 6431, 6181, 6432, 3946, 5763, 5823, 5822, 5821, 5179, 6634, 226, 228, 523, 693, 943, 1093, 1387, 1388, 1396, 1397, 1681, 1817, 2020, 2292, 2293, 2408, 2409, 2445, 2446, 2465, 2469, 2637, 2639, 2641, 2658, 2659, 2700, 2901, 2957, 2958, 2977, 2978, 2979, 2980, 2981, 3128, 3158, 3159, 3160, 3214, 3215, 3229, 3231, 3329, 3688, 3689, 4183, 4204, 4256, 4271, 4365, 4366, 4391, 4393, 4481, 4482, 4517, 4524, 4525, 4565, 4566, 4567, 4684, 4686, 4738, 4776, 4777, 4937, 5063, 5201, 5292, 5365, 5366, 5884, 5885, 6696, 6464, 6466, 6467, 6737, 6764, 6399, 6400, 6856, 6336, 6338, 6337, 4655, 7072, 7073, 7074, 7105, 7105, 7203, 4328, 2174, 2173, 4329, 2608, 2080, 2291, 2607, 5378, 6305, 6313, 5379, 6314, 5649, 6216, 6102, 6103, 6053, 847, 1016, 1017, 1018, 1187, 1544, 1602, 2699, 2701, 2916, 3230, 3364, 3687, 4255, 4257, 4571, 5081, 5200, 5677, 6697, 6465, 4673, 6942, 220, 221, 530, 6765, 6271, 6272, 6273, 6274, 6899, 2959, 773, 1683, 2306, 2845, 2862, 2863, 3188, 3511, 3709, 3755, 3881, 3883, 4005, 4007, 4513, 4514, 4515, 4571, 4572, 4811, 4938, 4951, 5079, 5080, 5380, 5404, 5405, 6733, 6735, 6734, 6877, 6662, 7152, 2725, 6600, 6492, 6493, 29, 31, 193, 3176, 3177, 3645, 3646, 3647, 4050, 4051, 4289, 4401, 5764, 6639, 6636, 6637, 6676, 2645, 2259, 2644, 2258, 390, 444, 906, 991, 1032, 2008, 2009, 2087, 2389, 2390, 2391, 2455, 2561, 2568, 2621, 2622, 2837, 2881, 3580, 3581, 3638, 3639, 3640, 3679, 3680, 3681, 3873, 3977, 3978, 4087, 4088, 4236, 4237, 4362, 4363, 4434, 4439, 4505, 4506, 4529, 4530, 4573, 4582, 4583, 4584, 4585, 4915, 4916, 4917, 4927, 4928, 4940, 4957, 5017, 5018, 5088, 5219, 6020, 2730, 2339, 4045, 5668, 5369, 5213, 5214, 5370, 5207, 6172, 5669, 5208, 6019, 6171, 1323, 1980, 4399, 4450, 6669, 6668, 6573, 6574, 7075, 7106, 2213, 5783, 5784, 49, 457, 458, 1145, 1184, 1185, 1321, 1323, 1968, 2196, 2831, 3332, 4444, 5112, 5113, 5455, 6115, 6116, 7224, 1630, 6250, 6211, 6249, 5654, 2085, 2848, 5046, 5048, 7059, 7061, 7062, 4006, 4991, 5064, 492, 498, 535, 800, 949, 950, 1155, 1156, 1161, 1467, 1731, 1877, 1878, 2195, 2748, 3882, 4557, 3642, 3643, 3644, 4516, 4813, 4814, 4919, 4920, 5460, 5461, 5462, 5477, 5478, 5479, 6839, 6840, 6879, 6880, 7021, 7022, 6124, 6368, 5732, 5731, 5742, 6123, 5734, 5733, 682, 683, 711, 870, 1044, 2001, 2542, 2664, 3056, 3786, 4158, 4486, 4820, 4855, 5210, 6531, 6936, 7228, 6184, 6358, 6202, 6185, 2017, 5043, 5417, 5418, 348, 480, 757, 758, 995, 996, 1019, 1481, 1653, 1693, 1792, 1793, 1799, 2505, 2707, 2983, 3260, 3369, 3669, 3884, 4065, 4508, 4560, 5227, 5228, 5267, 5331, 5635, 5636, 6629, 6630, 6568, 5778, 5779, 5777, 6389, 6829, 2020, 4454, 4459, 4460, 45, 46, 872, 873, 2515, 4586, 4750, 4751, 4752, 4851, 5092, 6749, 6699, 1072, 3126, 3127, 3658, 4511, 4720, 366, 465, 614, 837, 1084, 1114, 1223, 1225, 1555, 1882, 2000, 2043, 2101, 2539, 2660, 3465, 3466, 3467, 3718, 3729, 4020, 4021, 4445, 4512, 4642, 4685, 4787, 4815, 4998, 5071, 5196, 5690, 5691, 6830, 6664, 5252, 5254, 5253, 7227, 6113, 6392, 6391, 2447, 3602, 3603, 3604, 5027, 2531, 2532, 2533, 104, 152, 155, 241, 382, 956, 957, 958, 1011, 1014, 1015, 1168, 1503, 4593, 4739, 4740, 4749, 4964, 4965, 4967, 4969, 6729, 6730, 6982, 946, 5562, 3091],
                'crafts' => [],
            ],
            'apero' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [216, 227, 986, 1279, 1280, 1347, 1421, 1550, 1603, 1656, 1819, 1951, 1971, 2025, 2106, 2832, 2833, 2947, 2948, 2949, 3108, 3109, 3367, 3431, 3560, 3593, 3594, 3835, 3853, 3854, 3867, 4037, 4086, 4196, 4275, 4305, 4317, 4723, 4724, 4725, 4788, 5004, 5005, 5007, 5053, 5076, 5167, 5538, 5550, 5992, 6331, 6967, 6968, 7026, 6108, 7230, 6297, 6298, 6641, 5934, 290, 663, 955, 1024, 1332, 1419, 1422, 1919, 2026, 2251, 2415, 2703, 2994, 3074, 3106, 3263, 3432, 3433, 3514, 3515, 3516, 3851, 3913, 4124, 4370, 4427, 4428, 4470, 4526, 4528, 4646, 4647, 4648, 4688, 4770, 4792, 4897, 5056, 5161, 5306, 1659, 6794, 5799, 6198, 6610, 4728, 7215, 2995, 6438, 6037, 6326, 1892, 2016, 2274, 2276, 2986, 3140, 3173, 3175, 3241, 3371, 3474, 3761, 4242, 4243, 4307, 4448, 4649, 4821, 5032, 5033, 5034, 5275, 5276, 5294, 5423, 4748, 3039, 6484, 1603, 1904, 2251, 2415, 2703, 2994, 3074, 3075, 3106, 3432, 3433, 3473, 3515, 3516, 3921, 4306, 6717, 5798, 6438, 2655, 2996, 3434, 3479, 3564, 3850, 3912, 4469, 4793, 4812, 6437, 7215, 3011, 1659, 2731, 1658, 1273, 1348, 2692, 3075, 4748, 7094, 7095, 7096, 4003, 981, 1273, 1348, 1399, 1904, 3075, 3140, 3173, 3175, 3240, 3371, 3473, 3474, 3728, 3761, 3921, 4002, 4242, 4306, 4307, 4650, 4651, 5034, 5293, 4748, 2015, 3031, 5740, 5738, 4003, 6379],
                'crafts' => [],
            ],
            'nature-et-bien-etre' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [6802, 6231, 6232, 6803, 6905, 6906, 7071, 7138, 7142, 245, 890, 4268, 5433, 6278, 6633, 2031, 6788, 2448, 2909, 2950, 1427, 4596, 6715, 3224, 3225, 3264, 3368, 4195, 4215, 4981, 2035, 2864, 9, 271, 436, 496, 595, 620, 621, 895, 917, 918, 919, 952, 1023, 1107, 1247, 1248, 1353, 1412, 1413, 1416, 1676, 1738, 1739, 1740, 1881, 1959, 2690, 2880, 3161, 3493, 3548, 3575, 3710, 3983, 3984, 3985, 4058, 4059, 4175, 4201, 4299, 4476, 4477, 4483, 4484, 4536, 4537, 4664, 4674, 4690, 4691, 4692, 4694, 4695, 4708, 4709, 4758, 4759, 4898, 4899, 4955, 5105, 5162, 5386, 5471, 5883, 6694, 6745, 6776, 6817, 6832, 6901, 6925, 6926, 5964, 6565, 7028, 7029, 7067, 7092, 7098, 5938, 5947, 5948, 5986, 4652, 6078, 5987, 269, 270, 322, 323, 427, 589, 817, 818, 953, 1028, 1105, 1106, 1137, 1385, 1583, 1585, 1791, 1840, 1841, 1854, 1873, 1875, 1880, 1907, 2096, 2097, 2098, 2163, 2171, 2207, 2208, 2279, 2280, 2520, 2522, 2735, 2824, 2826, 2910, 2911, 2912, 2919, 2955, 2956, 2984, 2985, 3097, 3375, 3495, 3968, 4042, 4172, 4184, 4185, 4186, 4200, 4217, 4300, 4301, 4472, 4475, 4538, 4554, 4555, 4556, 4597, 4598, 4632, 4637, 4653, 4665, 4670, 4764, 4765, 4766, 4796, 4860, 4956, 5099, 5103, 5157, 5164, 5178, 5193, 5237, 5278, 5290, 5291, 5340, 5341, 5343, 5407, 5408, 5410, 5419, 5420, 5421, 5422, 5505, 5506, 5597, 5728, 5729, 5882, 5892, 5893, 5918, 5919, 5920, 5921, 6691, 6692, 6570, 6571, 6572, 6722, 6501, 5610, 6742, 6743, 6768, 6769, 6770, 6778, 6814, 6816, 6818, 6833, 6842, 6295, 6865, 6866, 6881, 6903, 5942, 6792, 6900, 6922, 6934, 6239, 6240, 6206, 6974, 6975, 6976, 6563, 6564, 6867, 7053, 7131, 7049, 7138, 5939, 5965, 7248, 7250, 7262, 2337, 2336, 6125, 6165, 6120, 5780, 6539, 6126, 5049, 6402, 2521, 4338, 4337, 2825, 3113, 6448, 6449, 6450, 6802, 6841, 3114, 3132, 6238, 7138, 7142, 5934, 2410, 3146, 4800, 4801, 4847, 6679, 6871, 6872, 7020, 6584, 5782, 6346, 771, 1027, 1333, 1334, 1707, 2388, 2510, 2661, 2749, 2885, 2953, 2954, 3267, 3275, 3503, 3504, 3505, 3711, 4954, 5342, 5376, 5504, 5507, 5715, 6744, 6827, 6209, 7070, 7135, 5660, 5666, 374, 584, 585, 700, 701, 739, 741, 862, 938, 939, 976, 992, 1269, 1426, 1427, 1428, 1711, 1867, 1883, 1884, 1885, 2364, 2400, 2401, 2454, 2462, 2463, 2500, 2502, 2739, 2751, 2809, 2889, 2926, 2937, 2938, 2939, 3200, 3201, 3265, 3266, 3296, 3383, 3558, 3583, 3605, 3654, 3655, 3712, 3738, 3845, 3919, 3920, 4025, 4124, 4148, 4195, 4219, 4270, 4272, 4323, 4398, 4626, 4828, 4830, 4875, 4959, 5055, 5187, 5188, 5192, 5197, 5398, 5543, 5658, 5714, 5717, 6710, 6711, 6946, 7020, 7044, 5827, 5828, 7256, 2890, 4345, 2738, 5709, 6072, 6360, 6134, 6132, 5701, 6080, 5791, 5699, 5790, 6133, 6363, 5789, 6071, 5700, 6131, 6362, 6448, 5665, 6361, 2501, 5935, 5936, 375, 701, 740, 1710, 1885, 2402, 2538, 2540, 2750, 3713, 3918, 6690, 6800, 6207, 863, 1702, 2807, 2808, 3007, 3247, 3736, 3844, 6413, 3631, 1483, 1810, 2847, 2940, 2941, 3053, 3061, 3343, 3349, 3350, 3468, 3484, 3634, 3676, 3701, 3923, 3947, 3948, 3986, 3992, 3993, 4176, 4177, 4178, 4179, 4249, 4250, 4251, 4322, 5158, 5165, 5217, 5218, 5424, 5449, 5450, 5451, 5862, 7023, 7024, 7047, 7071, 3113, 3615, 3012, 2944, 2942, 6643, 6306, 6138, 6093, 2176, 113, 560, 592, 634, 723, 726, 864, 899, 907, 1000, 1246, 1268, 1269, 1428, 1558, 1688, 1689, 1712, 1761, 1855, 1865, 1866, 2112, 2365, 2453, 2457, 2458, 2656, 2763, 3005, 3006, 3069, 3151, 3197, 3295, 3481, 3531, 3559, 3582, 3656, 3714, 3735, 3778, 3887, 3888, 4323, 4385, 4412, 4435, 4436, 4437, 4455, 4784, 4785, 4786, 4874, 4877, 4989, 5029, 5137, 5138, 5191, 5325, 5397, 5411, 5429, 5536, 5716, 6656, 6132, 5000, 2737, 3296, 6542, 5673, 6541, 5708, 5664, 5672, 6479, 4070],
                'crafts' => [],
            ],
            'beaux-objets' => [
                'query' => null,
                'craftsQuery' => null,
                'workshops' => [1, 3, 4, 9, 46, 49, 52, 52, 53, 54, 66, 79, 89, 100, 100, 102, 111, 122, 136, 137, 157, 185, 192, 220, 221, 226, 228, 230, 265, 267, 271, 283, 304, 312, 319, 326, 331, 339, 359, 362, 362, 366, 381, 382, 386, 392, 393, 404, 404, 405, 406, 410, 416, 425, 436, 449, 451, 452, 453, 457, 458, 465, 472, 484, 485, 485, 492, 495, 496, 498, 521, 522, 523, 525, 526, 530, 535, 536, 538, 538, 545, 595, 614, 618, 620, 621, 628, 630, 641, 664, 665, 666, 670, 679, 680, 681, 693, 698, 756, 759, 759, 760, 766, 781, 787, 787, 790, 791, 800, 801, 801, 801, 804, 815, 821, 825, 833, 837, 874, 888, 889, 895, 917, 918, 919, 949, 950, 952, 953, 964, 977, 983, 983, 984, 1002, 1005, 1006, 1018, 1023, 1038, 1050, 1051, 1058, 1059, 1059, 1059, 1059, 1080, 1081, 1084, 1093, 1094, 1107, 1114, 1123, 1124, 1137, 1143, 1145, 1147, 1155, 1155, 1156, 1157, 1161, 1184, 1185, 1186, 1199, 1200, 1209, 1209, 1209, 1223, 1225, 1242, 1247, 1248, 1270, 1271, 1272, 1296, 1307, 1312, 1321, 1321, 1321, 1323, 1323, 1338, 1353, 1374, 1375, 1387, 1388, 1396, 1397, 1412, 1413, 1415, 1416, 1418, 1452, 1462, 1462, 1463, 1466, 1467, 1498, 1498, 1511, 1530, 1531, 1532, 1536, 1537, 1555, 1569, 1570, 1580, 1581, 1582, 1590, 1600, 1624, 1627, 1628, 1629, 1668, 1669, 1676, 1681, 1683, 1726, 1727, 1728, 1731, 1733, 1785, 1817, 1818, 1851, 1853, 1876, 1877, 1878, 1881, 1882, 1903, 1954, 1955, 1956, 1959, 1967, 1968, 1980, 1980, 1980, 2000, 2020, 2043, 2101, 2128, 2128, 2128, 2129, 2158, 2195, 2196, 2212, 2212, 2213, 2292, 2293, 2310, 2310, 2378, 2379, 2380, 2408, 2409, 2418, 2436, 2436, 2436, 2436, 2445, 2446, 2465, 2469, 2511, 2511, 2539, 2546, 2554, 2555, 2556, 2556, 2569, 2595, 2627, 2628, 2629, 2637, 2639, 2641, 2648, 2649, 2658, 2659, 2660, 2666, 2667, 2690, 2700, 2719, 2747, 2748, 2769, 2788, 2789, 2789, 2789, 2829, 2830, 2831, 2838, 2875, 2880, 2901, 2957, 2958, 2964, 2977, 2978, 2979, 2980, 2981, 3128, 3143, 3148, 3148, 3158, 3159, 3160, 3161, 3165, 3213, 3214, 3215, 3217, 3229, 3230, 3231, 3251, 3252, 3253, 3260, 3283, 3284, 3326, 3327, 3328, 3329, 3330, 3331, 3332, 3358, 3359, 3366, 3372, 3384, 3386, 3388, 3407, 3422, 3444, 3447, 3448, 3451, 3455, 3455, 3456, 3456, 3465, 3466, 3467, 3493, 3506, 3548, 3555, 3575, 3577, 3584, 3598, 3611, 3636, 3637, 3648, 3648, 3648, 3651, 3652, 3670, 3671, 3672, 3684, 3685, 3686, 3688, 3689, 3691, 3710, 3718, 3729, 3752, 3755, 3756, 3770, 3779, 3792, 3793, 3794, 3798, 3841, 3865, 3875, 3876, 3877, 3890, 3904, 3910, 3911, 3914, 3915, 3928, 3929, 3937, 3940, 3941, 3944, 3944, 3983, 3984, 3985, 4020, 4021, 4058, 4059, 4069, 4103, 4126, 4127, 4155, 4159, 4159, 4163, 4175, 4183, 4199, 4201, 4203, 4204, 4208, 4211, 4220, 4221, 4232, 4253, 4256, 4271, 4276, 4276, 4277, 4280, 4283, 4290, 4299, 4313, 4316, 4319, 4320, 4349, 4350, 4356, 4358, 4365, 4366, 4369, 4375, 4391, 4392, 4393, 4397, 4399, 4399, 4406, 4407, 4408, 4413, 4414, 4419, 4420, 4421, 4422, 4442, 4443, 4444, 4445, 4449, 4450, 4451, 4466, 4473, 4474, 4476, 4477, 4481, 4482, 4483, 4484, 4495, 4496, 4500, 4500, 4500, 4512, 4516, 4517, 4521, 4524, 4525, 4536, 4537, 4543, 4549, 4550, 4551, 4552, 4557, 4558, 4559, 4565, 4566, 4567, 4572, 4575, 4577, 4581, 4581, 4581, 4601, 4620, 4630, 4631, 4633, 4634, 4634, 4634, 4642, 4664, 4672, 4674, 4675, 4682, 4684, 4685, 4686, 4690, 4691, 4692, 4693, 4693, 4694, 4695, 4696, 4708, 4709, 4738, 4756, 4757, 4758, 4759, 4762, 4763, 4774, 4775, 4776, 4777, 4779, 4780, 4782, 4787, 4808, 4813, 4814, 4815, 4816, 4817, 4820, 4820, 4820, 4822, 4823, 4824, 4831, 4832, 4833, 4834, 4835, 4848, 4849, 4894, 4895, 4898, 4899, 4919, 4920, 4923, 4923, 4924, 4925, 4930, 4937, 4943, 4944, 4945, 4946, 4947, 4950, 4951, 4955, 4979, 4980, 4998, 5022, 5023, 5028, 5040, 5044, 5059, 5059, 5060, 5063, 5071, 5077, 5083, 5089, 5097, 5104, 5105, 5112, 5113, 5114, 5115, 5116, 5152, 5162, 5163, 5195, 5196, 5201, 5223, 5230, 5231, 5274, 5285, 5292, 5318, 5319, 5320, 5338, 5347, 5348, 5349, 5365, 5366, 5367, 5373, 5374, 5374, 5375, 5382, 5383, 5386, 5400, 5400, 5400, 5400, 5417, 5418, 5452, 5454, 5455, 5456, 5461, 5462, 5464, 5471, 5477, 5479, 5493, 5498, 5498, 5519, 5521, 5522, 5523, 5528, 5531, 5540, 5581, 5582, 5612, 5612, 5613, 5613, 5690, 5691, 5693, 5743, 5743, 5744, 5768, 5768, 5768, 5787, 5797, 5834, 5837, 5852, 5852, 5852, 5853, 5858, 5859, 5860, 5883, 5884, 5885, 5888, 5905, 5906, 5914, 5915, 5916, 5917, 5978, 6694, 6696, 6697, 6697, 6697, 6456, 6442, 2672, 6700, 6700, 6700, 6700, 6645, 6464, 6712, 6713, 6466, 6467, 6670, 6372, 6719, 6720, 6576, 6501, 5611, 6739, 6745, 6746, 6747, 6748, 6756, 6757, 6758, 6536, 6760, 6762, 6764, 6765, 6771, 6737, 6615, 6669, 6668, 6790, 6399, 6791, 6271, 6272, 6273, 6274, 6795, 6400, 6817, 6597, 6598, 6599, 6830, 6832, 6843, 6844, 6846, 6858, 6861, 6317, 6664, 6879, 6880, 6663, 6663, 6882, 6292, 6292, 6292, 6292, 6294, 6896, 6899, 6901, 6336, 6338, 6337, 6908, 6909, 6910, 6919, 6920, 6925, 6926, 5964, 4655, 4655, 6928, 6929, 6930, 6930, 6930, 6931, 6823, 6517, 6621, 2959, 6625, 6624, 6952, 6953, 6954, 6955, 6956, 6957, 6958, 6958, 6959, 6960, 6961, 6961, 6962, 6962, 6962, 6962, 6962, 6972, 6976, 6977, 6063, 6112, 6112, 6979, 6980, 5223, 6988, 6989, 6565, 6564, 6415, 6935, 7008, 7011, 7018, 7021, 5258, 7056, 7065, 7066, 7067, 6978, 6978, 6115, 6116, 7073, 7074, 7075, 5252, 5254, 5253, 7080, 7083, 7086, 7087, 7087, 7088, 7092, 5692, 6416, 7103, 7105, 7106, 6553, 7113, 7119, 7120, 7057, 7139, 7139, 1738, 1739, 1740, 7152, 7159, 7159, 7162, 7163, 7164, 7165, 7165, 7166, 7172, 7158, 7160, 7153, 7192, 7193, 7194, 7194, 7195, 7196, 7197, 7209, 6681, 7211, 6682, 7224, 7229, 7231, 7232, 7233, 7227, 6091, 5938, 5941, 5943, 5944, 5947, 5948, 7250, 7251, 7251, 7254, 7260, 7267, 7276, 7275, 7278, 7285, 7291, 7295, 7295, 7295, 7293, 7293, 7307, 5986, 7319, 7320, 7322, 2649, 5766, 5633, 3609, 5727, 1663, 5990, 6054, 6044, 5988, 2987, 4873, 5477, 2591, 2614, 6144, 6155, 6175, 6099, 6180, 519, 2139, 821, 3036, 1646, 2452, 670, 725, 761, 1943, 1962, 2893, 4342, 2154, 6016, 5652, 2895, 1787, 5042, 2233, 6096, 6549, 2928, 2969, 4823, 5732, 5731, 6368, 2230, 2229, 1796, 2792, 2233, 6324, 2353, 5634, 5819, 6299, 3443, 3890, 4849, 5042, 4835, 4343, 1079, 1958, 5367, 1648, 4823, 5022, 2894, 1052, 2166, 5642, 6601, 6611, 2155, 4991, 5064, 2217, 3046, 5811, 5783, 5784, 2595, 3083, 6185, 3830, 4127, 3045, 3041, 359, 3305, 5655, 5943, 5783, 5784, 2595, 4397, 3577, 3165, 2786, 2504, 3043, 6184, 6202, 6185, 2411, 2912, 6402, 2521, 6584, 2134, 2164, 1705, 1842, 4341, 2137, 1745, 1651, 2272, 1922, 1780, 6282, 5651, 5722, 6458, 5752, 3443, 6522, 6176, 4652, 6078, 5987, 6177, 2330, 5726, 5215, 5818, 175, 3781, 4328, 2175, 2174, 5677, 2173, 4329, 2608, 2080, 2021, 2081, 2291, 6423, 5648, 5378, 6305, 6313, 5379, 6314, 5649, 6216, 6102, 6103, 6053, 2543, 958, 4965, 4964, 4969, 1168, 1503, 104, 4967, 152, 1011, 1015, 957, 1014, 5562, 241, 5761, 6562, 5043, 3830, 4127, 1732, 2650, 1790, 3042, 2795, 948, 6200, 6137, 6211, 5824, 6248, 5968, 5945, 6253, 5809, 5783, 6418, 6201, 5784, 6626, 2595, 4397, 3577, 3165, 2786, 3083, 2504, 3033, 3043, 6184, 6202, 6185, 6302, 1420, 2024, 6424, 6422, 6182, 5812, 2216, 1630, 359, 3304, 6250, 6199, 5946, 6106, 6332, 6529, 6088, 5967, 6419, 5661, 6249, 6210, 6556, 6247, 6107, 5945, 5783, 5810, 6251, 6089, 5784, 5654, 6034, 3612, 3165, 6184, 6185, 5913, 5561, 5352, 2310, 3060, 6393, 5089, 3032, 3791, 3083, 3456, 3047, 6252, 6010, 5969, 6033, 2744, 6550, 6167, 6478, 6113, 6392, 6391, 459, 607, 3657, 3900, 4106, 6462, 1963, 2928, 7294, 7293, 1306, 1324, 1921, 1957, 2451, 5887, 1337, 1895, 1944, 2138, 6535, 6688, 2967, 1306, 1324, 1398, 1432, 1647, 2407, 2968, 7124, 6776, 6825, 7028, 7029, 5825, 6842, 6991, 6992, 7058, 7123, 2028, 7122, 2534, 6455, 7328, 7350, 7351, 7352, 7352, 7353, 7353, 7354, 7354, 7354, 7355, 7359, 7360],
                'crafts' => [],
            ],
            'tete-a-tete' => [
                'query' => $this->getWorkshopsSource()
                    ->where('nb_pers_max', 1),
                'craftsQuery' => null,
                'workshops' => [],
                'crafts' => [],
            ],
        ];

        Log::info('START update workshops tags', [
            'count_to_process' => \count($toUpdate),
        ]);

        foreach ($toUpdate as $tagName => $tagInfo) {
            $this->processTag($tagName, $tagInfo);
        }

        Log::info('All tags processed', [
            'workshopsImpacted' => $this->workshopsImpacted,
        ]);

        $this->info("Fin de l'update des ateliers pour les tags");

        return self::SUCCESS;
    }

    /** @param array<string, mixed> $tagInfo */
    private function processTag(string $tagName, array $tagInfo): void
    {
        $tag = Tag::where('slug', $tagName)->first();
        $countWorkshops = null;
        $impactedWorkshops = [
            'attached' => [],
            'detached' => [],
        ];

        if ($tag instanceof Tag) {
            $workshopsId = $tagInfo['query']?->select('ateliers.id')->orderBy('ateliers.id')->lazy()->pluck('id') ?? collect();

            if (!empty($tagInfo['crafts'])) {
                $workshopsId = $workshopsId->concat(
                    ($tagInfo['craftsQuery'] ?? $this->getWorkshopsSource())
                    ->whereIn('craft_id', $tagInfo['crafts'])
                        ->select('ateliers.id')
                        ->orderBy('ateliers.id')
                        ->lazy()
                        ->pluck('id')
                );
            }

            if (!empty($tagInfo['workshops'])) {
                $workshopsId = $workshopsId->concat(array_map('intval', $tagInfo['workshops']));
            }

            $result = $tag->workshops()->sync($workshopsId->unique()->toArray());
            $this->workshopsImpacted += $countWorkshops = (\count($result['attached']) + \count($result['detached']));
            $impactedWorkshops = $result;
        }
        Log::info('New tag processed', [
            'tag' => $tagName,
            'workshops_changed_count' => $countWorkshops,
            'workshops_changed_list' => $impactedWorkshops,
        ]);
    }

    private function getWorkshopsSource(): Builder
    {
        if ($this->workshopsSource === null) {
            $this->workshopsSource = DB::table('ateliers')
                ->where('active', true)
                ->whereNull('invisible_until')
                ->whereNotNull('online_at');
        }

        return clone $this->workshopsSource;
    }
}
