<?php

declare(strict_types=1);

namespace App\Console\Commands\Technique;

use App\Console\Command;
use App\Domain\Content\Repositories\PageRepository;
use App\Models\Artisan;
use App\Models\Atelier;
use Carbon\Carbon;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Support\LazyCollection;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class SitemapGenerateCommand extends Command
{
    public const SITEMAP_STORAGE = 'private';
    public const SITEMAP_FILENAME = 'sitemap.xml';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var ?string
     */
    protected $description = 'Generate the sitemap XML file for each country';

    public static function getSitemapFilename(string $country): string
    {
        return $country.'/'.static::SITEMAP_FILENAME;
    }

    public function handle(
        PageRepository $pageRepository,
        ExceptionHandler $exceptionHandler
    ): int {
        $this->components->info('Generating sitemap.xml for each country...');

        try {
            foreach (config('countries.available_countries') as $country) {
                $this->components->task($country, fn () => $this->generate($country, $pageRepository));
            }
        } catch (\Exception $exception) {
            $exceptionHandler->report($exception);
            $this->components->error($exception->getMessage());

            return self::FAILURE;
        }

        return self::SUCCESS;
    }

    private function generate(string $country, PageRepository $pageRepository): void
    {
        $this->setUrlGeneratorForCountry($country);
        $sitemap = Sitemap::create();

        $sitemap->add(Url::create('/')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T20:10:00+02:00'))->setPriority(1.0)->setChangeFrequency('daily'));
        $sitemap->add(Url::create('/gifts')->setLastModificationDate(new \DateTimeImmutable('2024-11-14'))->setPriority(0.9)->setChangeFrequency('monthly'));

        $sitemap->add(Url::create('/ateliers')->setLastModificationDate(Carbon::now()->subWeek())->setPriority(0.9)->setChangeFrequency('daily'));

        $singlePages = $pageRepository->getAllActiveForCountryCursor($country);

        foreach ($singlePages as $singlePage) {
            $sitemap->add(Url::create($singlePage->getUrl())->setLastModificationDate(Carbon::now()->subWeek())->setPriority(0.8)->setChangeFrequency('daily'));
        }

        /** @var LazyCollection<int, Atelier> $ateliers */
        $ateliers = Atelier::where('ateliers.active', true)
            ->whereNull('ateliers.invisible_until')
            ->orderBy('ateliers.created_at', 'desc')
            ->leftJoin('artisans', 'artisans.id', '=', 'ateliers.artisan_id')
            ->leftJoin('artisan_location', 'artisan_location.artisan_id', '=', 'artisans.id')
            ->where('artisan_location.is_main', true)
            ->leftJoin('lieux', 'lieux.id', '=', 'artisan_location.location_id')
            ->leftJoin('villes', 'villes.id', '=', 'lieux.ville_id')
            ->where('villes.country_code', $country)
            ->select(['ateliers.permalien', 'ateliers.updated_at'])
            ->lazy();

        foreach ($ateliers as $atelier) {
            if ($atelier->updated_at === null) {
                continue;
            }
            $sitemap->add(Url::create('/atelier/'.$atelier->permalien)->setLastModificationDate($atelier->updated_at)->setPriority(0.7)->setChangeFrequency('monthly'));
        }

        $sitemap->add(Url::create('/artisans')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.5)->setChangeFrequency('weekly'));

        /** @var LazyCollection<int, Artisan> $artisans */
        $artisans = Artisan::where('artisans.active', true)
            ->orderBy('artisans.created_at', 'desc')
            ->leftJoin('artisan_location', 'artisan_location.artisan_id', '=', 'artisans.id')
            ->where('artisan_location.is_main', true)
            ->join('lieux', 'lieux.id', '=', 'artisan_location.location_id')
            ->join('villes', 'villes.id', '=', 'lieux.ville_id')
            ->where('villes.country_code', $country)
            ->select(['artisans.permalien', 'artisans.updated_at'])
            ->lazy();

        foreach ($artisans as $artisan) {
            if ($artisan->updated_at === null) {
                continue;
            }
            $sitemap->add(Url::create('/artisan/'.$artisan->permalien)->setLastModificationDate($artisan->updated_at)->setPriority(0.6)->setChangeFrequency('monthly'));
        }

        $sitemap->add(Url::create('/cgu-cgv')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.4)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/mentions-legales')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.4)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/faq')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.4)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/le-manifeste')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.5)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/contact-artisan')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.3)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/contact-entreprise')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.3)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/login')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.2)->setChangeFrequency('monthly'));
        $sitemap->add(Url::create('/register')->setLastModificationDate(new \DateTimeImmutable('2017-10-02T12:30:00+02:00'))->setPriority(0.2)->setChangeFrequency('monthly'));

        $sitemap->writeToDisk(self::SITEMAP_STORAGE, self::getSitemapFilename($country));

        $this->setUrlGeneratorForCountry(config('app.country'));
    }

    protected function setUrlGeneratorForCountry(string $country): void
    {
        app(UrlGenerator::class)->forceRootUrl($this->getRootUrlOfCountry($country));
    }

    protected function getRootUrlOfCountry(string $country): ?string
    {
        return config('app.url', null, $country);
    }
}
