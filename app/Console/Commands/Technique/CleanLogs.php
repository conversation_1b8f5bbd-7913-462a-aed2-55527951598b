<?php

declare(strict_types=1);

namespace App\Console\Commands\Technique;

use App\Console\Command;
use Spatie\Activitylog\Models\Activity;

class CleanLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'log:clean';

    /**
     * The console command description.
     *
     * @var ?string
     */
    protected $description = 'Clean logs that only contains an updated_at changes';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $logs = Activity::all();
        $bar = $this->output->createProgressBar(\count($logs));
        $cpt = 0;
        $bar->start();
        $logs->each(function ($log) use (&$bar, &$cpt): void {
            $changes = $log->changes()->toArray();
            if (\array_key_exists('attributes', $changes)
                && (is_countable($changes['attributes']) ? \count($changes['attributes']) : 0) === 1
                && \array_key_exists('updated_at', $changes['attributes'])) {
                $log->delete();
                ++$cpt;
            }

            if (\array_key_exists('attributes', $changes)
                && \array_key_exists('note', $changes['attributes'])) {
                $log->delete();
                ++$cpt;
            }

            $bar->advance();
        });
        $bar->finish();
        $this->info(\PHP_EOL.'cleaned '.$cpt.' entries');

        return self::SUCCESS;
    }
}
