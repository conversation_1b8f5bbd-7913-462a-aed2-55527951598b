<?php

declare(strict_types=1);

namespace App\Console\Commands\Technique;

use App\Console\Command;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Booking\Enums\BookingType;
use App\Domain\Booking\Services\BookingService;
use App\Models\Reservation;
use Carbon\Carbon;

class BookingAwaitingPaymentPrune extends Command
{
    protected $signature = 'booking:prune-awaiting-payment';
    protected $description = 'Suppression des réservations en attente de paiement périmées';

    public function handle(): int
    {
        $bookingsToPays = Reservation::where('status', BookingStatus::BOOKING_AWAITING_PAYMENT)
            ->where('expiration_date', '<=', Carbon::yesterday()->endOfDay())
            ->where('type', '<>', BookingType::BOOKING_TYPE_PRIVATIZATION)
            ->lazy(10);

        foreach ($bookingsToPays as $booking) {
            $this->components->info("Delete $booking->id");
            BookingService::delete($booking, true);
        }

        $this->info('deleted bookings with status awaiting payment and expired expiration_date');

        return self::SUCCESS;
    }
}
