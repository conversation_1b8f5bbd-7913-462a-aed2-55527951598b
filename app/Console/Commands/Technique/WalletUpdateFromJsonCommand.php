<?php

declare(strict_types=1);

namespace App\Console\Commands\Technique;

use App\Console\Command;
use App\Models\Admin;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\FilesystemException;

/**
 * @SuppressWarnings(PHPMD)
 */
class WalletUpdateFromJsonCommand extends Command
{
    protected $signature = 'wallet:update-from-json {disk} {filePathName} {adminEmail} {ignoreMinute?}';

    public function handle(): int
    {
        try {
            $jsonData = Storage::disk($this->argument('disk'))->read($this->argument('filePathName'));
        } catch (FilesystemException $e) {
            $this->components->error('Error retrieving file: '.$e->getMessage());

            return self::FAILURE;
        }

        $walletData = json_decode($jsonData, true);

        /** @var ?Admin $causer */
        $causer = Admin::query()->where('email', $this->argument('adminEmail'))->first();

        if ($causer === null) {
            $this->components->error("Couldn't find admin with email [{$this->argument('adminEmail')}]");

            return self::FAILURE;
        }

        $this->components->info("Found [{$causer->getName()}]");

        if (!$this->confirm('Ready to start?')) {
            $this->components->error('Interrupting.');

            return self::FAILURE;
        }

        $emails = [];

        $this->components->info('Starting');
        foreach ($walletData as $userToIncrement) {
            $email = $userToIncrement['email'];
            $amountToAdd = $userToIncrement['amount'];
            $emails[] = $email;

            /** @var ?User $user */
            $user = User::query()->where('email', $email)->first();

            if ($user === null) {
                $this->warn("User not found : $email");
                continue;
            }
            $cpu = $user->codeparrainageuser;

            if ($cpu === null) {
                $this->warn("Code parrainage user not found : $email");
                continue;
            }

            $ignoreMinute = (int) $this->argument('ignoreMinute');

            if ($ignoreMinute !== 0 && $cpu->updated_at?->isAfter(now()->subMinutes($ignoreMinute)) === true) {
                $this->warn("Code parrainage already updated : $email");
                continue;
            }

            $cpu->increaseAmount($amountToAdd);

            activity('userwallet_log')
                ->causedBy($causer)
                ->withProperty('amount', $amountToAdd)
                ->performedOn($cpu)
                ->log('Adding amount based on loyalty program '.Carbon::now()->format('M-Y'));
        }
        $this->components->info('Finished');

        $this->components->task('Settings users as out of sync', function () use (&$emails): void {
            foreach (array_chunk($emails, 100) as $sousPop) {
                \DB::table('users')->whereIn('email', $sousPop)->update(['synchronized_at' => null]);
            }
        });

        return self::SUCCESS;
    }
}
