<?php

declare(strict_types=1);

namespace App\Console\Commands\Technique;

use App\Console\Command;

class CleanCommissionPercentage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'booking:clean-commission';

    /**
     * The console command description.
     *
     * @var ?string
     */
    protected $description = "Clean bookings that doesn't have comission percentage";

    public function handle(): void
    {
        $defaultValue = 20;
        \DB::update("UPDATE
                    reservations INNER JOIN (SELECT
                            CASE
                                WHEN ateliers.commission_pourcentage IS NOT NULL THEN ateliers.commission_pourcentage
                                ELSE {$defaultValue}
                            END as percentage,
                            evenements.id as evenement_id
                            FROM evenements
                            JOIN ateliers
                                ON ateliers.id = evenements.atelier_id
                            JOIN artisans
                                ON artisans.id = ateliers.artisan_id) com ON com.evenement_id = reservations.evenement_id
                     SET reservations.commission_pourcentage = com.percentage WHERE reservations.commission_pourcentage IS NULL AND reservations.deleted_at IS NULL AND reservations.origin = 'customer';");
    }
}
