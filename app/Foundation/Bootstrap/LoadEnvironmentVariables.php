<?php

declare(strict_types=1);

namespace App\Foundation\Bootstrap;

use Dotenv\Dotenv;
use Dotenv\Exception\InvalidFileException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables as Base;
use Illuminate\Support\Env;

/**
 * @SuppressWarnings(PHPMD.Superglobals)
 * @SuppressWarnings(PHPMD.CyclomaticComplexity)
 */
class LoadEnvironmentVariables extends Base
{
    /**
     * Overrides the default method by load both `.env` AND `.env.<APP_ENV>` files instead of only one.
     */
    public function bootstrap(Application $app): void
    {
        if ($app->configurationIsCached()) {
            return;
        }

        $this->checkForSpecificEnvironmentFile($app);

        // Allow calling phpunit or `artisan test` without `--env=testing`
        if ($app->runningInConsole()) {
            $cmdLine = implode(' ', $_SERVER['argv'] ?? []);
            if (str_contains($cmdLine, 'phpunit') || str_contains($cmdLine, 'artisan test')) {
                $this->setEnvironmentFilePath($app, '.env.testing');
            }
        }

        $appEnvBeforeLoading = Env::get('APP_ENV');

        // Loading the default .env file BEFORE the extending one
        if ($app->environmentFile() !== '.env') {
            Dotenv::create(Env::getRepository(), $app->environmentPath(), '.env')->safeLoad();
        }

        try {
            $loadedEnv = $this->createDotenv($app)->safeLoad();
        } catch (InvalidFileException $e) {
            $this->writeErrorAndDie($e);
        }

        // If an APP_ENV value is defined into the .env file, loading the related env file
        if (\array_key_exists('APP_ENV', $loadedEnv) && $loadedEnv['APP_ENV'] !== $appEnvBeforeLoading) {
            try {
                Dotenv::create(Env::getRepository(), $app->environmentPath(), '.env.'.$loadedEnv['APP_ENV'])->safeLoad();
            } catch (\Throwable) {
                // Ignoring
            }
        }
    }
}
