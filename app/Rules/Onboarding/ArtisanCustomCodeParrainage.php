<?php

namespace App\Rules\Onboarding;

use App\Infrastructure\Auth\AuthFinder;
use App\Models\Artisan;
use App\Models\Parrainage\CodeParrainageInvitation;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Http\JsonResponse;

class ArtisanCustomCodeParrainage implements Rule
{
    public $errors;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->errors = [__('userartisan/controllers/errors.artisan.sponsorship_code.invalid')];
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     */
    public function passes($attribute, $value): bool|JsonResponse
    {
        if (($usersArtisan = AuthFinder::make()->tryArtisan()) === null) {
            return response()->json("Impossible de trouver l'utilisateur connecté", 500);
        }

        $code = CodeParrainageInvitation::where('code_reduction', $value)->first();
        if ($code != null) {
            if ($code->is_invalid) {
                return false;
            }

            if ($code->user_type !== (new Artisan())->getMorphClass()) {
                $this->errors = ['Seul un code de parrainage artisan peut être utilisé.'];

                return false;
            }

            if ($code->user()->is($usersArtisan->artisans->first())) {
                $this->errors = ['Vous ne pouvez utiliser votre propre code de parrainage.'];

                return false;
            }
        } else {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     */
    public function message(): array
    {
        return $this->errors;
    }
}
