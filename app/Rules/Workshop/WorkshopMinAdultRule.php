<?php

declare(strict_types=1);

namespace App\Rules\Workshop;

use App\Models\Atelier;
use Illuminate\Contracts\Validation\ValidationRule;

class WorkshopMinAdultRule implements ValidationRule
{
    public function __construct(
        protected Atelier $workshop
    ) {
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $minAllowed = $this->workshop->getFormatMinForAdult();

        if ((int) $value < $minAllowed) {
            $fail('rules.workshop.min_for_adult')->translate([
                'min' => $minAllowed,
            ]);
        }
    }
}
