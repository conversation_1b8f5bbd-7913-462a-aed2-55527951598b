<?php

declare(strict_types=1);

namespace App\Rules;

use App\Domain\Content\Models\SinglePage;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * @SuppressWarnings(PHPMD.UnusedFormalParameter) Cannot remove __invoke $attribute parameter since it is given by the framework anyway
 */
class UrlNotUsedBySinglePageRule implements ValidationRule
{
    /**
     * @param array<array<string|int>> $except
     */
    public function __construct(protected array $except = [])
    {
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (SinglePage::query()->where('slug', $value)->exists()) {
            $fail('validation.url_not_used.pages')->translate();
        }
    }
}
