<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\Rule;

class UniqueMultiple implements Rule, DataAwareRule
{
    protected array $data = [];

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(
        protected readonly string $table,
        protected readonly array $columns,
        protected readonly mixed $exceptDataKeyValue = null,
        protected readonly string $exceptDbKeyName = 'id'
    ) {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     */
    public function passes($attribute, $value): bool
    {
        try {
            $query = \DB::table($this->table);
            foreach ($this->columns as $key => $column) {
                if (\is_string($key)) {
                    $query->where($column, $this->data[$key] ?? null);
                    continue;
                }

                $query->where($column, $this->data[$column] ?? null);
            }

            if (!empty($this->exceptDbKeyName) && !empty($this->exceptDataKeyValue) && isset($this->data[$this->exceptDataKeyValue])) {
                $query->whereNot($this->exceptDbKeyName, $this->data[$this->exceptDataKeyValue]);
            }

            return !$query->exists();
        } catch (\Exception $e) {
            report($e);

            return false;
        }
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        $columns = [];
        foreach ($this->columns as $key => $column) {
            if (\is_string($key)) {
                $columns[] = $key;
                continue;
            }

            $columns[] = $column;
        }

        return 'The group of attributes ['.implode(',', $columns).'] must be unique';
    }

    public function setData($data): static
    {
        $this->data = $data;

        return $this;
    }
}
