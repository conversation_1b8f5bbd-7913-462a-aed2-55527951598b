<?php

namespace App\Rules;

use App\Models\Reservation;
use Illuminate\Contracts\Validation\Rule;

class MaxMontantRembourse implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct(protected Reservation $product)
    {
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     */
    public function passes($attribute, $value): bool
    {
        return $value <= $this->product->getMaxRemboursement();
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'Vous ne pouvez pas rembourser plus de '.currencyFormat($this->product->getMaxRemboursement(), $this->product->currency).'.';
    }
}
