<?php

declare(strict_types=1);

namespace App\Rules\Permalinks;

use Illuminate\Contracts\Validation\ValidationRule;

class ArtisanPermalinkRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (preg_match('/^[a-z]+(?:-[a-z0-9]+)*$/', $value) !== 1) {
            $fail("Le permalien ne suit pas le format demandé (minuscules sans accents ni espace et avec des traits d'union)");
        }
    }
}
