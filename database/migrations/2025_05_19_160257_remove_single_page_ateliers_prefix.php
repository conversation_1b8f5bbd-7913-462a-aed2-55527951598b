<?php

use Illuminate\Database\Migrations\Migration;

return new class() extends Migration {
    protected array $specificChanges = [
        '/cadeau/saint-valentin/elle' => 'saint-valentin-elle',
        '/cadeau/paris/fete-des-peres' => 'paris-fete-des-peres',
    ];

    public function up(): void
    {
        DB::table('single_pages')
            ->where('slug', 'like', '/ateliers/%')
            ->update([
                'slug' => DB::raw("REPLACE(slug, '/ateliers/', '')"),
            ]);

        foreach ($this->specificChanges as $previous => $new) {
            DB::table('single_pages')
                ->whereLike('slug', "%$previous%")
                ->update([
                    'slug' => $new,
                ]);
        }
    }

    public function down(): void
    {
        DB::table('single_pages')
            ->where('slug', 'not like', '/ateliers/%')
            ->update([
                'slug' => DB::raw("CONCAT('/ateliers/', slug)"),
            ]);

        foreach ($this->specificChanges as $previous => $new) {
            DB::table('single_pages')
                ->whereLike('slug', "%$new%")
                ->update([
                    'slug' => $previous,
                ]);
        }
    }
};
