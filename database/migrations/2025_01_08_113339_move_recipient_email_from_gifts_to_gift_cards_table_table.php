<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gift_cards', function (Blueprint $table): void {
            $table->string('contact_email')->nullable()->after('is_gift');
        });

        DB::update("UPDATE gift_cards
SET contact_email = (
    SELECT recipient_email
    FROM gifts
    WHERE gifts.subject_id = gift_cards.id
    AND gifts.subject_type =  'App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard'
);");

        Schema::table('gifts', function (Blueprint $table): void {
            $table->dropColumn('recipient_email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gifts', function (Blueprint $table): void {
            $table->string('recipient_email')->nullable()->after('to');
        });

        DB::update("UPDATE gifts
SET recipient_email = (
    SELECT contact_email
    FROM gift_cards
    WHERE gifts.subject_id = gift_cards.id
    AND gifts.subject_type =  'App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard'
);");

        Schema::dropColumns('gift_cards', 'contact_email');
    }
};
