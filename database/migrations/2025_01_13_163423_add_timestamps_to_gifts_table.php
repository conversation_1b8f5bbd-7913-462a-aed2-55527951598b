<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gifts', function (Blueprint $table): void {
            $table->timestamps();
        });

        DB::update("UPDATE gifts
JOIN (
	SELECT
	    id,
		created_at
	FROM
		gift_cards
) AS data_to_update ON gifts.subject_id = data_to_update.id
AND gifts.subject_type = 'App\\\\Domain\\\\Gift\\\\Models\\\\GiftCard'
SET gifts.created_at = data_to_update.created_at,
gifts.updated_at = data_to_update.created_at;");

        DB::update("UPDATE gifts
JOIN (
	SELECT
	    id,
		created_at
	FROM
		reservations
) AS data_to_update ON gifts.subject_id = data_to_update.id
AND gifts.subject_type = 'App\\\\Models\\\\Reservation'
SET gifts.created_at = data_to_update.created_at,
gifts.updated_at = data_to_update.created_at;");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gifts', function (Blueprint $table): void {
            $table->dropTimestamps();
        });
    }
};
