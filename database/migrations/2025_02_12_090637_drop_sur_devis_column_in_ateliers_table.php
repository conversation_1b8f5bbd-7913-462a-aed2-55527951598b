<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ateliers', function (Blueprint $table): void {
            $table->dropColumn('sur_devis');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ateliers', function (Blueprint $table): void {
            $table->boolean('sur_devis')->default(false);
        });
    }
};
