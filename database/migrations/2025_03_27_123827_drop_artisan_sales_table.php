<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    public function up(): void
    {
        Schema::dropIfExists('artisan_sales');
    }

    public function down(): void
    {
        DB::statement(
            <<<SQL
                CREATE TABLE `artisan_sales` (
                  `source` enum('reservations','gift_cards') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'The source table',
                  `type` enum('booking','gift') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'A source table alias',
                  `id` bigint unsigned NOT NULL COMMENT 'The id in the source table',
                  `booking_or_gift_id` bigint unsigned NOT NULL COMMENT 'Deprecated, same as id',
                  `created_at` timestamp NOT NULL,
                  `order_date` timestamp NOT NULL COMMENT 'Deprecated, same as created_at',
                  `price` double(8,2) NOT NULL,
                  `order_id` bigint unsigned NOT NULL,
                  `slots_count` int NOT NULL,
                  `status` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Can be a booking or a gift card status',
                  `workshop_id` bigint unsigned NOT NULL,
                  `workshop_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                  `artisan_id` bigint unsigned NOT NULL,
                  `artisan_name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                  UNIQUE KEY `artisan_sales_source_id_unique` (`source`,`id`),
                  KEY `artisan_sales_type_index` (`type`),
                  KEY `artisan_sales_created_at_index` (`created_at`),
                  KEY `artisan_sales_order_date_index` (`order_date`),
                  KEY `artisan_sales_status_index` (`status`),
                  KEY `artisan_sales_order_id_index` (`order_id`),
                  KEY `artisan_sales_workshop_id_index` (`workshop_id`),
                  KEY `artisan_sales_artisan_id_index` (`artisan_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                SQL
        );
    }
};
