<?php

declare(strict_types=1);

namespace Database\Seeders\Tables;

use App\Domain\Admin\Enums\RoleEnum;
use App\Domain\Admin\Enums\TeamEnum;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeamSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('teams')->insert(
            [
                [
                    'id' => 1,
                    'name' => TeamEnum::Relation_Artisan->name,
                    'shortname' => TeamEnum::Relation_Artisan->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 2,
                    'name' => TeamEnum::Operations_Support->name,
                    'shortname' => TeamEnum::Operations_Support->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 3,
                    'name' => TeamEnum::Tech_Product_Data->name,
                    'shortname' => TeamEnum::Tech_Product_Data->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 4,
                    'name' => TeamEnum::Marketing->name,
                    'shortname' => TeamEnum::Marketing->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 5,
                    'name' => TeamEnum::B2B->name,
                    'shortname' => TeamEnum::B2B->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 6,
                    'name' => TeamEnum::Admin_RH_Fin->name,
                    'shortname' => TeamEnum::Admin_RH_Fin->value,
                    'type' => 'functional',
                ],
                [
                    'id' => 7,
                    'name' => TeamEnum::External->name,
                    'shortname' => TeamEnum::External->value,
                    'type' => 'functional',
                ],
            ]
        );

        DB::table('roles')->insert(
            [
                [
                    'id' => 1,
                    'label' => RoleEnum::Administrateur->value,
                    'shortlabel' => RoleEnum::Administrateur->value,
                ],
                [
                    'id' => 2,
                    'label' => RoleEnum::Responsable_Ville->value,
                    'shortlabel' => RoleEnum::Responsable_Ville->value,
                ],
                [
                    'id' => 3,
                    'label' => RoleEnum::Responsable_Content->value,
                    'shortlabel' => RoleEnum::Responsable_Content->value,
                ],
                [
                    'id' => 4,
                    'label' => RoleEnum::Utilisateur->value,
                    'shortlabel' => RoleEnum::Utilisateur->value,
                ],
                [
                    'id' => 5,
                    'label' => RoleEnum::ESAT->value,
                    'shortlabel' => RoleEnum::ESAT->value,
                ],
                [
                    'id' => 6,
                    'label' => RoleEnum::HttpRedirectManager->value,
                    'shortlabel' => RoleEnum::HttpRedirectManager->value,
                ],
            ],
        );

        DB::table('teams_roles')->insert(
            [
                [
                    'team_id' => 1,
                    'role_id' => 1,
                ],
                [
                    'team_id' => 1,
                    'role_id' => 2,
                ],
                [
                    'team_id' => 2,
                    'role_id' => 1,
                ],
                [
                    'team_id' => 3,
                    'role_id' => 3,
                ],
                [
                    'team_id' => 4,
                    'role_id' => 1,
                ],
                [
                    'team_id' => 4,
                    'role_id' => 2,
                ],
            ]
        );
    }
}
