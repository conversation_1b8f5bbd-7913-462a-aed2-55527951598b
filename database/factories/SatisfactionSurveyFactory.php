<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Atelier;
use App\Models\Reservation;
use App\Models\SatisfactionSurvey;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends Factory<SatisfactionSurvey> */
class SatisfactionSurveyFactory extends Factory
{
    protected $model = SatisfactionSurvey::class;

    /** @return array<string, mixed> */
    public function definition(): array
    {
        $experienceRating = rand(0, 10);

        return [
            'nps_wecandoo' => rand(0, 10),
            'experience_rating' => $experienceRating,
            'comment_for_artisan' => $this->faker->realText(),
            'event_date' => $this->faker->date(),
            'has_loved_experience' => $experienceRating > 6,
        ];
    }

    public function forBooking(Reservation $booking): self
    {
        return $this->state([
            'booking_id' => $booking->getKey(),
            'event_date' => $booking->evenement->start->toDateString(),
        ]);
    }

    public function forWorkshop(Atelier $workshop): self
    {
        return $this->state([
            'workshop_id' => $workshop->getKey(),
        ]);
    }
}
