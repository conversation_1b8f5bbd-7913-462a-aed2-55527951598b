<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Accounting\Orders\Models\OrderItem;
use App\Domain\Accounting\Orders\Models\OrderItemGiftCard;
use App\Enums\Currency;
use App\Models\Adresse;
use App\Models\Atelier;
use App\Models\Commande;
use App\Models\ProduitComplementaire;
use App\Models\Reservation;
use App\Shared\Amount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<OrderItem>
 */
class OrderItemFactory extends Factory
{
    protected $model = OrderItem::class;

    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => 'item',
        ];
    }

    public function withAmount(Amount $amount, int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'total_price' => $amount,
            'final_price' => $amount,
            'discount_amount' => Amount::make(0, $amount->currency()),
            'unit_price' => $amount->divide($quantity),
            'quantity' => $quantity,
        ]);
    }

    public function forWorkshop(Atelier $workshop, int $quantity): static
    {
        $currency = Currency::from($workshop->currency);

        return $this->state(fn (array $attributes) => [
            'total_price' => Amount::fromFloat($workshop->prix, $currency)->multiply($quantity),
            'final_price' => Amount::fromFloat($workshop->prix, $currency)->multiply($quantity),
            'discount_amount' => Amount::make(0, $currency),
            'unit_price' => Amount::fromFloat($workshop->prix, $currency),
            'quantity' => $quantity,
        ]);
    }

    public function forOrderItemGiftCard(OrderItemGiftCard $orderItemGiftCard): static
    {
        return $this->state(fn () => [
            'item_type' => $orderItemGiftCard->getMorphClass(),
            'item_id' => $orderItemGiftCard->id,
        ]);
    }

    public function forOrder(Commande $order): static
    {
        return $this->state(fn () => [
            'order_id' => $order->id,
            'currency' => $order->currency,
        ]);
    }

    public function produitComplementaireOf(OrderItem $orderItem, Adresse $adresse, ?float $price = null): static
    {
        return $this->state(fn () => [
            'order_id' => $orderItem->order_id,
            'parent_id' => $orderItem->id,
            'total_price' => Amount::fromFloat($price ?? 5, $orderItem->currency),
            'final_price' => Amount::fromFloat($price ?? 5, $orderItem->currency),
            'unit_price' => Amount::fromFloat($price ?? 5, $orderItem->currency),
            'quantity' => 1,
            'discount_amount' => Amount::fromFloat(0, $orderItem->currency),
            'delivery_address_id' => $adresse->id,
            'delivery_address_recipient' => $adresse->destinataire,
            'delivery_address' => $adresse->adresse1,
            'delivery_address_postal_code' => $adresse->code_postal,
            'delivery_address_city' => $adresse->ville,
            'delivery_address_country' => $adresse->pays,
        ]);
    }

    public function forBooking(Reservation $booking, array $orderItemTicketParams = []): static
    {
        $currency = Currency::from($booking->currency);
        $price = Amount::fromFloat($booking->prix, $currency);
        $unitPrice = Amount::fromFloat($booking->prix / $booking->nb_places, $currency);

        return $this
            ->state(fn () => [
                'total_price' => $price,
                'final_price' => $price,
                'discount_amount' => Amount::fromFloat(0, $currency),
                'unit_price' => $unitPrice,
                'quantity' => $booking->nb_places,
            ])
            ->afterMaking(function (OrderItem $orderItem) use ($booking, $orderItemTicketParams): void {
                $orderItemTicket = OrderItemTicketFactory::new($orderItemTicketParams)->fromBooking($booking)->create();

                $orderItem->name = $booking->evenement->atelier->nom;
                $orderItem->item()->associate($orderItemTicket);
            });
    }

    public function forAdditionalProduct(ProduitComplementaire $additionalProduct): static
    {
        return $this->afterMaking(fn (OrderItem $orderItem) => $orderItem->item()->associate($additionalProduct));
    }
}
