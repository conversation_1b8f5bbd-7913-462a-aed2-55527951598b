<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Thread\Models\Thread;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Model;

/** @extends Factory<Thread> */
class ThreadFactory extends Factory
{
    protected $model = Thread::class;

    /** @return array<string, mixed> */
    public function definition(): array
    {
        return [];
    }

    public function forAssociable(Model $associable): static
    {
        return $this->state([
            'associable_type' => $associable->getMorphClass(),
            'associable_id' => $associable->getKey(),
        ]);
    }
}
