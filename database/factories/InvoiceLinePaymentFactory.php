<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Accounting\Invoices\Models\InvoiceLine;
use App\Domain\Accounting\Invoices\Models\InvoiceLinePaymentMethod;
use App\Domain\Accounting\Orders\Enums\PaymentMethodType;
use App\Enums\Currency;
use App\Shared\Amount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<InvoiceLinePaymentMethod>
 */
class InvoiceLinePaymentFactory extends Factory
{
    protected $model = InvoiceLinePaymentMethod::class;

    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'invoice_line_id' => InvoiceLineFactory::new(),
            'amount' => Amount::fromFloat(50, Currency::EURO),
            'tickets' => 0,
            'currency' => Currency::EURO,
            'type' => PaymentMethodType::Stripe,
            'payment_type' => null,
            'payment_id' => null,
        ];
    }

    public function withWorkshopGiftCard(int $tickets = 1): static
    {
        return $this->state(fn () => [
            'tickets' => $tickets,
            'type' => PaymentMethodType::WorkshopGiftCard,
        ]);
    }

    public function forInvoiceLine(InvoiceLine $invoiceLine): static
    {
        return $this->state(fn () => [
            'invoice_line_id' => $invoiceLine->id,
        ]);
    }
}
