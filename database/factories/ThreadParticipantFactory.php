<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Thread\Models\Thread;
use App\Domain\Thread\Models\ThreadParticipant;
use App\Models\Artisan;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends Factory<ThreadParticipant> */
class ThreadParticipantFactory extends Factory
{
    protected $model = ThreadParticipant::class;

    /** @return array<string, mixed> */
    public function definition(): array
    {
        return [];
    }

    public function forUser(User $user): static
    {
        return $this->state([
            'participant_type' => $user->getMorphClass(),
            'participant_id' => $user->getKey(),
        ]);
    }

    public function forArtisan(Artisan $artisan): static
    {
        return $this->state([
            'participant_type' => $artisan->getMorphClass(),
            'participant_id' => $artisan->getKey(),
        ]);
    }

    public function forThread(Thread $thread): static
    {
        return $this->state([
            'thread_id' => $thread->getKey(),
        ]);
    }
}
