<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Content\Abstracts\CategorizationModel;
use App\Domain\Content\Enums\SinglePageMode;
use App\Domain\Content\Models\SinglePage;
use App\Domain\Content\SinglePageService;
use App\Models\Ville;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<SinglePage>
 */
class SinglePageFactory extends Factory
{
    protected $model = SinglePage::class;

    public function definition(): array
    {
        return [
            'slug' => fn () => $this->faker->slug(),
            'subject_type' => fn () => Ville::class,
            'subject_id' => fn () => VilleFactory::new(['active' => true]),
            'mode' => SinglePageMode::Standard,
            'title' => fn () => $this->faker->words(3, true),
            'description' => fn () => $this->faker->sentences(3, true),
            'meta_title' => fn () => $this->faker->words(3, true),
            'meta_description' => fn () => $this->faker->sentences(3, true),
            'seo_title' => fn () => $this->faker->words(3, true),
            'seo_content' => fn () => $this->faker->randomHtml(),
            'hero_image' => fn () => SinglePageService::DEFAULT_FOLDER.'/'.$this->faker->uuid().'.jpeg',
            'badge_image' => fn () => SinglePageService::DEFAULT_FOLDER.'/'.$this->faker->uuid().'.jpeg',
            'publish_at' => fn () => null,
            'expires_at' => fn () => null,
            'country_code' => fn () => null,
        ];
    }

    public function forSubject(CategorizationModel $model): static
    {
        return $this->state(fn () => [
            'subject_type' => $model::class,
            'subject_id' => $model->getKey(),
            'slug' => $model->getSlug(),
        ]);
    }

    /**
     * @param array<int, SinglePageSectionFactory> $sections
     */
    public function hasSections(array $sections): static
    {
        $self = $this;

        foreach ($sections as $key => $section) {
            $self = $self->has($section->withOrder($key), 'sections');
        }

        return $self;
    }
}
