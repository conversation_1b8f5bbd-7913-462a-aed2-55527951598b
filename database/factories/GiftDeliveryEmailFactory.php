<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Domain\Gift\Models\Gift;
use App\Domain\Gift\Models\GiftDeliveryEmail;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<GiftDeliveryEmail>
 */
class GiftDeliveryEmailFactory extends Factory
{
    protected $model = GiftDeliveryEmail::class;

    /**
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'gift_id' => fn () => GiftFactory::new(),
            'email' => fn () => $this->faker->email(),
            'date' => fn () => Carbon::tomorrow(),
            'sent_at' => null,
        ];
    }

    public function forGift(Gift $gift): static
    {
        return $this->state([
            'gift_id' => $gift->getKey(),
        ]);
    }
}
