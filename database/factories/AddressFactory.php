<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Adresse;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/** @extends Factory<Adresse> */
class AddressFactory extends Factory
{
    protected $model = Adresse::class;

    public function definition(): array
    {
        return [
            'adresse1' => $this->faker->streetAddress(),
            'ville' => $this->faker->city(),
            'code_postal' => $this->faker->postcode(),
            'informations_complementaires' => null,
            'default_facturation' => true,
            'societe' => null,
            'adresse2' => $this->faker->streetAddress(),
            'pays' => 'France',
            'user_id' => null,
            'destinataire' => '',
            'region' => null,
            'telephone_contact' => null,
            'nom' => null,
            'created_at' => fn () => Carbon::now(),
            'updated_at' => fn () => Carbon::now(),
        ];
    }
}
