<?php

$cacheFiles = [
    '.cache/phpstan/resultCache.php',
    '.cache/phpstan-max/resultCache.php',
    '.cache/phpstan-max-global/resultCache.php',
];

foreach ($cacheFiles as $cacheFile) {
    if (!file_exists($cacheFile)) {
        continue;
    }
    // Replace the date of last analysis by a recent one, as the cache is not used if older than 7 days, and we don't want this behavior
    file_put_contents($cacheFile, preg_replace("#'lastFullAnalysisTime'\s*=>\s*\d+,#", "'lastFullAnalysisTime' => ".(time() - 1).',', file_get_contents($cacheFile)));
}
