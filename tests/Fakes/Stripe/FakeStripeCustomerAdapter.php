<?php

declare(strict_types=1);

namespace Tests\Fakes\Stripe;

use App\Enums\StripeAccount;
use App\Payment\Interfaces\CustomerGateway;
use App\Payment\Types\CustomerResponse;
use Illuminate\Support\Str;

readonly class FakeStripeCustomerAdapter implements CustomerGateway
{
    public function __construct(private ?string $expectedResponseId = null)
    {
    }

    public function createUser(StripeAccount $account, string $name, string $email): CustomerResponse
    {
        return new CustomerResponse($this->expectedResponseId ?? Str::random(15));
    }

    public function delete(StripeAccount $account, string $customerId): void
    {
        // nothing to do
    }
}
