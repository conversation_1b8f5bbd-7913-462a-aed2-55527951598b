<?php

declare(strict_types=1);

namespace Tests\Fakes\Crm;

use App\Infrastructure\Crm\Customerio\Handlers\CustomerioHandler;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Testing\Assert;

class CustomerioFakeHandler extends CustomerioHandler
{
    /** @var array<string, int> */
    private array $callsCount = [];

    /** @var array<int, array<int, mixed>> */
    private array $trackCalls = [];

    /** @var array<int, int> */
    private array $usersUpdatedInBatch = [];

    /** @var array<int, int> */
    private array $userEventByBatch = [];

    public function isApiDriver(): bool
    {
        return true;
    }

    public function addOrUpdateCustomer(User $user, array $overriddenData = []): void
    {
    }

    public function addOrUpdateCustomerBatch(Collection $users): void
    {
        $this->usersUpdatedInBatch = array_merge($this->usersUpdatedInBatch, $users->pluck('id')->toArray());
    }

    public function addOrUpdateGuest(string $email, array $data = []): void
    {
        $this->trackCall(__FUNCTION__, array_merge(['email' => $email], $data));
    }

    /**
     * @SuppressWarnings(PHPMD.BooleanArgumentFlag)
     */
    public function trackCustomerEvent(User $user, string $eventName, array $data = [], ?int $timestamp = null, bool $forceCreateOrUpdateUser = false): void
    {
        $this->trackFunctionCall(__FUNCTION__, "eventName=$eventName");
    }

    public function trackCustomerEventBatch(array $batch): void
    {
        $this->userEventByBatch = array_merge($this->userEventByBatch, $batch);
    }

    public function addCollection(string $collectionName, array $data): void
    {
        $this->trackCall(__FUNCTION__, $data);
    }

    public function updateCollection(int $collectionId, array $data): void
    {
    }

    public function allCollections(): array
    {
        return [];
    }

    protected function trackCall(string $function, array $params): void
    {
        $this->trackCalls[$function] = $params;
    }

    public function assertFunctionCalledWithParams($function, array $params = []): void
    {
        Assert::assertTrue(
            isset($this->trackCalls[$function]),
            'Failed asserting function was called at least once.'
        );

        Assert::assertEquals(
            $params,
            $this->trackCalls[$function] ?? [],
            'Failed asserting function was called with expected parameters.'
        );
    }

    protected function trackFunctionCall(string $function, string $paramsString = ''): void
    {
        if (!isset($this->callsCount[$function])) {
            $this->callsCount[$function] = 0;
        }

        if (!isset($this->callsCount[$function.$paramsString])) {
            $this->callsCount[$function.$paramsString] = 0;
        }

        ++$this->callsCount[$function];
        ++$this->callsCount[$function.$paramsString];
    }

    public function assertCustomerEventTrackedNTimesWithParams(array $params, int $count = 1): void
    {
        $formatedParams = [];
        foreach ($params as $key => $param) {
            $formatedParams[] = $key.'='.$param;
        }

        $paramsString = implode('+', $formatedParams);

        Assert::assertEquals(
            $count,
            $this->callsCount['trackCustomerEvent'.$paramsString] ?? 0,
            "Failed asserting trackCustomerEvent was called [$count] times with params : $paramsString"
        );
    }

    public function assertUsersWasUpdatedInBatch(array $userIds): void
    {
        Assert::assertEquals(
            $userIds,
            $this->usersUpdatedInBatch,
            'Failed asserting users were updated in batch.'
        );
    }

    public function assertEventWasSentByBatch(array $data): void
    {
        Assert::assertEquals(
            $data,
            $this->userEventByBatch,
            'Failed asserting users were updated in batch.'
        );
    }
}
