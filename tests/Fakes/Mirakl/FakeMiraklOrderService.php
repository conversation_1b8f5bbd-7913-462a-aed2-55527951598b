<?php

declare(strict_types=1);

namespace Tests\Fakes\Mirakl;

use App\Domain\Marketplace\Services\MiraklApi\OrderGateway;
use App\Domain\Marketplace\Services\MiraklApi\Types\Order;
use App\Shared\Uuid;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use PHPUnit\Framework\Assert;
use PHPUnit\Framework\Attributes\Test;

class FakeMiraklOrderService implements OrderGateway
{
    /** @var string[] */
    private array $acceptedOrders = [];

    /** @var string[] */
    private array $refusedOrders = [];

    /** @var array<string, CarbonImmutable> */
    private array $consumedOrders = [];

    /** @var string[] */
    private array $fetchAcceptedOrders = [];
    private int $expectedOrdersCursor = 0;

    private readonly Collection $expectedPendingOrders;
    private readonly Collection $expectedOrders;

    public function __construct(
        $expectedPendingOrders = null,
        $expectedOrders = null,
        private readonly ?\Exception $fetchAcceptedOrderException = null,
        private readonly ?\Exception $acceptOrderException = null,
    ) {
        $this->expectedPendingOrders = $expectedPendingOrders ?? new Collection();
        $this->expectedOrders = $expectedOrders ?? new Collection();
    }

    public function getPendingOrders(): Collection
    {
        return $this->expectedPendingOrders ?? new Collection();
    }

    public function getOrder(string $orderId): Order
    {
        if ($this->fetchAcceptedOrderException !== null) {
            throw $this->fetchAcceptedOrderException;
        }

        $this->fetchAcceptedOrders[] = $orderId;

        return $this->expectedOrders->isEmpty() ?
            new Order(
                Str::random(20),
                'WAITING_ACCEPTANCE',
                (string) Uuid::new(),
                50,
                1,
                1,
                Str::random(15),
                Str::random(50).'@example.com',
                'Didier',
                'Super',
                'My company',
                'Did',
                'Sup',
                '123 street',
                '456th floor',
                'elevator is down',
                '69100',
                'Lyon 10',
                'France',
            ) :
            $this->expectedOrders->get($this->expectedOrdersCursor++);
    }

    public function acceptOrder(string $orderId): void
    {
        if ($this->acceptOrderException !== null) {
            throw $this->acceptOrderException;
        }

        $this->acceptedOrders[] = $orderId;
    }

    public function refuseOrder(string $orderId): void
    {
        $this->refusedOrders[] = $orderId;
    }

    #[Test]
    public function consumeOrder(string $orderId, CarbonImmutable $consumptionDate): void
    {
        $this->consumedOrders[$orderId] = $consumptionDate;
    }

    public function assertOrderAcceptationCalledForOrderId(string $orderId): void
    {
        Assert::assertTrue(
            \in_array($orderId, $this->acceptedOrders, true),
            "Failed asserting order #{$orderId} was accepted"
        );
    }

    public function assertOrderAcceptationNotCalledForOrderId(string $orderId): void
    {
        Assert::assertFalse(
            \in_array($orderId, $this->acceptedOrders, true),
            "Failed asserting order #{$orderId} was not accepted"
        );
    }

    public function assertOrderRefusalCalledForOrderId(string $orderId): void
    {
        Assert::assertTrue(
            \in_array($orderId, $this->refusedOrders, true),
            "Failed asserting order #{$orderId} was refused"
        );
    }

    public function assertOrderRefusalNotCalledForOrderId(string $orderId): void
    {
        Assert::assertFalse(
            \in_array($orderId, $this->refusedOrders, true),
            "Failed asserting order #{$orderId} was not refused"
        );
    }

    public function assertFetchAcceptedOrderCalledForOrderId(string $orderId): void
    {
        Assert::assertTrue(
            \in_array($orderId, $this->fetchAcceptedOrders, true),
            "Failed asserting order #{$orderId} was fetched"
        );
    }

    public function assertOrderConsumptionCalledForOrder(string $orderId, ?CarbonInterface $date = null): void
    {
        Assert::assertArrayHasKey(
            $orderId,
            $this->consumedOrders,
            "Failed asserting order #{$orderId} consumption endptions was called"
        );

        if ($date !== null) {
            Assert::assertEquals(
                $date->toDateTimeString(),
                $this->consumedOrders[$orderId]?->toDateTimeString(),
                "Failed asserting order #{$orderId} consumption endptions was called"
            );
        }
    }

    public function assertOrderConsumptionNotCalled(): void
    {
        Assert::assertEmpty(
            $this->consumedOrders,
            'Failed asserting order consumption endptions was not called'
        );
    }
}
