<?php

declare(strict_types=1);

namespace Tests\Fakes\Mirakl;

use App\Domain\Marketplace\Services\MiraklApi\Exceptions\MiraklAPIValidationException;
use App\Domain\Marketplace\Services\MiraklApi\Types\Workshop;
use App\Domain\Marketplace\Services\MiraklApi\Types\WorkshopCreationResponse;
use App\Domain\Marketplace\Services\MiraklApi\WorkshopGateway;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;

class FailingMiraklWorkshopService extends FakeMiraklWorkshopService implements WorkshopGateway
{
    public function __construct(
        private readonly ?\Exception $expectedException = null,
    ) {
        parent::__construct();
    }

    public function create(Workshop $workshop, Collection $events): WorkshopCreationResponse
    {
        parent::create($workshop, $events);

        throw $this->expectedException ?? new MiraklAPIValidationException('Mirakl fake implem exception');
    }

    public function updateStockByWorkshopId(string $workshopid, Collection $events): Collection
    {
        parent::updateStockByWorkshopId($workshopid, $events);

        throw $this->expectedException ?? new MiraklAPIValidationException('Mirakl fake implem exception');
    }

    public function update(string $workshopId, Workshop $workshop): void
    {
        parent::update($workshopId, $workshop);

        throw $this->expectedException ?? new MiraklAPIValidationException('Mirakl fake implem exception');
    }

    public function deactivate(string $workshopId, CarbonImmutable $date, Collection $events): Collection
    {
        parent::deactivate($workshopId, $date, $events);

        throw $this->expectedException ?? new MiraklAPIValidationException('Mirakl fake implem exception');
    }
}
