<?php

declare(strict_types=1);

namespace Tests\Fakes\Randomizer;

use App\Services\Randomizer\Randomizer;
use App\Services\Randomizer\RandomizerInterface;

use function PHPUnit\Framework\assertEquals;

class RandomizerFaker extends Randomizer implements RandomizerInterface
{
    /** @var array<scalar> */
    protected static array $fakes = [];

    /** @var array<string, int> */
    protected static array $callsCount = [];

    /**
     * @param array<scalar>|scalar $values
     */
    public static function fake(mixed $values): void
    {
        self::$fakes = array_merge(self::$fakes, \Arr::wrap($values));
    }

    public static function assertFunctionWasCalled(string $function, int $times = 1): void
    {
        assertEquals(
            $times,
            self::$callsCount[$function] ?? 0,
            "Failed asserting [$function] was called [$times] times"
        );
    }

    public static function resetTrackers(): void
    {
        self::$callsCount = [];
    }

    protected function shouldFake(): bool
    {
        return !empty(self::$fakes);
    }

    protected function getLastFake(): mixed
    {
        return array_shift(self::$fakes);
    }

    protected function trackFunctionCall(string $function): void
    {
        if (!isset(self::$callsCount[$function])) {
            self::$callsCount[$function] = 0;
        }

        ++self::$callsCount[$function];
    }

    public function mtRand(int $min, int $max): int
    {
        $this->trackFunctionCall(__FUNCTION__);

        if ($this->shouldFake()) {
            return $this->getLastFake();
        }

        return parent::mtRand($min, $max);
    }

    public function randomChars(int $length): string
    {
        $this->trackFunctionCall(__FUNCTION__);

        if ($this->shouldFake()) {
            return $this->getLastFake();
        }

        return parent::randomChars($length);
    }

    public function randomInt(int $min, int $max): int
    {
        $this->trackFunctionCall(__FUNCTION__);

        if ($this->shouldFake()) {
            return $this->getLastFake();
        }

        return parent::randomInt($min, $max);
    }
}
