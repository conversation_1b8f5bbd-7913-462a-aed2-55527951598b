<?php

declare(strict_types=1);

namespace Tests\Cities\Admin;

use App\Domain\Admin\Enums\RoleEnum;
use App\Domain\Admin\Enums\TeamEnum;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\CityCreator;
use Tests\Traits\WorkshopCreator;

class AdminCitiesTest extends TestCase
{
    use CityCreator;
    use WorkshopCreator;

    #[Test]
    public function an_admin_can_open_cities_page(): void
    {
        $this
            ->actingAsAdmin()
            ->get(route('admin.cities.index'))
            ->assertSuccessful();
    }

    #[Test]
    public function admin_cannot_delete_a_city_when_it_is_already_activated(): void
    {
        $admin = $this->createAdminWithTeamAndRole(TeamEnum::Tech_Product_Data, RoleEnum::Administrateur);
        $city = $this->createCityActive();

        $this->withoutExceptionHandling();
        $this
            ->actingAsAdmin($admin)
            ->delete(route('admin.cities.destroy', $city))
            ->assertRedirect()
            ->assertSessionHas('flash_message_danger');
    }

    #[Test]
    public function admin_cannot_delete_a_city_when_at_least_a_workshop_is_linked(): void
    {
        $admin = $this->createAdminWithTeamAndRole(TeamEnum::Tech_Product_Data, RoleEnum::Administrateur);
        $city = $this->createCityDisabled();
        $workshop = $this->createWorkshopForCity($city);

        $this->withoutExceptionHandling();
        $this
            ->actingAsAdmin($admin)
            ->delete(route('admin.cities.destroy', $city))
            ->assertRedirect()
            ->assertSessionHas('flash_message_danger');

        $workshop->delete();

        $this
            ->actingAsAdmin($admin)
            ->delete(route('admin.cities.destroy', $city))
            ->assertSessionHas('flash_message_success');
    }
}
