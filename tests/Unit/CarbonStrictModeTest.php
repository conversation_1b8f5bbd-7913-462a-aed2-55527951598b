<?php

declare(strict_types=1);

namespace Tests\Unit;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CarbonStrictModeTest extends TestCase
{
    #[Test]
    public function should_return_false_when_create_from_format_contain_invalid_format(): void
    {
        static::assertFalse(Carbon::createFromFormat('Y-m-d', 'invalid'));
        static::assertFalse(CarbonImmutable::createFromFormat('Y-m-d', 'invalid'));
    }
}
