<?php

declare(strict_types=1);

namespace Tests\Unit\PrivateBooking;

use App\Domain\PrivateBooking\Notifications\Sms\SmsNewPrivateBookingRequest;
use App\Listeners\RestricSmsToMobileListener;
use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Notifications\Events\NotificationSending;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\PrivateBookingRequestCreator;
use Tests\Traits\WorkshopCreator;

class RestricSmsToMobileListenerTest extends TestCase
{
    use ArtisanCreator;
    use PrivateBookingRequestCreator;
    use WorkshopCreator;

    private RestricSmsToMobileListener $listener;

    protected function setUp(): void
    {
        parent::setUp();

        $this->listener = new RestricSmsToMobileListener();
    }

    #[Test]
    public function anonymous_notifiable_with_mobile_number_pass(): void
    {
        $workshop = $this->createWorkshop();
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $notifiable = (new AnonymousNotifiable())->route('vonage', '+33623456789');
        $event = new NotificationSending($notifiable, $notification, 'vonage');

        $this->assertTrue($this->listener->handle($event));
    }

    #[Test]
    public function anonymous_notifiable_with_landline_number_dont_pass(): void
    {
        $workshop = $this->createWorkshop();
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $notifiable = (new AnonymousNotifiable())->route('vonage', '+33423456789');
        $event = new NotificationSending($notifiable, $notification, 'vonage');

        $this->assertFalse($this->listener->handle($event));
    }

    #[Test]
    public function anonymous_notifiable_on_mail_channel_pass(): void
    {
        $workshop = $this->createWorkshop();
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $notifiable = (new AnonymousNotifiable())->route('mail', '<EMAIL>');
        $event = new NotificationSending($notifiable, $notification, 'mail');

        $this->assertTrue($this->listener->handle($event));
    }

    #[Test]
    public function artisan_with_mobile_number_pass(): void
    {
        $artisan = $this->createArtisan(['phone' => '+33623456789', 'envoi_sms_phone' => true]);
        $workshop = $this->createWorkshopForArtisan($artisan);
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $event = new NotificationSending($artisan, $notification, 'vonage');

        $this->assertTrue($this->listener->handle($event));
    }

    #[Test]
    public function artisan_with_mobile_number_disabled_dont_pass(): void
    {
        $artisan = $this->createArtisan(['phone' => '+33623456789', 'envoi_sms_phone' => false]);
        $workshop = $this->createWorkshopForArtisan($artisan);
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $event = new NotificationSending($artisan, $notification, 'vonage');

        $this->assertFalse($this->listener->handle($event));
    }

    #[Test]
    public function artisan_with_landline_number_dont_pass(): void
    {
        $artisan = $this->createArtisan(['phone' => '+33423456789', 'envoi_sms_phone' => true]);
        $workshop = $this->createWorkshopForArtisan($artisan);
        $request = $this->createPrivateBookingRequestForWorkshop($workshop);
        $notification = new SmsNewPrivateBookingRequest($request);
        $event = new NotificationSending($artisan, $notification, 'vonage');

        $this->assertFalse($this->listener->handle($event));
    }
}
