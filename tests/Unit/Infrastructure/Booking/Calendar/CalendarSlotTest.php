<?php

declare(strict_types=1);

namespace Tests\Unit\Infrastructure\Booking\Calendar;

use App\Domain\Booking\Enums\CalendarEventStatus;
use App\Domain\Booking\Enums\CalendarEventType;
use App\Domain\Booking\Enums\CalendarMultipleEventsType;
use App\Domain\Booking\Enums\CalendarViewMode;
use App\Infrastructure\Booking\Calendar\CalendarSlot;
use App\Models\Artisan;
use App\Models\Atelier;
use Carbon\CarbonImmutable;
use Database\Factories\BookingFactory;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AdminCreator;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\EventCreator;
use Tests\Traits\WorkshopCreator;

final class CalendarSlotTest extends TestCase
{
    use AdminCreator;
    use ArtisanCreator;
    use EventCreator;
    use WorkshopCreator;

    private Artisan $artisan;
    private Atelier $workshopA;
    private Atelier $workshopB;
    private Atelier $workshopCumulativeWithA;

    protected function setUp(): void
    {
        parent::setUp();

        $this->artisan = $this->createArtisan();
        $this->workshopA = $this->createWorkshopForArtisan($this->artisan, ['nb_pers_min' => 2]);
        $this->workshopB = $this->createWorkshopForArtisan($this->artisan);
        $this->workshopCumulativeWithA = $this->createCumulativeWorkshopForWorkshop($this->workshopA);
    }

    #[Test]
    public function only_stacked_events(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createEventForWorkshopForDate($this->workshopA, $start);
        $eventB = $this->createEventForWorkshopForDate($this->workshopB, $start);

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Stacked;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertNull($calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(2, $calendarSlot->extendedProps['eventCount']);
    }

    #[Test]
    public function only_cumulative_events(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createEventForWorkshopForDate($this->workshopA, $start);
        $eventB = $this->createEventForWorkshopForDate($this->workshopCumulativeWithA, $start);

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Cumulative;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertNull($calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(2, $calendarSlot->extendedProps['eventCount']);
    }

    #[Test]
    public function two_cumulative_events_and_one_just_stacked(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createEventForWorkshopForDate($this->workshopA, $start);
        $eventB = $this->createEventForWorkshopForDate($this->workshopB, $start);
        $eventC = $this->createEventForWorkshopForDate($this->workshopCumulativeWithA, $start);

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB, $eventC]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Stacked;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertNull($calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(3, $calendarSlot->extendedProps['eventCount']);
    }

    #[Test]
    public function only_cumulative_events_and_all_are_cancelled(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createCancelledEventForWorkshopForDate($this->workshopA, $start);
        $eventB = $this->createEventForWorkshopForDate($this->workshopCumulativeWithA, $start);
        $eventB->cancel();
        $eventB->save();

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Cumulative;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertEquals(CalendarEventStatus::Cancelled, $calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(2, $calendarSlot->extendedProps['eventCount']);
    }

    #[Test]
    public function only_cumulative_events_and_just_one_is_cancelled(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createCancelledEventForWorkshopForDate($this->workshopA, $start);
        $eventB = $this->createEventForWorkshopForDate($this->workshopCumulativeWithA, $start);

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Cumulative;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertNull($calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(2, $calendarSlot->extendedProps['eventCount']);
    }

    #[Test]
    public function only_cumulative_events_with_one_event_that_reached_the_cut(): void
    {
        $start = CarbonImmutable::now()->addMonth()->setTime(9, 0);

        $eventA = $this->createEventForWorkshopForDate($this->workshopA, $start);

        $bookings = [
            BookingFactory::new()
                ->forPlaces(1),
            BookingFactory::new()
                ->forPlaces(2),
        ];
        $eventB = $this->createEventWithBookingForAtelierForDate($this->workshopCumulativeWithA, $start, $bookings);

        $calendarSlot = CalendarSlot::fromEventMultiple(collect([$eventA, $eventB]), CalendarViewMode::TimeGridWeek);

        $multipleType = CalendarMultipleEventsType::Cumulative;
        $title = __(sprintf('enums.booking.calendar_multiple_events_type.%s', $multipleType->value));

        $this->assertEquals($title, $calendarSlot->title);
        $this->assertSame($this->getExpectedKeys(), array_keys($calendarSlot->extendedProps));
        $this->assertEquals(CalendarEventStatus::Confirmed, $calendarSlot->extendedProps['status']);
        $this->assertEquals(CalendarEventType::Multiple, $calendarSlot->extendedProps['type']);
        $this->assertEquals($multipleType, $calendarSlot->extendedProps['multipleType']);
        $this->assertEquals(2, $calendarSlot->extendedProps['eventCount']);
    }

    /**
     * @return string[]
     */
    private function getExpectedKeys(): array
    {
        return [
            'id',
            'start',
            'end',
            'type',
            'multipleType',
            'status',
            'eventCount',
            'events',
            'participants',
            'maxParticipants',
            'duration',
            'isRecurring',
            'recurringId',
            'isPrivate',
            'timezone',
            'canCancel',
            'cantCancelReason',
            'canMove',
            'cantMoveReason',
            'admin',
            'html',
        ];
    }
}
