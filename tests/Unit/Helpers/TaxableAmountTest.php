<?php

declare(strict_types=1);

namespace Tests\Unit\Helpers;

use App\Enums\Currency;
use App\Shared\Amount;
use App\Shared\Percent;
use App\Shared\TaxableAmount;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TaxableAmountTest extends TestCase
{
    #[Test]
    public function taxable_amount_are_correctly_calculated(): void
    {
        $taxableAmount = TaxableAmount::make(
            Amount::make(100_00, Currency::EURO),
            Percent::make(20_00),
            originalAmountInclVat: true,
        );

        $this->assertEquals(Currency::EURO, $taxableAmount->currency());
        $this->assertEquals(100_00, $taxableAmount->inclVat()->value());
        $this->assertEquals(83_33, $taxableAmount->exclVat()->value());

        $taxableAmount = TaxableAmount::make(
            Amount::make(83_33, Currency::EURO),
            Percent::make(20_00),
            originalAmountInclVat: false,
        );

        $this->assertEquals(Currency::EURO, $taxableAmount->currency());
        $this->assertEquals(100_00, $taxableAmount->inclVat()->value());
        $this->assertEquals(83_33, $taxableAmount->exclVat()->value());
    }
}
