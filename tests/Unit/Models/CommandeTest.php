<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AmountCreator;
use Tests\Traits\Assertions\AmountAssertions;
use Tests\Traits\GiftCardCreator;
use Tests\Traits\OrderCreator;
use Tests\Traits\WorkshopCreator;

class CommandeTest extends TestCase
{
    use AmountAssertions;
    use AmountCreator;
    use GiftCardCreator;
    use OrderCreator;
    use WorkshopCreator;

    #[Test]
    public function a_simple_booking_in_an_order_is_not_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(50)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 1, ['prix' => 50]);

        $order->refresh();

        $this->assertFalse($order->isGift());
        $this->assertFloatAmountEquals(50, $order->getTotalPriceForClient());
        $this->assertFalse($order->getBoughtBookings()->first()?->isGift);
    }

    #[Test]
    public function a_booking_having_a_workshop_gift_card_for_the_same_workshop_in_an_order_is_not_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(130)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 1, ['prix' => 50]);
        $this->addEventBookingToOrder($order, $this->createWorkshopWithOneEvent()->evenements->first(), 1, ['prix' => 80]); // unrelated booking
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop));

        $order->refresh();

        $this->assertFalse($order->isGift());
        $this->assertFloatAmountEquals(80, $order->getTotalPriceForClient());
        $this->assertTrue($order->getBoughtBookings()->get(0)?->isGift);
        $this->assertFalse($order->getBoughtBookings()->get(1)?->isGift);
    }

    #[Test]
    public function a_simple_booking_partially_having_a_workshop_gift_card_for_the_same_workshop_in_an_order_is_not_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(80)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 2, ['prix' => 80]);
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 1);

        $order->refresh();

        $this->assertFalse($order->isGift());
        $this->assertFloatAmountEquals(80, $order->getTotalPriceForClient());
        $this->assertFalse($order->getBoughtBookings()->first()?->isGift);
    }

    #[Test]
    public function several_bookings_paid_with_a_unique_gift_card_are_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(180)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 1, ['prix' => 60]);
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 2, ['prix' => 120]);
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 3);
        $this->applyAmountGiftCardDiscountToOrder($order, $this->createAmountGiftCard(20)); // unrelated

        $order->refresh();

        $this->assertTrue($order->isGift());
        $this->assertFloatAmountEquals(0, $order->getTotalPriceForClient());
        $this->assertTrue($order->getBoughtBookings()->get(0)?->isGift);
        $this->assertTrue($order->getBoughtBookings()->get(1)?->isGift);
    }

    #[Test]
    public function several_bookings_paid_with_a_several_gift_cards_are_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(100)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 1, ['prix' => 20]);
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 4, ['prix' => 80]);
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 3);
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 2);

        $this->assertTrue($order->isGift());
        $this->assertFloatAmountEquals(0, $order->getTotalPriceForClient());
        $this->assertTrue($order->getBoughtBookings()->get(0)?->isGift);
        $this->assertTrue($order->getBoughtBookings()->get(1)?->isGift);
    }

    #[Test]
    public function several_bookings_partially_paid_with_a_unique_gift_card_are_considered_as_gift(): void
    {
        $order = $this->createOrder(['total_price' => $this->euros(300)]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 3, ['prix' => 180]);
        $this->addEventBookingToOrder($order, $workshop->evenements->first(), 2, ['prix' => 120]);
        $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 3);

        $this->assertFalse($order->isGift());
        $this->assertFloatAmountEquals(300, $order->getTotalPriceForClient());
        $this->assertFalse($order->getBoughtBookings()->get(0)?->isGift);
        $this->assertFalse($order->getBoughtBookings()->get(1)?->isGift);
    }
}
