<?php

declare(strict_types=1);

namespace Tests\Feature\Boxs;

use App\Domain\Box\Enums\BoxLotStatus;
use App\Domain\Box\Enums\CadeauBoxStatus;
use App\Domain\Box\Models\BoxLot;
use App\Models\Reductions\CadeauBox;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\BoxCreator;
use Tests\Traits\CadeauBoxCreator;
use Tests\Traits\CompanyCreator;

class CadeauBoxAdminTest extends TestCase
{
    use BoxCreator;
    use CadeauBoxCreator;
    use CompanyCreator;

    #[Test]
    public function can_load_box_list(): void
    {
        $box = $this->createBox();

        $this
            ->actingAsAdmin()
            ->get(route('admin.box.product.list'))
            ->assertSuccessful()
            ->assertSee($box->nom);
    }

    #[Test]
    public function can_generate_boxes(): void
    {
        $box = $this->createBox();
        $this
            ->actingAsAdmin()
            ->fromRoute('admin.box.product.list')
            ->post(route('admin.box.product.generate-boxes'), [
                'isPhysical' => false,
                'activate' => true,
                'quantity' => 1,
                'productId' => $box->getKey(),
                'company_id' => 1,
            ])
            ->assertRedirectToRoute('admin.box.product.list')
            ->assertSessionHas('flash_message', 'The production batch is being generated');

        $this->assertDatabaseHas(BoxLot::class, [
            'box_quantity' => 1,
            'box_generated' => 1,
            'status' => BoxLotStatus::Created,
        ]);

        $this->assertDatabaseHas(CadeauBox::class, [
            'prix' => $box->prix,
            'box_id' => $box->getKey(),
            'est_bon_physique' => false,
            'date_peremption' => Carbon::today()->addMonths(config('box-domain.default_expiration_date')),
            'company_id' => 1,
            'is_active' => true,
            'status' => CadeauBoxStatus::SOLD,
        ]);
    }

    #[Test]
    public function can_activate_cadeau_box(): void
    {
        $cadeauBox = $this->createCadeauBoxWithPhysicalBox(enabled: false);

        $this
            ->actingAsAdmin()
            ->fromRoute('admin.box.product.list')
            ->post(route('admin.box.activate'), [
                'activation_code' => $cadeauBox->physicalBox->activation_code,
                'company_id' => $cadeauBox->company_id,
            ])
            ->assertRedirectToRoute('admin.box.product.list')
            ->assertSessionHas('flash_message', 'Coffret activé avec succès');

        self::assertTrue(
            $cadeauBox->refresh()->is_active,
            'Expected box to be activated'
        );
    }
}
