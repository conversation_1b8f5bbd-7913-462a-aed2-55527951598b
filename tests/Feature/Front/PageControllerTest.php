<?php

declare(strict_types=1);

namespace Feature\Front;

use App\Domain\Content\Enums\SinglePageMode;
use App\Domain\Content\SinglePageService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\PageCreator;

class PageControllerTest extends TestCase
{
    use PageCreator;

    private function createSinglePages(): void
    {
        // Standard single page
        $this->createSinglePage(
            params: [
                'id' => 41,
                'slug' => 'ma-page-standard',
                'title' => 'Ma page standard',
                'mode' => SinglePageMode::Standard,
                'hero_image' => SinglePageService::DEFAULT_FOLDER.'/hero-41.jpeg',
            ]
        );

        // Single page for companies
        $this->createSinglePage(
            params: [
                'id' => 42,
                'slug' => 'ma-page-entreprises',
                'title' => 'Ma page entreprises',
                'mode' => SinglePageMode::Company,
                'hero_image' => SinglePageService::DEFAULT_FOLDER.'/hero-42.jpeg',
            ],
        );
        $this->createSinglePage(
            params: [
                'id' => 43,
                'slug' => 'teambuiling-offer-test',
                'title' => 'Ma page entreprises teambuilding',
                'mode' => SinglePageMode::Company,
                'hero_image' => SinglePageService::DEFAULT_FOLDER.'/hero-43.jpeg',
            ],
        );
    }

    #[Test]
    public function list_pages_by_mode_standard(): void
    {
        $this->createSinglePages();

        $this
            ->get(route('front.api.pages.by-mode', SinglePageMode::Standard))
            ->assertSuccessful()
            ->assertExactJson([
                'data' => [
                    [
                        'id' => 41,
                        'slug' => '/ateliers/ma-page-standard',
                        'title' => 'Ma page standard',
                        'image' => '/pages_content/hero-41.jpeg',
                        'mode' => 'standard',
                    ],
                ],
            ]);
    }

    #[Test]
    public function list_pages_by_mode_company(): void
    {
        $this->createSinglePages();

        $this
            ->get(route('front.api.pages.by-mode', SinglePageMode::Company))
            ->assertSuccessful()
            ->assertExactJson([
                'data' => [
                    [
                        'id' => 43,
                        'slug' => '/ateliers/teambuiling-offer-test',
                        'title' => 'Ma page entreprises teambuilding',
                        'image' => '/pages_content/hero-43.jpeg',
                        'mode' => 'company',
                    ],
                ],
            ]);
    }
}
