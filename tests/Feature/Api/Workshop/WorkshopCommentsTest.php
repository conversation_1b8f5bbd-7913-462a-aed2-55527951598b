<?php

declare(strict_types=1);

namespace Feature\Api\Workshop;

use App\Models\PhotoComment;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\CommentCreator;
use Tests\Traits\WorkshopCreator;

class WorkshopCommentsTest extends TestCase
{
    use CommentCreator;
    use WorkshopCreator;

    #[Test]
    public function workshop_can_retrieve_its_comments(): void
    {
        $workshop = $this->createWorkshop();
        $comment1 = $this->createCommentForWorkshop($workshop, [
            'commentaire' => 'World world',
            'created_at' => now()->subDay(),
        ]);
        $comment2 = $this->createCommentForWorkshop($workshop, [
            'commentaire' => 'This was awesome !',
            'created_at' => now()->subDays(2),
        ]);

        $this
            ->getJson(route('front.api.workshops.comments', $workshop))
            ->assertSuccessful()
            ->assertJsonPath('data', [
                [
                    'id' => $comment1->id,
                    'user' => [
                        'avatar' => $comment1->user->avatar,
                        'name' => $comment1->user->prenom.' '.mb_substr($comment1->user->nom, 0, 1),
                    ],
                    'created_at' => $comment1->created_at->format(\DateTimeInterface::RFC3339),
                    'content' => $comment1->commentaire,
                    'pictures' => $comment1->validatedPictures->map(function (PhotoComment $picture) {
                        return $picture->getUrl();
                    })->toArray(),
                ],
                [
                    'id' => $comment2->id,
                    'user' => [
                        'avatar' => $comment2->user->avatar,
                        'name' => $comment2->user->prenom.' '.mb_substr($comment2->user->nom, 0, 1),
                    ],
                    'created_at' => $comment2->created_at->format(\DateTimeInterface::RFC3339),
                    'content' => $comment2->commentaire,
                    'pictures' => $comment2->validatedPictures->map(function (PhotoComment $picture) {
                        return $picture->getUrl();
                    })->toArray(),
                ],
            ]);
    }
}
