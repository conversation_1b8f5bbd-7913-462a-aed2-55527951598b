<?php

declare(strict_types=1);

namespace Tests\Feature\Api\PrivateBooking;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestStatus;
use App\Domain\PrivateBooking\Events\PrivateBookingRequestCancelled;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\PrivateBookingRequestCreator;
use Tests\Traits\UserCreator;

class PrivateBookingRequestCancelTest extends TestCase
{
    use PrivateBookingRequestCreator;
    use UserCreator;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = $this->createUser();
    }

    #[DataProvider('userArtisanCanCancelPrivateBookingRequestProvider')]
    #[Test]
    public function user_can_cancel_private_booking_request(PrivateBookingRequestStatus $status): void
    {
        Event::fake([
            PrivateBookingRequestCancelled::class,
        ]);

        $privateBookingRequest = $this->createPrivateBookingRequest([
            'status' => $status,
            'user_id' => $this->user->id,
        ]);

        $this
            ->actingAsCustomer($this->user)
            ->post(route('front.api.private-bookings.cancel', $privateBookingRequest))
            ->assertSuccessful();

        $privateBookingRequest->refresh();

        $this->assertEquals(PrivateBookingRequestStatus::Cancelled, $privateBookingRequest->status);

        Event::assertDispatched(PrivateBookingRequestCancelled::class, static fn (PrivateBookingRequestCancelled $event) => $event->privateBookingRequestId === $privateBookingRequest->id);
    }

    #[DataProvider('userArtisanCantCancelPrivateBookingRequestProvider')]
    #[Test]
    public function user_cant_cancel_private_booking_request(PrivateBookingRequestStatus $status): void
    {
        Event::fake([
            PrivateBookingRequestCancelled::class,
        ]);

        $privateBookingRequest = $this->createPrivateBookingRequest([
            'status' => $status,
            'user_id' => $this->user->id,
        ]);

        $this
            ->actingAsCustomer($this->user)
            ->post(route('front.api.private-bookings.cancel', ['privateBooking' => $privateBookingRequest]))
            ->assertUnprocessable();

        $privateBookingRequest->refresh();

        $this->assertEquals($status, $privateBookingRequest->status);

        Event::assertNotDispatched(PrivateBookingRequestCancelled::class);
    }

    #[Test]
    public function user_cant_cancel_private_booking_request_that_is_not_his_own(): void
    {
        $privateBookingRequest = $this->createPrivateBookingRequest([
            'status' => PrivateBookingRequestStatus::New,
            'user_id' => $this->user->id,
        ]);

        $otherUser = $this->createUser();

        $this
            ->actingAsCustomer($otherUser)
            ->post(route('front.api.private-bookings.cancel', $privateBookingRequest))
            ->assertForbidden();
    }

    public static function userArtisanCanCancelPrivateBookingRequestProvider(): \Generator
    {
        yield [PrivateBookingRequestStatus::New];
        yield [PrivateBookingRequestStatus::Accepted];
        yield [PrivateBookingRequestStatus::OtherProposal];
    }

    public static function userArtisanCantCancelPrivateBookingRequestProvider(): \Generator
    {
        yield [PrivateBookingRequestStatus::Processed];
        yield [PrivateBookingRequestStatus::Expired];
        yield [PrivateBookingRequestStatus::Cancelled];
        yield [PrivateBookingRequestStatus::Refused];
    }
}
