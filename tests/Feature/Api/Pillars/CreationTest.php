<?php

declare(strict_types=1);

namespace Feature\Api\Pillars;

use App\Enums\Country;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\CityCreator;
use Tests\Traits\CreationCreator;
use Tests\Traits\PageCreator;
use Tests\Traits\WorkshopCreator;

class CreationTest extends TestCase
{
    use CityCreator;
    use CreationCreator;
    use PageCreator;
    use WorkshopCreator;

    #[Test]
    public function api_can_return_creations_with_workshop_count(): void
    {
        $creation = $this->createCreation();
        $this->createSinglePage(params: [
            'subject_type' => $creation->getMorphClass(),
            'subject_id' => $creation->getKey(),
        ]);
        $workshop = $this->createWorkshopForCity($this->createActiveCityForCountry(Country::France));
        $workshop->creations()->sync($creation);

        $this
            ->get(route('front.api.creation.search'))
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    [
                        'value' => "$creation->id||{$creation->getLabel()}",
                        'identifier' => $creation->identifier,
                        'label' => $creation->name,
                        'thumbnail_image' => $creation->getThumbnailImageUrl(),
                        'redirectUrl' => $creation->singlePage->getUrl(),
                        'experiences_count' => 1,
                    ],
                ],
                'prev_page_url' => null,
                'to' => 1,
                'total' => 1,
            ]);
    }
}
