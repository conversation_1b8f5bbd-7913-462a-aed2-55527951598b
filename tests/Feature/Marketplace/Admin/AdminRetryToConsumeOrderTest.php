<?php

declare(strict_types=1);

namespace Tests\Feature\Marketplace\Admin;

use App\Domain\Booking\Enums\BookingOrigin;
use App\Models\Commande;
use App\Shared\Uuid;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Marketplace\MiraklApiTestCase;
use Tests\Traits\InvoiceCreator;
use Tests\Traits\MiraklOrderCreator;
use Tests\Traits\OrderCreator;

class AdminRetryToConsumeOrderTest extends MiraklApiTestCase
{
    use InvoiceCreator;
    use MiraklOrderCreator;
    use OrderCreator;

    #[Test]
    public function a_not_consumed_order_can_be_consumed(): void
    {
        $fakeMiraklOrderService = $this->bindMiraklOrderService(null, Collection::make([$this->pendingOrderResponseHavingState('ORDER_PENDING')]));

        $order = $this->createOrder();
        $this->createMiraklOrderForOrder($order);
        $workshop = $this->createWorkshop();
        $event = $this->createNdEventForWorkshopForDateWithCapacity($workshop, CarbonImmutable::now(), 10, miraklParams: ['mirakl_id' => (string) Uuid::new()]);
        $booking = $this->addBookingToOrder($order, $this->createBookingForEvent($event, ['origin' => BookingOrigin::NatureEtDecouvertes]));
        $invoice = $this->createInvoiceForOrder($order);
        $this->createInvoiceLineForInvoiceAndBooking($invoice, $booking);

        $this->consumeOrderEndpoint($order);

        $order->refresh();

        $fakeMiraklOrderService->assertOrderConsumptionCalledForOrder($order->miraklOrder->mirakl_id);
        $this->assertNotNull($order->miraklOrder->consumed_at);
    }

    #[Test]
    public function if_order_is_not_invoiced_yet_it_cannot_be_consumed(): void
    {
        $fakeMiraklOrderService = $this->bindMiraklOrderService(null, Collection::make([$this->pendingOrderResponseHavingState('ORDER_PENDING')]));

        $order = $this->createOrder();
        $this->createMiraklOrderForOrder($order);
        $workshop = $this->createWorkshop();
        $event = $this->createNdEventForWorkshopForDateWithCapacity($workshop, CarbonImmutable::now(), 10, miraklParams: ['mirakl_id' => (string) Uuid::new()]);
        $this->addBookingToOrder($order, $this->createBookingForEvent($event, ['origin' => BookingOrigin::NatureEtDecouvertes]));

        $this->actingAsAdmin()
            ->post(route('admin.commandes.mirakl-consume', $order))
            ->assertForbidden();

        $order->refresh();

        $fakeMiraklOrderService->assertOrderConsumptionNotCalled();
        $this->assertNull($order->miraklOrder?->consumed_at);
    }

    #[Test]
    public function if_order_is_not_linked_to_mirakl_it_cannot_be_consumed(): void
    {
        $fakeMiraklOrderService = $this->bindMiraklOrderService(null, Collection::make([$this->pendingOrderResponseHavingState('ORDER_PENDING')]));

        $order = $this->createOrder();
        $workshop = $this->createWorkshop();
        $event = $this->createNdEventForWorkshopForDateWithCapacity($workshop, CarbonImmutable::now(), 10, miraklParams: ['mirakl_id' => (string) Uuid::new()]);
        $booking = $this->addBookingToOrder($order, $this->createBookingForEvent($event, ['origin' => BookingOrigin::NatureEtDecouvertes]));
        $invoice = $this->createInvoiceForOrder($order);
        $this->createInvoiceLineForInvoiceAndBooking($invoice, $booking);

        $this->actingAsAdmin()
            ->post(route('admin.commandes.mirakl-consume', $order))
            ->assertForbidden();

        $order->refresh();

        $fakeMiraklOrderService->assertOrderConsumptionNotCalled();
        $this->assertNull($order->miraklOrder?->consumed_at);
    }

    #[Test]
    public function if_order_is_already_consumed_it_cannot_be_consumed_again(): void
    {
        $fakeMiraklOrderService = $this->bindMiraklOrderService(null, Collection::make([$this->pendingOrderResponseHavingState('ORDER_PENDING')]));

        $order = $this->createOrder();
        $this->createMiraklOrderForOrder($order, ['consumed_at' => Carbon::now()]);
        $workshop = $this->createWorkshop();
        $event = $this->createNdEventForWorkshopForDateWithCapacity($workshop, CarbonImmutable::now(), 10, miraklParams: ['mirakl_id' => (string) Uuid::new()]);
        $booking = $this->addBookingToOrder($order, $this->createBookingForEvent($event, ['origin' => BookingOrigin::NatureEtDecouvertes]));
        $invoice = $this->createInvoiceForOrder($order);
        $this->createInvoiceLineForInvoiceAndBooking($invoice, $booking);

        $this->actingAsAdmin()
            ->post(route('admin.commandes.mirakl-consume', $order))
            ->assertForbidden();

        $order->refresh();

        $fakeMiraklOrderService->assertOrderConsumptionNotCalled();
    }

    private function consumeOrderEndpoint(Commande $order): void
    {
        $this->actingAsAdmin()
            ->from(route('admin.commandes.show', $order))
            ->post(route('admin.commandes.mirakl-consume', $order))
            ->assertRedirect(route('admin.commandes.show', $order));
    }
}
