<?php

declare(strict_types=1);

namespace Tests\Feature\GiftCard;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class GiftCardOfferTest extends TestCase
{
    #[Test]
    public function offer_gift_card_page_always_load_except_for_text(): void
    {
        $this->get(route('gift-card-value.show'))->assertSuccessful();
        $this->get(route('gift-card-value.show', ['amount' => 100]))->assertSuccessful();
        $this->get(route('gift-card-value.show', ['amount' => 'a']))->assertNotFound();
    }
}
