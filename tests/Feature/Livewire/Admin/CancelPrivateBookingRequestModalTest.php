<?php

declare(strict_types=1);

namespace Tests\Feature\Livewire\Admin;

use App\Domain\PrivateBooking\Enums\PrivateBookingRequestStatus;
use App\Enums\NotificationEnum;
use App\Livewire\Admin\PrivateBooking\CancelPrivateBookingRequestModal;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\PrivateBookingRequestCreator;
use Tests\Traits\ThreadCreator;

class CancelPrivateBookingRequestModalTest extends TestCase
{
    use PrivateBookingRequestCreator;
    use ThreadCreator;

    #[Test]
    public function admin_can_cancel_private_booking_request_in_bo(): void
    {
        $privateBooking = $this->createPrivateBookingRequest([
            'status' => PrivateBookingRequestStatus::New,
        ]);
        $this->createThreadForPrivateBooking($privateBooking);

        $this->actingAsAdmin();
        \Livewire::test(CancelPrivateBookingRequestModal::class)
            ->set('requestId', $privateBooking->id)
            ->call('cancel')
            ->assertModalClosed('cancel-private-booking-request')
            ->assertDispatched('refreshPrivateBookingRequest')
            ->assertNotify('Private booking request cancelled successfully');

        $privateBooking->refresh();
        $this->assertEquals(PrivateBookingRequestStatus::Cancelled, $privateBooking->status);
    }

    #[Test]
    public function admin_cannot_cancel_private_booking_request_that_cant_be(): void
    {
        $privateBooking = $this->createPrivateBookingRequest([
            'status' => PrivateBookingRequestStatus::Processed,
        ]);

        $this->actingAsAdmin();
        \Livewire::test(CancelPrivateBookingRequestModal::class)
            ->set('requestId', $privateBooking->id)
            ->call('cancel')
            ->assertModalClosed('cancel-private-booking-request')
            ->assertNotify(
                message: 'This private booking request cannot be cancelled.',
                type: NotificationEnum::Error,
            );

        $privateBooking->refresh();
        $this->assertEquals(PrivateBookingRequestStatus::Processed, $privateBooking->status);
    }
}
