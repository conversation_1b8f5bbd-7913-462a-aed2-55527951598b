<?php

declare(strict_types=1);

namespace Tests\Feature\Livewire\Common;

use App\Domain\Booking\Enums\CalendarEventType;
use App\Domain\Booking\Enums\CalendarMultipleEventsType;
use App\Domain\Booking\Enums\CalendarViewMode;
use App\Domain\Booking\Services\RecurringEventCreator;
use App\Domain\Booking\ValueObjects\RecurringEventConfig;
use App\Enums\Event\DayWeek;
use App\Infrastructure\Calendar\PublicHolidays\Handlers\PublicHolidaysHandlerInterface;
use App\Infrastructure\Calendar\PublicHolidays\PublicHoliday;
use App\Livewire\Common\Calendar;
use App\Models\Artisan;
use App\Models\Atelier;
use App\Models\EvenementRecurrent;
use App\Models\UserArtisan;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Database\Factories\*********************;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\EventCreator;
use Tests\Traits\OrderCreator;
use Tests\Traits\WorkshopCreator;

class CalendarTest extends TestCase
{
    use EventCreator;
    use OrderCreator;
    use WorkshopCreator;

    protected UserArtisan $userArtisan;
    protected Artisan $artisan;
    protected Atelier $workshop;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userArtisan = $this->createUserArtisanWithAnArtisanAndAWorkshop(
            workshopParams: [
                'active' => true,
                'online_at' => '2024-01-01 00:00:00',
                'background_color' => '#A6DFCD',
            ]
        );
        $this->artisan = $this->userArtisan->artisans->firstOrFail();
        $this->workshop = $this->artisan->ateliers->firstOrFail();
    }

    #[Test]
    public function can_change_date(): void
    {
        foreach (CalendarViewMode::cases() as $view) {
            $date = '2024-01-22';

            \Livewire::test(Calendar::class, ['selectedArtisan' => $this->artisan, 'mode' => 'artisan'])
                ->call('dateChanged', $date, $view->name)
                ->assertSet('defaultView', $view)
                ->assertDispatched('dateChanged', $date, $view->name);
        }
    }

    #[Test]
    public function can_change_view(): void
    {
        foreach (CalendarViewMode::cases() as $view) {
            \Livewire::test(Calendar::class, ['selectedArtisan' => $this->artisan, 'mode' => 'artisan'])
                ->call('viewChanged', $view->name)
                ->assertSet('defaultView', $view);
        }
    }

    #[Test]
    public function can_delete_recurring_event(): void
    {
        $startDate = (new Carbon())
            ->setDate(2024, 1, 22)
            ->startOfDay();
        $endDate = (new Carbon())
            ->setDate(2024, 2, 22)
            ->startOfDay();

        $localeStartTime = (new Carbon())->setTime(9, 0)->setTimezone('Europe/Paris');

        \App::make(RecurringEventCreator::class)->createRecurringEvents(
            collect([$this->workshop]),
            new RecurringEventConfig(
                $startDate,
                $localeStartTime,
                $endDate,
                2,
                [
                    DayWeek::Monday->value => false,
                    DayWeek::Tuesday->value => false,
                    DayWeek::Wednesday->value => false,
                    DayWeek::Thursday->value => false,
                    DayWeek::Friday->value => false,
                    DayWeek::Saturday->value => true,
                    DayWeek::Sunday->value => true,
                ],
            )
        );

        $this->actingAsArtisan($this->userArtisan);

        $event = EvenementRecurrent::latest('id')->first();

        \Livewire::test(Calendar::class, ['selectedArtisan' => $this->artisan, 'mode' => 'artisan'])
            ->call('eventRecurringDelete', $event->getKey())
            ->assertDispatched('refreshCalendar')
            ->assertNotify();

        $event->refresh();

        static::assertNotNull($event->deleted_at);
    }

    #[Test]
    public function admin_can_delete_recurring_event(): void
    {
        $startDate = (new Carbon())
            ->setDate(2024, 1, 22)
            ->startOfDay();
        $endDate = (new Carbon())
            ->setDate(2024, 2, 22)
            ->startOfDay();

        $localeStartTime = (new Carbon())->setTime(9, 0)->setTimezone('Europe/Paris');

        \App::make(RecurringEventCreator::class)->createRecurringEvents(
            collect([$this->workshop]),
            new RecurringEventConfig(
                $startDate,
                $localeStartTime,
                $endDate,
                2,
                [
                    DayWeek::Monday->value => false,
                    DayWeek::Tuesday->value => false,
                    DayWeek::Wednesday->value => false,
                    DayWeek::Thursday->value => false,
                    DayWeek::Friday->value => false,
                    DayWeek::Saturday->value => true,
                    DayWeek::Sunday->value => true,
                ],
            )
        );

        $this->actingAsArtisan($this->userArtisan);

        $event = EvenementRecurrent::latest('id')->first();

        \Livewire::test(Calendar::class, [
            'selectedArtisan' => $this->artisan,
            'artisans' => collect([$this->artisan]),
            'mode' => 'admin',
        ])
            ->call('eventRecurringDelete', $event->getKey())
            ->assertDispatched('refreshCalendar')
            ->assertNotify();

        $event->refresh();

        static::assertNotNull($event->deleted_at);
    }

    #[Test]
    public function can_delete_recurring_event_not_own_recurring_event(): void
    {
        $startDate = (new Carbon())
            ->setDate(2024, 1, 22)
            ->startOfDay();
        $endDate = (new Carbon())
            ->setDate(2024, 2, 22)
            ->startOfDay();

        $localeStartTime = (new Carbon())->setTime(9, 0)->setTimezone('Europe/Paris');

        \App::make(RecurringEventCreator::class)->createRecurringEvents(
            collect([$this->workshop]),
            new RecurringEventConfig(
                $startDate,
                $localeStartTime,
                $endDate,
                2,
                [
                    DayWeek::Monday->value => false,
                    DayWeek::Tuesday->value => false,
                    DayWeek::Wednesday->value => false,
                    DayWeek::Thursday->value => false,
                    DayWeek::Friday->value => false,
                    DayWeek::Saturday->value => true,
                    DayWeek::Sunday->value => true,
                ],
            )
        );

        $userArtisan = $this->createUserArtisanWithAnArtisanAndAWorkshop();
        $this->actingAsArtisan($userArtisan);

        $event = EvenementRecurrent::latest('id')->first();

        \Livewire::test(Calendar::class, ['selectedArtisan' => $this->artisan, 'mode' => 'artisan'])
            ->call('eventRecurringDelete', $event->getKey())
            ->assertForbidden();
    }

    #[Test]
    public function can_get_events(): void
    {
        $user = $this->createUser();
        $order = $this->createPaidOrderForUser($user);

        $this->actingAsArtisan($this->userArtisan);

        $bookedEvent = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 12:30',
                'end' => '2024-01-22 14:30',
            ],
        );

        $this->addEventBookingToOrder($order, $bookedEvent);

        $event = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 08:30',
                'end' => '2024-01-22 10:30',
            ],
        );

        $unavailability = *********************::new()
            ->forArtisan($this->artisan)
            ->forWorkshop($this->workshop)
            ->create(
                [
                    'start_at' => '2024-01-23 00:00',
                    'end_at' => '2024-01-24 23:59',
                ]
            );

        $events = \Livewire::test(
            Calendar::class,
            [
                'selectedArtisan' => $this->artisan,
                'startDate' => '2024-01-22 00:00',
                'endDate' => '2024-01-28 23:59',
                'mode' => 'artisan',
            ]
        )
            ->call('getEvents')
            ->get('events');
        $events = json_decode((string) $events, true);

        static::assertFalse($events[0]['allDay']);
        static::assertSame($event->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['extendedProps']['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['extendedProps']['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame(CalendarEventType::Single->value, $events[0]['extendedProps']['type']);
        static::assertTrue($events[0]['editable']);

        static::assertFalse($events[1]['allDay']);
        static::assertSame($bookedEvent->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['extendedProps']['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['extendedProps']['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame(CalendarEventType::Single->value, $events[1]['extendedProps']['type']);
        static::assertTrue($events[1]['editable']);

        static::assertTrue($events[2]['allDay']);
        static::assertSame($unavailability->start_at->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[2]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($unavailability->end_at->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[2]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertCount(1, $events[2]['extendedProps']['workshopColors']);
        static::assertSame('#A6DFCD', $events[2]['extendedProps']['workshopColors'][0]);
        static::assertSame(CalendarEventType::Unavailability->value, $events[2]['extendedProps']['type']);
        static::assertFalse($events[2]['editable']);
    }

    #[Test]
    public function admin_can_get_events(): void
    {
        $user = $this->createUser();
        $order = $this->createPaidOrderForUser($user);

        $this->actingAsArtisan($this->userArtisan);

        $bookedEvent = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 12:30',
                'end' => '2024-01-22 14:30',
            ],
        );

        $this->addEventBookingToOrder($order, $bookedEvent);

        $event = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 08:30',
                'end' => '2024-01-22 10:30',
            ],
        );

        $unavailability = *********************::new()
            ->forArtisan($this->artisan)
            ->forWorkshop($this->workshop)
            ->create(
                [
                    'start_at' => '2024-01-23 00:00',
                    'end_at' => '2024-01-24 23:59',
                ],
            );

        $events = \Livewire::test(
            Calendar::class,
            [
                'selectedArtisan' => $this->artisan,
                'artisans' => collect([$this->artisan]),
                'startDate' => '2024-01-22 00:00',
                'endDate' => '2024-01-28 23:59',
                'mode' => 'admin',
            ]
        )
            ->call('getEvents')
            ->get('events');
        $events = json_decode((string) $events, true);

        static::assertFalse($events[0]['allDay']);
        static::assertSame($event->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['extendedProps']['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($event->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[0]['extendedProps']['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame(CalendarEventType::Single->value, $events[0]['extendedProps']['type']);
        static::assertTrue($events[0]['editable']);

        static::assertFalse($events[1]['allDay']);
        static::assertSame($bookedEvent->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->start->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['extendedProps']['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($bookedEvent->end->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[1]['extendedProps']['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame(CalendarEventType::Single->value, $events[1]['extendedProps']['type']);
        static::assertTrue($events[1]['editable']);

        static::assertTrue($events[2]['allDay']);
        static::assertSame($unavailability->start_at->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[2]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame($unavailability->end_at->timezone($event->timezone)->format('Y-m-d H:i'), Carbon::parse($events[2]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertCount(1, $events[2]['extendedProps']['workshopColors']);
        static::assertSame('#A6DFCD', $events[2]['extendedProps']['workshopColors'][0]);
        static::assertSame(CalendarEventType::Unavailability->value, $events[2]['extendedProps']['type']);
        static::assertFalse($events[2]['editable']);
    }

    #[Test]
    public function can_get_events_with_public_holiday(): void
    {
        $this->app->bind(
            PublicHolidaysHandlerInterface::class,
            fn () => new class() implements PublicHolidaysHandlerInterface {
                public function fetch(string $countryCode, CarbonInterface $start, CarbonInterface $end): Collection
                {
                    return collect([
                        new PublicHoliday('Christmas Day', 'Noël', CarbonImmutable::createFromDate(2024, 12, 25)->timezone(getTimezone())),
                        new PublicHoliday("New Year's Day", "Jour de l'an", CarbonImmutable::createFromDate(2025, 1, 1)->timezone(getTimezone())),
                    ]);
                }
            }
        );

        $events = \Livewire::test(
            Calendar::class,
            [
                'selectedArtisan' => $this->artisan,
                'artisans' => collect([$this->artisan]),
                'startDate' => '2024-11-25 00:00',
                'endDate' => '2025-01-05 23:59',
                'mode' => 'artisan',
            ]
        )
            ->call('getEvents')
            ->get('events');
        $events = json_decode((string) $events, true);

        static::assertCount(2, $events);

        static::assertTrue($events[0]['allDay']);
        static::assertSame('Noël', $events[0]['title']);
        static::assertSame('2024-12-25 00:00:00', Carbon::parse($events[0]['start'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2024-12-25 23:59:59', Carbon::parse($events[0]['end'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2024-12-25 00:00:00', Carbon::parse($events[0]['start'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2024-12-25 23:59:59', Carbon::parse($events[0]['end'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame(CalendarEventType::PublicHoliday->value, $events[0]['extendedProps']['type']);
        static::assertFalse($events[0]['editable']);

        static::assertTrue($events[1]['allDay']);
        static::assertSame("Jour de l'an", $events[1]['title']);
        static::assertSame('2025-01-01 00:00:00', Carbon::parse($events[1]['start'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2025-01-01 23:59:59', Carbon::parse($events[1]['end'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2025-01-01 00:00:00', Carbon::parse($events[1]['start'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame('2025-01-01 23:59:59', Carbon::parse($events[1]['end'])->timezone(getTimezone())->toDateTimeString());
        static::assertSame(CalendarEventType::PublicHoliday->value, $events[1]['extendedProps']['type']);
        static::assertFalse($events[1]['editable']);
    }

    #[Test]
    public function can_see_stacked_events(): void
    {
        $user = $this->createUser();
        $order = $this->createPaidOrderForUser($user);

        $this->actingAsArtisan($this->userArtisan);

        $bookedEvent = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 12:30',
                'end' => '2024-01-22 14:30',
            ],
        );

        $this->addEventBookingToOrder($order, $bookedEvent);

        $event = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-22 12:30',
                'end' => '2024-01-22 14:30',
            ],
        );

        $event->refresh()->lockStackedEvents();
        $bookedEvent->refresh()->lockStackedEvents();

        $events = \Livewire::test(
            Calendar::class,
            [
                'selectedArtisan' => $this->artisan,
                'artisans' => collect([$this->artisan]),
                'startDate' => '2024-01-22 00:00',
                'endDate' => '2024-01-28 23:59',
                'mode' => 'artisan',
            ],
        )
            ->call('getEvents')
            ->get('events');
        $events = json_decode((string) $events, true);

        static::assertCount(1, $events);

        static::assertFalse($events[0]['allDay']);
        static::assertSame('2024-01-22 13:30', Carbon::parse($events[0]['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame('2024-01-22 15:30', Carbon::parse($events[0]['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame('2024-01-22 13:30', Carbon::parse($events[0]['extendedProps']['start'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame('2024-01-22 15:30', Carbon::parse($events[0]['extendedProps']['end'])->timezone($event->timezone)->format('Y-m-d H:i'));
        static::assertSame(CalendarEventType::Multiple->value, $events[0]['extendedProps']['type']);
        static::assertSame(CalendarMultipleEventsType::Stacked->value, $events[0]['extendedProps']['multipleType']);
        static::assertCount(2, $events[0]['extendedProps']['events']);
        static::assertFalse($events[0]['extendedProps']['events'][0]['isUnavailableStacked']);
        static::assertTrue($events[0]['extendedProps']['events'][1]['isUnavailableStacked']);
        static::assertTrue($events[0]['editable']);
    }

    #[Test]
    public function can_see_events_with_deactivated_workshops(): void
    {
        $workshopDeactivated = $this->createWorkshopForArtisan(
            $this->artisan,
            [
                'online_at' => now(),
                'active' => false,
            ]
        );

        $eventA = $this->createEvent(
            $workshopDeactivated,
            [
                'start' => '2024-01-22 12:30',
                'end' => '2024-01-22 14:30',
            ],
        );

        $eventB = $this->createEvent(
            $this->workshop,
            [
                'start' => '2024-01-23 12:30',
                'end' => '2024-01-23 14:30',
            ],
        );

        $this->actingAsArtisan($this->userArtisan);

        $events = \Livewire::test(
            Calendar::class,
            [
                'selectedArtisan' => $this->artisan,
                'artisans' => collect([$this->artisan]),
                'startDate' => '2024-01-22 00:00',
                'endDate' => '2024-01-28 23:59',
                'mode' => 'artisan',
            ],
        )
            ->call('getEvents')
            ->get('events');
        $events = json_decode((string) $events, true);

        static::assertCount(2, $events);

        static::assertSame($eventA->getKey(), $events[0]['extendedProps']['id']);
        static::assertSame($eventB->getKey(), $events[1]['extendedProps']['id']);
    }
}
