<?php

declare(strict_types=1);

namespace Tests\Feature\Workshop;

use App\Domain\Workshop\Enums\BookingOptionType;
use App\Enums\Event\EventType;
use App\Enums\NotificationEnum;
use App\Livewire\Artisan\Workshop\UpdateOptionsModal;
use App\Models\UserArtisan;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AdminCreator;
use Tests\Traits\Assertions\WorkshopUpdateRequestAssertions;
use Tests\Traits\BookingOptionsCreator;
use Tests\Traits\UserArtisanCreator;
use Tests\Traits\WorkshopUpdateRequestCreator;

class OptionsUpdatedByArtisanTest extends TestCase
{
    use AdminCreator;
    use BookingOptionsCreator;
    use UserArtisanCreator;
    use WorkshopUpdateRequestAssertions;
    use WorkshopUpdateRequestCreator;

    private UserArtisan $userArtisan;

    protected function setUp(): void
    {
        parent::setUp();

        $this->userArtisan = $this->createUserArtisanWithAnArtisanAndAWorkshop(
            [],
            [
                'active' => true,
                'autredate_desactive' => 1,
                'nb_pers_min' => 3,
                'nb_pers_max' => 10,
            ]
        );

        $workshop = $this->userArtisan->artisans->first()->ateliers->first();
        $this->createBookingOptionsPrivateArtisanManagedLocationForWorkshop($workshop, min: 1, max: 60, activated: false);
        $this->createBookingOptionsPrivateClientManagedLocationForWorkshop($workshop, min: 1, max: 60, activated: false);
    }

    #[Test]
    public function when_an_artisan_update_workshop_options_the_workshop_is_updated(): void
    {
        $workshop = $this->userArtisan->artisans->first()->ateliers->first();

        $now = Carbon::now();

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 3)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 12)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 10)
            ->set('maxParticipantsPrivateClientManagedLocation', 55)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(__('components/modals/workshop/update-options.messages.success'));

        $workshop->refresh();

        $option = $workshop->getBookingOptionsForType(BookingOptionType::PrivateArtisanManagedLocation);
        $this->assertEquals(3, $workshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(12, $workshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertFalse($workshop->autredate_desactive);
        $this->assertGreaterThanOrEqual($now, $option?->updated_by_artisan_at);
        $this->assertGreaterThanOrEqual($now, $workshop->request_date_updated_by_artisan_at);

        $option = $workshop->getBookingOptionsForType(BookingOptionType::PrivateClientManagedLocation);
        $this->assertEquals(10, $workshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(55, $workshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
        $this->assertGreaterThanOrEqual($now, $option?->updated_by_artisan_at);
    }

    #[Test]
    public function an_artisan_cant_update_private_booking_options_more_than_once_in_a_day(): void
    {
        $workshop = $this->userArtisan->artisans->first()->ateliers->first();

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 3)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 12)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 10)
            ->set('maxParticipantsPrivateClientManagedLocation', 55)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(__('components/modals/workshop/update-options.messages.success'));

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', false)
            ->set('minParticipantsPrivateArtisanManagedLocation', 15)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 26)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 90)
            ->set('maxParticipantsPrivateClientManagedLocation', 150)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(
                message: __('components/modals/workshop/update-options.messages.error.artisan_cant_update_private_artisan_managed_location'),
                type: NotificationEnum::Error,
            );

        $workshop->refresh();

        $this->assertEquals(3, $workshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(12, $workshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertFalse($workshop->autredate_desactive);

        $this->assertEquals(10, $workshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(55, $workshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
    }

    #[Test]
    public function an_artisan_cant_update_request_date_options_more_than_once_in_a_day(): void
    {
        $workshop = $this->userArtisan->artisans->first()->ateliers->first();

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 3)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 12)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 10)
            ->set('maxParticipantsPrivateClientManagedLocation', 55)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(__('components/modals/workshop/update-options.messages.success'));

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 15)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 26)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 90)
            ->set('maxParticipantsPrivateClientManagedLocation', 150)
            ->set('dateRequestActivated', false)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(
                message: __('components/modals/workshop/update-options.messages.error.artisan_cant_update_date_request'),
                type: NotificationEnum::Error,
            );

        $workshop->refresh();

        $this->assertEquals(3, $workshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(12, $workshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertFalse($workshop->autredate_desactive);

        $this->assertEquals(10, $workshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(55, $workshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
    }

    #[Test]
    public function an_artisan_cant_update_private_booking_for_client_managed_location_options_more_than_once_in_a_day(): void
    {
        $workshop = $this->userArtisan->artisans->first()->ateliers->first();

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 3)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 12)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 10)
            ->set('maxParticipantsPrivateClientManagedLocation', 55)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(__('components/modals/workshop/update-options.messages.success'));

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 15)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 26)
            ->set('isPrivateClientManagedLocation', false)
            ->set('minParticipantsPrivateClientManagedLocation', 90)
            ->set('maxParticipantsPrivateClientManagedLocation', 150)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(
                message: __('components/modals/workshop/update-options.messages.error.artisan_cant_update_private_client_managed_location'),
                type: NotificationEnum::Error,
            );

        $workshop->refresh();

        $this->assertEquals(3, $workshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(12, $workshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertFalse($workshop->autredate_desactive);

        $this->assertEquals(10, $workshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(55, $workshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertTrue($workshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
    }

    #[Test]
    public function an_artisan_cant_set_max_lower_than_min(): void
    {
        $workshop = $this->userArtisan->artisans->first()->ateliers->first();

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $workshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 15)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 10)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 25)
            ->set('maxParticipantsPrivateClientManagedLocation', 10)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertHasErrors();

        $workshop->refresh();

        $this->assertEquals(1, $workshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(60, $workshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertFalse($workshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertTrue($workshop->autredate_desactive);

        $this->assertEquals(1, $workshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(60, $workshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertFalse($workshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
    }

    #[Test]
    public function an_artisan_cant_edit_options_of_disabled_workshop(): void
    {
        $userArtisanWithDisabledWorkshop = $this->createUserArtisanWithAnArtisanAndAWorkshop(
            [],
            [
                'active' => false,
                'autredate_desactive' => 1,
                'nb_pers_min' => 3,
                'nb_pers_max' => 10,
            ]
        );
        $disabledWorkshop = $userArtisanWithDisabledWorkshop->artisans->first()->ateliers->first();
        $this->createBookingOptionsPrivateArtisanManagedLocationForWorkshop($disabledWorkshop, min: 1, max: 60, activated: false);
        $this->createBookingOptionsPrivateClientManagedLocationForWorkshop($disabledWorkshop, min: 1, max: 60, activated: false);

        \Livewire::test(UpdateOptionsModal::class, ['workshopId' => $disabledWorkshop->id])
            ->set('isPrivateArtisanManagedLocation', true)
            ->set('minParticipantsPrivateArtisanManagedLocation', 3)
            ->set('maxParticipantsPrivateArtisanManagedLocation', 12)
            ->set('isPrivateClientManagedLocation', true)
            ->set('minParticipantsPrivateClientManagedLocation', 10)
            ->set('maxParticipantsPrivateClientManagedLocation', 55)
            ->set('dateRequestActivated', true)
            ->call('update')
            ->assertSuccessful()
            ->assertNotify(
                message: __('components/modals/workshop/update-options.messages.error.artisan_cant_update_disabled_workshop'),
                type: NotificationEnum::Error,
            );

        $disabledWorkshop->refresh();

        $this->assertEquals(1, $disabledWorkshop->getMinParticipantsForEventType(EventType::Private));
        $this->assertEquals(60, $disabledWorkshop->getMaxParticipantsForEventType(EventType::Private));
        $this->assertFalse($disabledWorkshop->isBookingOptionsActivated(BookingOptionType::PrivateArtisanManagedLocation));
        $this->assertTrue($disabledWorkshop->autredate_desactive);

        $this->assertEquals(1, $disabledWorkshop->getMinParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertEquals(60, $disabledWorkshop->getMaxParticipantsForEventType(EventType::PrivateClientManagedLocation));
        $this->assertFalse($disabledWorkshop->isBookingOptionsActivated(BookingOptionType::PrivateClientManagedLocation));
    }
}
