<?php

declare(strict_types=1);

namespace Tests\Feature\Commands;

use App\Console\Commands\Workshop\WorkshopTransferCommand;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\BookingCreator;
use Tests\Traits\EventCreator;
use Tests\Traits\UnavailabilityCreator;
use Tests\Traits\WorkshopCreator;

class WorkshopTransferCommandTest extends TestCase
{
    use ArtisanCreator;
    use BookingCreator;
    use EventCreator;
    use UnavailabilityCreator;
    use WorkshopCreator;

    #[Test]
    public function cant_transfer_a_workshop_with_booking(): void
    {
        $eventDate = Carbon::parse('2024-02-15 10:00:00')->toImmutable();

        $artisanA = $this->createArtisan();
        $artisanB = $this->createArtisan();

        $workshop = $this->createWorkshopForArtisan($artisanA);
        $event = $this->createEventForWorkshopForDate($workshop, $eventDate);
        $this->createBookingForEvent($event);

        $run = Carbon::parse('2024-02-25 10:00:00')->toImmutable();
        $nextRun = $run
            ->setDay(config('facturation.delai_generation_factures'))
            ->addMonth()
            ->toImmutable();

        $this->setCurrentDate($run);

        $this->artisan(
            WorkshopTransferCommand::class,
            [
                'workshopToTransfer' => $workshop->getKey(),
                'newArtisan' => $artisanB->getKey(),
            ]
        )
            ->expectsOutput(
                "Unable to transfer the workshop. Some bookings have yet to be invoiced. Run this command at this date: {$nextRun->format('Y-m-d')}"
            )
            ->assertFailed();

        $artisanA->refresh();
        $artisanB->refresh();
        $workshop->refresh();

        static::assertSame($workshop->artisan_id, $artisanA->getKey());
    }

    #[Test]
    public function can_transfer_a_workshop_without_booking(): void
    {
        $artisanA = $this->createArtisan();
        $artisanB = $this->createArtisan();

        $workshop = $this->createWorkshopForArtisan($artisanA);

        $run = Carbon::parse('2024-02-25 10:00:00')->toImmutable();

        $this->setCurrentDate($run);

        $this->artisan(
            WorkshopTransferCommand::class,
            [
                'workshopToTransfer' => $workshop->getKey(),
                'newArtisan' => $artisanB->getKey(),
            ]
        )
            ->expectsOutput(
                sprintf(
                    'Asking to transfer workshop "%s" from artisan "%s" to artisan "%s".',
                    $workshop->nom,
                    $workshop->artisan->getDenomination(),
                    $artisanB->getDenomination(),
                )
            )
            ->expectsConfirmation('Are-you sure?', 'yes')
            ->expectsOutput('Workshop transferred with success.')
            ->assertSuccessful();

        $artisanA->refresh();
        $artisanB->refresh();
        $workshop->refresh();

        static::assertSame($workshop->artisan_id, $artisanB->getKey());
    }

    #[Test]
    public function can_transfer_a_workshop_without_booking_but_abort(): void
    {
        $artisanA = $this->createArtisan();
        $artisanB = $this->createArtisan();

        $workshop = $this->createWorkshopForArtisan($artisanA);

        $run = Carbon::parse('2024-02-25 10:00:00')->toImmutable();

        $this->setCurrentDate($run);

        $this->artisan(
            WorkshopTransferCommand::class,
            [
                'workshopToTransfer' => $workshop->getKey(),
                'newArtisan' => $artisanB->getKey(),
            ]
        )
            ->expectsOutput(
                sprintf(
                    'Asking to transfer workshop "%s" from artisan "%s" to artisan "%s".',
                    $workshop->nom,
                    $workshop->artisan->getDenomination(),
                    $artisanB->getDenomination(),
                )
            )
            ->expectsConfirmation('Are-you sure?')
            ->expectsOutput('Command aborted')
            ->assertFailed();

        $artisanA->refresh();
        $artisanB->refresh();
        $workshop->refresh();

        static::assertSame($workshop->artisan_id, $artisanA->getKey());
    }

    #[Test]
    public function workshop_are_detached_from_unavailabilities_on_workshop_transfer(): void
    {
        $artisanA = $this->createArtisan();
        $artisanB = $this->createArtisan();

        $workshop1 = $this->createWorkshopForArtisan($artisanA);
        $workshop2 = $this->createWorkshopForArtisan($artisanA);

        $unavailability = $this->createUnavailabilityForArtisanAndWorkshops($artisanA, collect([$workshop1, $workshop2]));

        $run = Carbon::parse('2024-02-25 10:00:00')->toImmutable();

        $this->setCurrentDate($run);

        $this->artisan(
            WorkshopTransferCommand::class,
            [
                'workshopToTransfer' => $workshop1->getKey(),
                'newArtisan' => $artisanB->getKey(),
            ]
        )
            ->expectsOutput(
                sprintf(
                    'Asking to transfer workshop "%s" from artisan "%s" to artisan "%s".',
                    $workshop1->nom,
                    $workshop1->artisan->getDenomination(),
                    $artisanB->getDenomination(),
                )
            )
            ->expectsConfirmation('Are-you sure?', 'yes')
            ->expectsOutput('Workshop transferred with success.')
            ->assertSuccessful();

        $artisanA->refresh();
        $artisanB->refresh();
        $workshop1->refresh();
        $unavailability->refresh();

        static::assertSame($workshop1->artisan_id, $artisanB->getKey());
        static::assertCount(1, $unavailability->workshops);
        static::assertCount(0, $workshop1->unavailabilities);
        static::assertNull($unavailability->deleted_at);
    }

    #[Test]
    public function unavailabilities_are_deleted_on_workshop_transfer_when_no_other_workshop_are_linked(): void
    {
        $artisanA = $this->createArtisan();
        $artisanB = $this->createArtisan();

        $workshop = $this->createWorkshopForArtisan($artisanA);

        $unavailability = $this->createUnavailabilityForArtisanAndWorkshop($artisanA, $workshop);

        $run = Carbon::parse('2024-02-25 10:00:00')->toImmutable();

        $this->setCurrentDate($run);

        $this->artisan(
            WorkshopTransferCommand::class,
            [
                'workshopToTransfer' => $workshop->getKey(),
                'newArtisan' => $artisanB->getKey(),
            ]
        )
            ->expectsOutput(
                sprintf(
                    'Asking to transfer workshop "%s" from artisan "%s" to artisan "%s".',
                    $workshop->nom,
                    $workshop->artisan->getDenomination(),
                    $artisanB->getDenomination(),
                )
            )
            ->expectsConfirmation('Are-you sure?', 'yes')
            ->expectsOutput('Workshop transferred with success.')
            ->assertSuccessful();

        $artisanA->refresh();
        $artisanB->refresh();
        $workshop->refresh();
        $unavailability->refresh();

        static::assertSame($workshop->artisan_id, $artisanB->getKey());
        static::assertCount(0, $unavailability->workshops);
        static::assertCount(0, $workshop->unavailabilities);
        static::assertNotNull($unavailability->deleted_at);
    }
}
