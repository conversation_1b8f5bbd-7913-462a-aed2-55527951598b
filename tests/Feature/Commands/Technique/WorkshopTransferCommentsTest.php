<?php

declare(strict_types=1);

namespace Tests\Feature\Commands\Technique;

use App\Console\Commands\Technique\WorkshopTransferCommentsCommand;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\CommentCreator;
use Tests\Traits\WorkshopCreator;

class WorkshopTransferCommentsTest extends TestCase
{
    use CommentCreator;
    use WorkshopCreator;

    #[Test]
    public function can_transfer_comments(): void
    {
        $workshopA = $this->createWorkshop();
        $workshopB = $this->createWorkshop();

        $this->createCommentForWorkshop($workshopA, ['commentaire' => 'My comment']);

        $this
            ->artisan(WorkshopTransferCommentsCommand::class, [
                'initialWorkshop' => $workshopA->id,
                'targetWorkshop' => $workshopB->id,
            ])
            ->expectsQuestion('Start transfer?', true)
            ->assertOk();

        static::assertCount(0, $workshopA->refresh()->comments);
        static::assertCount(1, $workshopB->refresh()->comments);
    }

    #[Test]
    public function can_copy_comments(): void
    {
        $workshopA = $this->createWorkshop();
        $workshopB = $this->createWorkshop();

        $this->createCommentForWorkshop($workshopA, ['commentaire' => 'My comment']);

        $this
            ->artisan(WorkshopTransferCommentsCommand::class, [
                'initialWorkshop' => $workshopA->id,
                'targetWorkshop' => $workshopB->id,
                '--copy' => true,
            ])
            ->expectsQuestion('Start copy?', true)
            ->assertOk();

        static::assertCount(1, $workshopA->refresh()->comments);
        static::assertCount(1, $workshopB->refresh()->comments);

        static::assertEquals($workshopA->comments->firstOrFail()->commentaire, $workshopB->comments->firstOrFail()->commentaire);
    }
}
