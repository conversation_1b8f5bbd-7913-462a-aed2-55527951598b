<?php

declare(strict_types=1);

namespace Tests\Feature\Commands\Artisan;

use App\Console\Commands\Artisan\FirstWorkshop;
use App\Domain\Booking\Enums\BookingOrigin;
use App\Domain\Onboarding\Enums\OnboardingStatus;
use App\Mail\Artisan\FirstWorkshop as FirstWorkshopEmail;
use Carbon\CarbonImmutable;
use Database\Factories\BookingFactory;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\BookingCreator;
use Tests\Traits\EventCreator;
use Tests\Traits\OnboardingCreator;
use Tests\Traits\WorkshopCreator;

class FirstWorkshopCommandTest extends TestCase
{
    use ArtisanCreator;
    use BookingCreator;
    use EventCreator;
    use OnboardingCreator;
    use WorkshopCreator;

    #[Test]
    public function it_sends_email_to_eligible_artisan_with_first_upcoming_workshop(): void
    {
        Mail::fake();

        $eligibleArtisan = $this->createArtisan([
            'active' => true,
        ]);

        $this->createOnboarding([
            'artisan_id' => $eligibleArtisan->getKey(),
            'status' => OnboardingStatus::StatusIsOnboard,
        ]);

        $workshop = $this->createWorkshopForArtisan($eligibleArtisan);

        $confirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->addDays(2)->addHours(5),
            $confirmedBookings
        );

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertQueued(FirstWorkshopEmail::class, fn (FirstWorkshopEmail $mail) => $mail->assertTo($eligibleArtisan->email));
    }

    #[Test]
    public function it_does_not_send_email_to_artisan_with_upcoming_events_without_participants(): void
    {
        Mail::fake();

        $ineligibleArtisan = $this->createArtisan([
            'active' => true,
        ]);

        $this->createOnboarding([
            'artisan_id' => $ineligibleArtisan->getKey(),
            'status' => OnboardingStatus::StatusIsOnboard,
        ]);

        $workshop = $this->createWorkshopForArtisan($ineligibleArtisan);

        $this->createEventForWorkshopForDate($workshop, CarbonImmutable::now()->addDays(2)->addHours(5));

        // Should not be taken into account because the event is in more than 2 days
        $confirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->addDays(10)->addHours(5),
            $confirmedBookings
        );

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertNotQueued(FirstWorkshopEmail::class, function (FirstWorkshopEmail $mail) use ($ineligibleArtisan) {
            return collect($mail->to)->pluck('address')->contains($ineligibleArtisan->email);
        });
    }

    #[Test]
    public function it_does_not_send_email_to_artisan_that_have_already_been_notified_because_confirmed_event_is_between_now_and_2_days(): void
    {
        Mail::fake();

        $ineligibleArtisan = $this->createArtisan([
            'active' => true,
        ]);

        $this->createOnboarding([
            'artisan_id' => $ineligibleArtisan->getKey(),
            'status' => OnboardingStatus::StatusIsOnboard,
        ]);

        $workshop = $this->createWorkshopForArtisan($ineligibleArtisan, [
            'nb_minutes' => 30,
            'nb_heures' => 2,
            'nb_jours' => 0,
        ]);

        // Event confirmed but not in the past (it should have been notified before)
        $confirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->addDays(1)->addHours(5),
            $confirmedBookings
        );

        // Eligible event but should not be taken into account because another event has already been notified (but still not in the past)
        $otherConfirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->addDays(2)->addHours(5),
            $otherConfirmedBookings
        );

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertNotQueued(FirstWorkshopEmail::class, function (FirstWorkshopEmail $mail) use ($ineligibleArtisan) {
            return collect($mail->to)->pluck('address')->contains($ineligibleArtisan->email);
        });
    }

    #[Test]
    public function it_does_not_send_email_to_artisan_with_past_workshops(): void
    {
        Mail::fake();

        $ineligibleArtisan = $this->createArtisan([
            'active' => true,
        ]);
        $this->createOnboarding([
            'artisan_id' => $ineligibleArtisan->getKey(),
            'status' => OnboardingStatus::StatusIsOnboard,
        ]);

        $workshop = $this->createWorkshopForArtisan($ineligibleArtisan);

        $pastConfirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->subDays(5),
            $pastConfirmedBookings
        );

        $futurConfirmedBookings = [
            BookingFactory::new()
                ->forPlaces(2)
                ->withOrigin(BookingOrigin::Customer)
                ->confirmed(),
        ];
        $this->createEventWithBookingForAtelierForDate(
            $workshop,
            CarbonImmutable::now()->addDays(2)->addHours(5),
            $futurConfirmedBookings
        );

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertNotQueued(FirstWorkshopEmail::class, function (FirstWorkshopEmail $mail) use ($ineligibleArtisan) {
            return collect($mail->to)->pluck('address')->contains($ineligibleArtisan->email);
        });
    }

    #[Test]
    public function it_does_not_send_email_to_not_onboarded_artisan(): void
    {
        Mail::fake();

        $ineligibleArtisan = $this->createArtisan([
            'active' => true,
        ]);

        $this->createOnboarding([
            'artisan_id' => $ineligibleArtisan->getKey(),
            'status' => OnboardingStatus::NotOnboard,
        ]);

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertNotQueued(FirstWorkshopEmail::class, function (FirstWorkshopEmail $mail) use ($ineligibleArtisan) {
            return collect($mail->to)->pluck('address')->contains($ineligibleArtisan->email);
        });
    }

    #[Test]
    public function it_does_not_send_email_to_inactive_artisan(): void
    {
        Mail::fake();

        $ineligibleArtisan = $this->createArtisan([
            'active' => false,
        ]);

        $this->createOnboarding([
            'artisan_id' => $ineligibleArtisan->getKey(),
            'status' => OnboardingStatus::StatusIsOnboard,
        ]);

        $this->artisan(FirstWorkshop::class)->assertSuccessful();

        Mail::assertNotQueued(FirstWorkshopEmail::class, function (FirstWorkshopEmail $mail) use ($ineligibleArtisan) {
            return collect($mail->to)->pluck('address')->contains($ineligibleArtisan->email);
        });
    }
}
