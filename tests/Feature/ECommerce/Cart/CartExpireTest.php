<?php

declare(strict_types=1);

namespace Tests\Feature\ECommerce\Cart;

use App\Console\Commands\Cart\CartExpireBookingsCommand;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\Assertions\BookingAssertions;
use Tests\Traits\Assertions\CartItemAssertions;
use Tests\Traits\CartCreator;
use Tests\Traits\WorkshopCreator;

class CartExpireTest extends TestCase
{
    use BookingAssertions;
    use CartCreator;
    use CartItemAssertions;
    use WorkshopCreator;

    #[Test]
    public function expired_booking_is_soft_deleted(): void
    {
        $this->setCurrentDate(Carbon::now());

        $user = $this->createUser();
        $cart = $this->createCartForUser($user);

        $workshop = $this->createWorkshopWithOneEvent();
        $booking = $this->addEventBookingToCart(
            $cart,
            $workshop->evenements->first(),
            cartItemParams: [
                'expired_at' => Carbon::now()->subMinutes(2),
            ],
            bookingParams: [
                'expiration_date' => Carbon::now()->subMinutes(2),
            ]
        );

        $this->callCartExpireBookingsCommand();

        $booking->refresh();
        $cartItem = $cart->items()->withTrashed()->sole();

        $this->assertBookingTrashed($booking);
        $this->assertCartItemTrashed($cartItem);
    }

    protected function callCartExpireBookingsCommand(): void
    {
        $this->artisan(CartExpireBookingsCommand::class);
    }
}
