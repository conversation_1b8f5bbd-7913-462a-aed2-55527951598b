<?php

declare(strict_types=1);

namespace Tests\Feature\ECommerce\Cart;

use App\Console\Commands\Cart\CartSetAbandonedCommand;
use App\Console\Commands\Cart\CartTrashAbandonedCart;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\ECommerce\Enums\CartOrigin;
use App\Enums\Cart\CartStatus;
use App\Infrastructure\Crm\Customerio\CustomerioCartTransformer;
use App\Infrastructure\Crm\Customerio\Handlers\CustomerioInterface;
use App\Models\Panier;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterval;
use PHPUnit\Framework\Attributes\Test;
use Tests\Fakes\Crm\CustomerioFakeHandler;
use Tests\TestCase;
use Tests\Traits\BookingCreator;
use Tests\Traits\CartCreator;
use Tests\Traits\EventCreator;
use Tests\Traits\UserCreator;
use Tests\Traits\WorkshopCreator;

class CartAbandonedTest extends TestCase
{
    use BookingCreator;
    use CartCreator;
    use EventCreator;
    use UserCreator;
    use WorkshopCreator;

    private CustomerioFakeHandler $customerio;

    protected function setUp(): void
    {
        parent::setUp();

        $this->customerio = new CustomerioFakeHandler();
        $this->app->bind(
            CustomerioInterface::class,
            fn () => $this->customerio
        );
    }

    #[Test]
    public function cart_is_considered_abandoned(): void
    {
        // Let's generate many carts by intervals of 1 hour
        $from = Carbon::now()->subHours(5);
        $to = Carbon::now();
        $interval = CarbonInterval::hour();

        $from->toPeriod($to)->setDateInterval($interval)->forEach(function (Carbon $date): void {
            $this->createCart([
                'created_at' => $date,
                'updated_at' => $date,
            ]);
        });

        $this->artisan(CartSetAbandonedCommand::class);

        self::assertEquals(
            3,
            Panier::withTrashed()->where('status', CartStatus::Abandoned)->count(),
            "Couldn't assert the cart status was set to abandoned"
        );

        $this->customerio->assertCustomerEventTrackedNTimesWithParams(
            [
                'eventName' => CustomerioInterface::CART_ABANDONED,
            ],
            3,
        );
    }

    #[Test]
    public function booking_is_ignored_if_already_paid(): void
    {
        $user = $this->createUser();

        $cart = $this->createCartForUser($user, [
            'status' => CartStatus::Abandoned,
            'origin' => CartOrigin::Website,
            'created_at' => Carbon::now()->subDays(8),
            'updated_at' => Carbon::now()->subHours(8),
        ]);

        $event = $this->createEvent();
        $booking = $this->createBookingForEvent($event);
        $this->addBookingToCart($cart, $booking);

        $this->artisan(CartTrashAbandonedCart::class);

        $booking = $booking->refresh();

        self::assertEquals(BookingStatus::BOOKING_CONFIRMED, $booking->status, 'Expected to still be confirmed');
        self::assertNull($booking->deleted_at, 'Expected booking to no be deleted');
    }

    #[Test]
    public function cart_is_sent_to_customer_io(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user, [
            'created_at' => Carbon::now()->subWeek(),
            'updated_at' => Carbon::now()->subWeek(),
        ]);
        $workshop = $this->createWorkshopWithOneEvent();
        $this->addAmountPhysicalGiftCardToCart($cart);
        $this->addWorkshopGiftCardToCart($cart, $workshop, 'John Doe', 'Jane Doe', 2, '<EMAIL>', CarbonImmutable::tomorrow());
        $this->addEventBookingToCart($cart, $workshop->evenements->first());

        $dataForApi = CustomerioCartTransformer::formatForApi($cart->fresh());

        // Let's only check the format of the array, se we're extracting the keys
        self::assertEquals([
            'with_booking',
            'with_ticket',
            'with_value',
            'with_paper',
            'restore_cart_link',
            'items',
        ], array_keys($dataForApi));

        $items = $dataForApi['items'];

        self::assertEquals([
            'type',
            'name',
            'image',
            'link',
            'city',
            'workshop_id',
            'price',
            'currency',
            'recipient_name',
        ], array_keys($items[0]));

        self::assertEquals([
            'type',
            'name',
            'image',
            'link',
            'city',
            'workshop_id',
            'price',
            'currency',
            'recipient_name',
        ], array_keys($items[1]));

        self::assertEquals([
            'type',
            'name',
            'image',
            'link',
            'city',
            'workshop_id',
            'price',
            'currency',
            'recipient_name',
        ], array_keys($items[2]));

        self::assertEquals([
            'type',
            'name',
            'image',
            'link',
            'city',
            'workshop_id',
            'price',
        ], array_keys($items[3]));
    }
}
