<?php

declare(strict_types=1);

namespace Tests\Feature\ECommerce;

use App\Domain\ECommerce\CartMapper;
use App\Domain\ECommerce\CartService;
use App\Domain\ECommerce\Enums\CartOrigin;
use App\Domain\Gift\Enums\GiftTheme;
use App\Domain\Gift\Models\GiftCard;
use App\Enums\Country;
use App\Enums\Currency;
use App\Enums\DiscountType;
use App\Models\Panier;
use App\Models\Parrainage\CodeParrainageInvitation;
use App\Models\Parrainage\CodeParrainageUser;
use App\Models\Reductions\CadeauBox;
use App\Models\Reductions\CodeReductionGlobal;
use App\Models\Reductions\CodeReductionUser;
use App\Shared\Amount;
use App\Shared\ECommerce\Address;
use App\Shared\ECommerce\Cart;
use App\Shared\ECommerce\CartDiscount;
use App\Shared\ECommerce\CartItem;
use App\Shared\ECommerce\CartItemGiftCard;
use App\Shared\ECommerce\CartItemTicket;
use App\Shared\Percent;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AddressCreator;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\Assertions\AmountAssertions;
use Tests\Traits\BookingCreator;
use Tests\Traits\CartCreator;
use Tests\Traits\CodeParrainageInvitationCreator;
use Tests\Traits\CodeParrainageUserCreator;
use Tests\Traits\CodeReductionGlobalCreator;
use Tests\Traits\CodeReductionUserCreator;
use Tests\Traits\GiftBoxCreator;
use Tests\Traits\GiftCardCreator;
use Tests\Traits\WorkshopCreator;

class CartMapperTest extends TestCase
{
    use AddressCreator;
    use AmountAssertions;
    use ArtisanCreator;
    use BookingCreator;
    use CartCreator;
    use CodeParrainageInvitationCreator;
    use CodeParrainageUserCreator;
    use CodeReductionGlobalCreator;
    use CodeReductionUserCreator;
    use GiftBoxCreator;
    use GiftCardCreator;
    use WorkshopCreator;

    #[Test]
    public function ecommerce_cart_is_mapped_into_cart_shared_object(): void
    {
        $user = $this->createUser();
        $address = $this->createAddress([
            'nom' => 'mon domicile',
            'societe' => 'WeCanDoo',
            'adresse1' => '1 rue de la farandole',
            'adresse2' => '2eme étage',
            'code_postal' => '84000',
            'ville' => 'Avignon',
            'pays' => 'France',
            'destinataire' => 'Paul Bismuth',
            'telephone_contact' => '+330102030405',
            'informations_complementaires' => 'Attention, chat méchant',
        ]);
        $cart = $this->createCartForUser($user, ['adresse_id' => $address->id, 'currency' => Currency::GBP->value]);
        $artisan = $this->createArtisan(['nom' => 'Durand', 'prenom' => 'Michel', 'metier_nom' => 'barman']);
        $workshop = $this->createWorkshopWithOneEvent(
            ['prix' => 100, 'nom' => 'Un atelier', 'artisan_id' => $artisan->id, 'timezone' => 'Europe/Paris', 'currency' => Currency::GBP->value],
            ['start' => '2024-05-13 14:00:00', 'end' => '2024-05-13 17:00:00'],
        );
        $booking = $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2, ['currency' => Currency::GBP->value], ['telephone_contact' => '+33123456789']);

        $userDiscount = $this->createUserDiscount(10, 'montant', ['currency' => Currency::GBP->value]);
        $this->applyReductionOrGiftCardToCart($cart, $userDiscount);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertEquals(new Cart(
            $cart->id,
            CartOrigin::Website,
            Currency::GBP,
            Country::France,
            Amount::make(200_00, Currency::GBP),
            Amount::make(190_00, Currency::GBP),
            Amount::make(10_00, Currency::GBP),
            $cart->user->id,
            $cart->user->prenom,
            $cart->user->nom,
            $cart->user->email,
            $cart->user->phone,
            new Address(
                $cart->address->id,
                '1 rue de la farandole',
                '84000',
                'Avignon',
                'Paul Bismuth',
                'France',
                'mon domicile',
                'WeCanDoo',
                '2eme étage',
                '+330102030405',
                'Attention, chat méchant',
            ),
            Collection::make([
                new CartItem(
                    'Un atelier',
                    Amount::make(200_00, Currency::GBP),
                    Amount::make(200_00, Currency::GBP),
                    Amount::make(0, Currency::GBP),
                    Amount::make(100_00, Currency::GBP),
                    2,
                    Currency::GBP,
                    isGift: false,
                    shouldSendEmailToBeneficiary: false,
                    ticket: new CartItemTicket(
                        CarbonPeriodImmutable::create('2024-05-13 14:00:00', '2024-05-13 17:00:00'),
                        'Europe/Paris',
                        $booking->id,
                        $booking->evenement->atelier_id,
                        20,
                        '+33123456789',
                        2,
                        0,
                        'Michel Durand',
                    ),
                ),
            ]),
            Collection::make([
                new CartDiscount(
                    (new CodeReductionUser())->getMorphClass(),
                    $userDiscount->id,
                    $userDiscount->code_reduction,
                    Amount::make(10_00, Currency::GBP),
                    DiscountType::Amount,
                    10_00,
                ),
            ]),
        ), $mappedCart);
    }

    #[Test]
    public function ticket_gift_cards_are_mapped_as_well(): void
    {
        $workshop = $this->createWorkshop(['prix' => 100, 'nom' => 'Test atelier à offrir', 'sous_titre' => 'super atelier', 'image_vignette_id' => 1]);
        $cart = $this->createCartForUser($this->createUser());
        $this->addWorkshopGiftCardToCart($cart, $workshop, 'John Doe', 'Jane Doe', 2, '<EMAIL>', CarbonImmutable::tomorrow());
        $this->calculateCartPrices($cart);

        $mappedCart = $this->mapCart($cart, 'gb');

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertEquals(Country::GreatBritain, $mappedCart->country);

        $this->assertCartItems(Collection::make([
            new CartItem(
                'Test atelier à offrir',
                Amount::make(200_00, Currency::EURO),
                Amount::make(200_00, Currency::EURO),
                Amount::make(0, Currency::EURO),
                Amount::make(100_00, Currency::EURO),
                2,
                Currency::EURO,
                isGift: false,
                shouldSendEmailToBeneficiary: false,
                giftFrom: 'Jane Doe',
                giftTo: 'John Doe',
                giftRecipientEmail: '<EMAIL>',
                offeredAt: CarbonImmutable::tomorrow(),
                giftCard: new CartItemGiftCard(
                    giftFrom: 'Jane Doe',
                    giftTo: 'John Doe',
                    recipientEmail: '<EMAIL>',
                    adultTickets: 2,
                    childTickets: 0,
                    workshopId: $workshop->id,
                ),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function amount_gift_cards_with_physical_cards_are_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $address = $this->createAddress();
        $this->addAmountPhysicalGiftCardToCart(
            $cart,
            80,
            'Jane',
            'John',
            '<EMAIL>',
            CarbonImmutable::tomorrow(),
            $address,
        );
        $this->calculateCartPrices($cart);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCartItems(Collection::make([
            new CartItem(
                'Carte Cadeau',
                Amount::make(80_00, Currency::EURO),
                Amount::make(80_00, Currency::EURO),
                Amount::make(0, Currency::EURO),
                Amount::make(80_00, Currency::EURO),
                1,
                Currency::EURO,
                isGift: false,
                shouldSendEmailToBeneficiary: false,
                giftFrom: 'John',
                giftTo: 'Jane',
                giftRecipientEmail: '<EMAIL>',
                offeredAt: CarbonImmutable::tomorrow(),
                giftCard: new CartItemGiftCard(
                    giftFrom: 'John',
                    giftTo: 'Jane',
                    recipientEmail: '<EMAIL>',
                ),
                subItem: new CartItem(
                    'Carte papier',
                    Amount::make(5_00, Currency::EURO),
                    Amount::make(5_00, Currency::EURO),
                    Amount::make(0, Currency::EURO),
                    Amount::make(5_00, Currency::EURO),
                    1,
                    Currency::EURO,
                    isGift: false,
                    shouldSendEmailToBeneficiary: false,
                    address: new Address(
                        $address->id,
                        $address->adresse1,
                        $address->code_postal,
                        $address->ville,
                        $address->destinataire,
                        $address->pays,
                    )
                ),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_global_discount_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $globalDiscount = $this->createPercentGlobalDiscount(10);

        $workshop1 = $this->createWorkshopWithOneEvent(['prix' => 50]);
        $booking1 = $this->addEventBookingToCart($cart, $workshop1->evenements->first(), 3);

        $workshop2 = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $workshop2->artisan->update(['nom_alternatif' => 'Marcel Patulacci']);
        $booking2 = $this->addEventBookingToCart($cart, $workshop2->evenements->first(), 2);

        $this->applyReductionOrGiftCardToCart($cart, $globalDiscount);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(350_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(315_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(35_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(150_00, $mappedCart->items[0]->totalPrice);
        $this->assertCentsAmountEquals(135_00, $mappedCart->items[0]->finalPrice);
        $this->assertCentsAmountEquals(15_00, $mappedCart->items[0]->discountAmount);

        $this->assertCentsAmountEquals(200_00, $mappedCart->items[1]->totalPrice);
        $this->assertCentsAmountEquals(180_00, $mappedCart->items[1]->finalPrice);
        $this->assertCentsAmountEquals(20_00, $mappedCart->items[1]->discountAmount);

        $this->assertCartItems(Collection::make([
            new CartItem(
                $workshop1->nom,
                Amount::make(150_00, Currency::EURO),
                Amount::make(135_00, Currency::EURO),
                Amount::make(15_00, Currency::EURO),
                Amount::make(50_00, Currency::EURO),
                3,
                Currency::EURO,
                isGift: false,
                shouldSendEmailToBeneficiary: false,
                ticket: new CartItemTicket(
                    CarbonPeriodImmutable::create($booking1->evenement->start, $booking1->evenement->end),
                    'Europe/Paris',
                    $booking1->id,
                    $workshop1->id,
                    20,
                    $booking1->telephone_contact,
                    3,
                    0,
                    $workshop1->artisan->prenom.' '.$workshop1->artisan->nom,
                ),
            ),
            new CartItem(
                $workshop2->nom,
                Amount::make(200_00, Currency::EURO),
                Amount::make(180_00, Currency::EURO),
                Amount::make(20_00, Currency::EURO),
                Amount::make(100_00, Currency::EURO),
                2,
                Currency::EURO,
                isGift: false,
                shouldSendEmailToBeneficiary: false,
                ticket: new CartItemTicket(
                    CarbonPeriodImmutable::create($booking2->evenement->start, $booking2->evenement->end),
                    'Europe/Paris',
                    $booking2->id,
                    $workshop2->id,
                    20,
                    $booking2->telephone_contact,
                    2,
                    0,
                    'Marcel Patulacci',
                ),
            ),
        ]), $mappedCart);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeReductionGlobal())->getMorphClass(),
                $globalDiscount->id,
                $globalDiscount->code_reduction,
                Amount::make(35_00, Currency::EURO),
                DiscountType::Percent,
                10_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_amount_user_discount_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $userDiscount = $this->createUserDiscount(15, 'montant');

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $this->applyReductionOrGiftCardToCart($cart, $userDiscount);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(180_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(165_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(15_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(180_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(180_00, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeReductionUser())->getMorphClass(),
                $userDiscount->id,
                $userDiscount->code_reduction,
                Amount::make(15_00, Currency::EURO),
                DiscountType::Amount,
                15_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_percent_user_discount_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $userDiscount = $this->createUserDiscount(10, 'pourcentage');

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $this->applyReductionOrGiftCardToCart($cart, $userDiscount);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(180_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(162_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(18_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(180_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(162_00, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(18_00, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeReductionUser())->getMorphClass(),
                $userDiscount->id,
                $userDiscount->code_reduction,
                Amount::make(18_00, Currency::EURO),
                DiscountType::Percent,
                10_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_percent_invitation_sponsor_code_is_mapped_as_well(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user);
        $user->codeparrainageinvitation->montant = 12;
        $user->codeparrainageinvitation->type = 'pourcentage';
        $user->codeparrainageinvitation->save();

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1);

        $this->applyReductionOrGiftCardToCart($cart, $user->codeparrainageinvitation);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(90_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(79_20, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(10_80, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(79_20, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(10_80, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeParrainageInvitation())->getMorphClass(),
                $user->codeparrainageinvitation->id,
                $user->codeparrainageinvitation->code_reduction,
                Amount::make(10_80, Currency::EURO),
                DiscountType::Percent,
                12_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_amount_invitation_sponsor_code_is_mapped_as_well(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user);
        $user->codeparrainageinvitation->montant = 12;
        $user->codeparrainageinvitation->type = 'montant';
        $user->codeparrainageinvitation->save();

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1);

        $this->applyReductionOrGiftCardToCart($cart, $user->codeparrainageinvitation);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(90_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(78_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(12_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeParrainageInvitation())->getMorphClass(),
                $user->codeparrainageinvitation->id,
                $user->codeparrainageinvitation->code_reduction,
                Amount::make(12_00, Currency::EURO),
                DiscountType::Amount,
                12_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_user_wallet_is_mapped_as_well(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user);
        $user->codeparrainageuser->montant = 12;
        $user->codeparrainageuser->save();

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1);

        $this->applyReductionOrGiftCardToCart($cart, $user->codeparrainageuser);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(90_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(78_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(12_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeParrainageUser())->getMorphClass(),
                $user->codeparrainageuser->id,
                $user->codeparrainageuser->code_reduction,
                Amount::make(12_00, Currency::EURO),
                DiscountType::Amount,
                12_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function user_wallet_is_not_kept_when_order_is_already_paid_by_a_gift_card(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user);
        $user->codeparrainageuser->montant = 12;
        $user->codeparrainageuser->save();

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1);
        $giftCard = $this->createTicketGiftCard($workshop, 1);

        $this->applyReductionOrGiftCardToCart($cart, $user->codeparrainageuser, $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertFloatAmountEquals(0, $mappedCart->finalPrice);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard->id,
                $giftCard->code,
                Amount::fromFloat(90, Currency::EURO),
                DiscountType::Workshop,
                1,
                1,
                Amount::fromFloat(90, Currency::EURO),
                $workshop->id,
                Percent::fromFloat($workshop->commission_pourcentage),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function applied_amount_gift_card_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 90]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1);

        $giftCard = $this->createAmountGiftCard(50);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(90_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(40_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(50_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->totalPrice);
        $this->assertCentsAmountEquals(90_00, $mappedCart->items->first()->finalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->items->first()->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard->id,
                $giftCard->code,
                Amount::make(50_00, Currency::EURO),
                DiscountType::Amount,
                50_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function amount_gift_card_and_amount_reduction_can_be_applied_together(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $globalDiscount = $this->createAmountGlobalDiscount(10);

        $workshop1 = $this->createWorkshopWithOneEvent(['prix' => 80]);
        $this->addEventBookingToCart($cart, $workshop1->evenements->first(), 2);

        $giftCard = $this->createAmountGiftCard(50);
        $this->applyReductionOrGiftCardToCart($cart, $globalDiscount, $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(160_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->finalPrice);

        $this->assertCentsAmountEquals(160_00, $mappedCart->items[0]->totalPrice);
        $this->assertCentsAmountEquals(160_00, $mappedCart->items[0]->finalPrice);

        $this->assertCount(2, $mappedCart->discounts);
    }

    #[Test]
    public function bought_ticket_gift_cards_can_be_discounted_while_amount_gift_card_cannot(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $globalDiscount = $this->createPercentGlobalDiscount(10);

        $workshop = $this->createWorkshop(['prix' => 60]);

        $this->addWorkshopGiftCardToCart($cart, $workshop);
        $this->addAmountPhysicalGiftCardToCart($cart, 80);

        $this->applyReductionOrGiftCardToCart($cart, $globalDiscount);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(145_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(139_00, $mappedCart->finalPrice); // Discount is only applied on workshop gift card

        $ticketGiftCardItem = $mappedCart->items[0];
        $amountGiftCardItem = $mappedCart->items[1];
        $physicalCardItem = $mappedCart->items[1]->subItem;

        $this->assertCentsAmountEquals(60_00, $ticketGiftCardItem->totalPrice);
        $this->assertCentsAmountEquals(54_00, $ticketGiftCardItem->finalPrice);

        $this->assertCentsAmountEquals(80_00, $amountGiftCardItem->totalPrice);
        $this->assertCentsAmountEquals(80_00, $amountGiftCardItem->finalPrice);

        $this->assertCentsAmountEquals(5_00, $physicalCardItem->totalPrice);
        $this->assertCentsAmountEquals(5_00, $physicalCardItem->finalPrice);
    }

    #[Test]
    public function amount_gift_card_and_percent_reduction_can_be_summed(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $globalDiscount = $this->createPercentGlobalDiscount(10);

        $workshop1 = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop1->evenements->first(), 1);

        $giftCard = $this->createAmountGiftCard(50);

        $this->applyReductionOrGiftCardToCart($cart, $globalDiscount, $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(100_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(40_00, $mappedCart->finalPrice);

        $this->assertCentsAmountEquals(100_00, $mappedCart->items[0]->totalPrice);
        $this->assertCentsAmountEquals(90_00, $mappedCart->items[0]->finalPrice);

        $this->assertCount(2, $mappedCart->discounts);
    }

    #[Test]
    public function several_amount_gift_card_can_be_mapped_above_total_price_limit_and_each_applied_amount_are_calculated(): void
    {
        $cart = $this->createCartForUser($this->createUser());

        $workshop1 = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop1->evenements->first(), 1);

        $giftCard1 = $this->createAmountGiftCard(80);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard1);
        $giftCard2 = $this->createAmountGiftCard(40);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard2);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(100_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->finalPrice);

        $this->assertCentsAmountEquals(80_00, $mappedCart->discounts->firstWhere('code', $giftCard1->code)->appliedAmount);
        $this->assertEquals(80_00, $mappedCart->discounts->firstWhere('code', $giftCard1->code)->originalValue);
        $this->assertCentsAmountEquals(20_00, $mappedCart->discounts->firstWhere('code', $giftCard2->code)->appliedAmount);
        $this->assertEquals(40_00, $mappedCart->discounts->firstWhere('code', $giftCard2->code)->originalValue);
    }

    #[Test]
    public function applied_ticket_gift_card_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $giftCard = $this->createTicketGiftCard($workshop, 1);

        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(200_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard->id,
                $giftCard->code,
                Amount::make(100_00, Currency::EURO),
                DiscountType::Workshop,
                1,
                1,
                Amount::make(100_00, Currency::EURO),
                $workshop->id,
                Percent::fromFloat($workshop->commission_pourcentage),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function ticket_gift_card_applied_can_make_an_order_item_as_gift(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 50]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $giftCard = $this->createTicketGiftCard($workshop, 3, [
            'commission_percent' => 30,
        ]);

        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(100_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->discountAmount);

        $this->assertTrue($mappedCart->items[0]->isGift);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard->id,
                $giftCard->code,
                Amount::make(100_00, Currency::EURO),
                DiscountType::Workshop,
                3,
                2,
                Amount::make(50_00, Currency::EURO),
                $workshop->id,
                Percent::fromFloat(30),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function ticket_gift_card_created_with_old_workshop_price_is_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $giftCard = $this->createTicketGiftCard($workshop, 3, ['value' => 3 * 90, 'remaining_value' => 3 * 90]);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(200_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(200_00, $mappedCart->discountAmount);

        $this->assertTrue($mappedCart->items[0]->isGift);
        $this->assertCentsAmountEquals(200_00, $mappedCart->items[0]->totalPrice);
        $this->assertCentsAmountEquals(200_00, $mappedCart->items[0]->finalPrice);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard->id,
                $giftCard->code,
                Amount::make(200_00, Currency::EURO),
                DiscountType::Workshop,
                3,
                2,
                Amount::make(90_00, Currency::EURO),
                $workshop->id,
                Percent::fromFloat($workshop->commission_pourcentage),
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function several_discounts_can_be_mixed(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 4);

        $userCode = $this->createUserDiscount(10, 'pourcentage');
        $this->applyReductionOrGiftCardToCart($cart, reduction: $userCode);

        $giftCard1 = $this->createTicketGiftCard($workshop, 2, ['remaining_ticket' => 1]);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard1);

        $giftCard2 = $this->createAmountGiftCard(200, ['remaining_value' => 100]);
        $this->applyReductionOrGiftCardToCart($cart, giftCard: $giftCard2);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(400_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(160_00, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(240_00, $mappedCart->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeReductionUser())->getMorphClass(),
                $userCode->id,
                $userCode->code_reduction,
                Amount::make(40_00, Currency::EURO),
                DiscountType::Percent,
                10_00,
            ),
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard1->id,
                $giftCard1->code,
                Amount::make(100_00, Currency::EURO),
                DiscountType::Workshop,
                1,
                1,
                Amount::make(100_00, Currency::EURO),
                $workshop->id,
                Percent::fromFloat($workshop->commission_pourcentage),
            ),
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard2->id,
                $giftCard2->code,
                Amount::make(100_00, Currency::EURO),
                DiscountType::Amount,
                100_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function user_pool_can_pay_the_whole_order(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $this->updateUserWallet($cart->user, 100);

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 20]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 2);

        $this->addWorkshopGiftCardToCart($cart, $this->createWorkshop(['prix' => 30]));
        $this->addAmountPhysicalGiftCardToCart($cart, 20);

        $this->applyReductionOrGiftCardOnCart($cart, $cart->user->codeparrainageuser);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertFloatAmountEquals(95, $mappedCart->totalPrice);
        $this->assertFloatAmountEquals(0, $mappedCart->finalPrice);
        $this->assertFloatAmountEquals(95, $mappedCart->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeParrainageUser())->getMorphClass(),
                $cart->user->codeparrainageuser->id,
                $cart->user->codeparrainageuser->code_reduction,
                Amount::fromFloat(95, Currency::EURO),
                DiscountType::Amount,
                100_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function discount_and_user_pool_and_gift_cards_can_be_mixed(): void
    {
        $cart = $this->createCartForUser($this->createUser());
        $this->updateUserWallet($cart->user, 100);
        $globalDiscount = $this->createPercentGlobalDiscount(10);

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $this->addEventBookingToCart($cart, $workshop->evenements->first(), 4);

        $giftCard1 = $this->createAmountGiftCard(200);

        $this->applyReductionOrGiftCardOnCart($cart, $globalDiscount);
        $this->applyReductionOrGiftCardOnCart($cart, $cart->user->codeparrainageuser);
        $this->applyReductionOrGiftCardOnCart($cart, giftCard: $giftCard1);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertFloatAmountEquals(400, $mappedCart->totalPrice);
        $this->assertFloatAmountEquals(60, $mappedCart->finalPrice);
        $this->assertFloatAmountEquals(340, $mappedCart->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CodeReductionGlobal())->getMorphClass(),
                $globalDiscount->id,
                $globalDiscount->code_reduction,
                Amount::fromFloat(40, Currency::EURO),
                DiscountType::Percent,
                10_00,
            ),
            new CartDiscount(
                (new CodeParrainageUser())->getMorphClass(),
                $cart->user->codeparrainageuser->id,
                $cart->user->codeparrainageuser->code_reduction,
                Amount::fromFloat(100, Currency::EURO),
                DiscountType::Amount,
                100_00,
            ),
            new CartDiscount(
                (new GiftCard())->getMorphClass(),
                $giftCard1->id,
                $giftCard1->code,
                Amount::make(200_00, Currency::EURO),
                DiscountType::Amount,
                200_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function gift_box_can_be_mapped_as_well(): void
    {
        $cart = $this->createCartForUser($this->createUser(), ['adresse_id' => null]);
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100]);
        $booking = $this->createPendingBookingForEvent($workshop->evenements->first());

        $giftBox = $this->createGiftCardBoxForWorkshop($workshop, 120);

        CartService::addBookingToCart($booking, $cart);
        $cart->applyCadeauBox($giftBox);

        $mappedCart = $this->mapCart($cart);

        $this->assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum($mappedCart);

        $this->assertCentsAmountEquals(100_00, $mappedCart->totalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->finalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->discountAmount);

        $this->assertCentsAmountEquals(100_00, $mappedCart->items[0]->totalPrice);
        $this->assertCentsAmountEquals(100_00, $mappedCart->items[0]->finalPrice);
        $this->assertCentsAmountEquals(0, $mappedCart->items[0]->discountAmount);

        $this->assertCartDiscounts(Collection::make([
            new CartDiscount(
                (new CadeauBox())->getMorphClass(),
                $giftBox->id,
                $giftBox->code_reduction,
                Amount::make(100_00, Currency::EURO),
                DiscountType::Amount,
                120_00,
            ),
        ]), $mappedCart);
    }

    #[Test]
    public function gifts_data_is_mapped(): void
    {
        $user = $this->createUser();
        $cart = $this->createCartForUser($user);
        // Add booking as gift

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 100, 'nom' => 'Test atelier à offrir', 'sous_titre' => 'super atelier', 'image_vignette_id' => 1]);
        $booking = $this->addEventBookingToCart($cart, $workshop->evenements->first(), 1, cartItemParams: [
            'gift_from' => 'Toto',
            'gift_to' => 'Tata',
            'gift_recipient_email' => '<EMAIL>',
            'gift_message' => 'This is my gift message',
            'gift_theme' => GiftTheme::Pottery,
            'should_send_email_to_beneficiary' => true,
            'offered_at' => CarbonImmutable::today()->addWeek(),
        ]);

        $this->assertCartItems(Collection::make([
            new CartItem(
                'Test atelier à offrir',
                Amount::make(100_00, Currency::EURO),
                Amount::make(100_00, Currency::EURO),
                Amount::make(0, Currency::EURO),
                Amount::make(100_00, Currency::EURO),
                1,
                Currency::EURO,
                isGift: false,
                shouldSendEmailToBeneficiary: true,
                giftFrom: 'Toto',
                giftTo: 'Tata',
                giftRecipientEmail: '<EMAIL>',
                giftMessage: 'This is my gift message',
                giftTheme: GiftTheme::Pottery,
                offeredAt: CarbonImmutable::today()->addWeek(),
                ticket: new CartItemTicket(
                    CarbonPeriodImmutable::create($booking->evenement->start, $booking->evenement->end),
                    'Europe/Paris',
                    $booking->id,
                    $booking->evenement->atelier_id,
                    20,
                    $booking->telephone_contact,
                    1,
                    0,
                    $booking->evenement->atelier->artisan->getDenomination(),
                ),
                giftCard: null,
                subItem: null,
            ),
        ]), $this->mapCart($cart));
    }

    private function mapCart(Panier $cart, string $countryCode = 'fr'): Cart
    {
        /** @var CartMapper $mapper */
        $mapper = $this->app->make(CartMapper::class);

        return $mapper->map($cart, $countryCode, $cart->user);
    }

    private function assertCartItems(Collection $items, Cart $cart): void
    {
        $this->assertEquals($items, $cart->items);
    }

    private function assertCartDiscounts(Collection $discounts, Cart $cart): void
    {
        $this->assertEquals($discounts, $cart->discounts);
    }

    private function assertMappedCartFinalPriceEqualsItemsSumSubstractedByDiscountSum(Cart $cart): void
    {
        $cartFinalPrice = $cart->finalPrice->value();

        $itemsSum = $cart->items
            ->map(fn (CartItem $cartItem) => $cartItem->totalPrice->value() + $cartItem->subItem?->totalPrice->value())
            ->sum();

        $discountsSum = $cart->discounts
            ->map(fn (CartDiscount $discount) => $discount->appliedAmount->value())
            ->sum();

        $this->assertEquals(
            $cartFinalPrice,
            $itemsSum - $discountsSum,
            "Failed asserting cart final price {$cartFinalPrice} equals items sum {$itemsSum} substracted by discounts sum {$discountsSum}"
        );
    }
}
