<?php

declare(strict_types=1);

namespace Tests\Feature\Content;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\WorkshopCreator;

class GetWorkshopApiTest extends TestCase
{
    use WorkshopCreator;

    #[Test]
    public function find_workshop_by_id(): void
    {
        $workshop = $this->createWorkshop();

        $route = route('front.api.workshops.find', $workshop);

        $this->getJson($route)->assertJson([
            'data' => [
                'id' => $workshop->id,
                'name' => $workshop->nom,
                'price' => $workshop->prix,
                'permalink' => $workshop->permalien,
                'maxTickets' => $workshop->nb_pers_max,
                'format' => [
                    'id' => $workshop->format->id,
                    'name' => $workshop->format->name,
                ],
                'place' => [
                    'id' => $workshop->lieu->id,
                    'name' => $workshop->lieu->lieu_nom,
                    'district' => $workshop->lieu->district,
                    'city' => $workshop->lieu->city,
                ],
            ],
        ]);
    }

    #[Test]
    public function find_invalid_workshop_id(): void
    {
        $route = route('front.api.workshops.find', 4242);

        $this->getJson($route)->assertNotFound();
    }
}
