<?php

declare(strict_types=1);

namespace Tests\Feature\Content\Cities;

use Database\Factories\VilleFactory as CityFactory;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AdminCreator;

class ActivateCityRequestTest extends TestCase
{
    use AdminCreator;

    #[Test]
    public function activate_city(): void
    {
        $admin = $this->createAdmin();

        $city = CityFactory::new()
            ->with<PERSON>er<PERSON><PERSON>('test')
            ->create();

        Storage::fake('images');

        $this
            ->actingAsAdmin($admin)
            ->get(route('admin.cities.activate', $city))
            ->assertRedirectToRoute('admin.cities.index')
            ->assertSessionDoesntHaveErrors();

        $this->assertNotNull($city->singlePage);
        $this->get(route('singlePage.show', $city->singlePage->slug))
            ->assertSuccessful();
    }
}
