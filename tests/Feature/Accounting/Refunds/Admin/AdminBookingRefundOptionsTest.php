<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Refunds\Admin;

use App\Domain\Accounting\Refunds\Enums\RefundType;
use App\Domain\Gift\Models\GiftCard;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Refunds\RefundTestCase;
use Tests\Traits\Assertions\BookingAssertions;
use Tests\Traits\Assertions\OrderItemRefundAssertions;
use Tests\Traits\BookingCreator;

class AdminBookingRefundOptionsTest extends RefundTestCase
{
    use BookingAssertions;
    use BookingCreator;
    use OrderItemRefundAssertions;

    #[Test]
    public function refund_options_are_displayed(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 50]);
        $workshop2 = $this->createWorkshopWithOneEvent(['prix' => 60]);

        $order = $this->createOrder();

        $bookingToRefund = $this->addEventBookingToOrder($order, $workshop->evenements->first(), 3);
        $this->addEventBookingToOrder($order, $workshop2->evenements->first(), 1);

        $globalDiscount = $this->applyGlobalDiscountToOrder($order, $this->createPercentGlobalDiscount(10));
        $giftCardDiscount = $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 2, ['original_ticket_unit_value' => $this->euros(40)]);
        $stripePayment = $this->addStripeCardOrderPayment($order, amount: $this->euros(99));

        $this->actingAsAdmin()
            ->get(route('admin.reservations.available-refund-methods', $bookingToRefund))
            ->assertSuccessful()
            ->assertJson([
                'itemInformations' => [
                    'totalPrice' => ['value' => 15000, 'currency' => 'EUR'],
                    'unitPrice' => ['value' => 5000, 'currency' => 'EUR'],
                ],
            ])
            ->assertJson([
                'availableRefundMethods' => [
                    ['value' => 'giftcard', 'label' => 'Gift card value'],
                    ['value' => 'stripe', 'label' => 'Stripe'],
                ],
            ])
            ->assertJson([
                'remainingPaymentMethods' => [
                    [
                        'type' => GiftCard::class,
                        'id' => $giftCardDiscount->relatedDiscount->id,
                        'paymentMethodType' => 'workshop_gift_card',
                        'amount' => ['value' => 8000, 'currency' => 'EUR'],
                        'tickets' => 2,
                        'realAmount' => ['value' => 8000, 'currency' => 'EUR'],
                        'orderPaymentType' => 'order_discount',
                        'orderPaymentId' => $giftCardDiscount->id,
                    ],
                    [
                        'type' => 'order_payment',
                        'id' => $stripePayment->id,
                        'paymentMethodType' => 'stripe',
                        'amount' => ['value' => 9900, 'currency' => 'EUR'],
                        'tickets' => 0,
                        'realAmount' => ['value' => 9900, 'currency' => 'EUR'],
                        'orderPaymentType' => 'order_payment',
                        'orderPaymentId' => $stripePayment->id,
                    ],
                ],
            ]);
    }

    #[Test]
    public function specific_refund_option_is_displayed(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 50]);
        $workshop2 = $this->createWorkshopWithOneEvent(['prix' => 60]);

        $order = $this->createOrder();

        $bookingToRefund = $this->addEventBookingToOrder($order, $workshop->evenements->first(), 3);
        $this->addEventBookingToOrder($order, $workshop2->evenements->first(), 1);

        $globalDiscount = $this->applyGlobalDiscountToOrder($order, $this->createPercentGlobalDiscount(10));
        $giftCardDiscount = $this->applyTicketGiftCardDiscountToOrder($order, $this->createTicketGiftCard($workshop), 2, ['original_ticket_unit_value' => $this->euros(40)]);
        $stripePayment = $this->addStripeCardOrderPayment($order, amount: $this->euros(99));

        $this->actingAsAdmin()
            ->call('GET', route('admin.reservations.refund-method', $bookingToRefund), [
                'refund_method' => RefundType::Stripe->value,
                'refund_quantity' => $bookingToRefund->nb_places,
            ])
            ->assertSuccessful()
            ->assertExactJson([
                'refundMethod' => [
                    'choice' => 'stripe',
                    'quantity' => 3,
                    'refundAmount' => ['value' => 12500, 'currency' => 'EUR'],
                    'refundMethods' => [
                        [
                            'type' => 'stripe',
                            'amount' => ['value' => 4500, 'currency' => 'EUR'],
                        ],
                        [
                            'type' => 'giftcard',
                            'amount' => ['value' => 8000, 'currency' => 'EUR'],
                        ],
                    ],
                    'allocation' => [
                        [
                            'type' => 'order_payment',
                            'id' => $stripePayment->id,
                            'paymentMethodType' => 'stripe',
                            'amount' => ['value' => 4500, 'currency' => 'EUR'],
                            'tickets' => 0,
                            'realAmount' => ['value' => 4500, 'currency' => 'EUR'],
                            'orderPaymentType' => 'order_payment',
                            'orderPaymentId' => $stripePayment->id,
                        ],
                        [
                            'type' => GiftCard::class,
                            'id' => $giftCardDiscount->relatedDiscount->id,
                            'paymentMethodType' => 'workshop_gift_card',
                            'amount' => ['value' => 8000, 'currency' => 'EUR'],
                            'tickets' => 2,
                            'realAmount' => ['value' => 10000, 'currency' => 'EUR'],
                            'orderPaymentType' => 'order_discount',
                            'orderPaymentId' => $giftCardDiscount->id,
                        ],
                    ],
                ],
            ]);
    }
}
