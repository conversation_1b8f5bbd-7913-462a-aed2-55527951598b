<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Refunds\Admin;

use App\Domain\Accounting\Orders\Models\OrderItemRefund;
use App\Domain\Accounting\Refunds\Enums\RefundType;
use App\Domain\Booking\Enums\BookingStatusChangeOrigin;
use App\Domain\Gift\Enums\GiftCardCreationReason;
use App\Domain\Gift\Models\GiftCard;
use App\Payment\Exceptions\RefundException;
use App\Payment\Interfaces\RefundGateway;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\Fakes\Stripe\FakeStripeRefundAdapter;
use Tests\Feature\Accounting\Refunds\RefundTestCase;
use Tests\Traits\Assertions\BookingAssertions;
use Tests\Traits\Assertions\GiftCardAssertion;
use Tests\Traits\Assertions\OrderItemRefundAssertions;
use Tests\Traits\BookingCreator;
use Tests\Traits\CommissionInvoiceCreator;
use Tests\Traits\OrderItemRefundCreator;

class AdminGiftCardCancellationTest extends RefundTestCase
{
    use BookingAssertions;
    use BookingCreator;
    use CommissionInvoiceCreator;
    use GiftCardAssertion;
    use OrderItemRefundAssertions;
    use OrderItemRefundCreator;

    #[Test]
    public function order_gift_card_can_be_refunded_into_gift_card(): void
    {
        $this->app->singleton(RefundGateway::class, fn () => new FakeStripeRefundAdapter('refundId1234'));

        Mail::fake();
        $admin = $this->createAdmin();
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);
        $order = $this->createOrder();
        $this->addGiftCardItemToOrder($order, $giftCard);
        $this->applyAmountGiftCardDiscountToOrder($order, $this->createAmountGiftCard(30));
        $stripePayment = $this->addStripeCardOrderPayment($order, amount: $this->euros(40));

        $this->actingAsAdmin($admin)
            ->from(route('admin.gift-card.show', $giftCard))
            ->post(route('admin.gift-card.cancel', $giftCard), [
                'change_origin' => BookingStatusChangeOrigin::Wecandoo->value,
                'change_reason' => 'bla',
                'refund_method' => RefundType::Stripe->value,
            ])
            ->assertRedirect(route('admin.gift-card.show', $giftCard));

        $order->refresh();
        $orderItem = $order->items->first();

        $orderItemRefund = $this->assertOrderItemHasRefund($order, $orderItem);

        $this->assertOrderItemRefundAmount($this->euros(70), $orderItemRefund);
        $this->assertOrderItemRefundQuantity(1, $orderItemRefund);
        $this->assertOrderItemRefundOrigin(BookingStatusChangeOrigin::Wecandoo, $orderItemRefund);
        $this->assertOrderItemRefundComment('bla', $orderItemRefund);
        $this->assertOrderItemRefundCauser($admin, $orderItemRefund);

        $this->assertOrderItemRefundAmount($this->euros(70), $orderItemRefund);
        $this->assertOrderItemRefundRefundedOrderPayment($stripePayment, $orderItemRefund);
        $this->assertOrderItemRefundPaymentMethodsCount(2, $orderItemRefund);

        // Stripe refund method
        $stripeMethod = $orderItemRefund->orderItemRefundMethods->first();
        $this->assertOrderItemRefundMethodAmount($this->euros(40), $stripeMethod);
        $this->assertOrderItemRefundMethodHasStripeRefundId('refundId1234', $stripeMethod);
        $this->assertOrderItemRefundMethodType(RefundType::Stripe, $stripeMethod);

        // Gift card refund method
        $giftCardMethod = $orderItemRefund->orderItemRefundMethods->get(1);
        $this->assertOrderItemRefundMethodAmount($this->euros(30), $giftCardMethod);
        $this->assertOrderItemRefundMethodHasGiftCard($giftCardMethod);
        $this->assertOrderItemRefundMethodType(RefundType::GiftCard, $giftCardMethod);

        $giftCard->refresh();

        $this->assertGiftCardStatusIsCancelled($giftCard);
        $this->assertNotNull($giftCard->cancelled_at);
        $this->assertGiftCardRefundedAmount(70, $giftCard);

        /** @var GiftCard $createdGiftCard */
        $createdGiftCard = GiftCard::orderByDesc('id')->first();

        $this->assertFalse($createdGiftCard->is($giftCard));
        $this->assertGiftCardRefundedAmount(0, $createdGiftCard);
        $this->assertGiftCardCreationReason(GiftCardCreationReason::Refund, $createdGiftCard);
        $this->assertGiftCardValue(30, $createdGiftCard);
        $this->assertGiftCardRemainingValue(30, $createdGiftCard);
        $this->assertIsAmountGiftCard($createdGiftCard);
        $this->assertGiftCardStatusIsActive($createdGiftCard);
    }

    #[Test]
    public function if_no_refund_is_selected_then_gift_card_is_simply_cancelled(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);
        $order = $this->createOrder();
        $this->addGiftCardItemToOrder($order, $giftCard);
        $this->addStripeCardOrderPayment($order, amount: $this->euros(70));

        $this->actingAsAdmin()
            ->from(route('admin.gift-card.show', $giftCard))
            ->post(route('admin.gift-card.cancel', $giftCard), [
                'change_origin' => BookingStatusChangeOrigin::Artisan->value,
                'change_reason' => 'bla',
                'refund_method' => 'no_refund',
            ])
            ->assertRedirect(route('admin.gift-card.show', $giftCard));

        $giftCard->refresh();

        $this->assertCount(0, OrderItemRefund::all());

        $this->assertGiftCardStatusIsCancelled($giftCard);
        $this->assertNotNull($giftCard->cancelled_at);
        $this->assertGiftCardRefundedAmount(0, $giftCard);
    }

    #[Test]
    public function a_gift_card_without_order_can_be_cancelled_without_refund(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);

        $this->actingAsAdmin()
            ->from(route('admin.gift-card.show', $giftCard))
            ->post(route('admin.gift-card.cancel', $giftCard), [
                'change_origin' => BookingStatusChangeOrigin::Wecandoo->value,
                'change_reason' => 'bla',
                'refund_method' => 'no_refund',
            ])
            ->assertRedirect(route('admin.gift-card.show', $giftCard));

        $giftCard->refresh();

        $this->assertCount(0, OrderItemRefund::all());

        $this->assertGiftCardStatusIsCancelled($giftCard);
        $this->assertNotNull($giftCard->cancelled_at);
        $this->assertGiftCardRefundedAmount(0, $giftCard);
    }

    #[Test]
    public function if_an_error_occurs_during_refund_process_then_nothing_is_saved(): void
    {
        $refundAdapter = new FakeStripeRefundAdapter(throw: new RefundException());
        $this->app->singleton(RefundGateway::class, fn () => $refundAdapter);

        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);
        $order = $this->createOrder();
        $this->addGiftCardItemToOrder($order, $giftCard);
        $this->addStripeCardOrderPayment($order, amount: $this->euros(70));

        $this->actingAsAdmin()
            ->from(route('admin.gift-card.show', $giftCard))
            ->post(route('admin.gift-card.cancel', $giftCard), [
                'change_origin' => BookingStatusChangeOrigin::Customer->value,
                'change_reason' => 'bla',
                'refund_method' => RefundType::Stripe->value,
            ])
            ->assertRedirect(route('admin.gift-card.show', $giftCard));

        $this->assertCount(0, OrderItemRefund::all());
    }

    #[Test]
    public function selected_refund_type_should_be_available(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);
        $order = $this->createOrder();
        $this->addGiftCardItemToOrder($order, $giftCard);
        $this->applyAmountGiftCardDiscountToOrder($order, $this->createAmountGiftCard(70));

        $this->actingAsAdmin()
            ->from(route('admin.gift-card.show', $giftCard))
            ->post(route('admin.gift-card.cancel', $giftCard), [
                'change_origin' => BookingStatusChangeOrigin::Artisan->value,
                'change_reason' => 'bla',
                'refund_method' => RefundType::Stripe->value, // Stripe is not available in this case
            ])
            ->assertRedirect(route('admin.gift-card.show', $giftCard));

        $this->assertCount(0, OrderItemRefund::all());
    }

    #[Test]
    public function refund_form_data_are_validated(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 70]);
        $giftCard = $this->createTicketGiftCard($workshop);
        $order = $this->createOrder();
        $this->addGiftCardItemToOrder($order, $giftCard);
        $this->applyAmountGiftCardDiscountToOrder($order, $this->createAmountGiftCard(70));

        foreach ($this->failingFormDataProvider() as [$providedData, $expectedErrors]) {
            $this->actingAsAdmin()
                ->from(route('admin.gift-card.show', $giftCard))
                ->post(route('admin.gift-card.cancel', $giftCard), [
                    'change_origin' => BookingStatusChangeOrigin::Artisan->value,
                    'change_reason' => 'bla',
                    'refund_method' => RefundType::Stripe->value,
                    ...$providedData,
                ])
                ->assertRedirect(route('admin.gift-card.show', $giftCard));

            $this->assertCount(0, OrderItemRefund::all());
        }
    }

    private function failingFormDataProvider(): \Generator
    {
        yield [
            ['change_origin' => null],
            ['change_origin'],
        ];

        yield [
            ['change_origin' => 'unknown_origin'],
            ['change_origin'],
        ];

        yield [
            ['change_origin' => BookingStatusChangeOrigin::CutNotReach->value],
            ['change_origin'],
        ];

        yield [
            ['change_reason' => null],
            ['change_reason'],
        ];

        yield [
            ['refund_method' => null],
            ['refund_method'],
        ];

        yield [
            ['refund_method' => 'unknown_method'],
            ['refund_method'],
        ];
    }
}
