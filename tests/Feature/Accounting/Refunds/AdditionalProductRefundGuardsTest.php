<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Refunds;

use App\Domain\Accounting\Refunds\Exceptions\AdditionalProductRefundError;
use App\Domain\Accounting\Refunds\Exceptions\AdditionalProductRefundNotPossible;
use App\Domain\Accounting\Refunds\Exceptions\OrderRefundError;
use App\Domain\Accounting\Refunds\Exceptions\OrderRefundNotPossible;
use App\Domain\ECommerce\Enums\ProductStatus;
use App\Models\ProduitComplementaire;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\OrderItemRefundCreator;

class AdditionalProductRefundGuardsTest extends RefundTestCase
{
    use OrderItemRefundCreator;

    #[Test]
    public function already_refunded_additional_product_cannot_be_refunded_twice(): void
    {
        $this->expectRejectedAdditionalRefund(AdditionalProductRefundError::AlreadyOccurred);

        $additionalProduct = $this->createOrderedAdditionalProduct();
        $this->createStripeOrderRefundForOrderItem($additionalProduct->commande->items->get(1));

        $this->getAdditionalProductRefundOptions($additionalProduct);
    }

    #[Test]
    public function already_refunded_additional_product_cannot_be_refunded_again(): void
    {
        $this->expectRejectedAdditionalRefund(AdditionalProductRefundError::AlreadyOccurred);

        $additionalProduct = $this->createOrderedAdditionalProduct(['montant_rembourse' => 20]);

        $this->getAdditionalProductRefundOptions($additionalProduct);
    }

    #[Test]
    public function if_gift_card_status_is_already_cancelled_then_refund_is_rejected(): void
    {
        $this->expectRejectedAdditionalRefund(AdditionalProductRefundError::StatusNotValid);

        $giftCard = $this->createOrderedAdditionalProduct(['status' => ProductStatus::PRODUCT_CANCELLED]);

        $this->getAdditionalProductRefundOptions($giftCard);
    }

    private function expectRejectedAdditionalRefund(AdditionalProductRefundError $expectedState): void
    {
        $this->expectExceptionObject(AdditionalProductRefundNotPossible::fromState($expectedState));
    }

    private function expectRejectedOrderRefund(OrderRefundError $expectedState): void
    {
        $this->expectExceptionObject(OrderRefundNotPossible::fromState($expectedState));
    }

    private function createOrderedAdditionalProduct(array $additionalParams = []): ProduitComplementaire
    {
        $giftCard = $this->createAmountGiftCard(100);
        $order = $this->createOrder();
        $orderItem = $this->addGiftCardItemToOrder($order, $giftCard);
        $additionalItem = $this->addAdditionalProductForOrderItem($orderItem, params: $additionalParams);
        $this->addStripeCardOrderPayment($order, amount: $this->euros(100));

        return $additionalItem->item;
    }
}
