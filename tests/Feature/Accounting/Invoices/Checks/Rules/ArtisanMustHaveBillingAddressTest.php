<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Invoices\Checks\Rules;

use App\Domain\Accounting\Invoices\Services\ArtisanChecks\Rules\ArtisanInvoicesChecker;
use App\Domain\Accounting\Invoices\Services\ArtisanChecks\Rules\ArtisanMustHaveBillingAddress;
use App\Shared\YearMonth;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Invoices\Checks\ArtisanInvoiceCheckTestCase;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\InformationFacturationCreator;

class ArtisanMustHaveBillingAddressTest extends ArtisanInvoiceCheckTestCase
{
    use ArtisanCreator;
    use InformationFacturationCreator;

    #[Test]
    public function if_artisan_has_a_consistent_billing_address_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $this->createInformationFacturationForModel($artisan, [
            'adresse1' => '123 test street',
            'code_postal' => 'Test City',
            'ville' => 'Test City',
            'pays' => 'Tanzania',
        ]);

        $this->createBookingForWorkshop($this->createWorkshopForArtisan($artisan));

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_does_not_have_billing_address_then_error_is_generated(): void
    {
        $artisan = $this->createArtisan();

        $this->createBookingForWorkshop($this->createWorkshopForArtisan($artisan));

        $this->assertArtisanInvoiceCheckDoesNotPass($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_does_not_have_billing_address_and_no_booking_for_month_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[DataProvider('incompleteAddressesProvider')]
    #[Test]
    public function if_artisan_does_not_have_a_consistent_billing_address_then_error_is_generated($data): void
    {
        $artisan = $this->createArtisan();
        $this->createInformationFacturationForModel($artisan, $data);

        $this->createBookingForWorkshop($this->createWorkshopForArtisan($artisan));

        $this->assertArtisanInvoiceCheckDoesNotPass($artisan, YearMonth::current());
    }

    #[Test]
    public function artisan_scoped_checks_does_not_trigger_error_for_other_artisans(): void
    {
        // No error
        $artisan1 = $this->createArtisan(['is_billable' => false]);
        $this->createInformationFacturationForModel($artisan1);
        $this->createBookingForWorkshop($this->createWorkshopForArtisan($artisan1));

        // Incomplete address
        $artisan2 = $this->createArtisan();
        $this->createInformationFacturationForModel($artisan2, ['adresse1' => null]);
        $this->createBookingForWorkshop($this->createWorkshopForArtisan($artisan2));

        $this->assertScopedArtisanInvoiceCheckPasses($artisan1, YearMonth::current());
        $this->assertScopedArtisanInvoiceCheckDoesNotPass($artisan2, YearMonth::current());
    }

    public static function incompleteAddressesProvider(): \Generator
    {
        yield [[
            'adresse1' => null,
            'code_postal' => null,
            'ville' => null,
            'pays' => null,
        ]];
        yield [[
            'adresse1' => null,
            'code_postal' => 'Test City',
            'ville' => 'Test City',
            'pays' => 'Tanzania',
        ]];
        yield [[
            'adresse1' => '123 test street',
            'code_postal' => null,
            'ville' => 'Test City',
            'pays' => 'Tanzania',
        ]];
        yield [[
            'adresse1' => '123 test street',
            'code_postal' => 'Test City',
            'ville' => null,
            'pays' => 'Tanzania',
        ]];
        yield [[
            'adresse1' => '123 test street',
            'code_postal' => 'Test City',
            'ville' => 'Test City',
            'pays' => null,
        ]];
    }

    protected function checker(): ArtisanInvoicesChecker
    {
        return $this->app->make(ArtisanMustHaveBillingAddress::class);
    }
}
