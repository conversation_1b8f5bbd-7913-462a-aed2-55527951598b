<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Invoices\Checks\Rules;

use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use App\Domain\Accounting\Invoices\Services\ArtisanChecks\Rules\ArtisanBookingsMustBeInvoicedOnce;
use App\Domain\Accounting\Invoices\Services\ArtisanChecks\Rules\ArtisanInvoicesChecker;
use App\Shared\YearMonth;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Invoices\Checks\ArtisanInvoiceCheckTestCase;

class ArtisanBookingsMustBeInvoicedOnceTest extends ArtisanInvoiceCheckTestCase
{
    #[Test]
    public function if_artisan_does_not_have_booking_for_month_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_has_not_invoiced_booking_for_month_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);

        $this->createBookingForWorkshop($workshop);

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_has_invoiced_booking_for_month_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);

        $booking = $this->createBookingForWorkshop($workshop);
        $this->createInvoiceForBooking($booking);

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_has_a_booking_invoiced_twice_for_month_then_an_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now());

        $this->createInvoiceForBooking($booking);
        $this->createInvoiceForBooking($booking);

        $this->assertArtisanInvoiceCheckDoesNotPass($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_has_a_booking_invoiced_twice_in_different_months_then_an_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now());

        $this->createInvoiceForBooking($booking, issueDate: Carbon::now()->subMonth());
        $this->createInvoiceForBooking($booking);

        $this->assertArtisanInvoiceCheckDoesNotPass($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_has_a_booking_for_another_month_invoiced_twice_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now()->startOfMonth()->subMonth());

        $this->createInvoiceForBooking($booking, issueDate: Carbon::now()->startOfMonth()->subMonth());
        $this->createInvoiceForBooking($booking);

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_artisan_doublon_booking_invoice_is_cancelled_then_no_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now());

        $this->createInvoiceForBooking($booking, params: ['status' => InvoiceStatus::Cancelled]);
        $this->createInvoiceForBooking($booking);

        $this->assertArtisanInvoiceCheckPasses($artisan, YearMonth::current());
    }

    #[Test]
    public function if_booking_is_invoiced_twice_to_different_artisans_then_an_error_is_generated(): void
    {
        $artisan = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now());

        $this->createInvoiceForBooking($booking);
        $this->createInvoiceForBooking($booking, params: ['emitter_id' => $this->createArtisan()->id]);

        $this->assertArtisanInvoiceCheckDoesNotPass($artisan, YearMonth::current());
    }

    #[Test]
    public function artisan_scoped_checks_does_not_trigger_error_for_other_artisans(): void
    {
        // No error
        $artisan1 = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan1);
        $booking = $this->createBookingForWorkshop($workshop);
        $this->createInvoiceForBooking($booking);

        // Case with doublon invoices for the same booking : error
        $artisan2 = $this->createArtisan();
        $workshop = $this->createWorkshopForArtisan($artisan2);
        $booking = $this->createBookingForWorkshop($workshop, eventEnd: Carbon::now());
        $this->createInvoiceForBooking($booking, issueDate: Carbon::now()->subMonth());
        $this->createInvoiceForBooking($booking);

        $this->assertScopedArtisanInvoiceCheckPasses($artisan1, YearMonth::current());
        $this->assertScopedArtisanInvoiceCheckDoesNotPass($artisan2, YearMonth::current());
    }

    protected function checker(): ArtisanInvoicesChecker
    {
        return $this->app->make(ArtisanBookingsMustBeInvoicedOnce::class);
    }
}
