<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Exports\ExportsC;

use App\Domain\Accounting\Exports\Enums\ExportType;
use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use App\Models\Entreprise;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Exports\ExportTestCase;
use Tests\Traits\AmountCreator;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\CommissionInvoiceCreator;

class C1FrCommissionInvoicesExportTest extends ExportTestCase
{
    use AmountCreator;
    use ArtisanCreator;
    use CommissionInvoiceCreator;

    #[Test]
    public function fr_commission_invoices_can_be_exported(): void
    {
        $artisan1 = $this->createArtisan();
        $comInvoice1 = $this->createCommissionInvoiceForArtisan($artisan1, [
            'emitter_id' => Entreprise::WCD_FR,
            'issue_date' => Carbon::now(),
            'month' => Carbon::now()->month,
            'year' => Carbon::now()->year,
            'total_excluding_vat' => $this->relativeEuros(545.50),
            'total_including_vat' => $this->relativeEuros(623.45),
        ]);

        $artisan2 = $this->createArtisan();
        $comInvoice2 = $this->createCommissionInvoiceForArtisan($artisan2, [
            'emitter_id' => Entreprise::WCD_FR,
            'status' => InvoiceStatus::Cancelled,
            'issue_date' => Carbon::now()->startOfMonth(),
            'month' => Carbon::now()->startOfMonth()->month,
            'year' => Carbon::now()->startOfMonth()->year,
            'total_excluding_vat' => $this->relativeEuros(500),
            'total_including_vat' => $this->relativeEuros(600),
        ]);

        $artisan3 = $this->createArtisan();
        $this->createCommissionInvoiceForArtisan($artisan3, [
            'emitter_id' => Entreprise::WCD_UK,
            'issue_date' => Carbon::now()->startOfYear(),
            'month' => Carbon::now()->startOfYear()->month,
            'year' => Carbon::now()->startOfYear()->year,
        ]);

        $this->createCommissionInvoice([
            'emitter_id' => Entreprise::WCD_FR,
            'issue_date' => Carbon::now()->subYear(),
            'month' => Carbon::now()->subYear()->month,
            'year' => Carbon::now()->subYear()->year,
        ]);

        $this->createCommissionInvoice([
            'emitter_id' => Entreprise::WCD_FR,
            'issue_date' => Carbon::now()->addMonth(),
            'month' => Carbon::now()->addMonth()->month,
            'year' => Carbon::now()->addMonth()->year,
        ]);

        $this->createCommissionInvoice([
            'emitter_id' => Entreprise::WCD_UK,
            'issue_date' => Carbon::now(),
            'month' => Carbon::now()->month,
            'year' => Carbon::now()->year,
        ]);

        $results = $this->getExport();

        $this->assertEquals([
            [
                'Fact_ID' => $comInvoice1->id,
                'Numero' => $comInvoice1->number,
                'Date emission' => Carbon::now()->format('d/m/Y'),
                'Statut' => 'issued',
                'Destinataire ID' => $artisan1->id,
                'Montant HT' => 545.50,
                'Montant TTC' => 623.45,
                'Devise' => 'EUR',
                'Taux TVA' => 20,
            ],
            [
                'Fact_ID' => $comInvoice2->id,
                'Numero' => $comInvoice2->number,
                'Date emission' => Carbon::now()->startOfMonth()->format('d/m/Y'),
                'Statut' => 'cancelled',
                'Destinataire ID' => $artisan2->id,
                'Montant HT' => 500,
                'Montant TTC' => 600,
                'Devise' => 'EUR',
                'Taux TVA' => 20,
            ],
        ], $results);
    }

    protected function exportTypeUnderTest(): ExportType
    {
        return ExportType::FrCommissionInvoicesExport;
    }
}
