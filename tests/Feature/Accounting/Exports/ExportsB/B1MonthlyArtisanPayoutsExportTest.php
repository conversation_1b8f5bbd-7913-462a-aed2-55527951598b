<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Exports\ExportsB;

use App\Domain\Accounting\Exports\Enums\ExportType;
use App\Domain\Accounting\Invoices\Enums\InvoiceStatus;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Exports\ExportTestCase;
use Tests\Traits\AmountCreator;
use Tests\Traits\ArtisanCreator;
use Tests\Traits\CommissionInvoiceCreator;

class B1MonthlyArtisanPayoutsExportTest extends ExportTestCase
{
    use AmountCreator;
    use ArtisanCreator;
    use CommissionInvoiceCreator;

    #[Test]
    public function artisan_payout_can_be_exported(): void
    {
        $artisan1 = $this->createArtisan();
        $comInvoice1 = $this->createCommissionInvoiceForArtisan($artisan1, [
            'issue_date' => Carbon::now(),
            'month' => Carbon::now()->month,
            'year' => Carbon::now()->year,
            'total_sales' => $this->euros(10_000),
            'total_including_vat' => $this->relativeEuros(2_500),
            'amount_to_transfer' => $this->euros(7_500),
        ]);

        $artisan2 = $this->createArtisan();
        $comInvoice2 = $this->createCommissionInvoiceForArtisan($artisan2, [
            'issue_date' => Carbon::now()->startOfMonth(),
            'month' => Carbon::now()->startOfMonth()->month,
            'year' => Carbon::now()->startOfMonth()->year,
            'total_sales' => $this->euros(500),
            'total_including_vat' => $this->relativeEuros(-300),
            'amount_to_transfer' => $this->euros(2_000),
        ]);

        $artisan3 = $this->createArtisan();
        $comInvoice3 = $this->createCommissionInvoiceForArtisan($artisan3, [
            'emitter_id' => 166,
            'issue_date' => Carbon::now()->startOfYear(),
            'month' => Carbon::now()->startOfYear()->month,
            'year' => Carbon::now()->startOfYear()->year,
            'total_sales' => $this->pound(100),
            'total_including_vat' => $this->relativePound(-50),
            'amount_to_transfer' => $this->pound(150),
            'total_discount' => $this->pound(0),
            'total_excluding_vat' => $this->relativePound(0),
            'vat_amount' => $this->relativePound(0),
        ]);

        $this->createCommissionInvoice([
            'issue_date' => Carbon::now()->subYear(),
            'month' => Carbon::now()->subYear()->month,
            'year' => Carbon::now()->subYear()->year,
        ]);

        $this->createCommissionInvoice([
            'issue_date' => Carbon::now()->addMonth(),
            'month' => Carbon::now()->addMonth()->month,
            'year' => Carbon::now()->addMonth()->year,
        ]);

        $this->createCommissionInvoice([
            'status' => InvoiceStatus::Cancelled,
            'issue_date' => Carbon::now(),
            'month' => Carbon::now()->month,
            'year' => Carbon::now()->year,
        ]);

        $results = $this->getExport();

        $this->assertEquals([
            [
                'Artisan ID' => $artisan1->id,
                'Mois' => Carbon::now()->month,
                'Année' => Carbon::now()->year,
                'Montant des ventes TTC' => 10_000,
                'Montant des commissions TTC' => 2_500,
                'Montant Payé' => 7_500,
                'Pays' => 'fr',
                'Devise' => 'EUR',
            ],
            [
                'Artisan ID' => $artisan2->id,
                'Mois' => Carbon::now()->month,
                'Année' => Carbon::now()->year,
                'Montant des ventes TTC' => 500,
                'Montant des commissions TTC' => -300,
                'Montant Payé' => 2_000,
                'Pays' => 'fr',
                'Devise' => 'EUR',
            ],
            [
                'Artisan ID' => $artisan3->id,
                'Mois' => Carbon::now()->startOfYear()->month,
                'Année' => Carbon::now()->startOfYear()->year,
                'Montant des ventes TTC' => 100,
                'Montant des commissions TTC' => -50,
                'Montant Payé' => 150,
                'Pays' => 'gb',
                'Devise' => 'GBP',
            ],
        ], $results);
    }

    protected function exportTypeUnderTest(): ExportType
    {
        return ExportType::MonthlyArtisanPayoutsExport;
    }
}
