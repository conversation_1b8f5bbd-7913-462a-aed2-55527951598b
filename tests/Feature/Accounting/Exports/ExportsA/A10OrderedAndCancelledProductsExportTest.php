<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Exports\ExportsA;

use App\Domain\Accounting\Exports\Enums\ExportType;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\ECommerce\Enums\ProductStatus;
use App\Domain\Gift\Enums\GiftCardStatus;
use App\Enums\Currency;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Exports\ExportTestCase;
use Tests\Traits\GiftCardCreator;
use Tests\Traits\OrderCreator;
use Tests\Traits\OrderItemRefundCreator;
use Tests\Traits\WorkshopCreator;

class A10OrderedAndCancelledProductsExportTest extends ExportTestCase
{
    use GiftCardCreator;
    use OrderCreator;
    use OrderItemRefundCreator;
    use WorkshopCreator;

    #[Test]
    public function cancelled_products_without_refund_can_be_exported(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 50]);
        $order1 = $this->createOrder();
        $booking1 = $this->addEventBookingToOrder($order1, $workshop->evenements->first(), bookingParams: [
            'status' => BookingStatus::BOOKING_CANCELLED,
            'cancelled_at' => Carbon::now(),
        ]);
        $this->addEventBookingToOrder($order1, $workshop->evenements->first());
        $ticketGiftCard1 = $this->createTicketGiftCard($workshop, params: ['status' => GiftCardStatus::CANCELLED, 'cancelled_at' => Carbon::now()->startOfMonth(), 'currency' => Currency::EURO])->refresh();
        $giftCardItem = $this->addGiftCardItemToOrder($order1, $ticketGiftCard1);
        $additionalProduct1 = $this->addAdditionalProductForOrderItem($giftCardItem, params: [
            'status' => ProductStatus::PRODUCT_CANCELLED,
            'cancelled_at' => Carbon::now()->endOfMonth(),
        ]);

        $order2 = $this->createOrder();
        $booking2 = $this->addEventBookingToOrder($order2, $workshop->evenements->first(), bookingParams: [
            'status' => BookingStatus::BOOKING_CANCELLED,
            'cancelled_at' => Carbon::now(),
        ]);
        $this->createStripeOrderRefundForOrderItem($order2->getRelatedOrderItem($booking2));
        $booking3 = $this->addEventBookingToOrder($order2, $workshop->evenements->first(), 2, bookingParams: [
            'status' => BookingStatus::BOOKING_CANCELLED,
            'cancelled_at' => Carbon::now()->startOfYear(),
        ]);
        $amountGiftCard = $this->createAmountGiftCard(70, ['status' => GiftCardStatus::CANCELLED, 'cancelled_at' => Carbon::now()->startOfMonth(), 'currency' => Currency::EURO])->refresh();
        $this->addGiftCardItemToOrder($order1, $amountGiftCard);
        $otherAmountGiftCardItem = $this->addGiftCardItemToOrder($order1, $this->createAmountGiftCard(20));
        $additionalProduct2 = $this->addAdditionalProductForOrderItem($otherAmountGiftCardItem, params: [
            'status' => ProductStatus::PRODUCT_CANCELLED,
            'cancelled_at' => Carbon::now(),
        ]);
        $this->createStripeOrderRefundForOrderItem($additionalProduct2);

        $order3 = $this->createOrder();
        $booking4 = $this->addEventBookingToOrder($order3, $workshop->evenements->first(), bookingParams: [
            'status' => BookingStatus::BOOKING_CANCELLED,
            'cancelled_at' => Carbon::now()->subYear(), // out of date
        ]);

        $result = $this->getExport();

        $this->assertEquals([
            [
                'Item ID' => $booking1->id,
                'Item Type' => 'booking',
                'Item Status' => 'cancelled',
                'Date Annulation' => Carbon::now()->format('d/m/Y'),
                'Valeur Restante' => 50,
                'Devise' => 'EUR',
            ],
            [
                'Item ID' => $booking3->id,
                'Item Type' => 'booking',
                'Item Status' => 'cancelled',
                'Date Annulation' => Carbon::now()->startOfYear()->format('d/m/Y'),
                'Valeur Restante' => 100,
                'Devise' => 'EUR',
            ],
            [
                'Item ID' => $ticketGiftCard1->id,
                'Item Type' => 'ticket gift card',
                'Item Status' => 'cancelled',
                'Date Annulation' => Carbon::now()->startOfMonth()->format('d/m/Y'),
                'Valeur Restante' => 50,
                'Devise' => 'EUR',
            ],
            [
                'Item ID' => $amountGiftCard->id,
                'Item Type' => 'value gift card',
                'Item Status' => 'cancelled',
                'Date Annulation' => Carbon::now()->startOfMonth()->format('d/m/Y'),
                'Valeur Restante' => 70,
                'Devise' => 'EUR',
            ],
            [
                'Item ID' => $additionalProduct1->item->id,
                'Item Type' => 'physical card',
                'Item Status' => 'cancelled',
                'Date Annulation' => Carbon::now()->endOfMonth()->format('d/m/Y'),
                'Valeur Restante' => 5,
                'Devise' => 'EUR',
            ],
        ], $result);
    }

    protected function exportTypeUnderTest(): ExportType
    {
        return ExportType::OrderedAndCancellesProductsExport;
    }
}
