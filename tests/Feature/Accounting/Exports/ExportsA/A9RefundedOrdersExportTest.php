<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Exports\ExportsA;

use App\Domain\Accounting\Exports\Enums\ExportType;
use App\Domain\Accounting\Refunds\Enums\RefundType;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Gift\Enums\GiftCardStatus;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\Feature\Accounting\Exports\ExportTestCase;
use Tests\Traits\AmountCreator;
use Tests\Traits\GiftCardCreator;
use Tests\Traits\OrderCreator;
use Tests\Traits\OrderItemRefundCreator;
use Tests\Traits\WorkshopCreator;

class A9RefundedOrdersExportTest extends ExportTestCase
{
    use AmountCreator;
    use GiftCardCreator;
    use OrderCreator;
    use OrderItemRefundCreator;
    use WorkshopCreator;

    #[Test]
    public function cancelled_bookings_can_be_exported(): void
    {
        $workshop = $this->createWorkshopWithOneEvent(['prix' => 50]);

        $order1 = $this->createOrder();

        // we had a booking item, followed by a stripe refund
        $booking = $this->addEventBookingToOrder($order1, $workshop->evenements->first(), bookingParams: ['status' => BookingStatus::BOOKING_CANCELLED]);
        $this->createStripeOrderRefundForOrderItem($order1->getRelatedOrderItem($booking), refundParams: ['created_at' => Carbon::now()]);

        // we had a ticket gift card item, followed by stripe and gift card refunds
        $ticketGiftCard = $this->createTicketGiftCard($workshop, 1, ['status' => GiftCardStatus::USED]); // not very consistent status, here to prove export value
        $ticketGiftCardItem = $this->addGiftCardItemToOrder($order1, $ticketGiftCard);
        $orderItemRefund = $this->createOrderItemRefundForOrderItem($ticketGiftCardItem, ['created_at' => Carbon::now()->startOfMonth()]);
        $this->createOrderItemRefundMethodForOrderItemRefund($orderItemRefund, RefundType::GiftCard, ['amount' => $this->euros(30)]);
        $this->createOrderItemRefundMethodForOrderItemRefund($orderItemRefund, RefundType::Stripe, ['amount' => $this->euros(20)]);

        $order2 = $this->createOrder();

        // we had an amount gift card item, followed by a gift card refund
        $amountGiftCard = $this->createAmountGiftCard(70, ['status' => GiftCardStatus::CANCELLED]);
        $ticketGiftCardItem = $this->addGiftCardItemToOrder($order2, $amountGiftCard);
        $orderItemRefund = $this->createOrderItemRefundForOrderItem($ticketGiftCardItem, ['created_at' => Carbon::now()->endOfMonth()]);
        $this->createOrderItemRefundMethodForOrderItemRefund($orderItemRefund, RefundType::GiftCard, ['amount' => $this->euros(40)]);

        // we had an additional product refunded by stripe
        $additionalProductItem = $this->addAdditionalProductForOrderItem($ticketGiftCardItem);
        $orderItemRefund = $this->createOrderItemRefundForOrderItem($additionalProductItem, ['created_at' => Carbon::now()]);
        $this->createOrderItemRefundMethodForOrderItemRefund($orderItemRefund, RefundType::Stripe, ['amount' => $this->euros(7)]);

        // we had a booking item refunded last year (it must not be exported)
        $booking2 = $this->addEventBookingToOrder($order2, $workshop->evenements->first());
        $this->createStripeOrderRefundForOrderItem($order2->getRelatedOrderItem($booking2), refundParams: ['created_at' => Carbon::now()->subYear()]);

        $results = $this->getExport();

        $this->assertEquals([
            [
                'Order ID' => $order1->id,
                'Item ID' => $booking->id,
                'Item Type' => 'booking',
                'Date Remboursement' => Carbon::now()->format('d/m/Y'),
                'Statut Item' => 'cancelled',
                'Méthode de remboursement' => 'stripe',
                'Montant Remboursé' => 50,
                'Devise' => 'EUR',
            ],
            [
                'Order ID' => $order1->id,
                'Item ID' => $ticketGiftCard->id,
                'Item Type' => 'ticket gift card',
                'Date Remboursement' => Carbon::now()->startOfMonth()->format('d/m/Y'),
                'Statut Item' => 'used',
                'Méthode de remboursement' => 'giftcard',
                'Montant Remboursé' => 30,
                'Devise' => 'EUR',
            ],
            [
                'Order ID' => $order1->id,
                'Item ID' => $ticketGiftCard->id,
                'Item Type' => 'ticket gift card',
                'Date Remboursement' => Carbon::now()->startOfMonth()->format('d/m/Y'),
                'Statut Item' => 'used',
                'Méthode de remboursement' => 'stripe',
                'Montant Remboursé' => 20,
                'Devise' => 'EUR',
            ],
            [
                'Order ID' => $order2->id,
                'Item ID' => $amountGiftCard->id,
                'Item Type' => 'value gift card',
                'Date Remboursement' => Carbon::now()->endOfMonth()->format('d/m/Y'),
                'Statut Item' => 'cancelled',
                'Méthode de remboursement' => 'giftcard',
                'Montant Remboursé' => 40,
                'Devise' => 'EUR',
            ],
            [
                'Order ID' => $order2->id,
                'Item ID' => $additionalProductItem->item_id,
                'Item Type' => 'physical card',
                'Date Remboursement' => Carbon::now()->format('d/m/Y'),
                'Statut Item' => 'billed',
                'Méthode de remboursement' => 'stripe',
                'Montant Remboursé' => 7,
                'Devise' => 'EUR',
            ],
        ], $results);
    }

    protected function exportTypeUnderTest(): ExportType
    {
        return ExportType::RefundedOrdersExport;
    }
}
