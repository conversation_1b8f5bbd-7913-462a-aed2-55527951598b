<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Orders\OrderConsistencyValidation\Rules;

use App\Domain\Accounting\Orders\ConsistencyChecks\ConsistencyError;
use App\Domain\Accounting\Orders\ConsistencyChecks\Rules\AmountsShouldBeConsistent;
use App\Domain\Accounting\Orders\ConsistencyChecks\Rules\ConsistencyChecker;
use PHPUnit\Framework\Attributes\Test;

class AmountsShouldBeConsistantRuleTest extends ConsistencyRuleTestCase
{
    #[Test]
    public function checker_does_not_pass_if_amount_are_inconsistents(): void
    {
        foreach ($this->inconsistantAmountsProvider() as $index => $inconsistantAmounts) {
            $order = $this->createOrder($inconsistantAmounts);

            $this->assertConsistencyCheckDoesNotPass($order, $index);
        }
    }

    private function inconsistantAmountsProvider(): \Generator
    {
        yield [
            'total_price' => $this->euros(100),
            'final_price' => $this->euros(20),
            'total_discount_amount' => $this->euros(20),
        ];

        yield [
            'total_price' => $this->euros(100),
            'final_price' => $this->euros(100),
            'total_discount_amount' => $this->euros(20),
        ];

        yield [
            'total_price' => $this->euros(0),
            'final_price' => $this->euros(10),
            'total_discount_amount' => $this->euros(0),
        ];
    }

    #[Test]
    public function checker_passes_if_amount_are_consistents(): void
    {
        foreach ($this->consistantAmountsProvider() as $index => $inconsistantAmounts) {
            $order = $this->createOrder($inconsistantAmounts);

            $this->assertConsistencyCheckPasses($order, $index);
        }
    }

    private function consistantAmountsProvider(): \Generator
    {
        yield [
            'total_price' => $this->euros(100),
            'final_price' => $this->euros(100),
            'total_discount_amount' => $this->euros(0),
        ];

        yield [
            'total_price' => $this->euros(100),
            'final_price' => $this->euros(50),
            'total_discount_amount' => $this->euros(50),
        ];
    }

    protected function checker(): ConsistencyChecker
    {
        return $this->app->make(AmountsShouldBeConsistent::class);
    }

    protected function consistencyError(): ConsistencyError
    {
        return ConsistencyError::IncorrectAmounts;
    }
}
