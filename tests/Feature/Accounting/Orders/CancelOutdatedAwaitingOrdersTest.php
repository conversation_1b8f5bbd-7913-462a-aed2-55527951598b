<?php

declare(strict_types=1);

namespace Tests\Feature\Accounting\Orders;

use App\Domain\Accounting\Orders\Jobs\CancelOutdatedAwaitingOrders;
use App\Enums\Order\OrderStatus;
use App\Mail\Artisan\ReservationAnnulee;
use App\Mail\User\Client\ConfirmationAnnulationReservation;
use App\Models\Reservation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\Assertions\BookingAssertions;
use Tests\Traits\Assertions\OrderAssertions;
use Tests\Traits\OrderCreator;
use Tests\Traits\WorkshopCreator;

class CancelOutdatedAwaitingOrdersTest extends TestCase
{
    use BookingAssertions;
    use OrderAssertions;
    use OrderCreator;
    use WorkshopCreator;

    #[Test]
    public function orders_waiting_for_a_bank_transfer_payment_are_not_cancelled_rapidly_after_they_have_been_created(): void
    {
        $user = $this->createUser();
        $pendingOrder = $this->createPendingOrderForUser($user, ['created_at' => Carbon::now()->subHours(3)])->refresh();
        $this->addStripeBankTransferOrderPayment($pendingOrder);

        $this->callCancelOutdatedOrdersJob();

        $this->assertOrderStatusIsPending($pendingOrder->refresh());
    }

    #[DataProvider('cancellationConditionsProvider')]
    #[Test]
    public function orders_waiting_for_a_bank_transfer_payment_are_cancelled_depending_on_condition(OrderStatus $status, Carbon $orderCreatedAt, Carbon $eventStartDate, bool $cancellationExpected): void
    {
        Mail::fake();

        $user = $this->createUser();
        $order = $this->createOrderForUser($user, ['status' => $status, 'created_at' => $orderCreatedAt])->refresh();
        $workshop = $this->createWorkshopWithOneEvent(eventParams: ['start' => $eventStartDate]);
        $booking = $this->addEventBookingToOrder($order, $workshop->evenements->first());
        $this->addStripeBankTransferOrderPayment($order);

        $this->callCancelOutdatedOrdersJob();

        if ($cancellationExpected) {
            $this->assertOrderIsCancelled($order->refresh());
            $order->getAssociatedBookings()->each(function (Reservation $reservation): void {
                $this->assertBookingStatusIsCancelled($reservation);
            });

            Mail::assertQueued(ConfirmationAnnulationReservation::class, 1);
            Mail::assertQueued(ReservationAnnulee::class, 1);
        } else {
            $this->assertEquals($order->status, $order->refresh()->status);
            $order->getAssociatedBookings()->each(function (Reservation $reservation): void {
                $this->assertBookingStatusIsConfirmed($reservation);
            });

            Mail::assertNothingQueued();
        }
    }

    public static function cancellationConditionsProvider(): \Generator
    {
        yield [
            'status' => OrderStatus::Awaiting,
            'orderCreatedAt' => Carbon::today(),
            'eventStartDate' => Carbon::today()->addDays(10),
            'cancellationExpected' => true,
        ];

        yield [
            'status' => OrderStatus::Awaiting,
            'orderCreatedAt' => Carbon::today(),
            'eventStartDate' => Carbon::today()->addDays(11),
            'cancellationExpected' => false,
        ];

        yield [
            'status' => OrderStatus::Paid,
            'orderCreatedAt' => Carbon::now()->subDays(20),
            'eventStartDate' => Carbon::today()->addDays(2),
            'cancellationExpected' => false, // order was paid, no cancellation expected
        ];
    }

    private function callCancelOutdatedOrdersJob(): void
    {
        $this->app->call([new CancelOutdatedAwaitingOrders(), 'handle']);
    }
}
