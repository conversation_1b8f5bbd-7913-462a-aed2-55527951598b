<?php

namespace Tests\Feature\Booking;

use App\Domain\Booking\Enums\BookingOrigin;
use App\Domain\Booking\Enums\BookingStatus;
use App\Domain\Booking\Enums\BookingStatusChangeOrigin;
use App\Domain\Booking\Enums\EventCancellationOrigin;
use App\Domain\Booking\Exceptions\Booking\BookingCreationException;
use App\Domain\Booking\Services\BookingCancellationOptions;
use App\Domain\Booking\Services\BookingCanceller;
use App\Domain\Booking\Services\BookingService;
use App\Domain\ECommerce\Enums\CartOrigin;
use App\Models\Artisan;
use App\Models\Atelier;
use App\Models\AtelierBox;
use App\Models\Box;
use App\Models\Reductions\CadeauBox;
use App\Models\Reservation;
use App\Services\CodeGenerator;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Database\Factories\ArtisanFactory;
use Database\Factories\BookingFactory;
use Database\Factories\WorkshopFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\AdminCreator;
use Tests\Traits\BaseFeatureTestTrait;
use Tests\Traits\BookingCreator;
use Tests\Traits\CartCreator;
use Tests\Traits\EventCreator;
use Tests\Traits\UserArtisanCreator;
use Tests\Traits\WorkshopCreator;

class BookingTest extends TestCase
{
    use AdminCreator;
    use BaseFeatureTestTrait;
    use BookingCreator;
    use CartCreator;
    use EventCreator;
    use UserArtisanCreator;
    use WorkshopCreator;

    #[Test]
    public function route_create_customer_booking(): void
    {
        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = self::$event->id;
        $bookingData['nb_places'] = 1;
        $bookingData['full_phone'] = $bookingData['telephone_contact'];

        $response = $this->post('/evenement/'.static::$event->id.'/reservation', $bookingData);

        $response->assertStatus(302);
    }

    #[Test]
    public function booking_customer_with_code(): void
    {
        /** @var CodeGenerator $codeGenerator */
        $codeGenerator = app(CodeGenerator::class);

        $booking = BookingFactory::new()->create();
        $booking->evenement_id = self::$event->id;
        $booking->nb_places = 1;
        $booking->booking_code = $codeGenerator->newCodeForTesting('CB', 6);

        BookingService::setStatus($booking, BookingStatus::BOOKING_AWAITING_PAYMENT);

        $bookingCustomer = BookingService::bookEventCustomer($booking->toArray(), static::$event);

        $this->assertInstanceOf(Reservation::class, $bookingCustomer);

        $this->assertEquals($booking->id, $bookingCustomer->id);

        $this->assertEquals(BookingStatus::BOOKING_AWAITING_PAYMENT, $bookingCustomer->status);
    }

    #[Test]
    public function booking_with_boxe_code(): void
    {
        /** @var Box $box */
        $box = Box::make([
            'nom' => 'Luke',
            'description' => 'Use the force',
        ]);
        $box->prix = 200;
        $box->slug = 'tum-tudumdum';
        $box->save();

        $cadeaubox = CadeauBox::create([
            'prix' => $box->prix,
            'prix_reduction' => $box->prix,
            'box_id' => $box->id,
            'date_peremption' => Carbon::now()->addDay(),
            'is_active' => true,
            'status' => 'sold',
        ]);

        self::$workshop->prix = 100;
        self::$workshop->save();

        AtelierBox::create(['box_id' => $box->id, 'atelier_id' => self::$workshop->id, 'nb_places' => 99]);

        $response = $this->actingAs(self::$user, 'customer')
            ->withSession(['banned' => false])
            ->post('coffret/claim', [
                'box_discount_code' => $cadeaubox->code_reduction,
                'email' => self::$user->email,
            ]);

        $response->assertStatus(302);

        $this->assertEquals($cadeaubox->code_reduction, $this->app['session']->get('box')['code']);
    }

    #[Test]
    public function create_customer_booking_failure(): void
    {
        $this->expectException(BookingCreationException::class);

        $bookingData = $this->getBookingDefinition();

        BookingService::bookEventCustomer(array_merge($bookingData, ['nb_places' => 999]), static::$event);
    }

    #[Test]
    public function create_artisan_booking(): void
    {
        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = self::$event->id;
        $bookingData['nb_places'] = 1;

        $booking = BookingService::bookEventArtisan($bookingData, static::$event);

        $this->assertInstanceOf(Reservation::class, $booking);
        static::assertEquals(0, $booking->commission_pourcentage);
        static::assertEquals(0, $booking->commission_percent);
    }

    #[Test]
    public function create_widget_booking(): void
    {
        $this->initEvent();
        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = self::$event->id;
        $bookingData['nb_places'] = 1;

        $booking = BookingService::bookEventWidget($bookingData, static::$event);

        $this->assertInstanceOf(Reservation::class, $booking);
        static::assertEquals(10, $booking->commission_pourcentage);
        static::assertEquals(10, $booking->commission_percent);
    }

    #[Test]
    public function route_create_widget_booking(): void
    {
        $this->initEvent();
        $user = $this->createUser();
        $cartFromWidget = $this->createCartForUser($user, [
            'origin' => CartOrigin::Widget,
        ]);
        $workshop = $this->createWorkshop();
        $event = $this->createEvent($workshop);

        $bookingData = $this->getBookingDefinition();
        $bookingData['event_id'] = $event->getKey();
        $bookingData['cart_id'] = $cartFromWidget->getKey();
        $bookingData['nb_places'] = 1;
        $bookingData['adult_number'] = 1;
        $bookingData['cgucgv_accepted'] = true;

        $this
            ->actingAs($user)
            ->post('/api/v1/bookings?include=event,event.location,event.workshop,event.workshop.artisan', $bookingData)
            ->assertSuccessful();
    }

    #[Test]
    public function cart_origin_must_be_from_widget_when_creating_booking_in_widget(): void
    {
        $this->initEvent();
        $user = $this->createUser();
        $cartFromWidget = $this->createCartForUser($user, [
            'origin' => CartOrigin::Widget,
        ]);
        $cartFromWebsite = $this->createCartForUser($user, [
            'origin' => CartOrigin::Website,
        ]);
        $workshop = $this->createWorkshop();
        $event = $this->createEvent($workshop);

        $bookingData = $this->getBookingDefinition();
        $bookingData['event_id'] = $event->getKey();
        $bookingData['nb_places'] = 1;
        $bookingData['adult_number'] = 1;
        $bookingData['cgucgv_accepted'] = true;

        $bookingData['cart_id'] = $cartFromWidget->getKey();
        $this
            ->actingAs($user)
            ->post('/api/v1/bookings?include=event,event.location,event.workshop,event.workshop.artisan', $bookingData)
            ->assertSuccessful();

        $bookingData['cart_id'] = $cartFromWebsite->getKey();
        $this
            ->actingAs($user)
            ->post('/api/v1/bookings?include=event,event.location,event.workshop,event.workshop.artisan', $bookingData)
            ->assertNotFound();
    }

    #[Test]
    public function booking_amounts(): void
    {
        $this->initEvent();

        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = static::$event->id;
        $bookingData['nb_places'] = 1;

        $booking = BookingService::bookEventArtisan($bookingData, static::$event);

        $this->assertEquals($booking->commission_amount, $booking->participants()->sum('commission_amount'));

        $this->assertEquals($booking->amount, $booking->participants()->sum('amount'));
    }

    #[DataProvider('specificCommissionProvider')]
    #[Test]
    public function booking_with_specific_commission(?int $eventCommission, BookingOrigin $origin, int $expectedCommission): void
    {
        if ($eventCommission !== null) {
            self::$workshop->commission_pourcentage = $eventCommission;
            self::$workshop->save();
        }

        $this->initEvent();

        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = static::$event->id;
        $bookingData['nb_places'] = 1;

        $booking = BookingService::book($bookingData, static::$event, $origin);
        static::assertEquals($expectedCommission, $booking->commission_pourcentage);
        static::assertEquals($expectedCommission, $booking->commission_percent);
    }

    public static function specificCommissionProvider(): \Generator
    {
        yield [50, BookingOrigin::Artisan, 0];
        yield [null, BookingOrigin::Artisan, 0];
        yield [50, BookingOrigin::Wecandoo, 50];
        yield [null, BookingOrigin::Wecandoo, 20];
        yield [50, BookingOrigin::Customer, 50];
        yield [null, BookingOrigin::Customer, 20];
        yield [50, BookingOrigin::Partner, 50];
        yield [null, BookingOrigin::Partner, 20];
        yield [50, BookingOrigin::Widget, 10];
        yield [null, BookingOrigin::Widget, 10];
    }

    #[Test]
    public function admin_cant_book_a_canceled_event(): void
    {
        $admin = $this->createAdmin();

        self::$event->cancel(EventCancellationOrigin::EVENT_ORIGIN_ARTISAN);
        self::$event->save();

        $this
            ->actingAsAdmin($admin)
            ->post(
                route('admin.booking.store'),
                [
                    'id' => self::$event->getKey(),
                    'email' => '<EMAIL>',
                    'description' => 'Foo description',
                    'emailLocal' => 'Foo Bar',
                    'telephone' => '0102030405',
                    'type' => BookingOrigin::Wecandoo->value,
                ],
            )
            ->assertRedirect()
            ->assertSessionHas('flash_message_danger')
            ->assertSessionHas('flash_message', 'Unable to create a booking for a canceled event.');
    }

    #[Test]
    public function customer_cant_book_unavailable_stackable_event(): void
    {
        self::$event->indispo_superposition = 1;
        self::$event->save();

        $bookingData = $this->getBookingDefinition();
        $bookingData['evenement_id'] = static::$event->id;
        $bookingData['nb_places'] = 1;

        static::assertThrows(function () use ($bookingData): void {
            BookingService::book($bookingData, static::$event, BookingOrigin::Customer);
        }, BookingCreationException::class);
    }

    #[Test]
    public function unlock_stacked_event_after_booking_cancellation(): void
    {
        $artisan = ArtisanFactory::new()
            ->isVisible()
            ->create();

        $date = CarbonImmutable::now()->addMonth();

        $workshopA = $this->createNonStackableWorkshopForArtisan($artisan);
        $eventA = $this->createEventForWorkshopForDate($workshopA, $date);

        $workshopB = $this->createNonStackableWorkshopForArtisan($artisan);
        $eventB = $this->createEventForWorkshopForDate($workshopB, $date);

        $workshopC = $this->createNonStackableWorkshopForArtisan($artisan);
        $eventC = $this->createEventForWorkshopForDate($workshopC, $date);

        $user = $this->createUser();

        $booking = $this->createBookingForEvent($eventA);
        BookingService::setStatus($booking, BookingStatus::BOOKING_CONFIRMED);

        $eventA->refresh();
        $eventB->refresh();
        $eventC->refresh();

        static::assertFalse($eventA->indispo_superposition);
        static::assertTrue($eventB->indispo_superposition);
        static::assertTrue($eventC->indispo_superposition);

        $bookingCanceller = app(BookingCanceller::class);
        $bookingCanceller->cancel(
            $booking,
            new BookingCancellationOptions(
                $booking->nb_places,
                BookingStatusChangeOrigin::Customer,
                $user,
            ),
        );

        $booking->refresh();

        static::assertSame(BookingStatus::BOOKING_CANCELLED, $booking->status);

        $eventA->refresh();
        $eventB->refresh();
        $eventC->refresh();

        static::assertFalse($eventA->indispo_superposition);
        static::assertFalse($eventB->indispo_superposition);
        static::assertFalse($eventC->indispo_superposition);
    }

    private function createNonStackableWorkshopForArtisan(Artisan $artisan): Atelier
    {
        return WorkshopFactory::new()
            ->isOnline()
            ->forArtisan($artisan)
            ->create();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->initMockEvent();
    }

    private function getBookingDefinition(): array
    {
        return [
            'status' => 'pending',
            'information' => null,
            'est_bon_physique' => false,
            'est_cadeau' => false, // TODO: Si payé avec bc ou ao ?
            'nb_replacements' => 0,
            'offert_vendeur' => false,
            'child_number' => 0,
            'save_phone' => false,
            'reminder' => false,
            'montant_rembourse' => 0,
            'is_billable' => true,
            'commande_id' => null,
            'cart_id' => null,
            'telephone_contact' => '+33612345678',
        ];
    }
}
