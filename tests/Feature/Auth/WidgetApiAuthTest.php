<?php

declare(strict_types=1);

namespace Tests\Feature\Auth;

use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class WidgetApiAuthTest extends TestCase
{
    #[Test]
    public function widget_guest_user_can_create_an_account_and_his_given_password_is_hashed(): void
    {
        $this
            ->post(route('api-register'), [
                'prenom' => 'Ma',
                'nom' => 'Dalton',
                'email' => '<EMAIL>',
                'password' => '123456',
                'password_confirmation' => '123456',
            ])
            ->assertSessionDoesntHaveErrors()
            ->assertSuccessful();

        /** @var User $createdUser */
        $createdUser = User::query()
            ->where('prenom', 'Ma')
            ->where('nom', 'Dalton')
            ->where('email', '<EMAIL>')
            ->sole();

        $this->assertGuest();

        $this->assertTrue(
            \Hash::check('123456', $createdUser->password),
            "Failed asserting created user password matches '123456'",
        );
    }
}
