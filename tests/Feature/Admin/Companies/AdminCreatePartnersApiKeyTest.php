<?php

declare(strict_types=1);

namespace Feature\Admin\Companies;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\Assertions\CompanyAssertions;
use Tests\Traits\CompanyCreator;

class AdminCreatePartnersApiKeyTest extends TestCase
{
    use CompanyAssertions;
    use CompanyCreator;

    #[Test]
    public function partners_api_key_can_be_generated(): void
    {
        $company = $this->createCompany(['nom' => 'World Company']);

        $this->actingAsAdmin()
            ->from(url('/'))
            ->post(route('admin.entreprises.partners-api-keys.store', $company))
            ->assertRedirect(url('/'));

        $company->refresh();

        $this->assertCompanyHasActivePartnersApiClient($company);

        $this->assertEquals('World Company Partners API Client', $company->partnersApiClient->name);
        $this->assertFalse($company->partnersApiClient->personal_access_client);
        $this->assertFalse($company->partnersApiClient->password_client);
    }

    #[Test]
    public function if_company_already_has_active_partners_api_client_then_action_is_forbidden(): void
    {
        $company = $this->createCompanyWithActivePartnerApiClient();
        $clientId = $company->partners_api_client_id;

        $this->actingAsAdmin()
            ->post(route('admin.entreprises.partners-api-keys.store', $company))
            ->assertForbidden();

        $this->assertEquals($clientId, $company->refresh()->partners_api_client_id);
    }

    #[Test]
    public function if_company_already_has_revoked_partners_api_client_then_action_is_forbidden(): void
    {
        $company = $this->createCompanyWithRevokedPartnerApiClient();
        $clientId = $company->partners_api_client_id;

        $this->actingAsAdmin()
            ->post(route('admin.entreprises.partners-api-keys.store', $company))
            ->assertForbidden();

        $this->assertEquals($clientId, $company->refresh()->partners_api_client_id);
    }
}
