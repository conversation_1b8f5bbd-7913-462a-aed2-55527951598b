<?php

declare(strict_types=1);

namespace Tests\SSR;

use App\Domain\Content\Enums\SinglePageSectionTypes;
use App\Domain\Content\Models\Collection;
use App\Domain\Content\Models\SinglePage;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Tests\Traits\Assertions\SSRAssertions;
use Tests\Traits\CollectionCreator;
use Tests\Traits\PageCreator;

class SinglePagesTest extends TestCase
{
    use CollectionCreator;
    use PageCreator;
    use SSRAssertions;

    #[Test]
    public function accessing_a_single_page_in_ssr_displays_ssr_page(): void
    {
        Storage::fake('images');

        /**
         * @var array<int, Collection> $collectionsForSection
         */
        $collectionsForSection = [];
        /**
         * @var array<int, SinglePage> $linkedPages
         */
        $linkedPages = [];

        for ($i = 0; $i < 10; ++$i) {
            $collection = $this->createCollection();
            $collectionsForSection[] = $collection;

            $linkedPages[] = $this->createSinglePage(
                params: [
                    'slug' => '/ateliers/extra-slash/'.$collection->identifier,
                    'title' => $collection->name.'-page',
                    'subject_type' => $collection::class,
                    'subject_id' => $collection,
                ]
            );
        }

        $singlePage = $this->createSinglePageWithLinkedPages($linkedPages, [
            $this->createSinglePageSectionFactoryForType(SinglePageSectionTypes::COLLECTIONS, [
                'collections' => array_map(fn (int $id) => ['id' => $id], array_column($collectionsForSection, 'id')),
            ]),
        ], params: [
            'seo_content' => 'This is the SEO content',
        ]);
        $singlePageSections = $singlePage->sections;

        $this->assertEquals(1, $singlePageSections->count());

        $response = $this->getSsrResponse(url(localizedUrlPath($singlePage->slug)));

        $this->assertSSRRendered($response)
            ->assertSee([
                $singlePage->title,
                $singlePage->description,
                $singlePage->meta_title,
                $singlePage->meta_description,
                $singlePage->seo_title,
                $singlePage->seo_content,
                $singlePage->hero_image,
                $singlePage->badge_image,
            ], false);

        foreach ($singlePageSections as $section) {
            $response->assertSee([
                $section->title,
                $section->description,
            ], false);
        }

        foreach ($collectionsForSection as $collection) {
            $response
                ->assertSee([
                    $collection->name,
                    $collection->identifier,
                    $collection->thumbnail_image,
                ], false);
        }

        foreach ($linkedPages as $singlePage) {
            $response
                ->assertSee([
                    $singlePage->title,
                    $singlePage->slug,
                ], false);
        }
    }
}
