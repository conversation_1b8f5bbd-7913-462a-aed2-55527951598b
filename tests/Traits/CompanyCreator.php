<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Entreprise;
use Database\Factories\EntrepriseFactory;
use Laravel\Passport\Database\Factories\ClientFactory;

trait CompanyCreator
{
    public function createCompany(array $params = []): Entreprise
    {
        return EntrepriseFactory::new()->create($params);
    }

    public function createManyCompanies(int $count, array $params = []): void
    {
        EntrepriseFactory::new()
            ->count($count)
            ->create($params);
    }

    public function createCompanyWithActivePartnerApiClient(array $params = []): Entreprise
    {
        $client = ClientFactory::new()->asClientCredentials()->create(['acls' => '[]']);

        return EntrepriseFactory::new()
            ->withPartnerApiClient($client)
            ->create($params);
    }

    public function createCompanyWithRevokedPartnerApiClient(array $params = []): Entreprise
    {
        $client = ClientFactory::new()->asClientCredentials()->create([
            'acls' => '[]',
            'revoked' => true,
            ...$params,
        ]);

        return EntrepriseFactory::new()
            ->withPartnerApiClient($client)
            ->create($params);
    }
}
