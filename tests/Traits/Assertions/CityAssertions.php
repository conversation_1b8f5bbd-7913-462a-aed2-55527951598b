<?php

declare(strict_types=1);

namespace Tests\Traits\Assertions;

use App\Models\Ville;

trait CityAssertions
{
    public function assertCityHasSinglePage(Ville $city): void
    {
        $this->assertNotNull(
            $city->singlePage,
            'Expected city to have a single page'
        );
    }

    public function assertCityIsActivated(Ville $city): void
    {
        $this->assertTrue(
            $city->active,
            'Expected that city was activated'
        );
    }
}
