<?php

declare(strict_types=1);

namespace Tests\Traits\Assertions;

use App\Models\Parrainage\CodeParrainageUser;

trait UserSponsorCodeAssertions
{
    protected function assertUserSponsorCodeUsageCountApplied(int $count, CodeParrainageUser $userSponsorCode): void
    {
        $this->assertEquals(
            $count,
            $userSponsorCode->nb_utilisations,
            "Failed asserting user sponsor code has been used {$count} times. Current count is {$userSponsorCode->nb_utilisations}"
        );
    }
}
