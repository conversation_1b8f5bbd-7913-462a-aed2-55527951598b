<?php

declare(strict_types=1);

namespace Tests\Traits\Assertions;

use App\Models\Lieu;
use Carbon\CarbonInterface;

trait LocationAssertions
{
    public function assertLocationMiraklId(?string $value, Lieu $location): void
    {
        $this->assertEquals(
            $value,
            $location->miraklLocation?->mirakl_id,
            "Failed asserting location ND Mirakl id is '{$value}'. Current value is '{$location->miraklLocation?->mirakl_id}'",
        );
    }

    public function assertLocationMiraklOldId(?string $value, Lieu $location): void
    {
        $this->assertEquals(
            $value,
            $location->miraklLocation?->mirakl_old_id,
            "Failed asserting location ND Mirakl id is '{$value}'. Current value is '{$location->miraklLocation?->mirakl_old_id}'",
        );
    }

    public function assertLocationMiraklLastSyncedAt(?CarbonInterface $value, Lieu $location): void
    {
        $this->assertEquals(
            $value?->toDateTimeString(),
            $location->miraklLocation?->last_synced_at?->toDateTimeString(),
            "Failed asserting location ND Mirakl last sync date is '{$value}'. Current value is '{$location->miraklLocation?->last_synced_at?->toDateTimeString()}'",
        );
    }
}
