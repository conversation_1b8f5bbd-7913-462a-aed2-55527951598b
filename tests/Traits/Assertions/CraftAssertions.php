<?php

declare(strict_types=1);

namespace Tests\Traits\Assertions;

use App\Domain\Content\Models\Craft;

trait CraftAssertions
{
    public function assertCraftHasSinglePage(Craft $craft): void
    {
        $this->assertNotNull(
            $craft->singlePage,
            'Expected craft to have a single page'
        );
    }

    public function assertCraftIsActivated(Craft $craft): void
    {
        $this->assertTrue(
            $craft->is_active,
            'Expected that craft was activated'
        );
    }

    public function assertCraftIsVisible(Craft $craft): void
    {
        $this->assertTrue(
            $craft->is_visible,
            'Expected that craft was visible'
        );
    }
}
