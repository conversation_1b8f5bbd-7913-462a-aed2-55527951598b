<?php

declare(strict_types=1);

namespace Tests\Traits\Assertions;

use App\Domain\Content\Models\Technique;

trait TechniqueAssertions
{
    public function assertTechniqueHasSinglePage(Technique $technique): void
    {
        $this->assertNotNull(
            $technique->singlePage,
            'Expected technique to have a single page'
        );
    }

    public function assertTechniqueIsActivated(Technique $technique): void
    {
        $this->assertTrue(
            $technique->is_active,
            'Expected that technique was activated'
        );
    }
}
