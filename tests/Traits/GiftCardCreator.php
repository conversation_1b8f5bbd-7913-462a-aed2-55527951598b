<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Domain\Gift\Models\GiftCard;
use App\Models\Atelier;
use Database\Factories\GiftCardFactory;

trait GiftCardCreator
{
    public function createAmountGiftCard(float $amount = 100, array $params = []): GiftCard
    {
        return GiftCardFactory::new()
            ->withAmount($amount)
            ->create($params);
    }

    public function createTicketGiftCard(Atelier $workshop, int $ticketsCount = 1, array $params = []): GiftCard
    {
        return GiftCardFactory::new()
            ->forWorkshop($workshop, $ticketsCount)
            ->create($params);
    }
}
