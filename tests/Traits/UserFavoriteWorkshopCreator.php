<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Atelier;
use App\Models\User;
use App\Models\UserFavoriteWorkshop;
use Database\Factories\UserFavoriteWorkshopFactory;

trait UserFavoriteWorkshopCreator
{
    protected function createWorkshopAsFavorite(): UserFavoriteWorkshop
    {
        return UserFavoriteWorkshopFactory::new()->create();
    }

    protected function addWorkshopAsFavorite(Atelier $workshop, User $user): UserFavoriteWorkshop
    {
        return UserFavoriteWorkshopFactory::new()
            ->forWorkshop($workshop)
            ->forUser($user)
            ->create();
    }
}
