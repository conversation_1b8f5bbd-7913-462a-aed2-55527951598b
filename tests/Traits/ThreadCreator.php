<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Domain\PrivateBooking\Models\PrivateBookingRequest;
use App\Domain\Thread\Enums\ThreadAttachmentType;
use App\Domain\Thread\Models\Thread;
use App\Domain\Thread\Models\ThreadMessage;
use App\Domain\Thread\Models\ThreadMessageAttachment;
use App\Domain\Thread\Models\ThreadParticipant;
use App\Domain\Thread\Models\ThreadSystemMessage;
use App\Models\Artisan;
use App\Models\User;
use Carbon\CarbonInterface;
use Database\Factories\ThreadFactory;
use Database\Factories\ThreadMessageAttachmentFactory;
use Database\Factories\ThreadMessageFactory;
use Database\Factories\ThreadMessageReadFactory;
use Database\Factories\ThreadParticipantFactory;
use Database\Factories\ThreadSystemMessageFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

trait ThreadCreator
{
    public function createThread(array $params = []): Thread
    {
        return ThreadFactory::new()->create($params);
    }

    public function createThreadForPrivateBooking(
        PrivateBookingRequest $privateBooking,
        bool $addCustomerMessage = true,
        bool $addArtisanMessage = true,
        array $params = []
    ): Thread {
        $thread = $this->createThreadFor($privateBooking, $params);
        $userParticipant = $this->createUserParticipant($thread, $privateBooking->user);
        $artisanParticipant = $this->createArtisanParticipant($thread, $privateBooking->workshop->artisan);

        if ($addCustomerMessage) {
            $this->createMessage($thread, $userParticipant, 'Foo message');
        }

        if ($addArtisanMessage) {
            $this->createMessage($thread, $artisanParticipant, 'Bar message');
        }

        return $thread;
    }

    public function createThreadFor(Model $associable, array $params = []): Thread
    {
        return ThreadFactory::new()
            ->forAssociable($associable)
            ->create($params);
    }

    public function createUnreadMessage(Thread $thread, ThreadParticipant $participant, string $message, array $params = []): ThreadMessage
    {
        return ThreadMessageFactory::new()
            ->forThread($thread)
            ->withMessage($message)
            ->withSender($participant)
            ->create($params);
    }

    public function createMessage(Thread $thread, ThreadParticipant $participant, string $message, array $params = []): ThreadMessage
    {
        $message = $this->createUnreadMessage($thread, $participant, $message, $params);

        $this->readMessage($message, $message->created_at, $participant);

        return $message;
    }

    public function createSystemMessage(Thread $thread, ?ThreadParticipant $recipient, string $message, array $params = []): ThreadSystemMessage
    {
        return ThreadSystemMessageFactory::new()
            ->forThread($thread)
            ->withMessage($message)
            ->withRecipient($recipient)
            ->create($params);
    }

    public function createUserParticipant(Thread $thread, User $user, array $params = []): ThreadParticipant
    {
        return ThreadParticipantFactory::new()
            ->forThread($thread)
            ->forUser($user)
            ->create($params);
    }

    public function createArtisanParticipant(Thread $thread, Artisan $artisan, array $params = []): ThreadParticipant
    {
        return ThreadParticipantFactory::new()
            ->forThread($thread)
            ->forArtisan($artisan)
            ->create($params);
    }

    public function createThreadAttachmentMessage(
        ThreadMessage $message,
        UploadedFile $attachment,
        ThreadAttachmentType $type,
        array $params = [],
    ): ThreadMessageAttachment {
        $factory = ThreadMessageAttachmentFactory::new()->forThreadMessage($message);

        $factory = $type === ThreadAttachmentType::File
            ? $factory->withFile($attachment->hashName(), $attachment->getClientOriginalName())
            : $factory->withImage($attachment->hashName(), $attachment->getClientOriginalName());

        Storage::fake(config('thread.disk'));
        Storage::disk(config('thread.disk'))->put($attachment->hashName(), $attachment->getContent());

        return $factory->create($params);
    }

    public function readMessage(ThreadMessage $message, ?CarbonInterface $readAt = null, ThreadParticipant ...$participants): void
    {
        foreach ($participants as $participant) {
            ThreadMessageReadFactory::new()
                ->forMessage($message)
                ->readBy($participant)
                ->when($readAt instanceof CarbonInterface, fn (ThreadMessageReadFactory $factory) => $factory->readAt($readAt))
                ->create();
        }
    }
}
