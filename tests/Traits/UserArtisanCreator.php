<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Artisan;
use App\Models\UserArtisan;
use Database\Factories\ArtisanFactory;
use Database\Factories\UserArtisanFactory;
use Database\Factories\WorkshopFactory;

trait UserArtisanCreator
{
    protected function createUserArtisan(array $params = []): UserArtisan
    {
        return UserArtisanFactory::new()->create($params);
    }

    protected function createUserArtisanForArtisan(Artisan $artisan, array $params = []): UserArtisan
    {
        return UserArtisanFactory::new()
            ->forArtisan($artisan)
            ->create($params);
    }

    protected function createUserArtisanWithAnArtisanAndAWorkshop(array $userArtisanParams = [], array $workshopParams = []): UserArtisan
    {
        $workshop = WorkshopFactory::new($workshopParams);

        $artisan = ArtisanFactory::new()
            ->has($workshop);

        return UserArtisanFactory::new()
            ->has($artisan)
            ->create($userArtisanParams);
    }

    protected function createUserArtisanWithAnArtisanAndMultipleWorkshops(array $userArtisanParams = [], array $workshopParams = []): UserArtisan
    {
        $workshopA = WorkshopFactory::new($workshopParams);
        $workshopB = WorkshopFactory::new($workshopParams);
        $workshopC = WorkshopFactory::new($workshopParams);

        $artisan = ArtisanFactory::new()
            ->has($workshopA)
            ->has($workshopB)
            ->has($workshopC);

        return UserArtisanFactory::new()
            ->has($artisan)
            ->create($userArtisanParams);
    }

    protected function createUserArtisanWithMultipleArtisanAndMultipleWorkshops(array $userArtisanParams = [], array $workshopParams = []): UserArtisan
    {
        $workshopA = WorkshopFactory::new($workshopParams);
        $workshopB = WorkshopFactory::new($workshopParams);
        $workshopC = WorkshopFactory::new($workshopParams);
        $workshopD = WorkshopFactory::new($workshopParams);
        $workshopE = WorkshopFactory::new($workshopParams);

        $artisanA = ArtisanFactory::new()
            ->has($workshopA)
            ->has($workshopB);
        $artisanB = ArtisanFactory::new()
            ->has($workshopC)
            ->has($workshopD);
        $artisanC = ArtisanFactory::new()
            ->has($workshopE);

        return UserArtisanFactory::new()
            ->has($artisanA)
            ->has($artisanB)
            ->has($artisanC)
            ->create($userArtisanParams);
    }
}
