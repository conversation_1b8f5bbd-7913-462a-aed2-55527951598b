<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Atelier;
use App\Models\Reductions\CadeauBox;
use Database\Factories\BoxFactory;
use Database\Factories\CadeauBoxFactory;

trait GiftBoxCreator
{
    public function createGiftCardBoxForWorkshop(Atelier $workshop, float $amount): CadeauBox
    {
        $box = BoxFactory::new()
            ->withWorkshop($workshop)
            ->withAmount($amount)
            ->create();

        return CadeauBoxFactory::new()
            ->fromBox($box)
            ->create();
    }
}
