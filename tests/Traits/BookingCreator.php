<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Commande;
use App\Models\Evenement;
use App\Models\Reservation;
use Database\Factories\BookingFactory;

trait BookingCreator
{
    public function createBooking(array $params = []): Reservation
    {
        return BookingFactory::new()
            ->confirmed()
            ->create($params);
    }

    public function createBookingForEvent(Evenement $event, array $params = [], bool $quietly = false): Reservation
    {
        $factory = BookingFactory::new()
            ->forEvent($event)
            ->confirmed();

        if ($quietly) {
            return $factory->createQuietly($params);
        }

        return $factory->create($params);
    }

    public function createBookingForEventForOrder(Evenement $event, Commande $order, array $params = [], bool $quietly = false): Reservation
    {
        $factory = BookingFactory::new()
            ->forEvent($event)
            ->forOrder($order)
            ->confirmed();

        if ($quietly) {
            return $factory->createQuietly($params);
        }

        return $factory->create($params);
    }

    public function createBilledBookingForEvent(Evenement $event, array $params = []): Reservation
    {
        return BookingFactory::new()
            ->forEvent($event)
            ->billed()
            ->create($params);
    }

    public function createPendingBooking(array $params = []): Reservation
    {
        return BookingFactory::new()
            ->pending()
            ->create($params);
    }

    public function createPendingBookingForEvent(Evenement $event, array $params = []): Reservation
    {
        return BookingFactory::new()
            ->forEvent($event)
            ->pending()
            ->create($params);
    }

    public function createStandbyBookingForEvent(Evenement $event, array $params = []): Reservation
    {
        return BookingFactory::new()
            ->forEvent($event)
            ->standby()
            ->create($params);
    }

    public function createCancelledBookingForEvent(Evenement $event, array $params = []): Reservation
    {
        return BookingFactory::new()
            ->forEvent($event)
            ->cancelled()
            ->create($params);
    }

    public function createAwaitingPaymentBookingForEvent(Evenement $event, array $params = []): Reservation
    {
        return BookingFactory::new()
            ->forEvent($event)
            ->awaitingPayment()
            ->create($params);
    }
}
