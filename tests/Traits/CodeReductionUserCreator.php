<?php

declare(strict_types=1);

namespace Tests\Traits;

use App\Models\Reductions\CodeReductionUser;
use Database\Factories\CodeReductionUserFactory;

trait CodeReductionUserCreator
{
    public function createUserDiscount(int $amount, string $type, array $params = []): CodeReductionUser
    {
        return CodeReductionUserFactory::new()
            ->withAmount($amount, $type)
            ->create($params);
    }
}
