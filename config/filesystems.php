<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application for file storage.
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Below you may configure as many filesystem disks as necessary, and you
    | may even configure multiple disks for the same driver. Examples for
    | most supported storage drivers are configured here for reference.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [
        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => true,
        ],

        'testing' => [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
            'throw' => true,
        ],

        // =============
        // CDN DISK
        // =============

        // All images stored in this bucket will be available by requesting the CDN
        'images' => [
            'driver' => 's3',
            'key' => env('AWS_KEY'),
            'secret' => env('AWS_SECRET'),
            'region' => env('AWS_REGION'),
            'bucket' => env('AWS_BUCKET_IMG_PUBLIC'),
            'url' => env('AWS_BUCKET_IMG_PUBLIC_CDN_URL'),
            'endpoint' => env('AWS_S3_ENDPOINT'),
            'use_path_style_endpoint' => true,
            'throw' => true,
            'visibility' => 'public', // The public url should never be used, as the images are served by the CDN. TODO Set to private
        ],

        // =============
        // PRIVATE DISKS
        // =============

        // The root private bucket, you should never store more than one or two very important files in this storage
        'private' => [
            'driver' => 's3',
            'key' => env('AWS_KEY'),
            'secret' => env('AWS_SECRET'),
            'region' => env('AWS_REGION'),
            'bucket' => env('AWS_BUCKET_PRIVATE'),
            'endpoint' => env('AWS_S3_ENDPOINT'),
            'use_path_style_endpoint' => (bool) env('AWS_S3_ENDPOINT'), // required when using minio
            'throw' => true,
            'visibility' => 'private',
        ],

        // This bucket is used in order to store all generated xls exports
        'exports' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'exports',
        ],

        // This bucket is used in order to store all generated pdf
        // CAUTION: THIS DIRECTORY IS DELETED EVERY MONDAY !!!! See App\Console\Commands\Technique\CleanPdf
        'pdfs' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'pdf',
        ],

        'summary-sheets' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'summary-sheets',
        ],

        'calendars' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'calendar',
        ],

        'bank_details' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'bankdetails',
        ],

        'threads' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'threads',
        ],

        // This bucket is used for temp upload with Livewire
        'livewire' => [
            'driver' => 'scoped',
            'disk' => 'private',
            'prefix' => 'livewire',
        ],

        // TODO Remove this filesystem as soon as the "wecandoo-ftp" bucket will be used instead
        'sftp' => [
            'driver' => 'sftp',
            'host' => env('SFTP_HOST'),
            'username' => env('SFTP_USERNAME'),
            'privateKey' => str_replace('\n', "\n", env('SFTP_PRIVATE_KEY', '')) ?: env('SFTP_SSHKEY_PATH'),
            'root' => '/home',
            'throw' => true,
            'permPublic' => 0o777,
        ],

        'edenred' => [
            'driver' => 'sftp',
            'host' => env('EDENRED_FTP_HOST'),
            'username' => env('EDENRED_FTP_USERNAME'),
            'password' => env('EDENRED_FTP_PASSWORD'),
            'port' => (int) env('EDENRED_FTP_PORT', 22),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        //        public_path('storage') => storage_path('app/public'),
    ],
];
