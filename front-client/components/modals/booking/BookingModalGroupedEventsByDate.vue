<template>
  <div
    :class="[
      'tw-rounded-sm',
      isSelected && `
        tw-bg-secondary-600/50
        tw-outline tw-outline-secondary-600/50
        tw-outline-8 md:tw-outline-[1rem]
      `
    ]"
  >
    <w-typo
      class="tw-mb-2 md:tw-mb-4 tw-font-bold"
      type="body md:h6"
    >
      {{ formatDateTitle(date) }}
    </w-typo>
    <event-card
      v-for="event in getFirstEvents"
      :key="event.id"
      class="tw-mb-2 last:tw-mb-0"
      :event="event"
      :workshop="workshop"
      :box="workshop.box"
    />
    <template v-if="hasCollapse">
      <w-collapsible :opened="opened">
        <event-card
          v-for="event in getCollapsedEvents"
          :key="event.id"
          class="tw-mb-2 last:tw-mb-0"
          :event="event"
          :workshop="workshop"
          :box="workshop.box"
        />
      </w-collapsible>
      <w-collapsible :opened="!opened">
        <div class="tw-text-center tw-pt-4">
          <w-cta
            size="small md:medium"
            outlined
            @click="opened = !opened"
          >
            {{ $tc('modals.booking.events.show-x-more-items', getCollapsedEvents.length, { value: getCollapsedEvents.length }) }}
            <template #icon>
              <wi-plus />
            </template>
          </w-cta>
        </div>
      </w-collapsible>
    </template>
  </div>
</template>

<script>
import {
  WTypo, WCta, WCollapsible, WiPlus,
} from '@wecandoo/ui-kit';
import EventCard from '@/components/cards/EventCard.vue';
import { capitalize } from '@/tools/formating';
import { createDateTimeObjectFromIso } from '@/tools/dates';

/**
 * Max number of event visible before display a "show more" button
 */
const NB_EVENTS_BEFORE_COLLAPSE = 5;
/**
 * Minimum number of event hidden behind "show more" button.
 * We don't want a useless button for just 1 event
 */
const MINIMAL_EVENTS_TO_COLLAPSE = 2;

export default {

  components: {
    WTypo,
    WCta,
    WCollapsible,
    WiPlus,
    EventCard,
  },

  props: {
    date: {
      type: String,
      required: true,
    },
    events: {
      type: Array,
      required: true,
    },
    workshop: {
      type: Object,
      required: true,
    },
    selectedDay: {
      type: String,
      required: false,
      default: null,
    },
    coupon: {
      required: false,
      type: Object,
      default: null,
    },
  },

  data: () => ({
    opened: false,
  }),

  computed: {
    hasCollapse() {
      return this.events.length >= NB_EVENTS_BEFORE_COLLAPSE + MINIMAL_EVENTS_TO_COLLAPSE;
    },
    getFirstEvents() {
      if (this.hasCollapse) {
        return this.events.slice(0, NB_EVENTS_BEFORE_COLLAPSE);
      }
      return this.events;
    },
    getCollapsedEvents() {
      if (this.hasCollapse) {
        return this.events.slice(NB_EVENTS_BEFORE_COLLAPSE, this.events.length);
      }
      return null;
    },
    isSelected() {
      return this.selectedDay === this.date;
    },
  },

  methods: {
    formatDateTitle(date) {
      return capitalize(
        createDateTimeObjectFromIso(date)
          .toLocaleString({
            day: 'numeric',
            month: 'long',
            weekday: 'long',
          }),
      );
    },
  },

};
</script>
