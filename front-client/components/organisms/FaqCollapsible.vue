<template>
  <div>
    <div
      v-for="(item, index) in items"
      :key="index"
      class="tw-mb-6 last:tw-mb-0"
    >
      <button
        class="
          tw-appearance-none
          tw-text-left tw-w-full
          tw-flex tw-items-center tw-justify-between tw-gap-4
          tw-rounded
          focus-visible:tw-outline-4 focus-visible:tw-outline-offset-8
          focus-visible:tw-outline focus-visible:tw-outline-primary-200
        "
        @click="openedItems.splice(index, 1, !openedItems[index])"
      >
        <w-typo class="tw-font-bold">
          {{ item.question }}
        </w-typo>
        <wi-caret-circle-down
          :class="[
            'tw-text-2xl tw-transition',
            openedItems[index] && 'tw-rotate-180',
          ]"
        />
      </button>
      <w-collapsible
        class="tw-border-t-default tw-mt-2"
        :opened="openedItems[index]"
      >
        <div class="tw-py-4 tw-border-b-default tw-text-gray-700">
          <w-typo>
            <span v-html="item.answer" />
          </w-typo>
        </div>
      </w-collapsible>
    </div>
  </div>
</template>

<script>
import { WTypo, WCollapsible, WiCaretCircleDown } from '@wecandoo/ui-kit';

export const props = {
  /**
   * Array of Question / Answer object
   * @propery {String} items[].question
   * @propery {String} items[].answer
   */
  items: {
    type: Array,
    required: false,
    default: () => ([]),
  },
};

export default {

  components: {
    WTypo,
    WCollapsible,
    WiCaretCircleDown,
  },

  props,

  data: () => ({
    openedItems: [],
  }),

  watch: {
    items: {
      handler(val) {
        this.openedItems = new Array(val.length).fill(false);
      },
      immediate: true,
    },
  },

};
</script>
