<template>
  <form
    :action="localizedPath('/coffret/confirm')"
    method="post"
    class="d-flex product-card bg-beige rounded-sm flex-column justify-content-between align-items-center text-center p-3 position-relative"
  >
    <div class="d-flex flex-grow-1 justify-content-center align-items-center mb-3">
      <h5 class="text-primary my-auto">
        {{ $t('box.funnel.submit-confirmation.title') }}
      </h5>
    </div>
    <div class="d-flex flex-grow-1 justify-content-center align-items-center mb-3">
      <div class="my-auto">
        <attribute-error
          v-if="attributeHasErrors('telephone_contact')"
          :errors="getAttributeErrors('telephone_contact')"
        />
        <p class="mb-1">
          {{ $t('box.funnel.submit-confirmation.phone') }} <span class="text-danger">*</span>
        </p>
        <vue-tel-input
          v-model="phone"
          class="my-auto"
          :input-options="telInputOptions"
          :mode="telInputMode"
          name="telephone_contact"
          :valid-characters-only="true"
          placeholder=""
          :wrapper-classes="['form-control d-flex rounded', telInputValidityClasses]"
          input-classes="w-auto bg-transparent"
          :default-country="defaultCountry"
          :preferred-countries="preferredCountries"
          @validate="phoneValidation"
        />
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-content-center align-items-center mb-3">
      <div class="my-auto bootstrap-vue-wrapper">
        <attribute-error
          v-if="attributeHasErrors('cgucgv_accepted')"
          :errors="getAttributeErrors('cgucgv_accepted')"
        />
        <b-form-checkbox
          v-model="cgvCgu"
          name="cgucgv_accepted"
          class="tw-z-0"
          :value="1"
          :unchecked-value="0"
        >
          {{ $t('box.funnel.submit-confirmation.tos.term') }} <a
            target="_blank"
            :href="localizedPath('/cgu-cgv')"
          >{{ $t('box.funnel.submit-confirmation.tos.tos') }}</a><span class="text-danger">*</span>
        </b-form-checkbox>
      </div>
    </div>
    <div class="d-flex flex-grow-1 justify-content-center align-items-center">
      <div class="my-auto">
        <input
          type="hidden"
          name="_token"
          :value="csrfToken"
        >
        <button
          type="submit"
          :disabled="!canSubmit"
          class="btn btn-danger"
        >
          {{ $t('box.funnel.submit-confirmation.submit') }}
        </button>
      </div>
    </div>
  </form>
</template>

<script>
import { BFormCheckbox } from 'bootstrap-vue';
import VueTelInput from 'vue-tel-input';
import AttributeError from '@/components/atoms/errors/AttributeError.vue';
import DealsWithLoader from '@/mixins/deals-with-loader';
import DealsWithSessionErrors from '@/mixins/deals-with-session-errors';
import DealsWithVueTelInput from '@/mixins/deals-with-vue-tel-input';
import HasCsrfToken from '@/mixins/has-csrf-token';
import { localizedPath } from '@/tools/localization';

export default {
  name: 'BookingFormSubmit',
  components: {
    AttributeError,
    BFormCheckbox,
    VueTelInput,
  },
  mixins: [
    DealsWithVueTelInput,
    DealsWithSessionErrors,
    DealsWithLoader,
    HasCsrfToken,
  ],
  data() {
    return {
      cgvCgu: false,
      phone: '',
      phoneChanged: false,
      isPhoneValid: null,
      inputOptions_: {
        styleClasses: 'bg-transparent',
      },
      defaultCountry: process.env.country,
      preferredCountries: process.env.inputs.phone.preferred_countries,
    };
  },
  computed: {
    canSubmit() {
      return this.isPhoneValid && this.cgvCgu;
    },
    telInputValidityClasses() {
      if (this.phoneChanged) {
        return {
          'is-invalid': this.isPhoneValid === false,
          'is-valid': this.isPhoneValid === true,
        };
      }

      return '';
    },
  },
  watch: {
    phone(newVal, oldVal) {
      if (!this.phoneChanged) {
        this.phoneChanged = newVal !== oldVal;
      }
    },
  },
  methods: {
    localizedPath,
    phoneValidation(phoneObject) {
      if (typeof phoneObject.isValid !== 'undefined') {
        this.isPhoneValid = phoneObject.isValid;
      }
    },
  },
};
</script>
