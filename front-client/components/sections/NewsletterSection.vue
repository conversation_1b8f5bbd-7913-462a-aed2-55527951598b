<template>
  <container-section
    class="tw-text-white tw-bg-primary-500 tw-text-center"
    size="small"
  >
    <title-section>
      {{ $t('newsletter.title') }}
    </title-section>

    <w-typo
      class="-tw-mt-6 tw-mb-8"
      type="body"
      tag="p"
    >
      <b>{{ $t('newsletter.description') }}</b>
    </w-typo>

    <newsletter-form class="tw-mx-auto tw-text-left tw-max-w-2xl" />
  </container-section>
</template>

<script>
import ContainerSection from '@/components/sections/_components/ContainerSection.vue';
import TitleSection from '@/components/sections/_components/TitleSection.vue';
import NewsletterForm from '@/components/forms/NewsletterForm.vue';
import { WTypo } from '@wecandoo/ui-kit';

export default {

  components: {
    ContainerSection,
    TitleSection,
    NewsletterForm,
    WTypo,
  },

};
</script>
