<template>
  <cards-slider-section
    type="experience"
    :items="getItems"
    :redirect="getRedirect"
    :ready="isReady"
    :card-company-mode="cardCompanyMode"
    @visible="fetchSectionItems"
  >
    <template #title>
      <slot name="title" />
    </template>
    <template #subtitle>
      <slot name="subtitle" />
    </template>
    <template
      v-if="isReady"
      #last-card
    >
      <w-card-cta
        :btn-label="$t('cards.discover')"
        :to="getRedirect.to"
      >
        {{ getRedirect.label }}
      </w-card-cta>
    </template>
  </cards-slider-section>
</template>

<script>
import SearchOnWorkshops from '@/features/algolia-search/modules/search-on-workshops';

import { WCardCta } from '@wecandoo/ui-kit';

import { workshopAdapterFromAlgolia } from '@/features/workshop/tools/workshop-adapters';
import { mergeSearchParameters } from '@/features/algolia-search/tools/algolia';
import { getSearchPageFromAlgoliaSubject } from '@/features/algolia-search/tools/filters';

import CardsSliderSection from '@/components/sections/CardsSliderSection.vue';
import { SEARCH_MODE_DEFAULT } from '@/features/algolia-search/constants/search-page-mode';

export default {

  components: {
    WCardCta,
    CardsSliderSection,
  },

  props: {
    items: {
      type: Object,
      required: false,
      default: null,
    },
    searchParameters: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    subject: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    searchMode: {
      type: String,
      required: false,
      default: SEARCH_MODE_DEFAULT,
    },
    cardCompanyMode: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  data: () => ({
    localItems: [],
    ready: false,
  }),

  computed: {
    getItems() {
      return this.items ? this.items.map(workshopAdapterFromAlgolia) : this.localItems;
    },
    isReady() {
      return !!this.items || this.ready;
    },
    getRedirect() {
      return {
        label: this.$t('commons.see-all-experiences'),
        to: getSearchPageFromAlgoliaSubject(this.subject, this.searchMode),
      };
    },
  },

  methods: {
    fetchSectionItems() {
      if (!this.items) {
        SearchOnWorkshops({
          searchParameters: mergeSearchParameters({
            hitsPerPage: 10,
            distinct: true,
          }, this.searchParameters),
        }).then(({ hits }) => {
          this.ready = true;
          this.localItems = hits.map(workshopAdapterFromAlgolia);
        });
      }
    },
  },

};
</script>
