<template>
  <cards-slider-section
    type="experience"
    :items="getItems"
    :redirect="getRedirect"
    :ready="ready"
    :card-company-mode="cardCompanyMode"
    @visible="$emit('visible')"
    @card-click="(e) => $emit('card-click', e)"
    @redirect="(e) => $emit('redirect', e)"
  >
    <template #title>
      <slot name="title" />
    </template>
    <template #subtitle>
      <slot name="subtitle" />
    </template>
    <template #last-card>
      <slot name="last-card" />
    </template>
  </cards-slider-section>
</template>

<script>
import { workshopAdapterFromAlgolia } from '@/features/workshop/tools/workshop-adapters';
import CardsSliderSection from '@/components/sections/CardsSliderSection.vue';

export default {

  components: {
    CardsSliderSection,
  },

  props: {
    items: {
      type: Array,
      required: true,
    },
    ready: {
      type: Boolean,
      required: false,
      default: true,
    },
    cardCompanyMode: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  computed: {
    getItems() {
      return this.items ? this.items.map(workshopAdapterFromAlgolia) : [];
    },
    getRedirect() {
      return {
        label: this.$t('commons.see-all-experiences'),
        to: { name: 'search' },
      };
    },
  },

};
</script>
