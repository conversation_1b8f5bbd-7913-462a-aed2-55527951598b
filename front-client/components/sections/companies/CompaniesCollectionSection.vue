<template>
  <div>
    <cards-slider-section
      type="collection"
      :items="items"
      :ready="ready"
      @visible="fetchItems"
    >
      <template #title>
        {{ title }}
      </template>
      <template #subtitle>
        {{ subtitle }}
      </template>
    </cards-slider-section>

    <div
      v-if="hasItems"
      class="text-center tw-pb-16"
    >
      <w-cta :to="getRedirect">
        {{ $t('commons.see-all-experiences') }}
        <template #icon>
          <wi-arrow-circle-right />
        </template>
      </w-cta>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

import CardsSliderSection from '@/components/sections/CardsSliderSection.vue';
import { FETCH_COLLECTIONS_ACTION } from '@/store/modules/categorization/categorization-actions-types';
import { getImageUrlFromProdCdn } from '@/tools/images';
import { WCta, WiArrowCircleRight } from '@wecandoo/ui-kit';

export default {

  components: {
    CardsSliderSection,
    WiArrowCircleRight,
    WCta,
  },

  props: {
    title: {
      type: String,
      required: true,
    },
    subtitle: {
      type: String,
      required: true,
    },
  },

  data: () => ({
    items: null,
    ready: false,
  }),

  computed: {
    getRedirect() {
      return { name: 'search-companies' };
    },
    hasItems() {
      return !!this.items?.length;
    },
  },

  methods: {
    ...mapActions('categorization', {
      FETCH_COLLECTIONS_ACTION,
    }),
    fetchItems() {
      if (!this.items) {
        this.FETCH_COLLECTIONS_ACTION({ mode: 'company' })
          .then((collections) => {
            if (collections !== null) {
              this.ready = true;
              this.items = collections.map((collection) => ({
                name: collection.title,
                legend: this.$tc('commons.see-all-x-experiences', collection.count, { count: collection.count }),
                image: getImageUrlFromProdCdn(collection.image),
                to: { path: collection.slug },
              }));
            }
          })
          .catch((error) => console.error(error));
      }
    },
  },

};
</script>
