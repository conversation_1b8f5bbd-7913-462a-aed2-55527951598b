<template>
  <cards-slider-section
    type="collection"
    :items="computedItems"
  >
    <template #title>
      <slot name="title" />
    </template>
    <template #subtitle>
      <slot name="subtitle" />
    </template>
  </cards-slider-section>
</template>

<script>
import CardsSliderSection from '@/components/sections/CardsSliderSection.vue';

export default {
  components: { CardsSliderSection },
  props: {
    items: {
      required: false,
      type: Array,
      default: () => ([]),
    },
  },
  computed: {
    computedItems() {
      return this.items.map((item) => ({
        name: item.title,
        image: item.thumbnail_image,
        to: item.page_slug,
      }));
    },
  },
};
</script>
