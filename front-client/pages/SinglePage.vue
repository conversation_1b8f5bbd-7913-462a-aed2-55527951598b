<template>
  <div>
    <w-hero-single
      ref="hero"
      :image="hero_image"
      :illustration="badge_image"
      :breadcrumb="getBreadcrumb"
      :condensed="is_condensed"
    >
      <template #title>
        {{ title }}
      </template>
      <template #subtitle>
        {{ getSubtitle }}
      </template>
      <template
        v-if="getLegend"
        #legend
      >
        {{ getLegend }}
      </template>
      {{ description }}
      <div
        v-if="getRelatedSearchPage"
        class="tw-mt-4"
      >
        <w-cta
          size="small md:large"
          :to="getRelatedSearchPage"
        >
          {{ $t('singles.workshops.see-all') }}
        </w-cta>
      </div>
    </w-hero-single>

    <schema-org :schema="getBreadcrumbSchema" />

    <div>
      <template v-for="(section, index) in getSections">
        <component
          :is="section.component"
          :id="`section-${section.type}-${section.id}`"
          :key="index"
          :class="getSectionsBackgroundColors"
          v-bind="section.props"
        >
          <template #title>
            {{ section.title }}
          </template>
          <template #subtitle>
            {{ section.description }}
          </template>
        </component>

        <template v-if="!isBlackFriday">
          <gift-cards-banner-section
            v-if="showBannerSectionByIndex(index)"
            :key="`push-${index}`"
            :class="getBannerBackgroundColors"
            :click-id-theme="THEMES.singlePage.default"
          />
        </template>
      </template>

      <cross-links-section
        v-if="linked_pages.length > 0"
        :class="getSectionsBackgroundColors"
        :items="linked_pages"
        :subject="getSubject"
      >
        <template #title>
          {{ getCrossLinksTitle }}
        </template>
        <template #subtitle>
          {{ $t('singles.workshops.sections.cross-links.subtitle') }}
        </template>
      </cross-links-section>

      <cross-links-section
        v-if="custom_linked_pages.length > 0"
        :class="getSectionsBackgroundColors"
        :items="custom_linked_pages"
      >
        <template #title>
          {{ $t('singles.workshops.sections.custom-links.title') }}
        </template>
        <template #subtitle>
          {{ $t('singles.workshops.sections.custom-links.subtitle') }}
        </template>
      </cross-links-section>

      <search-bar-section
        :class="getSectionsBackgroundColors"
        :total-experiences="total_experiences"
      />

      <seo-section
        v-if="seo_title && seo_content"
        :class="getSectionsBackgroundColors"
        :title="seo_title"
        :content="seo_content"
      />
    </div>
  </div>
</template>

<script>
import SearchOnWorkshops from '@/features/algolia-search/modules/search-on-workshops';
import {
  CITY,
  CRAFTS,
  TECHNIQUES,
  CREATIONS,
  COLLECTION,
} from '@/constants/categorization-types';

import { WHeroSingle, WCta, WiMapLocation } from '@wecandoo/ui-kit';

import ClosestWorkshopsSection from '@/components/sections/workshops/ClosestWorkshopsSection.vue';
import PopularWorkshopsSection from '@/components/sections/workshops/PopularWorkshopsSection.vue';
import NewWorkshopsSection from '@/components/sections/workshops/NewWorkshopsSection.vue';
import SearchPreviewWorkshopsSection from '@/components/sections/workshops/SearchPreviewWorkshopsSection.vue';

import AssociatedTechniquesSection from '@/components/sections/techniques/AssociatedTechniquesSection.vue';
import AssociatedCreationsSection from '@/components/sections/creations/AssociatedCreationsSection.vue';
import AssociatedCitiesSection from '@/components/sections/cities/AssociatedCitiesSection.vue';
import AssociatedCraftsSection from '@/components/sections/crafts/AssociatedCraftsSection.vue';
import AssociatedArtisansSection from '@/components/sections/artisans/AssociatedArtisansSection.vue';

import SearchBarSection from '@/features/algolia-search/components/sections/SearchBarSection.vue';
import CrossLinksSection from '@/components/sections/CrossLinksSection.vue';
import SeoSection from '@/components/sections/SeoSection.vue';
import GiftCardsBannerSection from '@/features/gift/landing/components/GiftCardsBannerSection.vue';

import { facetAdapterFromAlgolia, artisanAdapterFromApi } from '@/tools/adapters';
import { getSearchPageFromAlgoliaSubject } from '@/features/algolia-search/tools/filters';
import AssociatedCollectionsSection from '@/components/sections/collections/AssociatedCollectionsSection.vue';
import SchemaOrg from '@/components/seo/SchemaOrg.vue';

import { isSSR } from '@/tools/dom';

import { THEMES } from '@/constants/tracking';
import { isBlackFriday } from '@/tools/events';
import { craftUrlRetrocompatibility } from '@/tools/formating';

import {
  SEARCH_MODE_DEFAULT,
  SEARCH_MODE_COMPANY,
  SEARCH_MODE_GROUP,
} from '@/features/algolia-search/constants/search-page-mode';

export default {

  components: {
    WHeroSingle,
    WCta,
    WiMapLocation,
    SearchBarSection,
    CrossLinksSection,
    SeoSection,
    GiftCardsBannerSection,
    SchemaOrg,
  },

  props: {
    subject: {
      type: Object,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: false,
      default: null,
    },
    mode: {
      required: false,
      type: String,
      default: SEARCH_MODE_DEFAULT,
    },
    hero_image: { // eslint-disable-line vue/prop-name-casing
      type: String,
      required: true,
    },
    badge_image: { // eslint-disable-line vue/prop-name-casing
      type: String,
      required: false,
      default: null,
    },
    sections: {
      type: Array,
      required: false,
      default: () => ([]),
    },
    linked_pages: { // eslint-disable-line vue/prop-name-casing
      type: Array,
      required: false,
      default: () => ([]),
    },
    custom_linked_pages: { // eslint-disable-line vue/prop-name-casing
      type: Array,
      required: false,
      default: () => ([]),
    },
    total_experiences: { // eslint-disable-line vue/prop-name-casing
      type: Number,
      required: false,
      default: 0,
    },
    seo_title: { // eslint-disable-line vue/prop-name-casing
      type: String,
      required: false,
      default: null,
    },
    seo_content: { // eslint-disable-line vue/prop-name-casing
      type: String,
      required: false,
      default: null,
    },
    is_condensed: { // eslint-disable-line vue/prop-name-casing
      type: Boolean,
      required: false,
      default: false,
    },
    city: {
      type: Object,
      required: false,
      default: null,
    },
    craft: {
      type: Object,
      required: false,
      default: null,
    },
    technique: {
      type: Object,
      required: false,
      default: null,
    },
  },

  data: () => ({
    nbExperiences: null,
    sessionToken: null,
    THEMES,
  }),

  computed: {
    isBlackFriday,
    getSubjectName() {
      return this.subject?.type?.split('\\').pop().toLowerCase();
    },
    getFacet() {
      return this.subject?.algolia?.facet;
    },
    isCity() {
      return this.getFacet === 'ville';
    },
    getSubtitle() {
      return this.$t(`singles.workshops.subtitle.${this.getFacet || 'default'}`);
    },
    getLegend() {
      return this.nbExperiences && this.$tc('singles.workshops.legend', this.nbExperiences, { value: this.nbExperiences });
    },
    getBreadcrumb() {
      // We always start with Wecandoo homepage
      const breadcrumb = [
        {
          label: 'Wecandoo',
          to: { name: 'homepage' },
        },
      ];

      const pillars = {
        [CITY]: {
          label: this.$t('pillars.ville.breadcrumb'),
          to: { path: '/villes' },
        },
        [CRAFTS]: {
          label: this.$t('pillars.craft.breadcrumb'),
          to: { path: '/savoir-faire' },
        },
        [CREATIONS]: {
          label: this.$t('pillars.creations.breadcrumb'),
          to: { path: '/creations' },
        },
      };

      // If current page is a city, a craft or a creation single page
      // We add the pillar page
      if (pillars[this.getFacet]) {
        breadcrumb.push(pillars[this.getFacet]);
      }

      // Technique single pages are children of their craft
      if (this.getFacet === TECHNIQUES) {
        breadcrumb.push(pillars[CRAFTS]);
        breadcrumb.push({
          label: this.technique.craft.name,
          to: {
            path: craftUrlRetrocompatibility(`/ateliers/${this.technique.craft.slug}`),
          },
        });
      }

      // If the current page is a collection, and the back-end give us a city and a craft
      // The single page is a city-craft
      // We add the city pillar page & the city single page
      if (this.getFacet === COLLECTION && this.craft && this.city) {
        breadcrumb.push(...[
          pillars[CITY],
          {
            label: this.city.name,
            to: { path: `/ateliers/${this.city.slug}` },
          },
        ]);
      }

      // We always end with the current page
      breadcrumb.push({
        label: this.subject.label,
      });

      return breadcrumb;
    },
    getBreadcrumbSchema() {
      return {
        '@type': 'BreadcrumbList',
        itemListElement: this.getBreadcrumb.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.label,
          ...item.to ? { item: this.$router.resolveInAbs(item.to) } : {},
        })),
      };
    },
    getSubject() {
      return this.subject
        ? {
          attribute: this.subject.algolia?.facet,
          identifier: this.subject.identifier,
          slug: this.subject.slug,
          // We duplicate the key to dissociate type (Back-end) & attribute (Algolia).
          // We need to refactor it asap
          type: this.subject.algolia?.facet,
        }
        : {};
    },
    getPageSearchParameters() {
      const searchParameters = this.subject?.algolia?.query || {};

      let filterToApply;

      switch (this.mode) {
        case SEARCH_MODE_GROUP:
          filterToApply = 'privatization_location: artisan';
          break;
        case SEARCH_MODE_COMPANY:
          // The tag name cannot be changed in the database
          filterToApply = "tags:'155||Teambuilding Offer'";
          break;
        case SEARCH_MODE_DEFAULT:
        default:
          return searchParameters;
      }

      if (typeof searchParameters.filters !== 'string' || searchParameters.filters === '') {
        searchParameters.filters = filterToApply;
      } else {
        searchParameters.filters = `(${filterToApply}) AND ${searchParameters.filters}`;
      }

      return searchParameters;
    },
    getSections() {
      const getComponent = (type) => {
        switch (type) {
          case 'cities':
            return AssociatedCitiesSection;
          case 'closest_experiences':
            return ClosestWorkshopsSection;
          case 'popular_experiences':
            return PopularWorkshopsSection;
          case 'news':
            return NewWorkshopsSection;
          case 'techniques':
            return AssociatedTechniquesSection;
          case 'creations':
            return AssociatedCreationsSection;
          case 'crafts':
            return AssociatedCraftsSection;
          case 'artisans':
            return AssociatedArtisansSection;
          case 'search_preview':
            return SearchPreviewWorkshopsSection;
          case 'collections':
            return AssociatedCollectionsSection;
          default:
            return null;
        }
      };

      const getItems = (section) => {
        switch (section.type) {
          case 'cities':
            return section.data.map((e) => facetAdapterFromAlgolia(e, CITY));
          case 'crafts':
            return section.data.map((e) => facetAdapterFromAlgolia(e, CRAFTS));
          case 'techniques':
            return section.data.map((e) => facetAdapterFromAlgolia(e, TECHNIQUES));
          case 'artisans':
            return section.data.map(artisanAdapterFromApi);
          case 'collections':
            return section.data.collections;
          case 'closest_experiences':
          case 'popular_experiences':
          case 'news':
          case 'search_preview':
          default:
            return section.data;
        }
      };

      return this.sections
        .map((section) => {
          const searchParameters = this.getPageSearchParameters;
          const subject = this.getSubject;

          return {
            ...section,
            component: getComponent(section.type),
            props: {
              items: section.data ? getItems(section) : null,
              searchParameters,
              subject,
              searchMode: this.mode,
            },
          };
        })
        .filter((e) => !!e.component);
    },
    getCrossLinksTitle() {
      return this.$t(`singles.workshops.sections.cross-links.title.${this.getFacet}`);
    },
    getRelatedSearchPage() {
      if (this.getSubject?.attribute) {
        return getSearchPageFromAlgoliaSubject(this.getSubject, this.mode);
      }
      return null;
    },
    getSearchPreviewIndex() {
      return this.getSections.findIndex((e) => e.type === 'search_preview');
    },
    getSectionsBackgroundColors() {
      if (this.getSearchPreviewIndex % 2 === 0) {
        return 'even:tw-bg-secondary-500 odd:tw-bg-white';
      }
      return 'odd:tw-bg-secondary-500 even:tw-bg-white';
    },
    getBannerBackgroundColors() {
      if (this.getSearchPreviewIndex % 2 === 0) {
        return 'odd:tw-bg-secondary-500 even:tw-bg-white';
      }
      return 'even:tw-bg-secondary-500 odd:tw-bg-white';
    },
  },

  created() {
    if (!isSSR()) {
      SearchOnWorkshops({
        searchParameters: {
          hitsPerPage: 1,
          distinct: true,
          ...this.getPageSearchParameters,
        },
      }).then(({ nbHits }) => {
        this.nbExperiences = nbHits;
      });
    }
  },

  mounted() {
    if (!isSSR() && google?.maps?.places) { // eslint-disable-line no-undef
      this.sessionToken = new google.maps.places.AutocompleteSessionToken(); // eslint-disable-line no-undef
    }
  },

  methods: {
    showBannerSectionByIndex(index) {
      let targetIndex = this.getSections.length > 1 ? 1 : 0;
      if (targetIndex === this.getSearchPreviewIndex) {
        targetIndex += 1;
      } else if (targetIndex === this.getSearchPreviewIndex - 1) {
        targetIndex -= 1;
      }
      return index === targetIndex;
    },
  },

};
</script>
