<template>
  <centered-layout
    :full-height="$wMq.is.md"
  >
    <div class="tw-text-center tw-flex tw-flex-col tw-items-center tw-py-8">
      <w-img
        class="tw-h-24 md:tw-h-32 tw-mx-auto tw-mb-8"
        :src="getImageUrlFromProdCdn('/front/icons/crop/broken-vase-blue.png')"
        force-height
        alt="404"
      />
      <div class="tw-mb-8">
        <w-typo
          type="h1"
          class="tw-text-primary tw-mb-2"
        >
          {{ $t('errors.404.title') }}
        </w-typo>
        <w-typo
          type="h6 md:h5"
          class="tw-text-gray-700 tw-mb-2"
        >
          {{ $t('errors.404.description') }}
        </w-typo>
        <w-typo
          type="caption"
          class="tw-text-gray-500 tw-font-bold"
        >
          {{ $t('errors.404.code') }}
        </w-typo>
      </div>
      <w-cta
        :to="{ name: 'homepage' }"
        tonal
      >
        {{ $t('errors.404.action') }}
      </w-cta>
    </div>
  </centered-layout>
</template>

<script>
import { getImageUrlFromProdCdn } from '@/tools/images';

import CenteredLayout from '@/components/layouts/CenteredLayout.vue';
import { WImg, WTypo, WCta } from '@wecandoo/ui-kit';

export default {

  components: {
    CenteredLayout,
    WImg,
    WTypo,
    WCta,
  },

  head() {
    return this.$head({
      title: `${this.$t('errors.404.title')} - ${this.$t('errors.404.code')}`,
      description: this.$t('errors.404.description'),
      meta: [
        {
          name: 'robots',
          content: 'noindex',
        },
        {
          name: 'prerender-status-code',
          content: '404',
        },
      ],
    });
  },

  methods: {
    getImageUrlFromProdCdn,
  },

};
</script>
