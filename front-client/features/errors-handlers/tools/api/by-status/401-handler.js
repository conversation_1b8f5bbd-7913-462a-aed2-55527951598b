import i18n from '@/translations/i18n';

import { SUBMIT_GROUP_BOOKING_FORM_ACTION } from '@/features/workshop/store/workshop-actions-types';
import { FETCH_USER_ACTION, LOGOUT_USER_ACTION } from '@/features/user/store/user-actions-types';

import { MODAL_NAME } from '@/features/user/auth/constants/login';
import { showErrorModalComponent } from '@/features/errors-handlers/tools/modals';

export default {
  default:
    (/* error */) => import('@/features/user/auth/components/modals/LoginModal.vue')
      .then((component) => showErrorModalComponent(component, null, MODAL_NAME)),
  [SUBMIT_GROUP_BOOKING_FORM_ACTION]:
    (/* error */) => import('@/features/user/auth/components/modals/LoginModal.vue')
      .then((component) => showErrorModalComponent(component, {
        message: i18n.t('workshop.group-booking.messages.login'),
      }, MODAL_NAME)),
  [FETCH_USER_ACTION]: null,
  [LOGOUT_USER_ACTION]: null,
};
