<template>
  <container-section
    class="tw-text-center"
    size="small"
  >
    <subtitle-section class="tw-text-primary-500">
      <slot name="subtitle">
        {{ $tc('sections.search-bar.subtitle', 0, {
          value: getRoundedTotalExperiences,
        }) }}
      </slot>
    </subtitle-section>

    <title-section>
      <slot name="title">
        {{ $t('sections.search-bar.title') }}
      </slot>
    </title-section>

    <search-bar-redirector class="tw-text-left" />
  </container-section>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { FETCH_TOTAL_EXPERIENCES_ACTION } from '@/features/workshop/store/workshop-actions-types';

import { isSSR } from '@/tools/dom';

import ContainerSection from '@/components/sections/_components/ContainerSection.vue';
import TitleSection from '@/components/sections/_components/TitleSection.vue';
import SubtitleSection from '@/components/sections/_components/SubtitleSection.vue';
import SearchBarRedirector from '@/features/algolia-search/components/filters/SearchBarRedirector.vue';

export default {

  components: {
    ContainerSection,
    TitleSection,
    SubtitleSection,
    SearchBarRedirector,
  },

  props: {
    totalExperiences: {
      type: Number,
      required: false,
      default: 0,
    },
  },

  computed: {
    ...mapGetters('workshop', [
      'getRoundedTotalExperiencesSSRProof',
    ]),
    getRoundedTotalExperiences() {
      return this.getRoundedTotalExperiencesSSRProof(this.totalExperiences);
    },
  },

  mounted() {
    if (!isSSR() && !this.totalExperiences) {
      this.FETCH_TOTAL_EXPERIENCES_ACTION();
    }
  },

  methods: {
    ...mapActions('workshop', {
      FETCH_TOTAL_EXPERIENCES_ACTION,
    }),
  },

};
</script>
