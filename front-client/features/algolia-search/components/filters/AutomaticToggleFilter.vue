<template>
  <div class="empty:tw-hidden">
    <slot
      :visible="visible"
      :is-group-visible="groupVisible"
    />
  </div>
</template>

<script>
import { createWidgetMixin } from 'vue-instantsearch';
import { connector } from '@/features/algolia-search/filters/connectors/AutomaticToggleFilterConnector';

export default {

  mixins: [
    createWidgetMixin({ connector }),
  ],

  props: {
    attributesNames: {
      type: [String, Array],
      required: false,
      default: null,
    },
    attributesNamesGrouped: {
      type: Array,
      required: false,
      default: null,
    },
  },

  data: () => ({
    visible: false,
    groupVisible: false,
  }),

  computed: {
    widgetParams() {
      return {
        attributesNames: this.attributesNames,
        attributesNamesGrouped: this.attributesNamesGrouped,
      };
    },
  },

  watch: {
    /**
     * We use a watcher and a data because in some case
     * the state is shortly undefined when the Algolia state is updated
     * causing some vue re-rendering & flickering.
     * We love it, it was fun to find a workaround... (not true)
     */
    state: {
      handler(value) {
        if (!value || typeof value !== 'object') {
          return;
        }
        const { isVisible, isGroupVisible } = value;
        if (typeof isVisible === 'boolean') this.visible = isVisible;
        if (typeof isGroupVisible === 'boolean') this.groupVisible = isGroupVisible;
      },
      immediate: true,
    },
  },

};
</script>
