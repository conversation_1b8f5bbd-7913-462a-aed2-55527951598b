import i18n from '@/translations/i18n';
import { prices } from '@/features/algolia-search/filters';
import { formatPrice } from '@/tools/formating';

const createWorkshopMinPriceMenuItemObject = (value) => ({
  label: i18n.t('navigation.entries.workshops-filtered.under-x', {
    value: formatPrice(value),
  }),
  to: {
    name: 'search',
    query: {
      [prices.getRouteQueryParamNameEnd()]: value,
    },
  },
});

export const WORKSHOPS_UNDER_50 = createWorkshopMinPriceMenuItemObject(50);

export const WORKSHOPS_UNDER_100 = createWorkshopMinPriceMenuItemObject(100);

export const WORKSHOPS_OVER_100 = {
  label: i18n.t('navigation.entries.workshops-filtered.over-100', {
    value: formatPrice(100),
  }),
  to: {
    name: 'search',
    query: {
      [prices.getRouteQueryParamNameStart()]: 100,
    },
  },
};
