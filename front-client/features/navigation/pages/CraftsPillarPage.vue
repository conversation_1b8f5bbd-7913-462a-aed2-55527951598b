<template>
  <div>
    <w-hero-pillar
      :stamp="getStamp"
      :breadcrumb="getBreadcrumb"
    >
      <template #title>
        {{ $t(`pillars.${CRAFTS}.title`) }}
      </template>
      <template #subtitle>
        {{ $tc(`pillars.${CRAFTS}.subtitle`, totalCount, {
          value: totalCount,
        }) }}
      </template>
      {{ $t(`pillars.${CRAFTS}.description`) }}
    </w-hero-pillar>

    <div class="tw-overflow-x-hidden">
      <container-section id="results">
        <div class="tw-mb-8 md:tw-mb-16">
          <overview-craft-section
            v-for="(item, index) in items"
            :key="`${index}-${currentPage}`"
            :item="item"
            class="tw-mb-8 md:tw-mb-16 last:tw-mb-0"
          />
        </div>

        <w-pagination
          prev-next-btns
          :value="currentPage"
          :length="lastPage"
          :create-url="createPaginationUrl"
        />
      </container-section>
    </div>

    <search-bar-section class="tw-bg-secondary" />
  </div>
</template>

<script>
import Store from '@/store';
import { FETCH_PAGINATED_CATEGORIES_ACTION } from '@/store/modules/categorization/categorization-actions-types';

import { WHeroPillar, WPagination } from '@wecandoo/ui-kit';
import OverviewCraftSection from '@/features/navigation/components/OverviewCraftSection.vue';
import SearchBarSection from '@/features/algolia-search/components/sections/SearchBarSection.vue';
import ContainerSection from '@/components/sections/_components/ContainerSection.vue';

import { mix } from '@/tools/mix';
import { scrollToElementID, getNavbarHeight } from '@/tools/dom';
import { categoryTypeToResource } from '@/tools/categorization';
import { handleRoutingFetchError } from '@/features/errors-handlers/tools/routing';

import { CURRENT_LANGUAGE } from '@/constants/languages';
import { CRAFTS } from '@/constants/categorization-types';

function fetchItems(page = 1) {
  return Store.dispatch(`categorization/${FETCH_PAGINATED_CATEGORIES_ACTION}`, {
    type: categoryTypeToResource(CRAFTS),
    page: parseInt(page, 10),
    hitsPerPage: 4,
  });
}

export default {

  components: {
    WHeroPillar,
    WPagination,
    ContainerSection,
    OverviewCraftSection,
    SearchBarSection,
  },

  beforeRouteEnter(to, from, next) {
    fetchItems(to.query.page)
      .then((response) => {
        next((vm) => {
          vm.setState(response);
        });
      })
      .catch((error) => {
        handleRoutingFetchError(error, next);
      });
  },

  beforeRouteUpdate(to, from, next) {
    fetchItems(to.query.page)
      .then((response) => {
        this.setState(response);
        scrollToElementID('results', false, getNavbarHeight());
        next();
      })
      .catch((error) => {
        handleRoutingFetchError(error, next, this);
      });
  },

  data: () => ({
    CRAFTS,
    currentPage: 1,
    lastPage: 1,
    totalCount: 0,
    items: [],
  }),

  head() {
    return this.$head({
      title: this.$t(`pillars.${CRAFTS}.meta-head.title`),
      description: this.$t(`pillars.${CRAFTS}.meta-head.description`),
    });
  },

  computed: {
    getStamp() {
      return mix(`/front-client/images/stamps/stamp-default-${CURRENT_LANGUAGE.locale}.png`);
    },
    getBreadcrumb() {
      return [
        {
          label: 'Wecandoo',
          redirect: {
            name: 'homepage',
          },
        },
        {
          label: this.$t(`pillars.${CRAFTS}.breadcrumb`),
        },
      ];
    },
  },

  methods: {
    setState(response) {
      this.currentPage = response.current_page ?? 1;
      this.lastPage = response.last_page ?? 1;
      this.items = response.data ?? [];
      this.totalCount = response.total ?? 0;
    },
    createPaginationUrl(page) {
      return this.$router.resolve({
        path: this.$route.path,
        query: {
          ...page > 1 ? { page } : {},
        },
      }).resolved.fullPath;
    },
  },

};
</script>
