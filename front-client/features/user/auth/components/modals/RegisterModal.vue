<template>
  <w-modal
    :illustration="getImageUrlFromProdCdn(ILLUSTRATION)"
    :title="$t(TITLE)"
    size="small"
    disable-mobile-offset
    :closable="!isLoading"
    @onShow="$gtmService.pushModalShow"
    @onHide="$gtmService.pushModalHide"
  >
    <register-form
      :context="MODAL"
    />
  </w-modal>
</template>

<script>
import { getImageUrlFromProdCdn } from '@/tools/images';

import { REGISTER_USER_ACTION } from '@/features/user/store/user-actions-types';
import { ILLUSTRATION, TITLE } from '@/features/user/auth/constants/register';
import { MODAL } from '@/features/user/auth/constants/contexts';

import { WModal } from '@wecandoo/ui-kit';

import RegisterForm from '@/features/user/auth/components/forms/RegisterForm.vue';

export default {

  components: {
    WModal,
    RegisterForm,
  },

  data: () => ({
    ILLUSTRATION,
    TITLE,
    MODAL,
  }),

  computed: {
    isLoading() {
      return this.$wait.is(REGISTER_USER_ACTION);
    },
  },

  methods: {
    getImageUrlFromProdCdn,
  },

};
</script>
