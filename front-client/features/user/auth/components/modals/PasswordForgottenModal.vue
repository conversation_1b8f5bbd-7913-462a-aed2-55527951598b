<template>
  <w-modal
    :illustration="getImageUrlFromProdCdn(ILLUSTRATION)"
    :title="$t(TITLE)"
    size="small"
    disable-mobile-offset
    :closable="!isLoading"
    @onShow="$gtmService.pushModalShow"
    @onHide="$gtmService.pushModalHide"
  >
    <password-forgotten-form
      :context="MODAL"
    />
  </w-modal>
</template>

<script>
import { getImageUrlFromProdCdn } from '@/tools/images';

import { PASSWORD_FORGOTTEN_ACTION } from '@/features/user/store/user-actions-types';
import { ILLUSTRATION, TITLE } from '@/features/user/auth/constants/password-forgotten';
import { MODAL } from '@/features/user/auth/constants/contexts';

import { WModal } from '@wecandoo/ui-kit';

import PasswordForgottenForm from '@/features/user/auth/components/forms/PasswordForgottenForm.vue';

export default {

  components: {
    WModal,
    PasswordForgottenForm,
  },

  data: () => ({
    ILLUSTRATION,
    TITLE,
    MODAL,
  }),

  computed: {
    isLoading() {
      return this.$wait.is(PASSWORD_FORGOTTEN_ACTION);
    },
  },

  methods: {
    getImageUrlFromProdCdn,
  },

};
</script>
