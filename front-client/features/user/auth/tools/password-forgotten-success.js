import { MODAL_NAME } from '@/features/user/auth/constants/password-forgotten-success';

import PasswordForgottenSuccessModal from '@/features/user/auth/components/modals/PasswordForgottenSuccessModal.vue';

import { openModalAndCloseEverytingElse } from '@/features/user/auth/tools/navigation';

export const openPasswordForgottenSuccessModal = (modalProps = {}) => {
  openModalAndCloseEverytingElse(
    PasswordForgottenSuccessModal,
    modalProps,
    MODAL_NAME,
  );
};
