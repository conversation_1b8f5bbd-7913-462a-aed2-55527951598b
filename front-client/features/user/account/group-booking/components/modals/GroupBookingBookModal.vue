<template>
  <w-modal
    :title="$t('user-account.group-booking.modals.book.title')"
    :closable="!isLoading"
    disable-mobile-offset
  >
    <w-typo class="tw-mb-4">
      <span v-html="$t('user-account.group-booking.modals.book.introduction')" />
    </w-typo>
    <w-form
      :action="submit"
    >
      <w-validator
        name="eventId"
        :value="selectedEventId"
      >
        <w-grid
          :columns="1"
          gap="condensed"
        >
          <w-radio
            v-for="event in events"
            :key="event.id"
            v-model="selectedEventId"
            :value="event.id"
          >
            {{ formatTwoIsoToReadableInterval(event.start, event.end) }}
          </w-radio>
        </w-grid>
      </w-validator>

      <w-cta
        size="medium"
        type="submit"
        :loading="isLoading"
        :disabled="!selectedEventId"
      >
        {{ $t('commons.actions.next') }}
      </w-cta>
    </w-form>
  </w-modal>
</template>

<script>
import { mapActions } from 'vuex';
import { BOOK_GROUP_BOOKING_EVENT_ACTION } from '@/features/user/store/user-actions-types';

import { formatTwoIsoToReadableInterval } from '@/tools/dates';
import { getFormErrors } from '@/features/errors-handlers/tools/api/by-form';

import {
  WModal,
  WTypo,
  WGrid,
  WForm,
  WRadio,
  WValidator,
  WCta,
} from '@wecandoo/ui-kit';

export default {

  components: {
    WModal,
    WTypo,
    WGrid,
    WForm,
    WRadio,
    WValidator,
    WCta,
  },

  props: {
    groupBookingId: {
      type: Number,
      required: true,
    },
    events: {
      type: Array,
      required: false,
      default: () => ([]),
    },
  },

  data: () => ({
    selectedEventId: null,
  }),

  computed: {
    isLoading() {
      return this.$wait.is(BOOK_GROUP_BOOKING_EVENT_ACTION);
    },
  },

  mounted() {
    if (this.events.length === 1) {
      // eslint-disable-next-line prefer-destructuring
      this.selectedEventId = this.events[0].id;
      this.submit();
      this.$emit('abort');
      return;
    }
    this.$emit('ready');
  },

  methods: {
    ...mapActions('user', {
      BOOK_GROUP_BOOKING_EVENT_ACTION,
    }),
    formatTwoIsoToReadableInterval,
    submit() {
      return this.BOOK_GROUP_BOOKING_EVENT_ACTION({
        groupBookingId: this.groupBookingId,
        eventId: this.selectedEventId,
      })
        .then((response) => {
          if (response.data.data.paymentUrl) {
            document.location = response.data.data.paymentUrl;
          }
          return response;
        })
        .catch((error) => {
          throw getFormErrors(error);
        });
    },
  },

};
</script>
