<template>
  <w-cta
    v-bind="$attrs"
    :loading="isLoading"
    @click="onClick"
  >
    {{ getLabel }}
    <template #icon>
      <wi-credit-card />
    </template>
  </w-cta>
</template>

<script>
import { mapActions } from 'vuex';
import {
  FETCH_USER_GROUP_BOOKING_DETAILS_ACTION,
  PAY_GROUP_BOOKING_ACTION,
} from '@/features/user/store/user-actions-types';

import { WCta, WiCreditCard } from '@wecandoo/ui-kit';

export default {

  components: {
    WCta,
    WiCreditCard,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
    condensed: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  computed: {
    getLabel() {
      if (this.condensed) {
        return this.$t('user-account.group-booking.details.actions.pay-with-stripe.labels.short');
      }
      return this.$t('user-account.group-booking.details.actions.pay-with-stripe.labels.long');
    },
    isLoading() {
      return this.$wait.is([
        PAY_GROUP_BOOKING_ACTION,
        FETCH_USER_GROUP_BOOKING_DETAILS_ACTION,
      ]);
    },
  },

  methods: {
    ...mapActions('user', {
      PAY_GROUP_BOOKING_ACTION,
    }),
    onClick() {
      this.PAY_GROUP_BOOKING_ACTION(this.item.id);
    },
  },

};
</script>
