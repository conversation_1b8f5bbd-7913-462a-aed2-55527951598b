<template>
  <div class="tw-pb-4">
    <div
      v-if="canHavePreviousMessages"
      class="tw-text-center"
    >
      <w-cta
        size="small"
        text
        :loading="isLoading"
        @click="$emit('fetch-previous-messages')"
      >
        {{ $t('user-account.group-booking.details.sections.chat.fetch-previous-messages') }}
        <template #icon>
          <wi-arrow-up />
        </template>
      </w-cta>
    </div>

    <div
      v-else
      class="tw-text-center tw-text-gray-500"
    >
      <w-typo
        class="tw-max-w-md tw-mx-auto"
        type="footnote"
      >
        {{ $t('user-account.group-booking.details.sections.chat.conversation-start') }}
      </w-typo>
    </div>
  </div>
</template>

<script>
import { FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION } from '@/features/user/store/user-actions-types';

import {
  <PERSON><PERSON>,
  WTypo,
  WiArrowUp,
} from '@wecandoo/ui-kit';

export default {

  components: {
    <PERSON><PERSON>,
    WTypo,
    Wi<PERSON>rrowUp,
  },

  props: {
    canHavePreviousMessages: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  computed: {
    isLoading() {
      return this.$wait.is(FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION);
    },
  },

};
</script>
