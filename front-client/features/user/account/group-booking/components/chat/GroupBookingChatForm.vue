<template>
  <w-form
    class="
      tw-sticky tw-bg-white
      tw-bottom-0 tw-pt-4
      tw-border-t-default
    "
    :action="submitNewMessage"
  >
    <w-validator
      name="attachments.[\d]|body"
      :value="[
        ...attachments,
        body,
      ]"
    >
      <div class="tw-flex tw-items-start tw-gap-2">
        <w-input-files
          :key="attachmentsResetKey"
          v-model="attachments"
          size="small"
          :max="$wMq.is.md ? 3 : 1"
          accept="application/pdf, application/acrobat, application/nappdf, application/x-pdf, image/pdf, image/jpeg, image/jpg, image/png, image/bmp, image/x-bmp, image/x-ms-bmp, image/tiff."
          :disabled="disabled"
        />
        <w-textarea
          v-model="body"
          class="tw-flex-1"
          placeholder="Aa"
          size="medium"
          :rows="1"
          :max-rows="4"
          :readonly="isLoading"
          :disabled="disabled"
          @resize="$emit('textarea-resize')"
          @keydown="onKeyDown"
        />
        <w-cta
          v-if="!$wMq.is.md"
          :loading="isLoading"
          size="medium"
          icon-only
          :disabled="disabled"
          type="submit"
        >
          <template #icon>
            <wi-paper-plane />
          </template>
        </w-cta>
        <w-cta
          v-else
          :loading="isLoading"
          :disabled="disabled"
          size="medium"
          type="submit"
        >
          {{ $t('user-account.group-booking.details.sections.chat.send') }}
          <template #icon>
            <wi-paper-plane />
          </template>
        </w-cta>
      </div>
    </w-validator>
  </w-form>
</template>

<script>
import { mapActions } from 'vuex';
import { SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION } from '@/features/user/store/user-actions-types';

import {
  WTextarea,
  WInputFiles,
  WCta,
  WForm,
  WValidator,
  WiPaperPlane,
} from '@wecandoo/ui-kit';

export default {

  components: {
    WTextarea,
    WInputFiles,
    WCta,
    WForm,
    WValidator,
    WiPaperPlane,
  },

  props: {
    groupBookingId: {
      type: Number,
      required: true,
    },
    lastMessageDate: {
      type: String,
      required: false,
      default: null,
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  data: () => ({
    body: '',
    attachments: [],
    attachmentsResetKey: 0,
  }),

  computed: {
    isLoading() {
      return this.$wait.is(`${SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION}_${this.groupBookingId}`);
    },
  },

  methods: {
    ...mapActions('user', {
      SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION,
    }),
    submitNewMessage() {
      if (this.disabled) {
        return null;
      }
      if (this.isLoading) {
        return null;
      }

      return this
        .SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION({
          groupBookingId: this.groupBookingId,
          date: this.lastMessageDate,
          body: this.body,
          attachments: this.attachments,
        })
        .then((response) => {
          this.body = '';
          this.attachments.splice(0);
          this.attachmentsResetKey += 1;
          this.$emit('new-message-sent', response.data.data);
          return response;
        });
    },
    onKeyDown(e) {
      if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        this.submitNewMessage();
      }
    },
  },

};
</script>
