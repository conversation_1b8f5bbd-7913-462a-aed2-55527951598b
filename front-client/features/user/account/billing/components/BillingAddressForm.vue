<template>
  <w-form
    :action="submit"
    gap="condensed md:default"
    scroll-to-errors
  >
    <w-typo class="tw-font-bold tw-text-gray-700">
      {{ $t('user-account.billing-address.form.subtitles.label') }}
    </w-typo>
    <w-validator
      name="label"
      :value="form.label"
    >
      <w-input
        v-model="form.label"
        name="label"
        :placeholder="`${$t('user-account.billing-address.form.label.placeholder')}*`"
        size="medium"
      >
        <template #legend>
          {{ $t('user-account.billing-address.form.label.legend') }}
        </template>
      </w-input>
    </w-validator>

    <w-divider v-if="!condensed" />

    <w-typo class="tw-font-bold tw-text-gray-700">
      {{ $t('user-account.billing-address.form.subtitles.identity') }}
    </w-typo>
    <w-grid
      columns="1 md:2"
      gap="condensed md:default"
    >
      <w-validator
        name="full_name"
        :value="form.full_name"
      >
        <w-input
          v-model="form.full_name"
          name="full_name"
          :placeholder="`${$t('user-account.billing-address.form.full-name.placeholder')}*`"
          size="medium"
        />
      </w-validator>
      <w-validator
        name="company"
        :value="form.company"
      >
        <w-input
          v-model="form.company"
          name="company"
          :placeholder="$t('user-account.billing-address.form.society.placeholder')"
          size="medium"
        />
      </w-validator>
    </w-grid>

    <w-divider v-if="!condensed" />

    <w-typo class="tw-font-bold tw-text-gray-700">
      {{ $t('user-account.billing-address.form.subtitles.address') }}
    </w-typo>
    <w-validator
      name="address"
      :value="form.address"
    >
      <w-input
        v-model="form.address"
        name="address"
        :placeholder="`${$t('user-account.billing-address.form.address.placeholder')}*`"
        size="medium"
      />
    </w-validator>
    <w-grid
      :columns="condensed ? '2' : '2 md:3'"
      gap="condensed md:default"
    >
      <w-validator
        name="city"
        :value="form.city"
      >
        <w-input
          v-model="form.city"
          name="city"
          :placeholder="`${$t('user-account.billing-address.form.city.placeholder')}*`"
          size="medium"
        />
      </w-validator>
      <w-validator
        name="zip_code"
        :value="form.zip_code"
      >
        <w-input
          v-model="form.zip_code"
          name="zip_code"
          :placeholder="`${$t('user-account.billing-address.form.zip-code.placeholder')}*`"
          size="medium"
        />
      </w-validator>
      <w-grid-col :size="condensed ? '2' : '2 md:1'">
        <w-validator
          name="country"
          :value="form.country"
        >
          <w-input
            v-model="form.country"
            name="country"
            :placeholder="`${$t('user-account.billing-address.form.country.placeholder')}*`"
            size="medium"
          />
        </w-validator>
      </w-grid-col>
    </w-grid>

    <span v-if="!forceDefault">
      <w-validator
        name="default"
        :value="form.default"
      >
        <w-checkbox
          v-model="form.default"
          name="default"
          :value="true"
          size="small"
          :disabled="isAlreadyDefault"
        >
          {{ $t('user-account.billing-address.form.set-default.label') }}
        </w-checkbox>
      </w-validator>
    </span>

    <w-cta
      size="medium"
      type="submit"
      :loading="isLoading"
    >
      {{ btnLabel }}
    </w-cta>
  </w-form>
</template>

<script>
import i18n from '@/translations/i18n';
import { mapActions } from 'vuex';
import { UPDATE_USER_BILLING_ADDRESS_ACTION } from '@/features/user/store/user-actions-types';

import {
  WForm,
  WInput,
  WValidator,
  WGrid,
  WCheckbox,
  WCta,
  WTypo,
  WDivider,
  WGridCol,
} from '@wecandoo/ui-kit';

import { getFormErrors } from '@/features/errors-handlers/tools/api/by-form';

export default {

  components: {
    WForm,
    WInput,
    WValidator,
    WGrid,
    WCheckbox,
    WCta,
    WTypo,
    WDivider,
    WGridCol,
  },

  props: {
    btnLabel: {
      type: String,
      required: false,
      default: i18n.t('user-account.billing-address.actions.save'),
    },
    address: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    forceDefault: {
      type: Boolean,
      required: false,
      default: false,
    },
    condensed: {
      type: Boolean,
      required: false,
      default: false,
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  data: () => ({
    form: {
      id: null,
      label: i18n.t('user-account.billing-address.form.label.default'),
      full_name: '',
      company: '',
      address: '',
      city: '',
      zip_code: '',
      country: '',
      default: false,
    },
  }),

  computed: {
    isLoading() {
      return this.loading || this.$wait.is([
        UPDATE_USER_BILLING_ADDRESS_ACTION,
      ]);
    },
    isAlreadyDefault() {
      return this.address.default;
    },
  },

  mounted() {
    const {
      id, label,
      full_name, company,
      address, city, zip_code, country,
      default: defaultBilling,
    } = this.address || {};

    if (id) {
      this.form = {
        id,
        label,
        full_name,
        company,
        address,
        city,
        zip_code,
        country,
        default: defaultBilling,
      };
    }

    if (this.forceDefault) {
      this.form.default = true;
    }
  },

  methods: {
    ...mapActions('user', {
      UPDATE_USER_BILLING_ADDRESS_ACTION,
    }),
    submit() {
      return this.UPDATE_USER_BILLING_ADDRESS_ACTION(this.form)
        .then(() => {
          this.$emit('success');
        })
        .catch((error) => {
          throw getFormErrors(error);
        });
    },
  },

};
</script>
