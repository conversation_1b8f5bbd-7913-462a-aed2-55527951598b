<template>
  <w-modal
    :title="$t('user-account.billing-address.title')"
    :closable="!isLoading"
    disable-mobile-offset
  >
    <w-typo
      v-if="helpLabel"
      class="tw-mb-4"
      type="caption"
    >
      {{ helpLabel }}
    </w-typo>
    <manage-billing-address
      :loading="isLoading"
      :on-submit="submit"
      :submit-label="submitLabel"
      :auto-fetch-addresses="false"
    />
  </w-modal>
</template>

<script>
import { mapActions } from 'vuex';
import {
  FETCH_USER_BILLING_ADDRESSES_ACTION,
  UPDATE_USER_BILLING_ADDRESS_ACTION,
} from '@/features/user/store/user-actions-types';

import { WModal, WTypo } from '@wecandoo/ui-kit';

import i18n from '@/translations/i18n';
import ManageBillingAddress from '@/features/user/account/billing/components/ManageBillingAddress.vue';

export default {

  components: {
    WModal,
    WTy<PERSON>,
    ManageBillingAddress,
  },

  props: {
    onSubmit: {
      type: Function,
      required: true,
    },
    address: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
    submitLabel: {
      type: String,
      required: false,
      default: i18n.t('user-account.billing-address.actions.submit'),
    },
    helpLabel: {
      type: String,
      required: false,
      default: null,
    },
  },

  computed: {
    isLoading() {
      return this.loading || this.$wait.is(UPDATE_USER_BILLING_ADDRESS_ACTION);
    },
  },

  mounted() {
    this.FETCH_USER_BILLING_ADDRESSES_ACTION()
      .then(() => {
        this.$emit('ready');
      })
      .catch(() => {
        this.$emit('abort');
      });
  },

  methods: {
    ...mapActions('user', {
      FETCH_USER_BILLING_ADDRESSES_ACTION,
    }),
    submit(selectedAddressId) {
      return this.onSubmit(selectedAddressId)
        .then((response) => {
          this.$wModal.hide(this.$attrs.modalName);
          return response;
        });
    },
  },

};
</script>
