import { STANDBY, CANCELLED } from '@/features/user/account/booking/constants/booking-status';
import { isDateAheadFromNow } from '@/tools/dates';

/**
 * Checks if the booking status is in standby.
 * @param {Object} booking - The booking object to check.
 * @returns {boolean} Returns true if the booking is in standby, false otherwise.
 */
export const isBookingInStandby = (booking) => {
  if (!booking) {
    return false; // Return false if booking is null or undefined.
  }
  return booking.status === STANDBY;
};

/**
 * Checks if the booking has been cancelled.
 * @param {Object} booking - The booking object to check.
 * @returns {boolean} Returns true if the booking has been cancelled, false otherwise.
 */
export const isBookingCancelled = (booking) => {
  if (!booking) {
    return false; // Return false if booking is null or undefined.
  }
  return booking.status === CANCELLED;
};

/**
 * Checks if the booking is marked as private.
 * @param {Object} booking - The booking object to check.
 * @returns {boolean} Returns true if the booking is private, false otherwise.
 */
export const isBookingPrivate = (booking) => {
  if (!booking) {
    return false; // Return false if booking is null or undefined.
  }
  return booking.event.isPrivate;
};

/**
 * Determines whether the booking can be updated based on its status and date.
 * @param {Object} booking - The booking object to evaluate.
 * @returns {boolean} Returns true if the booking can be updated, false otherwise.
 */
export const canBookingBeUpdated = (booking) => {
  if (!booking) {
    return false; // Return false if booking is null or undefined.
  }
  if (isBookingPrivate(booking)) {
    return false; // Booking cant be updated if it's marked as private.
  }
  if (isBookingInStandby(booking)) {
    return true; // Booking can be updated if it's in standby status.
  }
  // Booking can be updated if the update is set for a future date and it has not been cancelled.
  return isDateAheadFromNow(booking.updatePossibleUntilDateTime)
    && !isBookingCancelled(booking);
};
