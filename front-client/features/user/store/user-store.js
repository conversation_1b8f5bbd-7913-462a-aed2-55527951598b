import { loadStripe } from '@stripe/stripe-js';

import { pushAuthCallback } from '@/tools/gtm/gtm-service';
import { startLoading, endLoading } from '@/tools/loading-states';
import http from '@/tools/http';
import { setDatadogUser, clearDatadogUser } from '@/features/errors-handlers/monitoring/datadog';

import { IN_PROGRESS } from '@/features/user/account/group-booking/constants/group-booking-tabs';

import {
  FETCH_CART_ACTION,
} from '@/features/cart/store/cart-actions-types';

import { pushUserLoginInGtm, pushUserRegisteredInGtm } from '@/tools/gtm/gtm-general';
import {
  ADD_USER_FAVORITE_WORKSHOP_ACTION,
  DELETE_USER_FAVORITE_WORKSHOP_ACTION,
  ADD_NEW_WORKSHOP_COMMENT_ACTION,
  FETCH_USER_BILLING_ADDRESSES_ACTION,
  UPDATE_USER_BILLING_ADDRESS_ACTION,
  DELETE_USER_BILLING_ADDRESS_ACTION,
  LOGIN_USER_ACTION,
  REGISTER_USER_ACTION,
  PASSWORD_FORGOTTEN_ACTION,
  PASSWORD_RESET_ACTION,
  LOGOUT_USER_ACTION,
  FETCH_USER_ACTION,
  FETCH_USER_GROUP_BOOKINGS_ACTION,
  FETCH_USER_GROUP_BOOKING_DETAILS_ACTION,
  FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION,
  UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION,
  FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION,
  FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION,
  SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION,
  CANCEL_GROUP_BOOKING_ACTION,
  SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION,
  BOOK_GROUP_BOOKING_EVENT_ACTION,
  VALIDATE_GROUP_BOOKING_QUOTE_ACTION,
  PAY_GROUP_BOOKING_ACTION,
} from './user-actions-types';

import {
  SET_USER_MUTATION,
  RESET_USER_MUTATION,
  ADD_USER_FAVORITE_WORKSHOP_MUTATION,
  DELETE_USER_FAVORITE_WORKSHOP_MUTATION,
  SET_USER_BILLING_ADDRESSES_MUTATION,
} from './user-mutations-types';

export default {

  namespaced: true,

  state: {
    user: null,
    userBillingAddresses: [],
  },

  getters: {
    isAuth: (state, getters) => !!getters.getUser?.id,
    getUser: (state) => state.user,
    getUserId: (s, getters) => getters.getUser?.id,
    getUserFavoriteWorkshopsIds: (s, getters) => getters.getUser?.favorites || [],
    isWorkshopFavoriteByUser: (s, getters) => (id) => getters.getUserFavoriteWorkshopsIds.includes(parseInt(id, 10)),
    getUserBillingAddresses: (state) => {
      const addresses = [...state.userBillingAddresses || []];
      return addresses.sort(((a) => (a.default ? -1 : 1)));
    },
    hasAtLeastOneUserBillingAddress: (s, getters) => getters.getUserBillingAddresses.length > 0,
    getDefaultUserBillingAddress: (s, getters) => {
      if (!getters.hasAtLeastOneUserBillingAddress) {
        return null;
      }
      const defaultAddress = getters.getUserBillingAddresses.find((e) => e.default);
      return defaultAddress || getters.getUserBillingAddresses.addresses[0];
    },
  },

  mutations: {
    [SET_USER_MUTATION](state, user) {
      state.user = user;
      setDatadogUser(user);
    },
    [RESET_USER_MUTATION](state) {
      state.user = null;
      clearDatadogUser();
    },
    [ADD_USER_FAVORITE_WORKSHOP_MUTATION](state, workshopId) {
      let id = workshopId;

      if (typeof id === 'string') id = parseInt(id, 10);
      if (typeof id !== 'number') return;

      const { user } = state;
      user.favorites.push(id);
      state.user = user;
    },
    [DELETE_USER_FAVORITE_WORKSHOP_MUTATION](state, workshopId) {
      if (!workshopId) return;

      const { user } = state;
      const id = parseInt(workshopId, 10);
      const index = user.favorites.findIndex((e) => e === id);

      if (index === -1) return;

      user.favorites.splice(index, 1);
      state.user = user;
    },
    [SET_USER_BILLING_ADDRESSES_MUTATION](state, addresses) {
      state.userBillingAddresses = addresses;
    },
  },

  actions: {
    [ADD_USER_FAVORITE_WORKSHOP_ACTION]({ dispatch, commit }, workshopId) {
      startLoading(dispatch, `${ADD_USER_FAVORITE_WORKSHOP_ACTION}_${workshopId}`);
      return http
        .post(`/users/workshops-favorites/${workshopId}`)
        .then(() => {
          commit(ADD_USER_FAVORITE_WORKSHOP_MUTATION, workshopId);
        })
        .finally(() => {
          endLoading(dispatch, `${ADD_USER_FAVORITE_WORKSHOP_ACTION}_${workshopId}`);
        });
    },
    [DELETE_USER_FAVORITE_WORKSHOP_ACTION]({ dispatch, commit }, workshopId) {
      startLoading(dispatch, `${DELETE_USER_FAVORITE_WORKSHOP_ACTION}_${workshopId}`);
      return http
        .delete(`/users/workshops-favorites/${workshopId}`)
        .then(() => {
          commit(DELETE_USER_FAVORITE_WORKSHOP_MUTATION, workshopId);
        })
        .finally(() => {
          endLoading(dispatch, `${DELETE_USER_FAVORITE_WORKSHOP_ACTION}_${workshopId}`);
        });
    },
    [ADD_NEW_WORKSHOP_COMMENT_ACTION]({ dispatch }, { workshopId, comment = '', files = [] }) {
      startLoading(dispatch, `${ADD_NEW_WORKSHOP_COMMENT_ACTION}_${workshopId}`);

      const formData = new FormData();
      formData.append('comment', comment);

      files.forEach((file, index) => {
        formData.append(`photos[${index}]`, file);
      });

      return http
        .post(`/api/ateliers/${workshopId}/comment`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .finally(() => {
          endLoading(dispatch, `${ADD_NEW_WORKSHOP_COMMENT_ACTION}_${workshopId}`);
        });
    },
    [FETCH_USER_BILLING_ADDRESSES_ACTION]({ dispatch, commit }) {
      startLoading(dispatch, FETCH_USER_BILLING_ADDRESSES_ACTION);

      return http
        .get('/api/user/billing-addresses')
        .then((response) => {
          commit(SET_USER_BILLING_ADDRESSES_MUTATION, response.data.data);
          return response;
        })
        .finally(() => {
          endLoading(dispatch, FETCH_USER_BILLING_ADDRESSES_ACTION);
        });
    },
    [UPDATE_USER_BILLING_ADDRESS_ACTION]({ dispatch }, address = {}) {
      startLoading(dispatch, UPDATE_USER_BILLING_ADDRESS_ACTION);

      const { id, ...params } = address;

      const action = id ? http.put : http.post;
      const routeApi = id ? `/api/user/billing-addresses/${id}` : '/api/user/billing-addresses';

      return action(routeApi, params)
        .then((response) => dispatch(FETCH_USER_BILLING_ADDRESSES_ACTION)
          .then(() => response)
          .finally(() => {
            endLoading(dispatch, UPDATE_USER_BILLING_ADDRESS_ACTION);
          }))
        .finally(() => {
          endLoading(dispatch, UPDATE_USER_BILLING_ADDRESS_ACTION);
        });
    },
    [DELETE_USER_BILLING_ADDRESS_ACTION]({ dispatch }, address = {}) {
      startLoading(dispatch, DELETE_USER_BILLING_ADDRESS_ACTION);

      return http
        .delete(`/api/user/billing-addresses/${address.id}`)
        .then((response) => dispatch(FETCH_USER_BILLING_ADDRESSES_ACTION)
          .then(() => response)
          .finally(() => {
            endLoading(dispatch, DELETE_USER_BILLING_ADDRESS_ACTION);
          }))
        .finally(() => {
          endLoading(dispatch, DELETE_USER_BILLING_ADDRESS_ACTION);
        });
    },
    [LOGIN_USER_ACTION]({ dispatch, rootGetters }, { email, password }) {
      if (rootGetters['wait/is'](LOGIN_USER_ACTION)) {
        throw new Error('Call API pending');
      }

      startLoading(dispatch, LOGIN_USER_ACTION);
      return http
        .post('/api/auth/login', {
          email,
          password,
        })
        .then(() => Promise.all([
          dispatch(FETCH_USER_ACTION),
          dispatch(`cart/${FETCH_CART_ACTION}`, null, { root: true }),
        ])
          .finally(() => {
            pushUserLoginInGtm({ method: 'email', user: rootGetters['user/getUser'] });
            endLoading(dispatch, LOGIN_USER_ACTION);
          }))
        .catch((error) => {
          endLoading(dispatch, LOGIN_USER_ACTION);
          throw error;
        });
    },
    [REGISTER_USER_ACTION]({ dispatch, rootGetters }, {
      firstName,
      lastName,
      email,
      password,
      passwordConfirmation: password_confirmation,
    }) {
      if (rootGetters['wait/is'](REGISTER_USER_ACTION)) {
        throw new Error('Call API pending');
      }

      startLoading(dispatch, REGISTER_USER_ACTION);

      return http
        .post('/api/auth/register', {
          firstName,
          lastName,
          email,
          password,
          password_confirmation,
        })
        .then(() => Promise.all([
          dispatch(FETCH_USER_ACTION),
          dispatch(`cart/${FETCH_CART_ACTION}`, null, { root: true }),
        ])
          .finally(() => {
            // @deprecated use pushUserRegisteredInGtm instead
            pushAuthCallback('email', 'register');
            pushUserRegisteredInGtm({
              method: 'email',
              user: rootGetters['user/getUser'],
            });
            endLoading(dispatch, REGISTER_USER_ACTION);
          }))
        .catch((err) => {
          endLoading(dispatch, REGISTER_USER_ACTION);
          throw err;
        });
    },
    [PASSWORD_FORGOTTEN_ACTION]({ dispatch, rootGetters }, { email }) {
      if (rootGetters['wait/is'](PASSWORD_FORGOTTEN_ACTION)) {
        throw new Error('Call API pending');
      }

      startLoading(dispatch, PASSWORD_FORGOTTEN_ACTION);

      return http
        .post('/api/auth/password/forgot', { email })
        .finally(() => {
          endLoading(dispatch, PASSWORD_FORGOTTEN_ACTION);
        });
    },
    [PASSWORD_RESET_ACTION]({ dispatch, rootGetters }, {
      email,
      token,
      password,
      passwordConfirmation: password_confirmation,
    }) {
      if (rootGetters['wait/is'](PASSWORD_RESET_ACTION)) {
        throw new Error('Call API pending');
      }

      startLoading(dispatch, PASSWORD_RESET_ACTION);

      return http
        .post('/api/auth/password/reset', {
          email,
          token,
          password,
          password_confirmation,
        })
        .then(() => Promise.all([
          dispatch(FETCH_USER_ACTION),
          dispatch(`cart/${FETCH_CART_ACTION}`, null, { root: true }),
        ])
          .finally(() => {
            endLoading(dispatch, PASSWORD_RESET_ACTION);
          }))
        .catch((err) => {
          endLoading(dispatch, PASSWORD_RESET_ACTION);
          throw err;
        });
    },
    [LOGOUT_USER_ACTION]({ dispatch, commit, rootGetters }) {
      if (rootGetters['wait/is'](PASSWORD_RESET_ACTION)) {
        throw new Error('Call API pending');
      }

      startLoading(dispatch, LOGOUT_USER_ACTION);

      return http
        .post('/api/auth/logout')
        .then((response) => {
          commit(RESET_USER_MUTATION);
          dispatch(`cart/${FETCH_CART_ACTION}`, null, { root: true });
          return response;
        })
        .finally(() => {
          endLoading(dispatch, LOGOUT_USER_ACTION);
        });
    },
    [FETCH_USER_ACTION]({ dispatch, commit }) {
      startLoading(dispatch, FETCH_USER_ACTION);
      return http
        .get('/api/auth/user')
        .then((response) => {
          commit(SET_USER_MUTATION, response.data.data);
          return response;
        })
        .catch((error) => {
          commit(RESET_USER_MUTATION);
          throw error;
        })
        .finally(() => {
          endLoading(dispatch, FETCH_USER_ACTION);
        });
    },
    [FETCH_USER_GROUP_BOOKINGS_ACTION]({ dispatch }, { tab = IN_PROGRESS, page = 1 }) {
      startLoading(dispatch, FETCH_USER_GROUP_BOOKINGS_ACTION);
      startLoading(dispatch, `${FETCH_USER_GROUP_BOOKINGS_ACTION}_${tab}`);

      return http
        .get('/api/group-bookings', {
          params: {
            tab,
            page,
          },
        })
        .finally(() => {
          endLoading(dispatch, FETCH_USER_GROUP_BOOKINGS_ACTION);
          endLoading(dispatch, `${FETCH_USER_GROUP_BOOKINGS_ACTION}_${tab}`);
        });
    },
    [FETCH_USER_GROUP_BOOKING_DETAILS_ACTION]({ dispatch }, id) {
      startLoading(dispatch, FETCH_USER_GROUP_BOOKING_DETAILS_ACTION);
      startLoading(dispatch, `${FETCH_USER_GROUP_BOOKING_DETAILS_ACTION}_${id}`);

      return http
        .get(`/api/group-bookings/${id}`)
        .finally(() => {
          endLoading(dispatch, FETCH_USER_GROUP_BOOKING_DETAILS_ACTION);
          endLoading(dispatch, `${FETCH_USER_GROUP_BOOKING_DETAILS_ACTION}_${id}`);
        });
    },
    [FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION]({ dispatch }, { id, adultNumber, childNumber }) {
      startLoading(dispatch, FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION);
      startLoading(dispatch, `${FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION}_${id}`);

      return http
        .get(`/api/group-bookings/${id}/change/estimate-price`, {
          params: {
            adultNumber,
            childNumber,
          },
        })
        .finally(() => {
          endLoading(dispatch, FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION);
          endLoading(dispatch, `${FETCH_USER_GROUP_BOOKING_ESTIMATE_CHANGE_PRICE_ACTION}_${id}`);
        });
    },
    [UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION]({ dispatch }, { id, adultNumber, childNumber }) {
      startLoading(dispatch, UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION);
      startLoading(dispatch, `${UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION}_${id}`);

      return http
        .post(`/api/group-bookings/${id}/change/participants`, {
          adultNumber,
          childNumber,
        })
        .finally(() => {
          endLoading(dispatch, UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION);
          endLoading(dispatch, `${UPDATE_USER_GROUP_BOOKING_PARTICIPANTS_ACTION}_${id}`);
        });
    },
    [FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION]({ dispatch }, { groupBookingId, date, count }) {
      startLoading(dispatch, FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION);
      startLoading(dispatch, `${FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION}_${groupBookingId}`);

      const params = {
        count,
      };

      if (date) {
        params.message_date = date;
      }

      return http
        .get(`/api/group-bookings/${groupBookingId}/threads/messages`, { params })
        .finally(() => {
          endLoading(dispatch, FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION);
          endLoading(dispatch, `${FETCH_GROUP_BOOKING_THREAD_MESSAGES_ACTION}_${groupBookingId}`);
        });
    },
    [FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION]({ dispatch }, { groupBookingId, date }) {
      startLoading(dispatch, FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION);
      startLoading(dispatch, `${FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION}_${groupBookingId}`);

      const params = {};

      if (date) {
        params.message_date = date;
      }

      return http
        .get(`/api/group-bookings/${groupBookingId}/threads/messages/latest`, { params })
        .finally(() => {
          endLoading(dispatch, FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION);
          endLoading(dispatch, `${FETCH_GROUP_BOOKING_THREAD_LATEST_MESSAGES_ACTION}_${groupBookingId}`);
        });
    },
    [SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION]({ dispatch }, {
      groupBookingId,
      body,
      attachments,
      date,
    }) {
      startLoading(dispatch, SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION);
      startLoading(dispatch, `${SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION}_${groupBookingId}`);

      const formData = new FormData();
      formData.append('body', body);
      if (date) formData.append('message_date', date);

      attachments.forEach((attachment, index) => {
        formData.append(`attachments[${index}]`, attachment);
      });

      return http
        .post(`/api/group-bookings/${groupBookingId}/threads/messages`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .finally(() => {
          endLoading(dispatch, SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION);
          endLoading(dispatch, `${SEND_GROUP_BOOKING_NEW_MESSAGE_ACTION}_${groupBookingId}`);
        });
    },
    [CANCEL_GROUP_BOOKING_ACTION]({ dispatch }, groupBookingId) {
      startLoading(dispatch, CANCEL_GROUP_BOOKING_ACTION);
      startLoading(dispatch, `${CANCEL_GROUP_BOOKING_ACTION}_${groupBookingId}`);

      return http
        .post(`/api/group-bookings/${groupBookingId}/cancel`)
        .finally(() => {
          endLoading(dispatch, CANCEL_GROUP_BOOKING_ACTION);
          endLoading(dispatch, `${CANCEL_GROUP_BOOKING_ACTION}_${groupBookingId}`);
        });
    },
    [SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION]({ dispatch }, { groupBookingId, addressId }) {
      startLoading(dispatch, SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION);
      startLoading(dispatch, `${SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION}_${groupBookingId}`);

      return http
        .put(`/api/group-bookings/${groupBookingId}/billing-address`, { id: addressId })
        .finally(() => {
          endLoading(dispatch, SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION);
          endLoading(dispatch, `${SET_GROUP_BOOKING_BILLING_ADDRESS_ACTION}_${groupBookingId}`);
        });
    },
    [BOOK_GROUP_BOOKING_EVENT_ACTION]({ dispatch }, { groupBookingId, eventId }) {
      startLoading(dispatch, BOOK_GROUP_BOOKING_EVENT_ACTION);
      startLoading(dispatch, `${BOOK_GROUP_BOOKING_EVENT_ACTION}_${groupBookingId}`);

      return http
        .post(`/api/group-bookings/${groupBookingId}/book-event`, {
          eventId,
        })
        /**
         * We don't want to stop the loading if we got a succes.
         * We redirect the user to an other page, so we want to hide it behind the same loading.
         */
        .catch((error) => {
          endLoading(dispatch, BOOK_GROUP_BOOKING_EVENT_ACTION);
          endLoading(dispatch, `${BOOK_GROUP_BOOKING_EVENT_ACTION}_${groupBookingId}`);
          throw error;
        });
    },
    [VALIDATE_GROUP_BOOKING_QUOTE_ACTION]({ dispatch }, { groupBookingId, ...remainingParams }) {
      startLoading(dispatch, VALIDATE_GROUP_BOOKING_QUOTE_ACTION);
      startLoading(dispatch, `${VALIDATE_GROUP_BOOKING_QUOTE_ACTION}_${groupBookingId}`);

      return http
        .post(`/api/group-bookings/${groupBookingId}/quote/validate`, remainingParams)
        .finally(() => {
          endLoading(dispatch, VALIDATE_GROUP_BOOKING_QUOTE_ACTION);
          endLoading(dispatch, `${VALIDATE_GROUP_BOOKING_QUOTE_ACTION}_${groupBookingId}`);
        });
    },
    [PAY_GROUP_BOOKING_ACTION]({ dispatch }, groupBookingId) {
      startLoading(dispatch, PAY_GROUP_BOOKING_ACTION);
      startLoading(dispatch, `${PAY_GROUP_BOOKING_ACTION}_${groupBookingId}`);

      return http
        .post(`/api/group-bookings/${groupBookingId}/pay`)
        .then((response) => {
          const { publicKey, sessionId } = response.data.data;
          return loadStripe(publicKey)
            .then((stripe) => {
              stripe.redirectToCheckout({
                sessionId,
              });
            })
            .finally(() => {
              endLoading(dispatch, PAY_GROUP_BOOKING_ACTION);
              endLoading(dispatch, `${PAY_GROUP_BOOKING_ACTION}_${groupBookingId}`);
            });
        })
        .catch((error) => {
          endLoading(dispatch, PAY_GROUP_BOOKING_ACTION);
          endLoading(dispatch, `${PAY_GROUP_BOOKING_ACTION}_${groupBookingId}`);
          throw error;
        });
    },
  },

};
