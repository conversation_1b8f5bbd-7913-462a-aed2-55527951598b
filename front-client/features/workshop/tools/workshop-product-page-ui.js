import WorkshopChooseDateAction from '@/features/workshop/components/actions/WorkshopChooseDateAction.vue';
import WorkshopGiftAction from '@/features/workshop/components/actions/WorkshopGiftAction.vue';
import WorkshopPrivatizeAction from '@/features/workshop/components/actions/WorkshopPrivatizeAction.vue';
import WorkshopNotifyNewDatesAction from '@/features/workshop/components/actions/WorkshopNotifyNewDatesAction.vue';

import {
  canChooseDate,
  canBeOffered,
  canBeNotify,
  canBePrivatize,
  canOnlyBePrivatize,
} from '@/features/workshop/tools/workshop-states';

/**
 * Retourne toutes les actions possibles pour un atelier donnée
 * @return {Array}
 */
export const getWorkshopsAvailableActions = (workshop, availability) => {
  const actions = [];

  if (canChooseDate(workshop, availability)) {
    actions.push({
      group: 'primary',
      component: WorkshopChooseDateAction,
    });
  }

  if (canBeOffered(workshop)) {
    actions.push({
      group: 'primary',
      component: WorkshopGiftAction,
    });
  }

  if (canBeNotify(workshop, availability)) {
    actions.push({
      group: 'primary',
      component: WorkshopNotifyNewDatesAction,
    });
  }

  if (canBePrivatize(workshop)) {
    actions.push({
      group: canOnlyBePrivatize(workshop) ? 'primary' : 'secondary',
      component: WorkshopPrivatizeAction,
    });
  }

  return actions;
};
