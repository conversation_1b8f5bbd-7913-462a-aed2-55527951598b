import i18n from '@/translations/i18n';
import { formatPrice } from '@/tools/formating';
import { getCouponMonetaryValue, isCouponValid } from '@/features/cart/tools/coupons';

export const getWorkshopPricesDetails = (workshop = {}, coupon = {}) => {
  /**
   * If we get the workshop data from Algolia, we transform it and we receive the raw price in the key "value"
   * but if we get the data from our API, we receive the price in the key "prix".
   * so... ¯\_(ツ)_/¯
   */
  const value = workshop?.value || workshop?.price.allTaxIncluded || 0;
  const currency = workshop?.currency || workshop?.price.currency || 'EUR';

  const values = {
    currency,
    value,
    withDiscount: value,
    remainingDiscount: 0,
    couponValue: 0,
  };

  if (!coupon || !isCouponValid(coupon) || coupon.data.currency !== currency) {
    return values;
  }

  const couponValue = getCouponMonetaryValue(coupon);
  if (!couponValue) {
    return values;
  }

  const diffValue = value - couponValue;
  const absDiffValue = Math.abs(diffValue);
  const withDiscount = diffValue <= 0 ? 0 : absDiffValue;

  values.couponValue = couponValue;
  values.withDiscount = withDiscount;
  values.remainingDiscount = absDiffValue;

  return values;
};

export const getWorkshopDiscountTagFromPrices = (prices = {}) => {
  const {
    currency,
    couponValue,
    withDiscount,
    remainingDiscount,
  } = prices;

  if (Number.isNaN(couponValue) || !couponValue) return null;
  if (Number.isNaN(withDiscount)) return null;
  if (Number.isNaN(remainingDiscount)) return null;

  if (withDiscount > 0) {
    return {
      label: i18n.t('cards.tags.gift-diff-price.upper.label'),
      theme: 'info',
    };
  }

  if (remainingDiscount > 0) {
    return {
      label: i18n.t('cards.tags.gift-diff-price.lower.label', {
        value: formatPrice(remainingDiscount, currency),
      }),
      theme: 'success',
    };
  }

  if (remainingDiscount === 0) {
    return {
      label: i18n.t('cards.tags.gift-diff-price.equal.label'),
      theme: 'success',
    };
  }

  return null;
};
