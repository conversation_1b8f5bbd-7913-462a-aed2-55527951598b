<template>
  <div class="tw-mb-8">
    <div class="tw-mb-4">
      <div class="tw-grid tw-gap-4">
        <w-validator
          v-for="field in getFieldsConfig"
          :key="field.name"
          :name="field.name"
          :value="field.value"
        >
          <w-input-stepper
            class="md:tw-max-w-xs"
            :value="field.value"
            :min="1"
            size="medium"
            @input="(e) => field.onInput(e)"
          >
            <template #label>
              {{ field.label }}
            </template>
          </w-input-stepper>
        </w-validator>
      </div>

      <w-collapsible :opened="hasAlertRedirectToSearchPage">
        <div
          v-if="hasAlertRedirectToSearchPage"
          class="tw-pt-2"
        >
          <w-alert type="info">
            <w-link :to="getSearchPageRedirect">
              {{ $t('workshop.group-booking.form.participants-number.errors.too-much', {
                participantsNumber: getTotalParticipants,
                maxParticipants: maxParticipantsAvailable,
              }) }}
            </w-link>
          </w-alert>
        </div>
      </w-collapsible>
    </div>
  </div>
</template>

<script>
import {
  WValidator,
  WInputStepper,
  WCollapsible,
  WAlert,
  WLink,
} from '@wecandoo/ui-kit';

import { groupSize as GroupSize } from '@/features/algolia-search/filters/configurations';

export default {

  components: {
    WValidator,
    WInputStepper,
    WCollapsible,
    WAlert,
    WLink,
  },

  props: {
    adultNumber: {
      type: Number,
      required: false,
      default: 0,
    },
    childNumber: {
      type: Number,
      required: false,
      default: 0,
    },
    isForAdults: {
      type: Boolean,
      required: false,
      default: false,
    },
    isForKids: {
      type: Boolean,
      required: false,
      default: false,
    },
    isForDuo: {
      type: Boolean,
      required: false,
      default: false,
    },
    maxParticipantsAvailable: {
      type: Number,
      required: false,
      default: 10,
    },
    isCompany: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  computed: {
    getFieldsConfig() {
      const fields = [];

      if (this.isForDuo) {
        fields.push({
          name: 'duoNumber',
          value: Math.round(this.getTotalParticipants / 2),
          onInput: (value) => {
            if (!this.isForKids) {
              this.updateFieldValue('adultNumber', value * 2);
              return;
            }
            this.updateFieldValue('adultNumber', value);
            this.updateFieldValue('childNumber', value);
          },
          label: this.$t(`workshop.group-booking.form.participants-number.labels.${this.isForKids ? 'duo-parent-kid' : 'duo'}`),
        });
        return fields;
      }

      if (this.isForAdults) {
        fields.push({
          name: 'adultNumber',
          value: this.adultNumber,
          onInput: (value) => {
            this.updateFieldValue('adultNumber', value);
          },
          label: this.$t(`workshop.group-booking.form.participants-number.labels.${this.isForKids ? 'adult' : 'default'}`),
        });
      }

      if (this.isForKids) {
        fields.push({
          name: 'childNumber',
          value: this.childNumber,
          onInput: (value) => {
            this.updateFieldValue('childNumber', value);
          },
          label: this.$t(`workshop.group-booking.form.participants-number.labels.${this.isForAdults ? 'kid' : 'default'}`),
        });
      }

      return fields;
    },
    getTotalParticipants() {
      return this.adultNumber + this.childNumber;
    },
    hasAlertRedirectToSearchPage() {
      return this.getTotalParticipants > this.maxParticipantsAvailable;
    },
    getSearchPageRedirect() {
      const query = {
        [GroupSize.routeQueryParamName]: this.getTotalParticipants,
      };
      if (this.isCompany) {
        return {
          name: 'search-companies',
          query,
        };
      }
      return {
        name: 'search-groups',
        query,
      };
    },
  },

  methods: {
    updateFieldValue(key, value) {
      this.$emit('input', { key, value });
    },
  },

};
</script>
