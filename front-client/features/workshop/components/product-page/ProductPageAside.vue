<template>
  <div
    id="product-page-aside"
    class="tw-mb-10 md:tw-mb-0"
  >
    <div
      class="
        tw-mb-4 last:tw-mb-0
        tw-p-5 md:tw-p-6 tw-rounded-xl
        tw-bg-white tw-border-default
      "
    >
      <div class="tw-flex tw-items-center tw-justify-between">
        <product-page-price
          :workshop="workshop"
          :company-mode="companyMode"
        />
        <wi-loading
          v-if="isAvailabilityLoading"
          class="tw-text-4xl tw-text-primary"
        />
      </div>

      <product-page-availability-message
        class="tw-mt-4 md:tw-mt-6"
        :workshop="workshop"
        :availability="availability"
      />

      <w-grid
        v-if="!isAvailabilityLoading && getPrimaryActions.length"
        class="tw-mt-4 md:tw-mt-6"
        columns="1"
        gap="condensed"
      >
        <template v-for="(item, index) in getPrimaryActions">
          <component
            :is="item.component"
            :key="`action-${index}`"
            v-bind="$props"
            context="aside"
          />
          <product-page-aside-btn-divider
            v-if="index === 0 && getPrimaryActions.length > 1"
            :key="`divider-${index}`"
          />
        </template>
      </w-grid>
    </div>

    <div
      v-if="!isAvailabilityLoading && getSecondaryActions.length"
      class="
      tw-mb-4 last:tw-mb-0
      tw-p-5 md:tw-p-6 tw-rounded-xl tw-bg-white tw-border-default
      "
    >
      <template v-for="(item, index) in getSecondaryActions">
        <component
          :is="item.component"
          :key="index"
          v-bind="$props"
          context="aside"
        />
      </template>
    </div>

    <div v-if="showAvailablePaymentMethods">
      <w-typo class="tw-text-center tw-font-bold tw-mb-2">
        {{ $t('payment-methods.title') }}
      </w-typo>
      <available-payment-methods-list
        :available-payment-methods="availablePaymentMethods"
      />
    </div>
  </div>
</template>

<script>
import { WGrid, WTypo, WiLoading } from '@wecandoo/ui-kit';

import { FETCH_WORKSHOP_AVAILABILITY_BY_ID_ACTION } from '@/features/workshop/store/workshop-actions-types';

import AvailablePaymentMethodsList from '@/components/lists/AvailablePaymentMethodsList.vue';

import { canPayByBankTransfer } from '@/tools/payment-methods';

import { isGift } from '@/features/workshop/tools/workshop-states';
import { getWorkshopsAvailableActions } from '@/features/workshop/tools/workshop-product-page-ui';

import ProductPagePrice from '@/features/workshop/components/product-page/ProductPagePrice.vue';
import ProductPageAvailabilityMessage from '@/features/workshop/components/product-page/ProductPageAvailabilityMessage.vue';
import ProductPageAsideBtnDivider from '@/features/workshop/components/product-page/ProductPageAsideBtnDivider.vue';

export default {

  components: {
    WGrid,
    WTypo,
    WiLoading,
    ProductPagePrice,
    ProductPageAvailabilityMessage,
    ProductPageAsideBtnDivider,
    AvailablePaymentMethodsList,
  },

  props: {
    workshop: {
      type: Object,
      required: true,
    },
    availability: {
      type: Object,
      required: false,
      default: null,
    },
    availablePaymentMethods: {
      type: Array,
      required: false,
      default: () => ([]),
    },
    companyMode: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  computed: {
    isAvailabilityLoading() {
      return this.$wait.is(`${FETCH_WORKSHOP_AVAILABILITY_BY_ID_ACTION}_${this.workshop.id}`);
    },
    getActions() {
      return getWorkshopsAvailableActions(this.workshop, this.availability);
    },
    getPrimaryActions() {
      return this.getActions.filter((e) => e.group === 'primary');
    },
    getSecondaryActions() {
      return this.getActions.filter((e) => e.group === 'secondary');
    },
    showAvailablePaymentMethods() {
      if (isGift(this.workshop)) {
        return false;
      }

      return this.getActions.length === 1 && canPayByBankTransfer(this.availablePaymentMethods);
    },
  },

};
</script>
