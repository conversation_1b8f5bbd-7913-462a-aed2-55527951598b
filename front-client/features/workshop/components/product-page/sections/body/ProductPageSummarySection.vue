<template>
  <div
    class="
      md:tw-border-b tw-border-solid tw-border-secondary-600
      md:tw-pb-12 tw-mb-10 md:tw-mb-12
    "
  >
    <template v-if="$wMq.is.md">
      <w-typo
        class="tw-mb-4"
        type="h6 md:h5"
        tag="p"
      >
        {{ $t('product-page.sections.summary.title') }}
      </w-typo>
      <w-avatar
        class="tw-float-right tw-ml-8"
        size="medium"
        :image="workshop.artisan.photo_profil"
        :name="`${workshop.artisan.prenom} ${workshop.artisan.nom}`"
      />
      <w-typo
        class="tw-mb-4 last:tw-mb-0 tw-text-gray-700"
        tag="h2"
      >
        {{ workshop.sous_titre }}
      </w-typo>
    </template>
    <div>
      <w-typo
        v-for="(item, i) in workshop.pedagogie_prix"
        :key="i"
        tag="h3"
        class="tw-font-bold tw-flex tw-items-center tw-mb-3 last:tw-mb-0"
      >
        <wi-wavy-check class="tw-text-xl tw-mr-2" />
        {{ item }}
      </w-typo>
    </div>
  </div>
</template>

<script>
import { WAvatar, WTypo, WiWavyCheck } from '@wecandoo/ui-kit';

export default {

  components: {
    WAvatar,
    WTypo,
    WiWavyCheck,
  },

  props: {
    workshop: {
      type: Object,
      required: true,
    },
  },

};
</script>
