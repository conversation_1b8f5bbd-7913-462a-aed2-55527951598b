<template>
  <w-modal :title="$t('modals.share-workshop.title')">
    <div class="tw-mb-6">
      <w-typo
        class="tw-text-gray-700"
        type="caption"
      >
        {{ $t('modals.share-workshop.intro', {
          friendDiscount: getFriendDiscount,
          userDiscount: getUserDiscount
        }) }}
      </w-typo>
    </div>

    <div
      v-for="(section, index) in sections"
      :key="index"
      :class="index !== sections.length - 1 ? 'tw-mb-6' : ''"
    >
      <w-typo
        class="tw-mb-2"
        type="body"
      >
        <b>{{ section.title }}</b>
      </w-typo>
      <w-input
        :key="section.key"
        :ref="section.name"
        :name="section.name"
        class="tw-w-full"
        :value="section.value"
        size="medium"
        @input="section.key += 1"
      >
        <template #actions>
          <w-cta
            size="small"
            :disabled="!canCopy"
            icon-position="left"
            @click="copy(section)"
          >
            <template #icon>
              <wi-copy />
            </template>
            {{ section.btn }}
          </w-cta>
        </template>
      </w-input>
    </div>

    <w-grid
      v-if="getLink"
      class="tw-mt-4"
      columns="auto"
      gap="none"
      justify="center"
    >
      <share-network
        v-for="(social, i) in socialNetworks"
        :key="i"
        class="tw-block tw-no-underline"
        :network="social.name"
        :url="getLink"
        :title="getMetaTitle"
        :description="getMetaDescription"
        @click.native="trackShare(social)"
      >
        <w-cta
          text
          label-position="bottom"
          theme="neutral"
          size="small"
          icon-position="left"
        >
          <template #icon>
            <component :is="social.icon" />
          </template>
          {{ social.label }}
        </w-cta>
      </share-network>
    </w-grid>
  </w-modal>
</template>

<script>
import { mapGetters } from 'vuex';

import {
  WModal,
  WCta,
  WTypo,
  WInput,
  WGrid,
  WiColorFacebook,
  WiColorWhatsApp,
  WiColorTwitter,
  WiMail,
  WiCopy,
} from '@wecandoo/ui-kit';

import { ShareNetwork } from 'vue-social-sharing';

import { formatPercent, formatPrice } from '@/tools/formating';
import { config } from '@/tools/config';

export default {

  components: {
    WModal,
    WCta,
    WTypo,
    WInput,
    WGrid,
    ShareNetwork,
    WiCopy,
  },

  props: {
    workshop: {
      type: Object,
      required: true,
    },
  },

  data: () => ({
    sections: [],
    socialNetworks: [
      {
        name: 'facebook',
        label: 'Facebook',
        icon: WiColorFacebook,
      },
      {
        name: 'whatsapp',
        label: 'WhatsApp',
        icon: WiColorWhatsApp,
      },
      {
        name: 'twitter',
        label: 'Twitter',
        icon: WiColorTwitter,
      },
      {
        name: 'email',
        label: 'Email',
        icon: WiMail,
      },
    ],
  }),

  computed: {
    ...mapGetters('user', [
      'isAuth',
      'getUser',
    ]),
    getLink() {
      const { fullPath } = this.$router.resolve({
        name: 'product',
        params: {
          slug: this.workshop.permalink,
        },
      }).resolved;
      const origin = typeof document === 'object' ? document.location.origin : '';
      return `${origin}${fullPath}`;
    },
    getMetaTitle() {
      if (this.isAuth) {
        return this.$t('modals.share-workshop.meta-title', {
          code: this.getUserSponsorshipCode,
          friendDiscount: this.getFriendDiscount,
          workshopName: this.workshop.nom,
        });
      }
      return this.workshop.nom;
    },
    getMetaDescription() {
      return typeof document === 'object' ? document.querySelector('meta[name="description"]')?.content : '';
    },
    getFriendDiscount() {
      const invitation = this.getUser?.sponsorship?.invitation;
      return invitation
        ? formatPercent(invitation.amount)
        : formatPercent(config('sponsorship.invited.invitation_percentage', 10));
    },
    getUserDiscount() {
      const user = this.getUser?.sponsorship?.user;
      return user
        ? formatPrice(user.bonus_amount, user.currency)
        : formatPrice(config('sponsorship.user.bonus_amount', 5));
    },
    getUserSponsorshipCode() {
      return this.getUser?.sponsorship?.invitation?.code;
    },
    getSectionDynamicContent() {
      return [
        {
          title: `
            ${this.$t('modals.share-workshop.sections.sponsorship-code.title')}
            : -${this.getFriendDiscount}
          `,
          name: 'code',
          value: this.getUserSponsorshipCode,
        },
        {
          title: this.$t('modals.share-workshop.sections.link.title'),
          name: 'link',
          value: this.getLink,
        },
      ];
    },
    canCopy() {
      return !!navigator?.clipboard;
    },
  },

  watch: {
    getSectionDynamicContent: {
      handler(sections) {
        this.sections = sections
          .map((section) => ({
            ...section,
            btn: this.$t('modals.share-workshop.clipboard.copy'),
            key: 0,
            timer: 0,
          }))
          .filter((e) => !!e.value);
      },
      immediate: true,
    },
  },

  methods: {
    /**
     * @param obj {object} - The object to copy in clipboard
     * @param obj.name {string} - Send to gtm, for tracking purpose
     * @param obj.value {string} - The value to copy
     * @param obj.btn {string} - The btn's label to update
     * @param obj.timer {number} - Timer use to update the btn's label and revert
     */
    copy(obj = {}) {
      clearTimeout(obj.timer);

      this.$gtmService.pushEvent('share-workshop-modal', {
        isConnected: this.isAuth,
        type: `copy - ${obj.name}`,
      });

      if (obj.value && this.canCopy) {
        navigator.clipboard.writeText(obj.value);

        /* eslint-disable no-param-reassign */
        obj.btn = this.$t('modals.share-workshop.clipboard.copied');
        obj.timer = setTimeout(() => {
          obj.btn = this.$t('modals.share-workshop.clipboard.copy');
        }, 1000);
        /* eslint-enable no-param-reassign */
      }
    },
    trackShare(e) {
      this.$gtmService.pushEvent('share-workshop-modal', {
        isConnected: this.isAuth,
        type: `share - ${e.name}`,
      });
    },
  },

};
</script>
