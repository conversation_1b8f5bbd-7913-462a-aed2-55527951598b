<template>
  <span>
    <slot :on-click="onClick">
      <w-cta
        :id="createClickId({
          theme: THEMES.workshop.share,
          action: ACTIONS.openModal,
        })"
        link
        theme="neutral"
        size="small"
        @click="onClick"
      >
        <template #icon>
          <wi-share />
        </template>

        {{ $t(`actions.workshop.share.${shorten ? 'short' : 'long'}`) }}
      </w-cta>
    </slot>
  </span>
</template>

<script>
import { WCta, WiShare } from '@wecandoo/ui-kit';

import WorkshopShareModal from '@/features/workshop/components/modals/WorkshopShareModal.vue';
import { THEMES, ACTIONS } from '@/constants/tracking';
import { createClickId } from '@/tools/tracking';

export default {

  components: {
    WCta,
    WiShare,
  },

  props: {
    workshop: {
      type: Object,
      required: true,
    },
    shorten: {
      type: Boolean,
      required: false,
      default: false,
    },
  },

  data: () => ({
    THEMES,
    ACTIONS,
  }),

  methods: {
    createClickId,
    onClick() {
      this.$wModal.show(WorkshopShareModal, {
        workshop: this.workshop,
      });
    },
  },

};
</script>
