<template>
  <div class="tw-bg-secondary-500">
    <w-hero
      :image="getImageUrlFromProdCdn('/front/page-presenter/manifest-right.jpg')"
      hide-image-on-mobile
    >
      <template #title>
        {{ $t('static-pages.manifest.hero.title') }}
      </template>
      <template #subtitle>
        {{ $t('static-pages.manifest.hero.subtitle') }}
      </template>
      <w-cta
        size="medium md:large"
        @click="scrollToFirstElement"
      >
        {{ $t('static-pages.manifest.hero.cta') }}
        <template #icon>
          <wi-arrow-circle-down />
        </template>
      </w-cta>
    </w-hero>

    <container-section
      id="manifest-section"
      class="tw-bg-white"
      size="large"
    >
      <editorial-card
        v-for="(item, i) in getFirstSection"
        :key="i"
        :item="item"
        :invert="i % 2 !== 0"
      />
    </container-section>

    <container-section
      class="tw-bg-primary tw-text-white tw-text-center"
      size="large"
    >
      <subtitle-section>
        {{ $t('static-pages.manifest.quote.subtitle') }}
      </subtitle-section>
      <title-section>
        <span v-html="$t('static-pages.manifest.quote.title')" />
      </title-section>
    </container-section>

    <container-section
      class="tw-bg-white"
      size="large"
    >
      <editorial-card
        v-for="(item, i) in getLastSection"
        :key="i"
        :item="item"
        :invert="i % 2 !== 0"
      />
    </container-section>
  </div>
</template>

<script>
import { WHero, WCta, WiArrowCircleDown } from '@wecandoo/ui-kit';

import EditorialCard from '@/components/cards/EditorialCard.vue';
import ContainerSection from '@/components/sections/_components/ContainerSection.vue';
import TitleSection from '@/components/sections/_components/TitleSection.vue';
import SubtitleSection from '@/components/sections/_components/SubtitleSection.vue';

import { getImageUrlFromProdCdn } from '@/tools/images';
import { scrollToElementID } from '@/tools/dom';
import { slugify } from '@/tools/formating';

export default {

  components: {
    WHero,
    WCta,
    WiArrowCircleDown,
    EditorialCard,
    ContainerSection,
    TitleSection,
    SubtitleSection,
  },

  computed: {
    getHydratedItems() {
      return this.$t('static-pages.manifest.cards').map((e, i) => ({
        ...e,
        imageUrl: getImageUrlFromProdCdn(`/front/manifest/manifest-${i + 1}.jpg`),
      }));
    },
    getHalfCardIndex() {
      return Math.ceil(this.getHydratedItems.length / 2);
    },
    getFirstSection() {
      return this.getHydratedItems.slice(0, this.getHalfCardIndex);
    },
    getLastSection() {
      return this.getHydratedItems.slice(this.getHalfCardIndex, this.getHydratedItems.length);
    },
  },

  methods: {
    getImageUrlFromProdCdn,
    scrollToFirstElement() {
      const ID = slugify(this.getHydratedItems?.[0]?.subtitle);
      if (ID) scrollToElementID(`${ID}-content`);
    },
  },

};
</script>
