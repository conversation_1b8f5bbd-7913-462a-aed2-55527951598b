import i18n from '@/translations/i18n';
import { getImageUrlFromProdCdn } from '@/tools/images';

import { GB, BE } from '@/constants/countries';
import { config } from '@/tools/config';

import {
  START,
  NAME,
  AGE,
  LOCATION,
  PREFERENCE,
  PRICE,
  SEATS,
} from '@/features/workshop-finder/constants/questions';

import HandleQuestionsFiltersGuard from '@/features/workshop-finder/router/guards/handler-questions-filters-guard';

export default [
  {
    path: 'workshop-finder',
    name: 'workshop-finder',
    component: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/pages/WorkshopFinderPage.vue'),
    meta: {
      head: {
        title: i18n.t('workshop-finder.questions.start.title'),
        description: i18n.t('workshop-finder.questions.start.description'),
      },
    },
    children: [
      {
        path: '',
        name: 'workshop-finder-questions',
        component: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/pages/WorkshopFinderQuestionsPage.vue'),
        meta: {
          spaReady: true,
          test: true,
        },
        children: [
          {
            path: '',
            name: START,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
            },
            meta: {
              spaReady: true,
              body: {
                fullwidth: false,
              },
              aside: {
                fullwidth: false,
              },
              background: {
                image: getImageUrlFromProdCdn('/front/workshop-finder/workshop-finder-background-02.jpg'),
              },
            },
            props: {
              header: {
                illustration: getImageUrlFromProdCdn('/front/icons/crop/gift-pink-and-red.png'),
                title: i18n.t('workshop-finder.questions.start.title'),
                description: i18n.t('workshop-finder.questions.start.description'),
              },
              navigation: {
                nextQuestionLabel: i18n.t('workshop-finder.questions.start.next-question'),
              },
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'name',
            alias: 'q1',
            name: NAME,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/NameQuestion.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
            },
            meta: {
              spaReady: true,
              body: {
                fullwidth: false,
              },
              aside: {
                fullwidth: false,
              },
              background: {
                image: getImageUrlFromProdCdn('/front/workshop-finder/workshop-finder-background-02.jpg'),
              },
            },
            props: {
              header: {
                illustration: getImageUrlFromProdCdn('/front/icons/crop/gift-pink-and-red.png'),
                title: i18n.t('workshop-finder.questions.name.title'),
              },
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'age',
            alias: 'q2',
            name: AGE,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/AgeQuestion.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
            },
            meta: {
              spaReady: true,
              background: {
                image: getImageUrlFromProdCdn('/front/workshop-finder/workshop-finder-background-02.jpg'),
              },
            },
            props: {
              header: (route) => ({
                illustration: getImageUrlFromProdCdn('/front/icons/crop/user-pink-red.png'),
                title: i18n.t('workshop-finder.questions.age.title', { name: route.query.name }),
              }),
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'location',
            alias: 'q3',
            name: LOCATION,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => {
                if ([GB, BE].includes(config('country'))) {
                  return import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/CityQuestion.vue');
                }
                return import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/LocationQuestion.vue');
              },
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
            },
            meta: {
              spaReady: true,
              background: {
                image: getImageUrlFromProdCdn('/front/workshop-finder/workshop-finder-background-02.jpg'),
              },
            },
            props: {
              header: (route) => ({
                illustration: getImageUrlFromProdCdn('/front/icons/crop/pin-location-wecandoo-blue.png'),
                title: i18n.t('workshop-finder.questions.location.title', { name: route.query.name }),
              }),
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'preference',
            alias: 'q4',
            name: PREFERENCE,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/PreferenceQuestion.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
              aside: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/results/WorkshopsResultsPreview.vue'),
            },
            meta: {
              spaReady: true,
            },
            props: {
              header: (route) => ({
                illustration: getImageUrlFromProdCdn('/front/icons/crop/heart-red-02.png'),
                title: i18n.t('workshop-finder.questions.preference.title', { name: route.query.name }),
              }),
              navigation: {
                preview: true,
              },
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'pricing',
            alias: 'q5',
            name: PRICE,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/PriceQuestion.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
              aside: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/results/WorkshopsResultsPreview.vue'),
            },
            meta: {
              spaReady: true,
            },
            props: {
              header: (route) => ({
                illustration: getImageUrlFromProdCdn('/front/icons/crop/piggy-bank-pink.png'),
                title: i18n.t('workshop-finder.questions.price.title', { name: route.query.name }),
              }),
              navigation: {
                preview: true,
              },
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
          {
            path: 'seats',
            alias: 'q6',
            name: SEATS,
            components: {
              header: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsHeader.vue'),
              question: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/SeatsQuestion.vue'),
              navigation: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/questions/QuestionsNavigation.vue'),
              aside: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/components/results/WorkshopsResultsPreview.vue'),
            },
            meta: {
              spaReady: true,
            },
            props: {
              header: (route) => ({
                illustration: getImageUrlFromProdCdn('/front/icons/crop/heart-red-02.png'),
                title: i18n.t('workshop-finder.questions.seats.title', { name: route.query.name }),
              }),
              navigation: {
                preview: true,
                nextQuestionLabel: i18n.t('workshop-finder.navigation.see-results'),
              },
            },
            beforeEnter: HandleQuestionsFiltersGuard,
          },
        ],
      },
      {
        path: 'results',
        name: 'workshop-finder-results',
        component: () => import(/* webpackChunkName: "workshop-finder" */ '@/features/workshop-finder/pages/WorkshopFinderResultsPage.vue'),
        meta: {
          spaReady: true,
          aside: {
            fullwidth: true,
          },
        },
      },
    ],
  },
];
