import {
  AGE,
  LOCATION,
  NAME,
  PREFERENCE,
  PRICE,
  SEATS,
  START,
} from '@/features/workshop-finder/constants/questions';

import { NL } from '@/constants/countries';
import { config } from '@/tools/config';

import {
  age as AgeFilter,
  crafts as PreferenceFilter,
  places as SeatsFilter,
  prices as PriceFilter,
} from '@/features/algolia-search/filters';

import PlaceIdPreFilter from '@/features/algolia-search/pre-filters/PlaceId';
import UserLocationPreFilter from '@/features/algolia-search/pre-filters/UserLocation';

export const questionsConfig = {
  [START]: {
    queries: [],
  },
  [NAME]: {
    queries: ['name'],
  },
  [AGE]: {
    queries: AgeFilter.getAllRouteParamName(),
  },
  [LOCATION]: {
    queries: [
      PlaceIdPreFilter.attribute,
      UserLocationPreFilter.attribute,
    ],
  },
  [PREFERENCE]: {
    queries: PreferenceFilter.getAllRouteParamName(),
  },
  [PRICE]: {
    queries: PriceFilter.getAllRouteParamName(),
    optional: true,
  },
  [SEATS]: {
    queries: SeatsFilter.getAllRouteParamName(),
    optional: true,
  },
};

/**
 * @param {string} name
 */
export const getQuestionConfig = (name) => {
  if (name in questionsConfig) {
    return questionsConfig[name];
  }

  console.error(`Couldn't find question config for [${name}].`);
  return [];
};

export const getAllAvailableParams = () => Object.values(questionsConfig).reduce((acc, item) => acc.concat(item.queries), []);

export const getOrderedQuestions = () => ([
  START,
  NAME,
  AGE,
  ...config('country') !== NL ? [LOCATION] : [],
  PREFERENCE,
  PRICE,
  SEATS,
]);
