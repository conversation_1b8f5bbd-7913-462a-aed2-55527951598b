<template>
  <w-modal
    :title="review.author"
    size="small"
    :disable-mobile-offset="true"
  >
    <div class="tw-flex tw-flex-col tw-gap-5">
      <w-typo type="body">
        {{ review.comment }}
      </w-typo>
      <w-typo
        type="footnote"
        class="tw-text-gray-600"
      >
        {{ $t('gift-landing.users-reviews.source-language') }}
      </w-typo>
    </div>

    <template #footer>
      <gift-star-rating
        :rating="review.rating"
      />
    </template>
  </w-modal>
</template>

<script>
import { WModal, WTypo } from '@wecandoo/ui-kit';
import GiftStarRating from '@/features/gift/landing/components/GiftStarRating.vue';

export default {

  components: {
    WModal,
    WTypo,
    GiftStarRating,
  },

  props: {
    review: {
      type: Object,
      required: true,
    },
  },

};
</script>
