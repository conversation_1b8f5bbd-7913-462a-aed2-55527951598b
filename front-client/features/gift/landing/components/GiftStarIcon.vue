<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="16"
    :fill="filled ? filledColor : unfilledColor"
    viewBox="0 0 18 16"
  >
    <path
      v-if="halfFilled"
      fill="url(#half-star)"
      d="M8.568.742a.5.5 0 0 1 .864 0L11.8 4.805a.5.5 0 0 0 .326.237l4.596.995a.5.5 0 0 1 .267.822l-3.133 3.506a.5.5 0 0 0-.125.384l.474 4.678a.5.5 0 0 1-.7.508l-4.302-1.896a.5.5 0 0 0-.404 0l-4.303 1.896a.5.5 0 0 1-.699-.508l.474-4.678a.5.5 0 0 0-.125-.384L1.012 6.86a.5.5 0 0 1 .267-.822l4.596-.995a.5.5 0 0 0 .326-.237L8.568.742Z"
    />
    <path
      v-else
      :fill="filled ? filledColor : unfilledColor"
      d="M8.568.742a.5.5 0 0 1 .864 0L11.8 4.805a.5.5 0 0 0 .326.237l4.596.995a.5.5 0 0 1 .267.822l-3.133 3.506a.5.5 0 0 0-.125.384l.474 4.678a.5.5 0 0 1-.7.508l-4.302-1.896a.5.5 0 0 0-.404 0l-4.303 1.896a.5.5 0 0 1-.699-.508l.474-4.678a.5.5 0 0 0-.125-.384L1.012 6.86a.5.5 0 0 1 .267-.822l4.596-.995a.5.5 0 0 0 .326-.237L8.568.742Z"
    />
    <defs>
      <linearGradient
        id="half-star"
        x1="0"
        x2="100%"
        y1="0"
        y2="0"
      >
        <stop
          offset="50%"
          :stop-color="filledColor"
        />
        <stop
          offset="50%"
          :stop-color="unfilledColor"
        />
      </linearGradient>
    </defs>
  </svg>
</template>

<script>
export default {

  props: {
    filled: {
      type: Boolean,
      default: true,
    },
    halfFilled: {
      type: Boolean,
      default: false,
    },
  },

  /**
   * #Todo: dynamic colors
   */
  data: () => ({
    filledColor: '#FFCC4E',
    unfilledColor: '#EEEEEE',
  }),

};
</script>
