$breakpoints: (
  "2xs": 340px,
  "xs":  500px,
  "sm":  700px, // -> md
  "md":  1000px, // -> lg
  "lg":  1200px, // -> xl
  "xl":  1400px,
  "2xl": 1600px,
  "3xl": 1800px,
  "4xl": 2000px,
  "5xl": 2400px
);

@function mq($breakpoint, $type: "min") {
  @if not map-has-key($breakpoints, $breakpoint) {
    @warn "Unknown media query breakpoint: `#{$breakpoint}`";
  }

  $value: map-get($breakpoints, $breakpoint);

  @if ($type == "min") {
    @return "(min-width: #{$value})";
  }
  @if ($type == "max") {
    @return "(max-width: #{$value - 1px})";
  }

  @error "Unknown media query type: #{$type}";
}

@function mq-min($breakpoint) {
  @return mq($breakpoint, "min");
}

@function mq-max($breakpoint) {
  @return mq($breakpoint, "max");
}

@function mq-between($breakpointMin, $breakpointMax) {
 @return "#{mq-min($breakpointMin)} and #{mq-max($breakpointMax)}";
}