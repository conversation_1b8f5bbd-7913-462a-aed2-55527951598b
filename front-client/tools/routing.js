/**
 * Determines if a given route configuration can be previewed.
 *
 * If so, it generates an additional preview route with
 * - `/preview` suffix in its path
 * - `-preview` suffix in its name.
 *
 * @param {Object} [routeConfig={}] - The route configuration object to evaluate.
 * @returns {Object[]} An array containing the original route configuration and, if possible,
 * an additional route configuration for the preview route is included.
 *
 * @example
 * generateWithPreview({
 *   path: '/example',
 *   name: 'example',
 *   meta: { spaReady: true },
 * });
 * // Returns:
 * // [
 * //   {
 * //     path: '/example',
 * //     name: 'example',
 * //     meta: { spaReady: true }
 * //   },
 * //   {
 * //     path: '/example/preview',
 * //     name: 'example-preview',
 * //     meta: { spaReady: true, preview: true },
 * //   }
 * // ]
 */
export function generateWithPreview(routeConfig = {}) {
  if (!routeConfig || typeof routeConfig !== 'object') {
    return [];
  }

  if (!routeConfig.name || !routeConfig.path) {
    return [
      routeConfig,
    ];
  }

  return [
    routeConfig,
    {
      ...routeConfig,
      path: `${routeConfig.path}/preview`,
      name: `${routeConfig.name}-preview`,
      meta: {
        ...routeConfig.meta || {},
        preview: true,
      },
    },
  ];
}

/**
 * Check if the given route object is actually previewed
 * @param {Object} route - A vue router route object
 * @return {Boolean}
 */
export function isPreviewed(route) {
  return !!route?.meta?.preview;
}
