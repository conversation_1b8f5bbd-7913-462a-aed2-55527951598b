import VueRouter from 'vue-router';

/**
 * Hard refresh guard
 */
export default (to, from, next) => {
  /**
   * First navigation,
   * We obsviously don't want an hard refesh (infinite loop...)
   */
  if (from === VueRouter.START_LOCATION) {
    next();
    return;
  }

  /**
   * If the current page (from) & the target page (to)
   * are "SPA Ready" -> they don't need hard browser refresh
   * between them. So we leave vue-router handle the navigation
   *
   * "That's one small step for us, one giant leap for Wecandoo".
   */
  if (to?.meta?.spaReady && from?.meta?.spaReady) {
    next();
    return;
  }

  /**
   * If the path, aka the "base URL" is the same before & after,
   * we assumed we only update some queries or params, and so we
   * don't want to hard refresh everything.
   */
  if (to.path === from.path) {
    next();
    return;
  }

  document.location.href = to.fullPath;
};
