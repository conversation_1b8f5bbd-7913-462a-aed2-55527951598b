export default {
  props: {
    errors: {
      required: false,
      default: null,
      type: Object,
    },
  },
  computed: {
    hasSessionErrors() {
      return Boolean(this.errors);
    },
  },
  methods: {
    attributeHasErrors(attribute) {
      const result = this.getAttributeErrors(attribute);

      if (Array.isArray(result)) {
        return this.getAttributeErrors(attribute).length > 0;
      }

      return result !== null;
    },
    getAttributeErrors(attribute) {
      const properties = attribute.split('.');
      if (this.errors) {
        let result = this.errors;
        // eslint-disable-next-line no-restricted-syntax
        for (const property of properties) {
          if (!result[property]) {
            return [];
          }
          result = result[property];
        }

        return result || [];
      }

      return [];
    },
  },
};
