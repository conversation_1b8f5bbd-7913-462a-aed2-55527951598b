.dropdown-toggle {
  &::after {
    display: inline-block;
    margin-left: $caret-spacing;
    vertical-align: middle;
    border: none;
    background-repeat: no-repeat;
  }

  &[aria-expanded="true"] {
    &::after {
      @extend %fa-icon;
      @extend .fas;

      content: fa-content($fa-var-chevron-up);
    }
  }

  &[aria-expanded="false"] {
    &::after {
      @extend %fa-icon;
      @extend .fas;

      content: fa-content($fa-var-chevron-down);
    }
  }
}

.show > .btn-secondary.dropdown-toggle {
  color: $primary;
}
