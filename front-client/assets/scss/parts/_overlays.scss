.transparent-hover-overlay {
  @extend .no-link;

  z-index: 3;
  position: relative;

  .hover-overlay {
    @extend .position-absolute;
    @extend .h-100;
    @extend .w-100;

    top: 0;
    left: 0;
    z-index: 3;
    cursor: pointer;

    &:hover {
      background-color: white;
      opacity: 0.3;
    }
  }

  .hover-image-overlay {
    @extend .position-absolute;
    @extend .h-100;
    @extend .w-100;

    top: 0;
    left: 0;
    z-index: 3;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;

    img {
      object-fit: cover;

      @extend .h-100;
      @extend .w-100;
    }
  }

  &:hover {
    .hover-image-overlay {
      visibility: visible !important;
      opacity: 1;
      transition: opacity 0.5s linear;
    }
  }
}
