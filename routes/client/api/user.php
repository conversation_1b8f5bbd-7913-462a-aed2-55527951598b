<?php

use App\Http\Controllers\Front\API\User\BillingAddressController;

Route::prefix('user')
    ->name('user.')
    ->middleware([
        'auth:customer',
    ])
    ->group(function (): void {
        Route::apiResource('billing-addresses', BillingAddressController::class)
            ->only([
                'index',
                'store',
                'update',
                'destroy',
            ]);
    });
