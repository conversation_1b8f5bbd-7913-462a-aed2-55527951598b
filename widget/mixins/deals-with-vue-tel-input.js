export default {
  data () {
    return {
      inputOptions_: {},
      telInputMode: 'international'
    }
  },
  computed: {
    telInputOptions () {
      return Object.assign({
        placeholder: this.$t('form.vueTelInput.placeholder'),
        validCharactersOnly: true
      }, this.inputOptions_)
    },
    invalidMsg () {
      return this.$t('form.vueTelInput.invalid')
    }
  },
  mounted () {
    if (process.client) {
      for (const elem of document.getElementsByClassName('vti__input')) {
        elem.setAttribute('inputmode', 'tel')
      }
    }
  }
}
