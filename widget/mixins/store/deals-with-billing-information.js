export default {
  computed: {
    billingInformation () {
      return this.$store.state.user.billingInformation.billingInformation
    },
    billingInformationErrors () {
      return this.$store.state.user.billingInformation.errors
    },
    billingInformationAttributesErrors () {
      return this.$store.state.user.billingInformation.attributesErrors
    },
    billingInformationLoading () {
      return this.$store.state.user.billingInformation.loading
    }
  },
  methods: {
    createBillingInformation (toCreate) {
      return this.$store.dispatch('user/billingInformation/create', toCreate)
    },
    updateBillingInformation (toUpdate) {
      return this.$store.dispatch('user/billingInformation/update', toUpdate)
    }
  }
}
