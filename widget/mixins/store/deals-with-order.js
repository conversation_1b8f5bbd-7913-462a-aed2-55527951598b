import DealsWithStripe from '@/mixins/payment-sources/deals-with-stripe'

export default {
  computed: {
    payment () {
      return this.$store.state.order.payment
    },
    orderErrors () {
      return this.$store.state.order.errors
    },
    orderAttributesErrors () {
      return this.$store.state.order.attributesErrors
    },
    orderLoading () {
      return this.$store.state.order.loading
    }
  },
  mixins: [
    DealsWithStripe
  ],
  methods: {
    payOrder (toCreate) {
      return this.$store.dispatch('order/pay', toCreate)
    },
    setPaymentPaid (value) {
      this.$store.commit('order/setIsPaid', value)
    },
    proceedPayment (payment, paymentWindow) {
      return new Promise((resolve, reject) => {
        switch (payment.service) {
          case ('stripe'):
            resolve(this.proceedStripePayment(payment.paymentSessionId, paymentWindow).then((result) => {
              if (result) {
                this.setPaymentPaid(true)
              }

              return result
            }))
            break
          default:
            reject(new Error(this.$t('form.payment.errors.paymentSources.unknown')))
            break
        }
      })
    }
  }
}
