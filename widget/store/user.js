import { createModule as CreateBillingInformationModule } from './billingInformation'
import {
  <PERSON>rror<PERSON><PERSON><PERSON>,
  getModulePath,
  buildResourceUrl,
  createGetters,
  createMutations,
  createState,
  createBaseModule
} from '@/helpers/store/building'

const moduleName = 'user'
const resourceBaseUrl = '/users'

export const state = () => {
  return createState({
    authToken: null,
    user: null
  }, moduleName, resourceBaseUrl)
}

export const getters = createGetters({})

export const mutations = createMutations({
  reset (moduleState) {
    this.commit(`${moduleState.modulePath}/resetErrors`)
    this.commit(`${moduleState.modulePath}/resetAttributesErrors`)
    this.commit(`${moduleState.modulePath}/resetResourceUrl`)
    moduleState.user = null
  },
  setAuthToken (moduleState, data) {
    moduleState.authToken = data
  },
  setUser (moduleState, data) {
    moduleState.user = data
  }
})

export const actions = {
  register (context, { firstname, lastname, email, password, passwordConfirm }) {
    context.commit('startLoading')
    context.commit('reset')
    const url = buildResourceUrl(context, 'register')
    return this.$axios
      .post(url, {
        prenom: firstname,
        nom: lastname,
        email,
        password,
        password_confirmation: passwordConfirm
      })
      .then((response) => {
        return response.data
      })
      .catch(ErrorHandler(context))
      .then((result) => {
        context.commit('stopLoading')
        return result
      })
  },
  login (context, { email, password }) {
    context.commit('startLoading')
    context.commit('reset')

    return this.$axios
      .post('/oauth/token', {
        grant_type: 'password',
        client_id: context.rootState.configuration?.oauthClient?.id,
        client_secret: context.rootState.configuration?.oauthClient?.secret,
        username: email,
        password
      })
      .then((response) => {
        context.commit('setAuthToken', response.data.access_token)
        // TODO Save the refresh token and use it if necessary, but the current token's TTL is 1 year...

        return response.data
      })
      .finally(() => {
        context.commit('stopLoading')
      })
  },
  fetch (context) {
    context.commit('startLoading')
    context.commit('reset')
    const url = buildResourceUrl(context)
    return this.$axios
      .get(url)
      .then((response) => {
        context.commit('setUser', response.data.data)
        return response.data.data
      })
      .catch(e => Promise.reject(e?.response?.data?.message || e?.message || e))
      .finally(() => {
        context.commit('stopLoading')
      })
  }
}

export const modules = {
  billingInformation: CreateBillingInformationModule(moduleName)
}

export const namespaced = true

export const createModule = function (parentModulePath, customResourceBaseUrl = null, customModuleName = null) {
  const modulePath = getModulePath(parentModulePath, (customModuleName || moduleName))
  const module = createBaseModule(
    parentModulePath,
    customResourceBaseUrl,
    customModuleName,
    modulePath,
    {
      state,
      getters,
      mutations,
      actions,
      namespaced
    })

  module.modules = {
    billingInformation: CreateBillingInformationModule(modulePath)
  }

  return module
}
