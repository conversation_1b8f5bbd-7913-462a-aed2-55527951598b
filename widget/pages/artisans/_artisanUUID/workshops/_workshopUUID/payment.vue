<template>
  <b-container
    fluid
    class="row no-gutters text-center overflow-auto justify-content-center align-items-center"
  >
    <div>
      <expires-at
        class="text-left text-danger mb-3"
        :expires-at="bookingExpiresAt"
      />
      <products-table
        :cart="cart"
      />
      <b-row
        no-gutters
        class="align-items-center"
      >
        <div class="col-12 offset-sm-8 col-sm-4">
          <attribute-error v-if="errors" :errors="errors" />
          <payment-source @submit="submit" />
        </div>
      </b-row>
    </div>
  </b-container>
</template>

<script>
import ExpiresAt from '@/components/Atoms/Cart/ExpiresAt'
import ProductsTable from '@/components/Molecules/Tables/ProductsTable'
import PaymentSource from '@/components/Molecules/Inputs/Payment/Source'
import DealsWithOrder from '@/mixins/store/deals-with-order'
import DealsWithBooking from '@/mixins/store/deals-with-booking'
import DealsWithLoader from '@/mixins/deals-with-loader'
import DealsWithErrors from '@/mixins/deals-with-errors'
import AttributeError from '@/components/Atoms/Errors/AttributeError'
import DealsWithCart from '@/mixins/store/deals-with-cart'

export default {
  name: 'PaymentPage',
  components: { AttributeError, PaymentSource, ProductsTable, ExpiresAt },
  mixins: [
    DealsWithOrder,
    DealsWithBooking,
    DealsWithLoader,
    DealsWithErrors,
    DealsWithCart
  ],
  layout: 'payment-funnel',
  middleware (context) {
    if (!context.store.state.booking.booking || !context.store.state.user.user || !context.store.state.user.billingInformation.billingInformation) {
      context.redirect({ name: 'artisans-artisanUUID-workshops-workshopUUID-book', params: context.route.params })
    }
  },
  data () {
    return {
      cart: {
        products: []
      }
    }
  },
  head () {
    return {
      title: this.$t('pages.payment.pageTitle')
    }
  },
  created () {
    if (process.client) {
      const booking = this.getBookingAsProduct()
      this.cart.products.push(booking)
      this.cart.currency = booking.currency
    }
  },
  methods: {
    submit (e) {
      e.preventDefault()
      this.showLoader({
        label: this.$t('form.payment.loader.label')
      })

      // Must be opened outside of the promise otherwise Safari blocks it
      const paymentWindow = window.open('about:blank', this.$t('form.payment.sources.stripe.windowName'), 'dependent=yes,toolbar=0,location=0,menubar=0,directories=0,status=0,minimizable=0,personalbar=0')
      this.payOrder({
        cart_id: this.currentCart?.id,
        cancel_url: this.getStripePaymentUrl(null, {
          success: false
        }),
        success_url: this.getStripePaymentUrl(null, {
          success: true
        })
      }).then((result) => {
        if (this.payment) {
          this.proceedPayment(this.payment, paymentWindow)
            .then((result) => {
              this.hideLoader()
              if (result) {
                this.$router.push({
                  name: 'artisans-artisanUUID-workshops-workshopUUID-receipt',
                  params: this.$route.params
                })
              }
            })
            .catch(() => {
              paymentWindow.close()
              this.hideLoader()
              this.setDefaultErrors()
            })
        } else {
          paymentWindow.close()
          this.hideLoader()
          if (this.orderErrors.length > 0) {
            this.setErrors(this.orderErrors)
          } else {
            this.setDefaultErrors()
          }
        }
      })
        .catch(() => {
          paymentWindow.close()
        })
    }
  }
}
</script>
