<template>
  <b-form-group
    label-size="lg"
    :label="$t('form.booking.slotsCount.label')"
  >
    <component
      :is="slotsSelector"
      v-bind="{ adultCount, childCount }"
      @input-adult="$emit('input-adult', $event)"
      @input-child="$emit('input-child', $event)"
    />
  </b-form-group>
</template>

<script>
import DealsWithArtisan from '@/mixins/store/deals-with-artisan'
import DuoAdultChildCounter from '@/components/Molecules/Inputs/Booking/Counters/DuoAdultChildCounter'
import DuoCounter from '@/components/Molecules/Inputs/Booking/Counters/DuoCounter'
import FamillyCounter from '@/components/Molecules/Inputs/Booking/Counters/FamillyCounter'
import SoloAdultCounter from '@/components/Molecules/Inputs/Booking/Counters/SoloAdultCounter'
import SoloChildCounter from '@/components/Molecules/Inputs/Booking/Counters/SoloChildCounter'

export default {
  name: 'BookingFormSlotsCount',
  components: {
    DuoCounter,
    DuoAdultChildCounter,
    FamillyCounter,
    SoloChildCounter,
    SoloAdultCounter
  },
  mixins: [
    DealsWithArtisan
  ],
  props: {
    adultCount: {
      required: true,
      type: [String, Number]
    },
    childCount: {
      required: true,
      type: [String, Number]
    }
  },
  data () {
    return {
      slotComponentForFormat: {
        1: 'SoloAdultCounter',
        2: 'DuoCounter',
        3: 'SoloChildCounter',
        4: 'DuoAdultChildCounter',
        5: 'FamillyCounter'
      }
    }
  },
  computed: {
    slotsSelector () {
      return this.slotComponentForFormat[this.artisanWorkshop.format.data.id]
    }
  }
}
</script>
