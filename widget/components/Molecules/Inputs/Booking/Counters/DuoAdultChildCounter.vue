<template>
  <div>
    <b-form-select
      :options="countSelectOptions"
      :value="adultCount"
      class="mx-2"
      @input="$emit('input-adult', $event); $emit('input-child', $event);"
    />
  </div>
</template>

<script>
import range from 'lodash/range'
import DealsWithArtisan from '@/mixins/store/deals-with-artisan'

export default {
  name: 'DuoAdultChildCounter',
  mixins: [
    DealsWithArtisan
  ],
  props: {
    adultCount: {
      required: true,
      type: [String, Number]
    },
    childCount: {
      required: true,
      type: [String, Number]
    }
  },
  computed: {
    countSelectOptions () {
      const nbPersMax = (this.artisanWorkshop.nb_pers_max - this.artisanWorkshop.nb_pers_max % 2) / 2

      return range(1, nbPersMax + 1)
        .map((value) => {
          return {
            value,
            text: `${value} ` + (value > 1 ? this.$t('formats.parentChild.place.multiple') : this.$t('formats.parentChild.place.single'))
          }
        })
    }
  },
  created () {
    this.initValues()
  },
  methods: {
    initValues () {
      if (this.countSelectOptions[0]) {
        this.$emit('input-child', this.countSelectOptions[0].value)
        this.$emit('input-adult', this.countSelectOptions[0].value)
      }

      this.$emit('input-child', 1)
      this.$emit('input-adult', 1)
    }
  }
}
</script>
