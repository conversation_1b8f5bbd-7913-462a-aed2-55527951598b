<template>
  <div>
    <b-form-select
      :options="childCountSelectOptions"
      :value="childCount"
      @input="$emit('input-child', $event);"
    />
  </div>
</template>

<script>
import range from 'lodash/range'
import DealsWithArtisan from '@/mixins/store/deals-with-artisan'

export default {
  name: 'SoloChildCounter',
  mixins: [
    DealsWithArtisan
  ],
  props: {
    childCount: {
      required: true,
      type: [String, Number]
    }
  },
  computed: {
    childCountSelectOptions () {
      return range(1, this.artisanWorkshop.nb_pers_max + 1)
        .map((value) => {
          return {
            value,
            text: `${value} ` + (value > 1 ? this.$t('children') : this.$t('child'))
          }
        })
    }
  },
  created () {
    this.initChild()
  },
  methods: {
    initChild () {
      if (this.childCountSelectOptions[0]) {
        this.$emit('input-child', this.childCountSelectOptions[0].value)
      }

      this.$emit('input-child', 1)
    }
  }
}
</script>
