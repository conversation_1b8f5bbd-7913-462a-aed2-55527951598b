import langFr from './lang/fr/messages'
import langEn from './lang/en/messages'
import langNl from './lang/nl/messages'

export default {
  target: 'static',
  ssr: false,

  head: {
    title: 'Wecandoo - Booking widget',
    meta: [
      { charset: 'utf-8' },
      { name: 'robots', content: 'noindex' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' },
      { hid: 'description', name: 'description', content: '' }
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
    ]
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    '@/assets/scss/main.scss'
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    { src: '~/plugins/axios', mode: 'client' },
    { src: '~/plugins/vue-tel-input', mode: 'client' },
    { src: '~/plugins/lodash' },
    { src: '~/plugins/localization' }
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // https://go.nuxtjs.dev/eslint
    '@nuxtjs/eslint-module',
    '@nuxtjs/google-analytics'
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    '@nuxtjs/style-resources',
    // https://go.nuxtjs.dev/bootstrap
    'bootstrap-vue/nuxt',
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',
    'nuxt-i18n'
  ],

  i18n: {
    locales: [
      {
        code: 'en',
        iso: 'en'
      },
      {
        code: 'fr',
        iso: 'fr'
      },
      {
        code: 'nl',
        iso: 'nl'
      }
    ],
    defaultLocale: 'fr',
    strategy: 'no_prefix',
    vueI18n: {
      fallbackLocale: 'fr',
      messages: {
        en: langEn,
        fr: langFr,
        nl: langNl
      }
    }
  },

  styleResources: {
    scss: ['@/assets/scss/main.scss']
  },

  bootstrapVue: {
    icons: true,
    bootstrapCSS: false,
    bootstrapVueCSS: false
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    babel: {
      plugins: ['@babel/plugin-proposal-export-default-from']
    },
    loaders: {
      fontUrl: {}
    }
  }
}
