user:
  - Prefer concise, well-structured code explanations
  - Always follow SOLID and DRY principles in solutions
  - Explain Laravel- or Livewire-specific patterns when used
  - Keep examples clean and relevant, no extra boilerplate
  - Use PHP 8.4 strict types and typing conventions
  - Follow modern OOP best practices in all code

workspace:
  - Use Laravel's built-in features and conventions (e.g., Eloquent, Validation, Policies)
  - Prefer Livewire for dynamic frontend behavior, AlpineJS for lightweight interactivity
  - Use TailwindCSS utility classes for styling, avoid custom CSS unless necessary
  - Follow PSR-12 for PHP code style
  - Use meaningful class and method names (verbs for methods, nouns for classes)
  - Controllers must stay thin; move logic to Services or Actions
  - Blade components should be reusable and single-responsibility
  - Avoid facades in core logic; use dependency injection
  - Organize frontend logic clearly: AlpineJS for state, Livewire for server interaction
  - Do not use inline JavaScript or CSS
  - Prefer Enums, DTOs, and typed properties where applicable
  - Avoid using magic strings and numbers; use constants or config
  - Reject outdated Laravel practices (e.g., using `view()->share()` globally)
