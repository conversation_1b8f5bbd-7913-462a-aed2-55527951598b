user nginx;
worker_processes auto; # Should be 4 on prod: grep -c ^processor /proc/cpuinfo
pid /run/nginx.pid;
include /etc/nginx/modules/*.conf;
worker_shutdown_timeout 25s;

error_log /dev/stderr crit;

events {
    worker_connections 4096;
    multi_accept on;
    use epoll;
}

http {
    server_tokens off;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    types_hash_max_size 2048;
    map_hash_bucket_size 2048; # To remove once we no longer filter prerender URIs
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    gzip on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_comp_level 5;
    gzip_vary on;
    gzip_types text/plain text/css application/json application/ld+json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/x-font-ttf application/javascript font/eot font/opentype image/svg+xml image/x-icon;
    open_file_cache max=2000 inactive=5m;

    client_body_buffer_size 1M;
    client_max_body_size 200M;

    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;

    real_ip_header X-Forwarded-For;
    set_real_ip_from 0.0.0.0/0;

    map $http_x_request_id $reqid {
        default   $http_x_request_id;
        ""        ng-$request_id;
    }

    map $gzip_ratio $gzip_ratio_number {
        default   $gzip_ratio;
        ""        1;
    }

    map $upstream_response_time $upstream_response_time_int {
      default $upstream_response_time;
      ""      0;
    }

    map $upstream_header_time $upstream_header_time_int {
      default $upstream_header_time;
      ""      0;
    }

    map $upstream_connect_time $upstream_connect_time_int {
      default $upstream_connect_time;
      ""      0;
    }

    map $status $level_name {
      default INFO;
      ~^5     ERROR;
      ~^4     WARNING;
    }

    map $msec $msec_no_decimal { ~(.*)\.(.*) $1$2; }

    log_format json_short escape=json
    '{'
      '"message":"$request_method $request_uri",'
      '"http":{'
        '"status_code":$status,'
        '"method":"$request_method",'
        '"url":"$request_uri",'
        '"host":"$host",'
        '"useragent":"$http_user_agent",'
        '"referrer":"$http_referer",'
        '"x_forwarded_for":"$http_x_forwarded_for",'
        '"x_forwarded_host":"$http_x_forwarded_host",'
        '"x_real_ip":"$http_x_real_ip",'
      '},'
      '"response":{'
        '"location":"$sent_http_location"'
      '},'
      '"network":{'
        '"bytes_written":$bytes_sent,'
        '"bytes_read":$request_length,'
        '"body_bytes_sent":$body_bytes_sent,'
        '"gzip_ratio":$gzip_ratio_number,'
        '"client":{'
          '"ip":"$remote_addr"'
        '}'
      '},'
      '"timestamp":"$msec_no_decimal",'
      '"time_local":"$time_iso8601",'
      '"level_name":"$level_name"'
    '}';

	log_format json_php escape=json
        '{'
          '"message":"$request_method $request_uri",'
          '"http":{'
            '"status_code":$status,'
            '"method":"$request_method",'
            '"url":"$request_uri",'
            '"host":"$host",'
            '"useragent":"$http_user_agent",'
            '"referrer":"$http_referer",'
            '"x_forwarded_for":"$http_x_forwarded_for",'
            '"x_forwarded_host":"$http_x_forwarded_host",'
            '"x_real_ip":"$http_x_real_ip"'
          '},'
          '"response":{'
            '"location":"$sent_http_location"'
          '},'
          '"network":{'
            '"bytes_written":$bytes_sent,'
            '"bytes_read":$request_length,'
            '"body_bytes_sent":$body_bytes_sent,'
            '"gzip_ratio":$gzip_ratio_number,'
            '"client":{'
              '"ip":"$remote_addr"'
            '},'
            '"nginx":{'
              '"request_time":$request_time,'
              '"upstream_connect_time":"$upstream_connect_time_int",'
              '"upstream_header_time":"$upstream_header_time_int",'
              '"upstream_response_time":"$upstream_response_time_int",'
              '"upstream_cache_status":"$upstream_cache_status"'
            '}'
          '},'
          '"timestamp":"$msec_no_decimal",'
          '"time_local":"$time_iso8601",'
          '"extra":{"request_id":"$reqid","is_bot":"$is_bot", "handled_by_prerender":"$prerender_allowed"},'
          '"dd":{"env":"__ENV__","service":"nginx","version":"__VERSION__","trace_id":"$sent_http_x_trace_id"},'
          '"level_name":"$level_name"'
        '}';

    include /etc/nginx/conf.d/*.conf;
}
